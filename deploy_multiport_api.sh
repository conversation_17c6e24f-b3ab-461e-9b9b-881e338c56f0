#!/bin/bash

# 部署多端口API服务脚本

echo "=========================================="
echo "部署单服务多端口API架构"
echo "=========================================="

# 停止旧的服务
echo "1. 停止旧的独立API服务..."
sudo systemctl stop Douyin_TikTok_Download_API_New 2>/dev/null
sudo systemctl stop Douyin_TikTok_API_Instance1 2>/dev/null
sudo systemctl stop Douyin_TikTok_API_Instance2 2>/dev/null
sudo systemctl stop Douyin_TikTok_API_Instance3 2>/dev/null
sudo systemctl disable Douyin_TikTok_Download_API_New 2>/dev/null
sudo systemctl disable Douyin_TikTok_API_Instance1 2>/dev/null
sudo systemctl disable <PERSON><PERSON><PERSON>_TikTok_API_Instance2 2>/dev/null
sudo systemctl disable Douyin_TikTok_API_Instance3 2>/dev/null

# 安装新的systemd服务
echo "2. 安装新的多端口API服务..."
sudo cp /home/<USER>/API/Douyin_TikTok_API_Multiport.service /etc/systemd/system/
sudo systemctl daemon-reload

# 启用并启动新服务
echo "3. 启用并启动新服务..."
sudo systemctl enable Douyin_TikTok_API_Multiport.service
sudo systemctl start Douyin_TikTok_API_Multiport.service

# 检查服务状态
echo "4. 检查服务状态..."
sleep 5
sudo systemctl status Douyin_TikTok_API_Multiport.service --no-pager

# 检查各端口
echo "5. 检查各端口监听状态..."
for port in 8080 8081 8082 8083; do
    echo -n "端口 $port: "
    if netstat -tln | grep -q ":$port "; then
        echo "✓ 监听中"
    else
        echo "✗ 未监听"
    fi
done

echo ""
echo "部署完成！"
echo ""
echo "API服务现在运行在以下端口："
echo "- 实例0: http://localhost:8080"
echo "- 实例1: http://localhost:8081"
echo "- 实例2: http://localhost:8082"
echo "- 实例3: http://localhost:8083"
echo ""
echo "查看日志: sudo journalctl -u Douyin_TikTok_API_Multiport -f"