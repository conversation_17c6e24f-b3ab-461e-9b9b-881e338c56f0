# API

这个仓库包含两个主要项目：
- Douyin_TikTok_Download_API：抖音/TikTok视频下载API
- local-singbox：本地代理服务配置

## 安装步骤

### 1. 克隆仓库
```bash
git clone https://github.com/ohiu/API.git
cd API
```

### 2. Douyin_TikTok_Download_API 设置
进入目录并安装依赖：
```bash
cd Douyin_TikTok_Download_API
pip install -r requirements.txt
```

启动服务：
```bash
# 使用脚本启动
./start.sh

# 或者使用Python启动
python3 start.py
```

### 3. local-singbox 设置
配置和启动local-singbox服务：
```bash
cd local-singbox

# 如果需要，先修改配置文件
nano config/config.json

# 启动服务
scripts/singbox_service.sh start
```

### 4. 日志管理配置
该项目已配置logrotate来管理日志文件大小，防止日志文件过大：

```bash
# 创建logrotate配置链接
sudo ln -sf "$(pwd)/local-singbox/logrotate.conf" /etc/logrotate.d/local-singbox
sudo chown root:root /etc/logrotate.d/local-singbox

# 手动执行一次日志轮转（如需要）
sudo logrotate -f /etc/logrotate.d/local-singbox
```

### 5. 设置系统自启动
为确保服务在系统重启后自动运行，可以设置systemd服务：

#### local-singbox服务自启动
```bash
# 复制服务文件到系统目录
sudo cp local-singbox/integrated-singbox.service /etc/systemd/system/

# 编辑服务文件，确保路径正确
sudo nano /etc/systemd/system/integrated-singbox.service

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable integrated-singbox.service
sudo systemctl start integrated-singbox.service

# 检查服务状态
sudo systemctl status integrated-singbox.service
```

#### Douyin/TikTok API服务自启动
```bash
# 复制服务文件到系统目录
sudo cp Douyin_TikTok_Download_API_New.service /etc/systemd/system/

# 编辑服务文件，确保路径正确
sudo nano /etc/systemd/system/Douyin_TikTok_Download_API_New.service

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable Douyin_TikTok_Download_API_New.service
sudo systemctl start Douyin_TikTok_Download_API_New.service

# 检查服务状态
sudo systemctl status Douyin_TikTok_Download_API_New.service
```

## 服务配合说明

这两个服务之间的配合关系如下：

1. **网络连接配合**：
   - local-singbox提供网络代理服务，Douyin_TikTok_Download_API可以通过此代理连接外部服务
   - 在`Douyin_TikTok_Download_API/config.yaml`中可以配置代理设置，确保它指向local-singbox的地址和端口

2. **监控与重启**：
   - local-singbox中的`scripts/monitor_douyin_api.py`脚本用于监控Douyin_TikTok_Download_API服务状态
   - 当检测到API服务不可用时，监控脚本会尝试自动重启服务

3. **启动顺序**：
   - 先启动local-singbox服务
   - 再启动Douyin_TikTok_Download_API服务

4. **配置文件关联**：
   - 确保两个服务的配置文件中的IP地址、端口等参数相互匹配
   - 如果修改了一个服务的配置，可能需要相应调整另一个服务的配置

## 文件结构
- `Douyin_TikTok_Download_API/`: 抖音/TikTok视频下载API
- `local-singbox/`: 本地代理服务配置
  - `config/`: 配置文件目录 
  - `scripts/`: 脚本文件目录
  - `logs/`: 日志文件目录（由.gitignore忽略）
- `Douyin_TikTok_Download_API_New.service`: 抖音API的systemd服务文件

## 注意事项
1. 确保服务器上已安装Python 3.6+
2. 确保有足够的权限运行脚本和设置logrotate
3. 日志文件设置为超过10MB自动轮转，保留最近7个版本
4. 如遇服务启动失败，请检查日志文件了解详细错误信息
5. 修改配置文件后需要重启相应服务

## 更多信息
- 抖音/TikTok API详情可参考 `Douyin_TikTok_Download_API/README.md`
- local-singbox配置详情可参考 `local-singbox/README.md`
