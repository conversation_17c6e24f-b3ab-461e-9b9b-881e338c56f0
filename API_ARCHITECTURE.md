# API服务架构说明

## 当前API服务配置

我们现在运行**4个独立的API实例**，每个实例都有：
- 独立的代码目录
- 独立的端口配置  
- 独立的代理配置
- 独立的Cookie配置

### 服务详情

| 实例名称 | 端口 | 代理端口 | 服务名称 | 工作目录 |
|---------|-----|----------|----------|----------|
| API实例0 | 8080 | 1080 | Douyin_TikTok_Download_API_New | /home/<USER>/API/Douyin_TikTok_API_Instance0 |
| API实例1 | 8081 | 1081 | Douyin_TikTok_API_Instance1 | /home/<USER>/API/Douyin_TikTok_API_Instance1 |
| API实例2 | 8082 | 1082 | Douyin_TikTok_API_Instance2 | /home/<USER>/API/Douyin_TikTok_API_Instance2 |
| API实例3 | 8083 | 1083 | Douyin_TikTok_API_Instance3 | /home/<USER>/API/Douyin_TikTok_API_Instance3 |

### 为什么使用独立实例？

1. **避免共享代码风险**: 防止一个实例的问题影响其他实例
2. **独立配置管理**: 每个实例可以使用不同的Cookie和代理
3. **故障隔离**: 单个实例故障不会影响其他实例
4. **负载分散**: 分散请求负载，提高系统稳定性

### 代码管理策略

- **原始代码**: `Douyin_TikTok_Download_API/` (在git中跟踪)
- **独立实例**: `Douyin_TikTok_API_Instance0-3/` (在git中忽略)
- **重建脚本**: `setup_independent_instances.sh` (可重建所有独立实例)

### 配置文件

每个实例的配置文件位于：
```
Douyin_TikTok_Download_API/crawlers/douyin/web/
├── config.yaml (Instance0使用)
├── config_instance1.yaml (Instance1使用)  
├── config_instance2.yaml (Instance2使用)
└── config_instance3.yaml (Instance3使用)
```

### 集成监控

所有实例由 `integrated-monitor-multi.service` 统一监控：
- 自动Cookie更新
- 智能节点切换
- 故障自动恢复
- Telegram通知

### 智能节点管理

系统具备智能节点优化功能：
- **自动延迟检测**: 排除高延迟节点(>400ms)
- **质量分级**: 自动选择最优质节点
- **定期优化**: 每4小时重新评估节点质量
- **故障预防**: 主动排除劣质节点

### 服务管理命令

```bash
# 启动所有服务
sudo systemctl start Douyin_TikTok_Download_API_New Douyin_TikTok_API_Instance1 Douyin_TikTok_API_Instance2 Douyin_TikTok_API_Instance3

# 停止所有服务  
sudo systemctl stop Douyin_TikTok_Download_API_New Douyin_TikTok_API_Instance1 Douyin_TikTok_API_Instance2 Douyin_TikTok_API_Instance3

# 查看所有服务状态
sudo systemctl status Douyin_TikTok_Download_API_New Douyin_TikTok_API_Instance1 Douyin_TikTok_API_Instance2 Douyin_TikTok_API_Instance3

# 重建所有独立实例
./setup_independent_instances.sh
```

### 测试工具

- `local-singbox/scripts/test_proxy_ips.py`: 测试所有代理端口IP
- `test_smart_nodes.py`: 测试智能节点选择功能

### 注意事项

1. **不要手动修改独立实例代码**: 使用配置文件进行自定义
2. **定期备份配置**: 重要的配置变更应该提交到git
3. **监控日志**: 使用 `journalctl -u <服务名>` 查看服务日志
4. **集成监控日志**: 使用 `journalctl -u integrated-monitor-multi.service` 查看监控日志