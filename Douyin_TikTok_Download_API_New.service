[Unit]
Description=Douyin_TikTok_Download_API_New daemon
After=network.target

[Service]
User=ubuntu
WorkingDirectory=/home/<USER>/API/Douyin_TikTok_Download_API
ExecStart=/usr/bin/python3 /home/<USER>/API/Douyin_TikTok_Download_API/start.py
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=douyinapi
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
