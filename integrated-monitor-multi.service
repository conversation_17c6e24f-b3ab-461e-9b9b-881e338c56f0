[Unit]
Description=Integrated Monitor Service (Multi-Instance)
After=network.target singbox.service

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/API/local-singbox/scripts
ExecStart=/usr/bin/python3 /home/<USER>/API/local-singbox/scripts/integrated_monitor_multi.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=integrated-monitor-multi
Environment="PYTHONUNBUFFERED=1"
Environment="JOURNAL_STREAM=1"

# 确保服务依赖
Wants=singbox.service

[Install]
WantedBy=multi-user.target