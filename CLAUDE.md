# 集成监控服务系统文档

## 一、系统概述

集成监控服务是一个全自动化的服务管理系统，用于管理和监控抖音/TikTok数据采集API服务。该系统实现了服务的自动化运维、故障恢复、性能优化和状态监控。我们的主程序不应该被任何新创建的程序取代，请在原程序修改，绝对不能创建新的程序替代原来的主程序。w

### 1.1 核心目标

1. **高可用性**：确保API服务24/7稳定运行
2. **自动故障恢复**：自动检测并修复常见错误
3. **负载均衡**：通过多端口部署分散请求压力
4. **智能代理管理**：自动切换代理节点，选择最优线路
5. **Cookie池管理**：自动更新和轮换Cookie，避免被封禁
6. **实时监控**：实时监控服务状态并发送通知

### 1.2 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    集成监控服务 (Node.js)                      │
│  integrated-monitor-multi-js.service                         │
├─────────────────────────────────────────────────────────────┤
│  • 环境清理与初始化                                            │
│  • 服务状态监控                                               │
│  • 错误检测与自动修复                                          │
│  • Cookie池管理                                               │
│  • 代理节点管理                                               │
│  • 订阅更新管理                                               │
│  • 统计数据收集                                               │
│  • 消息通知 (Supabase)                                        │
└─────────────────────────────────────────────────────────────┘
                               │
                               │ 管理
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                         被管理的服务                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────┐    ┌──────────────────────────┐  │
│  │   Singbox代理服务     │    │    多端口API服务          │  │
│  │  singbox.service    │    │ Douyin_TikTok_API_       │  │
│  │                     │    │ Multiport.service        │  │
│  │  端口:              │    │                          │  │
│  │  • 1080-1083 (SOCKS)│    │  端口:                   │  │
│  │  • 9090 (Clash API) │    │  • 8080-8083 (HTTP API)  │  │
│  └─────────────────────┘    └──────────────────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 二、核心组件详解

### 2.1 Singbox代理服务

**功能**：提供SOCKS5代理服务，支持多节点切换

**配置文件**：`/home/<USER>/API/local-singbox/config/config.json`

**端口分配**：
- 1080: 代理端口0 (proxy0) → API实例0使用
- 1081: 代理端口1 (proxy1) → API实例1使用
- 1082: 代理端口2 (proxy2) → API实例2使用
- 1083: 代理端口3 (proxy3) → API实例3使用
- 9090: Clash API端口（用于节点管理）

**特性**：
- 支持自动订阅更新
- 智能节点选择（基于延迟）
- 节点质量监控
- 自动故障转移

### 2.2 多端口API服务

**功能**：提供抖音/TikTok数据采集API

**启动脚本**：`/home/<USER>/API/Douyin_TikTok_Download_API/start_multiport.py`

**进程结构**：
- 1个主进程：负责进程管理和监控
- 4个工作进程：分别监听不同端口

**端口分配**：
```
端口 8080 → API实例0 → 使用代理端口 1080 → 配置文件 config.yaml
端口 8081 → API实例1 → 使用代理端口 1081 → 配置文件 config_instance1.yaml
端口 8082 → API实例2 → 使用代理端口 1082 → 配置文件 config_instance2.yaml
端口 8083 → API实例3 → 使用代理端口 1083 → 配置文件 config_instance3.yaml
```

**每个实例独立配置**：
- 独立的Cookie
- 独立的代理端口
- 独立的配置文件
- 独立的错误计数

### 2.3 集成监控服务

**主程序**：`/home/<USER>/API/local-singbox/scripts/integrated_monitor_multi.js`

**配置文件**：`/home/<USER>/API/local-singbox/config/integrated_config_multi.yaml`

**核心功能模块**：

#### 2.3.1 环境清理模块
```javascript
cleanupEnvironment()
```
- 停止并禁用冗余服务
- 清理端口占用
- 清理遗留进程
- 验证必要服务状态

#### 2.3.2 服务初始化模块
```javascript
initializeServices()
```
- 执行环境清理
- 重启所有服务
- 初始化Cookie池
- 分配Cookie给各实例
- 执行节点优化

#### 2.3.3 Cookie池管理
```javascript
// Cookie池相关函数
loadCookiePool()      // 加载Cookie池
saveCookiePool()      // 保存Cookie池
getCookieFromPool()   // 获取Cookie
addToCookiePool()     // 添加Cookie
refreshCookiePool()   // 刷新Cookie池
fetchDouyinCookie()   // 从TikHub获取新Cookie
```

**Cookie池特性**：
- 容量：20个Cookie
- 自动轮换使用
- 定时刷新（每6小时）
- 失效自动更新
- TikHub API集成

#### 2.3.4 代理节点管理
```javascript
// 节点管理相关函数
switchAllNodes()           // 批量切换节点
selectNextNodes()          // 智能选择节点
testNodeLatency()          // 测试节点延迟
ensureDifferentNodes()     // 确保节点不重复
selectBestNodesForGroups() // 选择最优节点
```

**节点切换策略**：
- 每2分钟自动切换
- 基于延迟的智能选择
- 节点质量分级（优秀/良好/可接受/较差）
- 使用历史统计优化选择
- 确保4个实例使用不同节点

#### 2.3.5 错误监控与修复
```javascript
// 错误监控相关函数
checkApiErrors()          // 检查API错误
handleApiInstanceError()  // 处理错误
apiMonitorThread()        // API监控线程
```

**错误检测机制**：
- 每分钟检查systemd日志
- 端口级别的错误识别
- 错误模式匹配
- 错误频率统计

**自动修复流程**：
1. 检测到错误（Cookie失效、验证码等）
2. 从Cookie池获取新Cookie
3. 更新配置文件
4. 重启服务
5. 设置90秒监控忽略期

#### 2.3.6 订阅管理
```javascript
// 订阅管理相关函数
updateSubscription()      // 更新订阅
parseSubscriptionContent() // 解析订阅内容
generateSingBoxConfig()   // 生成配置
```

**订阅特性**：
- 支持多订阅链接
- 自动/手动模式切换
- 每12小时自动更新
- Base64解码支持
- SS/VMess协议支持

#### 2.3.7 统计与报告
```javascript
// 统计相关函数
recordApiRequest()           // 记录请求
getInstanceStatisticsSummary() // 获取统计摘要
generateStatusReport()       // 生成状态报告
saveStatistics()            // 保存统计数据
```

**统计指标**：
- 请求总数/成功率
- 每小时请求数
- 响应时间统计
- 错误类型分布
- 节点使用统计

## 三、监控策略

### 3.1 错误模式

**立即行动错误**（触发自动修复）：
- "Resolved Douyin with cookies"
- "搜索接口响应失败:"
- "msToken=undefined"
- "list is empty"
- "odin_tt"
- "UserProfileException"
- "请求失败"
- "post count is 0"
- "_signature"
- "失败结果: 用户"

**标准错误**（计入错误统计）：
- "ERROR"
- "Exception"
- "Failed"
- "错误"
- "失败"

### 3.2 监控时间设置

- **错误检查间隔**：30秒
- **错误统计窗口**：5分钟
- **错误阈值**：3次/窗口
- **状态报告间隔**：12小时
- **Cookie刷新间隔**：6小时
- **节点切换间隔**：2分钟
- **节点质量检查**：4小时
- **订阅更新间隔**：12小时

### 3.3 端口识别机制

日志格式要求：
```
2025-01-13 10:30:45 - PORT:8080 - ERROR - Cookie失效
```

识别逻辑：
1. 解析日志中的端口标识（PORT:xxxx）
2. 只处理属于当前实例端口的错误
3. 无端口标识的错误只由第一个实例处理

## 四、通信机制

### 4.1 Supabase集成

**功能**：
- 发送状态通知
- 接收控制命令
- 实时命令监听

**支持的命令**：
- `sub1`: 切换到订阅链接1
- `sub2`: 切换到订阅链接2
- `switch`: 切换到自动模式
- `status`: 生成状态报告

### 4.2 消息类型

1. **启动通知**：服务启动时的初始化报告
2. **状态报告**：定期的详细状态信息
3. **错误通知**：自动修复的错误信息
4. **节点切换**：代理节点切换报告
5. **订阅更新**：订阅更新结果
6. **环境清理**：环境清理报告

## 五、故障恢复机制

### 5.1 服务级别恢复

1. **进程崩溃**：systemd自动重启
2. **端口占用**：清理非预期进程
3. **配置错误**：恢复默认配置

### 5.2 应用级别恢复

1. **Cookie失效**：
   - 自动从池中获取新Cookie
   - 更新配置文件
   - 重启相应服务

2. **代理故障**：
   - 自动切换到其他节点
   - 基于延迟选择最优节点

3. **API错误**：
   - 错误频率监控
   - 触发阈值后自动修复

### 5.3 监控服务自身恢复

1. **重复进程检测**：启动时清理重复进程
2. **内存泄漏预防**：定期重启（通过systemd）
3. **异常捕获**：所有关键操作都有try-catch保护

## 六、部署与维护

### 6.1 系统要求

- **操作系统**：Ubuntu 20.04+
- **Node.js**：14.0+
- **Python**：3.8+
- **权限**：sudo权限（用于systemctl和端口管理）

### 6.2 依赖包

**Node.js依赖**：
```json
{
  "dependencies": {
    "js-yaml": "^4.1.0",
    "axios": "^1.6.0",
    "@supabase/supabase-js": "^2.39.0"
  }
}
```

### 6.3 服务管理命令

```bash
# 查看服务状态
sudo systemctl status integrated-monitor-multi-js

# 查看服务日志
sudo journalctl -u integrated-monitor-multi-js -f

# 重启服务
sudo systemctl restart integrated-monitor-multi-js

# 停止服务
sudo systemctl stop integrated-monitor-multi-js

# 启用开机自启
sudo systemctl enable integrated-monitor-multi-js
```

### 6.4 故障排查

1. **检查端口占用**：
```bash
ss -tlnp | grep -E "(8080|8081|8082|8083|1080|1081|1082|1083|9090)"
```

2. **检查进程状态**：
```bash
ps aux | grep -E "(node.*integrated|python.*multiport|sing-box)"
```

3. **查看错误日志**：
```bash
sudo journalctl -u integrated-monitor-multi-js --since "10 minutes ago"
```

4. **手动测试API**：
```bash
curl http://localhost:8080/api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo
```

## 七、性能优化

### 7.1 资源使用

- **CPU**：约5-10%（正常运行）
- **内存**：约200-300MB（Node.js进程）
- **网络**：取决于API请求量

### 7.2 优化策略

1. **并发处理**：多个API实例并行处理请求
2. **连接复用**：HTTP Keep-Alive
3. **日志轮转**：避免日志文件过大
4. **定期清理**：自动清理过期数据

---

**版本**：2.1  
**更新日期**：2025-01-14  
**作者**：Cascade AI  
**维护者**：System Administrator
