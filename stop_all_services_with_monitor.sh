#!/bin/bash

echo "======================================"
echo "停止所有API服务实例和监控服务"
echo "======================================"

# 停止监控服务 (JS版本)
echo ""
echo "1. 停止集成监控服务 (JS版本)..."
echo "------------------------"
sudo systemctl stop integrated-monitor-multi-js.service
echo "监控服务已停止"

# 停止所有API服务
echo ""
echo "2. 停止API服务实例..."
echo "------------------------"

# 停止原始服务
echo "停止 API实例0 (端口 8080)..."
sudo systemctl stop Douyin_TikTok_Download_API_New.service

# 停止新的3个实例
for i in 1 2 3; do
    port=$((8080 + i))
    echo "停止 API实例$i (端口 $port)..."
    sudo systemctl stop Douyin_TikTok_API_Instance$i.service
done

echo ""
echo "======================================"
echo "所有服务已停止！"
echo "======================================"