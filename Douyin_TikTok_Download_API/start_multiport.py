#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多端口API服务启动脚本
支持在多个端口上运行API服务，每个端口使用独立的配置文件
"""

import asyncio
import os
import sys
import signal
import logging
import uvicorn
from multiprocessing import Process, Queue
from typing import List, Dict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 默认配置
DEFAULT_CONFIGS = [
    {
        "name": "API实例0",
        "port": 8080,
        "config_file": "config.yaml",
        "proxy_port": 1080
    },
    {
        "name": "API实例1", 
        "port": 8081,
        "config_file": "config_instance1.yaml",
        "proxy_port": 1081
    },
    {
        "name": "API实例2",
        "port": 8082,
        "config_file": "config_instance2.yaml",
        "proxy_port": 1082
    },
    {
        "name": "API实例3",
        "port": 8083,
        "config_file": "config_instance3.yaml",
        "proxy_port": 1083
    }
]

# 存储所有进程
processes: List[Process] = []
queue = Queue()

def run_api_instance(config: Dict, queue: Queue):
    """运行单个API实例"""
    try:
        # 设置环境变量，指定配置文件
        os.environ['API_CONFIG_FILE'] = config['config_file']
        
        # 设置douyin爬虫的配置文件
        douyin_config_name = config['config_file'].replace('.yaml', '')
        if douyin_config_name != 'config':
            os.environ['DOUYIN_CONFIG_FILE'] = f"config_{douyin_config_name.split('_')[-1]}.yaml"
        else:
            os.environ['DOUYIN_CONFIG_FILE'] = 'config.yaml'
        
        # 设置代理端口环境变量（如果需要）
        os.environ['PROXY_PORT'] = str(config['proxy_port'])
        
        logger.info(f"启动 {config['name']} - 端口: {config['port']}, 配置: {config['config_file']}, Douyin配置: {os.environ.get('DOUYIN_CONFIG_FILE')}")
        
        # 导入并运行FastAPI应用
        from app.main import app
        import logging
        
        # 创建自定义日志配置，包含端口标识
        log_config = {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
                "access": {
                    "format": f"PORT:{config['port']} - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
                "access": {
                    "formatter": "access", 
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": "INFO"},
                "uvicorn.error": {"level": "INFO"},
                "uvicorn.access": {"handlers": ["access"], "level": "INFO", "propagate": False},
            },
        }
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=config['port'],
            log_level="info",
            access_log=True,
            log_config=log_config
        )
        
    except Exception as e:
        logger.error(f"{config['name']} 启动失败: {e}")
        queue.put(f"{config['name']} 启动失败: {e}")
        sys.exit(1)

def start_all_instances():
    """启动所有API实例"""
    global processes
    
    logger.info("开始启动多端口API服务...")
    
    for config in DEFAULT_CONFIGS:
        process = Process(
            target=run_api_instance,
            args=(config, queue),
            name=config['name']
        )
        process.start()
        processes.append(process)
        logger.info(f"{config['name']} 进程已启动 (PID: {process.pid})")
    
    logger.info(f"已启动 {len(processes)} 个API实例")

def stop_all_instances():
    """停止所有API实例"""
    global processes
    
    logger.info("正在停止所有API实例...")
    
    for process in processes:
        if process.is_alive():
            logger.info(f"停止进程 {process.name} (PID: {process.pid})")
            process.terminate()
            process.join(timeout=5)
            
            if process.is_alive():
                logger.warning(f"强制停止进程 {process.name}")
                process.kill()
                process.join()
    
    processes.clear()
    logger.info("所有API实例已停止")

def signal_handler(signum, frame):
    """处理信号"""
    logger.info(f"接收到信号 {signum}")
    stop_all_instances()
    sys.exit(0)

def monitor_processes():
    """监控进程状态"""
    while True:
        try:
            # 检查队列中的错误消息
            while not queue.empty():
                error_msg = queue.get_nowait()
                logger.error(f"进程错误: {error_msg}")
            
            # 检查进程状态
            for i, process in enumerate(processes):
                if not process.is_alive():
                    config = DEFAULT_CONFIGS[i]
                    logger.warning(f"{config['name']} 已停止，正在重启...")
                    
                    # 重启进程
                    new_process = Process(
                        target=run_api_instance,
                        args=(config, queue),
                        name=config['name']
                    )
                    new_process.start()
                    processes[i] = new_process
                    logger.info(f"{config['name']} 已重启 (PID: {new_process.pid})")
            
            # 等待一段时间再检查
            asyncio.run(asyncio.sleep(10))
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            logger.error(f"监控进程时出错: {e}")

def main():
    """主函数"""
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动所有实例
        start_all_instances()
        
        # 监控进程
        monitor_processes()
        
    except Exception as e:
        logger.error(f"主程序错误: {e}")
        stop_all_instances()
        sys.exit(1)

if __name__ == "__main__":
    main()