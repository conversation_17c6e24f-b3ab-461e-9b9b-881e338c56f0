# Web
Web:
  # APP Switch
  PyWebIO_Enable: true    # Enable APP | 启用APP

  # APP Information
  Domain: https://douyin.wtf    # Web domain | Web域名

  # APP Configuration
  PyWebIO_Theme: minty    # PyWebIO theme | PyWebIO主题
  Max_Take_URLs: 30    # Maximum number of URLs that can be taken at a time | 一次最多可以取得的URL数量


  # Web Information
  Tab_Title: Douyin_TikTok_Download_API    # Web title | Web标题
  Description: Douyin_TikTok_Download_API is a free open-source API service for Douyin/TikTok. It provides a simple, fast, and stable API for developers to develop applications based on Douyin/TikTok.    # Web description | Web描述
  Favicon: https://raw.githubusercontent.com/Evil0ctal/Douyin_TikTok_Download_API/main/logo/logo192.png    # Web favicon | Web图标

  # Fun Configuration
  Easter_Egg: true    # Enable Easter Egg | 启用彩蛋
  Live2D_Enable: true
  Live2D_JS: https://fastly.jsdelivr.net/gh/TikHubIO/TikHub_live2d@latest/autoload.js

# API
API:
  # Network Configuration
  Host_IP: 0.0.0.0    # default IP | 默认IP
  Host_Port: 8080    # default port is 80 | 默认端口为80
  Docs_URL: /docs    # API documentation URL | API文档URL
  Redoc_URL: /redoc    # API documentation URL | API文档URL

  # API Information
  Version: V4.1.2    # API version | API版本
  Update_Time: 2025/03/16    # API update time | API更新时间
  Environment: Demo    # API environment | API环境

  # Download Configuration
  Download_Switch: true    # Enable download function | 启用下载功能

  # File Configuration
  Download_Path: "./download"    # Default download directory | 默认下载目录
  Download_File_Prefix: "douyin.wtf_"    # Default download file prefix | 默认下载文件前缀


# iOS Shortcut
iOS_Shortcut:
  iOS_Shortcut_Version: 7.0
  iOS_Shortcut_Update_Time: 2024/07/05
  iOS_Shortcut_Link: https://www.icloud.com/shortcuts/06f891a026df40cfa967a907feaea632
  iOS_Shortcut_Link_EN: https://www.icloud.com/shortcuts/06f891a026df40cfa967a907feaea632
  iOS_Shortcut_Update_Note: 重构了快捷指令以兼容TikHub API。
  iOS_Shortcut_Update_Note_EN: Refactored the shortcut to be compatible with the TikHub API.
