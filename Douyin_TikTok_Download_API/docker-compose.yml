version: "3.9"  # Docker Compose 文件版本

services:  # 定义服务列表
  douyin_tiktok_download_api:  # 服务名称
    image: evil0ctal/douyin_tiktok_download_api  # 使用的 Docker 镜像
    network_mode: host  # 使用主机网络模式
    container_name: douyin_tiktok_download_api  # 容器名称
    restart: always  # 容器退出后总是重启
    volumes: # 挂载卷配置
      - ./douyin_tiktok_download_api/douyin_web/config.yaml:/app/crawlers/douyin/web/config.yaml
      - ./douyin_tiktok_download_api/tiktok_web/config.yaml:/app/crawlers/tiktok/web/config.yaml
      - ./douyin_tiktok_download_api/tiktok_app/config.yaml:/app/crawlers/tiktok/app/config.yaml
    environment:  # 环境变量配置
      TZ: Asia/Shanghai  # 设置时区为亚洲/上海
      PUID: 1026  # 设置容器内部的用户 ID
      PGID: 100  # 设置容器内部的用户组 ID
    privileged: true  # 设置特权模式以便容器内部可以执行特权操作
