# ==============================================================================
# Copyright (C) 2021 Evil0ctal
#
# This file is part of the Douyin_TikTok_Download_API project.
#
# This project is licensed under the Apache License 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at:
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

import sys
import os
import yaml
import uvicorn

# 获取配置文件路径参数
if len(sys.argv) > 1:
    config_file = sys.argv[1]
else:
    config_file = 'config.yaml'

# 读取配置文件
config_path = os.path.join(os.path.dirname(__file__), config_file)
with open(config_path, 'r', encoding='utf-8') as file:
    config = yaml.safe_load(file)

# 设置环境变量，让app.main.py读取正确的配置
os.environ['API_CONFIG_FILE'] = config_file

Host_IP = config['API']['Host_IP']
Host_Port = config['API']['Host_Port']

if __name__ == '__main__':
    uvicorn.run('app.main:app', host=Host_IP, port=Host_Port, reload=True, log_level="info")