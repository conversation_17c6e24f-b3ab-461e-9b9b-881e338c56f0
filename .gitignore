# 忽略日志文件
*.log
*.log.*
local-singbox/logs/
local-singbox/**/*.log
local-singbox/**/*.log.*

# 忽略pid文件
*.pid
local-singbox/*.pid
local-singbox/**/*.pid
monitor_api.pid
singbox.pid
singbox_service.pid
integrated_monitor.pid

# 忽略内部Git仓库(但保留其内容)
Douyin_TikTok_Download_API/.git/

# 忽略独立API实例目录(这些是从同一源码克隆的完整副本)
# 可以通过 setup_independent_instances.sh 脚本重建
Douyin_TikTok_API_Instance0/
Douyin_TikTok_API_Instance1/
Douyin_TikTok_API_Instance2/
Douyin_TikTok_API_Instance3/

# 忽略临时测试文件
test_smart_nodes.py 

local-singbox/scripts/node_modules/