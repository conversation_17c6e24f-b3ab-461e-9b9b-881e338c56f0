# 抖音API服务部署指南

## 文件结构
- app: API应用程序代码
- crawlers: 抖音爬虫代码
- config.yaml: 主配置文件
- start.py: 启动脚本

## 安装步骤
1. 解压缩归档文件:
   ```bash
   tar -xzvf douyin_api_backup_*.tar.gz -C /opt/
   ```

2. 安装依赖:
   ```bash
   cd /opt/Douyin_TikTok_Download_API
   pip install -r requirements.txt
   ```

3. 更新配置文件:
   ```bash
   # 编辑配置文件，修改端口（如果需要）
   # 默认运行在80端口，如需更改：
   # nano config.yaml
   ```

4. 创建服务文件:
   ```bash
   cat > /etc/systemd/system/Douyin_TikTok_Download_API_New.service << 'EOF'
   [Unit]
   Description=Douyin_TikTok_Download_API_New daemon
   After=network.target

   [Service]
   User=ubuntu
   WorkingDirectory=/opt/Douyin_TikTok_Download_API
   ExecStart=/usr/bin/python3 /opt/Douyin_TikTok_Download_API/start.py
   Restart=always
   RestartSec=5
   StandardOutput=syslog
   StandardError=syslog
   SyslogIdentifier=douyinapi
   Environment=PYTHONUNBUFFERED=1

   [Install]
   WantedBy=multi-user.target
   EOF

   # 重新加载systemd
   systemctl daemon-reload
   ```

5. 验证安装但不启动:
   ```bash
   systemctl status Douyin_TikTok_Download_API_New.service
   ```

## 端口配置
- API服务默认端口: 80
- 如需与当前服务共存，请修改config.yaml中的端口

## 注意事项
- 确保Python 3.8+已安装
- 确保与Singbox代理服务配合使用（端口1080）
- Cookie将由监控脚本自动管理
- 默认不会自动启动服务，需手动启动
