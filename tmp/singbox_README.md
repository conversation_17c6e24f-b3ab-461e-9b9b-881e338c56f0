# Singbox 服务部署指南

## 文件结构
- bin: 可执行文件
- config: 配置文件
- scripts: 管理和监控脚本
- logs: 日志文件夹

## 安装步骤
1. 解压缩归档文件:
   ```bash
   tar -xzvf singbox_backup_*.tar.gz -C /opt/
   ```

2. 更新配置文件中的路径:
   ```bash
   cd /opt/local-singbox
   find scripts -type f -name "*.py" -o -name "*.sh" | xargs sed -i 's|/home/<USER>/local-singbox|/opt/local-singbox|g'
   find . -type f -name "*.service" | xargs sed -i 's|/home/<USER>/local-singbox|/opt/local-singbox|g'
   ```

3. 安装服务:
   ```bash
   cd /opt/local-singbox
   ./scripts/install_integrated_service.sh
   ```

4. 验证安装但不启动:
   ```bash
   systemctl status integrated-singbox.service
   ```

## 端口配置
- Singbox代理服务: 1080端口
- Clash API: 9090端口

## 注意事项
- 确保安装了所有必要的依赖项 (Python包)
- 确保Docker已经安装并配置
- 默认不会自动启动服务，需手动启动
