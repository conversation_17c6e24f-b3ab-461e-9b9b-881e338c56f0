#!/bin/bash

# 启动所有3个API服务实例的脚本

echo "正在启动Douyin_TikTok_Download_API的3个实例..."

# 复制服务文件到systemd目录
sudo cp /home/<USER>/API/Douyin_TikTok_API_Instance*.service /etc/systemd/system/

# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用并启动所有3个服务
for i in 1 2 3; do
    echo "启动实例 $i (端口 808$i)..."
    sudo systemctl enable Douyin_TikTok_API_Instance$i.service
    sudo systemctl start Douyin_TikTok_API_Instance$i.service
    echo "实例 $i 启动完成"
done

echo ""
echo "所有实例已启动！"
echo ""
echo "查看服务状态："
echo "  sudo systemctl status Douyin_TikTok_API_Instance1"
echo "  sudo systemctl status Douyin_TikTok_API_Instance2"
echo "  sudo systemctl status Douyin_TikTok_API_Instance3"
echo ""
echo "访问服务："
echo "  实例1: http://localhost:8081"
echo "  实例2: http://localhost:8082"
echo "  实例3: http://localhost:8083"
echo ""
echo "查看日志："
echo "  sudo journalctl -u Douyin_TikTok_API_Instance1 -f"
echo "  sudo journalctl -u Douyin_TikTok_API_Instance2 -f"
echo "  sudo journalctl -u Douyin_TikTok_API_Instance3 -f"