[Unit]
Description=Integrated Monitor for Singbox and API Services
After=network.target

[Service]
User=ubuntu
WorkingDirectory=/home/<USER>/API/local-singbox/scripts
ExecStart=/usr/bin/python3 /home/<USER>/API/local-singbox/scripts/integrated_monitor.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=integrated-monitor
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target 