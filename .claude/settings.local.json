{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(mv:*)", "Bash(sudo systemctl restart:*)", "Bash(systemctl status:*)", "Bash(sudo kill:*)", "<PERSON><PERSON>(journalctl:*)", "Bash(grep:*)", "Bash(systemctl:*)", "<PERSON>sh(sudo cp:*)", "Bash(sudo systemctl:*)", "<PERSON><PERSON>(sudo journalctl:*)", "Bash(sudo lsof:*)", "<PERSON><PERSON>(diff:*)", "Bash(sudo rm:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(sudo netstat:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(sudo:*)", "Bash(ss:*)", "<PERSON><PERSON>(sing-box check:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "Bash(find:*)", "<PERSON><PERSON>(git clone:*)", "Bash(./setup_independent_instances.sh:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(crontab:*)", "<PERSON><PERSON>(timeout:*)", "Bash(cp:*)", "Bash(ps:*)", "Bash(readlink:*)", "<PERSON><PERSON>(docker inspect:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "Bash(rg:*)", "Bash(node:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(true)", "Bash(python test_log_format.py:*)"], "deny": []}, "enableAllProjectMcpServers": false}