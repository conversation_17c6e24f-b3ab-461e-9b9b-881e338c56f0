# Singbox 自动切换代理服务

## 服务组件

整个服务系统包含以下组件：

### 核心服务

1. **Singbox 代理服务**
   - 可执行文件: `/home/<USER>/local-singbox/bin/sing-box`
   - 配置文件: `/home/<USER>/local-singbox/config/config.json`
   - 功能: 提供 SOCKS5 代理服务，监听 1080 端口

2. **节点切换服务**
   - 脚本: `/home/<USER>/local-singbox/scripts/switch_node.py`
   - 功能: 每 2 分钟自动切换 IP 地址，过滤高延迟节点
   - 特性: 支持 Telegram 通知，显示 IP 地理位置信息

3. **订阅更新服务**
   - 脚本: `/home/<USER>/local-singbox/scripts/update_subscription.sh`
   - 功能: 从订阅链接获取最新节点，更新配置文件
   - 计划任务: 每天凌晨 4 点自动更新

### 监控工具

1. **Singbox 监控脚本**
   - 脚本: `/home/<USER>/local-singbox/scripts/monitor.sh`
   - 功能: 监控 Singbox 代理服务状态，自动修复异常
   - 计划任务: 每 10 分钟运行一次

2. **代理测试工具**
   - 脚本: `/home/<USER>/local-singbox/scripts/proxy_test.py`
   - 功能: 测试代理连接，显示出口 IP 信息
   - 特性: 支持网站可访问性测试，记录 IP 变化历史

### 管理工具

1. **服务管理脚本**
   - 脚本: `/home/<USER>/local-singbox/scripts/manage_service.sh`
   - 功能: 启动、停止、重启 Singbox 代理服务

2. **节点切换重启脚本**
   - 脚本: `/home/<USER>/local-singbox/scripts/restart_switch_node.sh`
   - 功能: 重启节点切换服务

3. **全服务启动脚本**
   - 脚本: `/home/<USER>/local-singbox/scripts/start_all_services.sh`
   - 功能: 一键启动所有服务，设置定时任务

4. **日志查看工具**
   - 脚本: `/home/<USER>/local-singbox/scripts/view_logs.py`
   - 功能: 整合查看所有服务日志，支持过滤和实时刷新

## 日志文件

所有服务的日志文件存放在以下位置：

- Singbox 代理日志: `/home/<USER>/local-singbox/logs/singbox.log`
- 节点切换日志: `/home/<USER>/local-singbox/logs/switch_node.log`
- 订阅更新日志: `/home/<USER>/local-singbox/logs/update_subscription.log`
- 监控脚本日志: `/home/<USER>/local-singbox/logs/monitor.log`

## 需要同时启动的程序

为了使整个代理服务正常运行，需要同时启动以下程序：

1. **Singbox 代理服务** - 提供基础代理功能
   ```bash
   /home/<USER>/local-singbox/bin/sing-box run -c /home/<USER>/local-singbox/config/config.json
   ```

2. **节点切换服务** - 自动切换 IP 地址
   ```bash
   python3 /home/<USER>/local-singbox/scripts/switch_node.py
   ```

3. **监控服务** - 监控系统状态并自动修复
   ```bash
   # 设置为定时任务
   */10 * * * * bash /home/<USER>/local-singbox/scripts/monitor.sh
   ```

4. **订阅更新服务** - 自动更新节点
   ```bash
   # 设置为定时任务
   0 4 * * * bash /home/<USER>/local-singbox/scripts/update_subscription.sh
   ```

最简单的方法是使用一键启动脚本，它会自动启动所有必要的服务并设置定时任务：
```bash
/home/<USER>/local-singbox/scripts/start_all_services.sh
```

## 使用方法

### 启动所有服务

```bash
/home/<USER>/local-singbox/scripts/start_all_services.sh
```

### 单独重启节点切换服务

```bash
/home/<USER>/local-singbox/scripts/restart_switch_node.sh
```

### 单独重启 Singbox 代理服务

```bash
/home/<USER>/local-singbox/scripts/manage_service.sh restart
```

### 查看所有服务日志

```bash
/home/<USER>/local-singbox/scripts/view_logs.py
```

### 测试代理连接

```bash
/home/<USER>/local-singbox/scripts/proxy_test.py -v
```

## 代理使用方法

其他程序可以通过以下方式使用代理服务：

### Python 程序

```python
import requests

proxies = {
    "http": "socks5://127.0.0.1:1080",
    "https": "socks5://127.0.0.1:1080"
}

response = requests.get("https://api.ipify.org", proxies=proxies)
print(f"当前 IP: {response.text}")
```

### Curl 命令

```bash
curl --socks5 127.0.0.1:1080 https://api.ipify.org
```

### 系统级代理

```bash
export http_proxy=socks5://127.0.0.1:1080
export https_proxy=socks5://127.0.0.1:1080
```

## Telegram 通知

服务状态变化会通过 Telegram 机器人发送通知，包括：

- 服务启动和停止
- IP 地址变化
- 订阅更新状态
- 异常情况和自动修复

## 自动化任务

系统配置了以下自动化任务：

- 每 2 分钟自动切换 IP 地址
- 每天凌晨 4 点自动更新订阅
- 每 10 分钟自动检查服务状态
- 自动修复异常服务
