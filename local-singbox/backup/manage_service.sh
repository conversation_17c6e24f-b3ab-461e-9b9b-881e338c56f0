#!/bin/bash
# Singbox 本地代理服务管理脚本

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 设置路径
SINGBOX_DIR="/home/<USER>/api/local-singbox"
SINGBOX_BIN="$SINGBOX_DIR/bin/sing-box"
CONFIG_FILE="$SINGBOX_DIR/config/config.json"
LOG_FILE="$SINGBOX_DIR/logs/singbox.log"
SWITCH_SCRIPT="$SINGBOX_DIR/scripts/switch_node.py"
UPDATE_SCRIPT="$SINGBOX_DIR/scripts/update_subscription.py"
PID_FILE="$SINGBOX_DIR/singbox.pid"
SWITCH_PID_FILE="$SINGBOX_DIR/switch_node.pid"

# 检查 sing-box 是否正在运行
check_running() {
    if [ -f "$PID_FILE" ]; then
        pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null; then
            return 0
        fi
    fi
    return 1
}

# 检查节点切换脚本是否正在运行
check_switch_running() {
    if [ -f "$SWITCH_PID_FILE" ]; then
        pid=$(cat "$SWITCH_PID_FILE")
        if ps -p "$pid" > /dev/null; then
            return 0
        fi
    fi
    return 1
}

# 启动 sing-box 服务
start_service() {
    echo -e "${YELLOW}正在启动 Singbox 代理服务...${NC}"
    
    # 检查是否已经在运行
    if check_running; then
        echo -e "${YELLOW}Singbox 代理服务已经在运行${NC}"
        return 0
    fi
    
    # 确保日志目录存在
    mkdir -p "$SINGBOX_DIR/logs"
    
    # 启动 sing-box
    nohup $SINGBOX_BIN run -c $CONFIG_FILE > $LOG_FILE 2>&1 &
    echo $! > $PID_FILE
    
    # 等待服务启动
    sleep 3
    
    # 检查是否成功启动
    if check_running; then
        echo -e "${GREEN}Singbox 代理服务已成功启动${NC}"
        
        # 启动节点切换脚本
        echo -e "${YELLOW}正在启动节点切换脚本...${NC}"
        nohup python3 $SWITCH_SCRIPT > "$SINGBOX_DIR/logs/switch_node.log" 2>&1 &
        echo $! > $SWITCH_PID_FILE
        
        echo -e "${GREEN}节点切换脚本已启动${NC}"
        echo -e "${GREEN}代理服务地址: 127.0.0.1:1080 (SOCKS5)${NC}"
        return 0
    else
        echo -e "${RED}Singbox 代理服务启动失败${NC}"
        return 1
    fi
}

# 停止 sing-box 服务
stop_service() {
    echo -e "${YELLOW}正在停止 Singbox 代理服务...${NC}"
    
    # 停止节点切换脚本
    if check_switch_running; then
        pid=$(cat "$SWITCH_PID_FILE")
        kill $pid 2>/dev/null
        rm -f "$SWITCH_PID_FILE"
    else
        pkill -f "$SWITCH_SCRIPT" 2>/dev/null
    fi
    
    # 停止 sing-box
    if check_running; then
        pid=$(cat "$PID_FILE")
        kill $pid 2>/dev/null
        rm -f "$PID_FILE"
    else
        pkill -f "$SINGBOX_BIN" 2>/dev/null
    fi
    
    # 检查是否成功停止
    sleep 2
    if ! check_running; then
        echo -e "${GREEN}Singbox 代理服务已停止${NC}"
        return 0
    else
        echo -e "${RED}无法停止 Singbox 代理服务${NC}"
        return 1
    fi
}

# 重启 sing-box 服务
restart_service() {
    echo -e "${YELLOW}正在重启 Singbox 代理服务...${NC}"
    stop_service
    sleep 2
    start_service
}

# 查看 sing-box 服务状态
check_status() {
    echo -e "${YELLOW}Singbox 代理服务状态:${NC}"
    
    if check_running; then
        echo -e "${GREEN}Singbox 代理服务正在运行${NC}"
        
        # 显示进程信息
        echo -e "\n${YELLOW}进程信息:${NC}"
        ps aux | grep -E "$SINGBOX_BIN|$SWITCH_SCRIPT" | grep -v grep
        
        # 显示当前IP
        echo -e "\n${YELLOW}当前出口IP:${NC}"
        curl -s --socks5 127.0.0.1:1080 https://api.ipify.org
        echo ""
        
        # 显示当前节点
        echo -e "\n${YELLOW}当前节点:${NC}"
        curl -s http://127.0.0.1:9090/proxies/proxy | grep -o '"now":"[^"]*"' | cut -d'"' -f4
        echo ""
        
        return 0
    else
        echo -e "${RED}Singbox 代理服务未运行${NC}"
        return 1
    fi
}

# 更新订阅
update_subscription() {
    echo -e "${YELLOW}正在更新订阅...${NC}"
    bash "$SINGBOX_DIR/scripts/update_subscription.sh"
    echo -e "${GREEN}订阅更新完成${NC}"
}

# 设置定时任务
setup_cron() {
    echo -e "${YELLOW}正在设置定时任务...${NC}"
    
    # 移除旧的定时任务
    (crontab -l 2>/dev/null | grep -v "$UPDATE_SCRIPT\|update_subscription.sh") > /tmp/crontab.tmp
    
    # 添加新的定时任务
    echo "# Singbox 自动更新订阅" >> /tmp/crontab.tmp
    echo "0 4 * * * /home/<USER>/api/local-singbox/scripts/update_subscription.sh >> /home/<USER>/api/local-singbox/logs/update_subscription.log 2>&1" >> /tmp/crontab.tmp
    
    # 安装新的定时任务
    crontab /tmp/crontab.tmp
    rm /tmp/crontab.tmp
    
    echo -e "${GREEN}定时任务设置完成${NC}"
    echo -e "${GREEN}每天凌晨4点自动更新订阅${NC}"
}

# 主函数
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        check_status
        ;;
    update)
        update_subscription
        ;;
    setup)
        setup_cron
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|update|setup}"
        exit 1
        ;;
esac

exit 0
