# Generated by iptables-save v1.8.7 on Sun May  4 07:39:13 2025
*filter
:INPUT ACCEPT [60794:4250973]
:FORWARD DROP [0:0]
:OUTPUT ACCEPT [25399785:173296295695]
:DOCKER - [0:0]
:DOCKER-ISOLATION-STAGE-1 - [0:0]
:DOCKER-ISOLATION-STAGE-2 - [0:0]
:DOCKER-USER - [0:0]
-A INPUT -m state --state RELATED,ESTABLISHED -j ACCEPT
-A INPUT -p icmp -j ACCEPT
-A INPUT -i lo -j ACCEPT
-A INPUT -p tcp -m state --state NEW -m tcp --dport 22 -j ACCEPT
-A FORWARD -j DOCKER-USER
-A FORWARD -j DOCKER-ISOLATION-STAGE-1
-A FORWARD -o docker0 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A FORWARD -o docker0 -j DOCKER
-A FORWARD -i docker0 ! -o docker0 -j ACCEPT
-A FORWARD -i docker0 -o docker0 -j ACCEPT
-A FORWARD -o br-bcd66ed44013 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A FORWARD -o br-bcd66ed44013 -j DOCKER
-A FORWARD -i br-bcd66ed44013 ! -o br-bcd66ed44013 -j ACCEPT
-A FORWARD -i br-bcd66ed44013 -o br-bcd66ed44013 -j ACCEPT
-A FORWARD -o br-262d4fd8db28 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A FORWARD -o br-262d4fd8db28 -j DOCKER
-A FORWARD -i br-262d4fd8db28 ! -o br-262d4fd8db28 -j ACCEPT
-A FORWARD -i br-262d4fd8db28 -o br-262d4fd8db28 -j ACCEPT
-A FORWARD -o br-1d1e6a77ebf5 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A FORWARD -o br-1d1e6a77ebf5 -j DOCKER
-A FORWARD -i br-1d1e6a77ebf5 ! -o br-1d1e6a77ebf5 -j ACCEPT
-A FORWARD -i br-1d1e6a77ebf5 -o br-1d1e6a77ebf5 -j ACCEPT
-A DOCKER -d **********/32 ! -i docker0 -o docker0 -p tcp -m tcp --dport 80 -j ACCEPT
-A DOCKER-ISOLATION-STAGE-1 -i docker0 ! -o docker0 -j DOCKER-ISOLATION-STAGE-2
-A DOCKER-ISOLATION-STAGE-1 -i br-bcd66ed44013 ! -o br-bcd66ed44013 -j DOCKER-ISOLATION-STAGE-2
-A DOCKER-ISOLATION-STAGE-1 -i br-262d4fd8db28 ! -o br-262d4fd8db28 -j DOCKER-ISOLATION-STAGE-2
-A DOCKER-ISOLATION-STAGE-1 -i br-1d1e6a77ebf5 ! -o br-1d1e6a77ebf5 -j DOCKER-ISOLATION-STAGE-2
-A DOCKER-ISOLATION-STAGE-1 -j RETURN
-A DOCKER-ISOLATION-STAGE-2 -o docker0 -j DROP
-A DOCKER-ISOLATION-STAGE-2 -o br-bcd66ed44013 -j DROP
-A DOCKER-ISOLATION-STAGE-2 -o br-262d4fd8db28 -j DROP
-A DOCKER-ISOLATION-STAGE-2 -o br-1d1e6a77ebf5 -j DROP
-A DOCKER-ISOLATION-STAGE-2 -j RETURN
-A DOCKER-USER -j RETURN
COMMIT
# Completed on Sun May  4 07:39:13 2025
# Generated by iptables-save v1.8.7 on Sun May  4 07:39:13 2025
*nat
:PREROUTING ACCEPT [576464:********]
:INPUT ACCEPT [71470:4276934]
:OUTPUT ACCEPT [751364:********]
:POSTROUTING ACCEPT [751364:********]
:DOCKER - [0:0]
-A PREROUTING -m addrtype --dst-type LOCAL -j DOCKER
-A OUTPUT ! -d *********/8 -m addrtype --dst-type LOCAL -j DOCKER
-A POSTROUTING -s **********/16 ! -o docker0 -j MASQUERADE
-A POSTROUTING -s **********/16 ! -o br-bcd66ed44013 -j MASQUERADE
-A POSTROUTING -s **********/16 ! -o br-262d4fd8db28 -j MASQUERADE
-A POSTROUTING -s **********/16 ! -o br-1d1e6a77ebf5 -j MASQUERADE
-A POSTROUTING -s **********/32 -d **********/32 -p tcp -m tcp --dport 80 -j MASQUERADE
-A DOCKER -i docker0 -j RETURN
-A DOCKER -i br-bcd66ed44013 -j RETURN
-A DOCKER -i br-262d4fd8db28 -j RETURN
-A DOCKER -i br-1d1e6a77ebf5 -j RETURN
-A DOCKER ! -i docker0 -p tcp -m tcp --dport 8081 -j DNAT --to-destination **********:80
COMMIT
# Completed on Sun May  4 07:39:13 2025
