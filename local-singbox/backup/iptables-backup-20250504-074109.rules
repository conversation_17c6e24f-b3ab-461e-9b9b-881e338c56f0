# Generated by iptables-save v1.8.7 on Sun May  4 07:41:09 2025
*filter
:INPUT ACCEPT [60794:4250973]
:FORWARD DROP [0:0]
:OUTPUT ACCEPT [25415816:173307206726]
:DOCKER - [0:0]
:DOCKER-ISOLATION-STAGE-1 - [0:0]
:DOCKER-ISOLATION-STAGE-2 - [0:0]
:DOCKER-USER - [0:0]
:DOCKER_FILTER - [0:0]
-A INPUT -m state --state RELATED,ESTABLISHED -j ACCEPT
-A INPUT -p icmp -j ACCEPT
-A INPUT -i lo -j ACCEPT
-A INPUT -p tcp -m state --state NEW -m tcp --dport 22 -j ACCEPT
-A FORWARD -j DOCKER-USER
-A FORWARD -j DOCKER-ISOLATION-STAGE-1
-A FORWARD -o docker0 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A FORWARD -o docker0 -j DOCKER
-A FORWARD -i docker0 ! -o docker0 -j ACCEPT
-A FORWARD -i docker0 -o docker0 -j ACCEPT
-A FORWARD -o br-bcd66ed44013 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A FORWARD -o br-bcd66ed44013 -j DOCKER
-A FORWARD -i br-bcd66ed44013 ! -o br-bcd66ed44013 -j ACCEPT
-A FORWARD -i br-bcd66ed44013 -o br-bcd66ed44013 -j ACCEPT
-A FORWARD -o br-262d4fd8db28 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A FORWARD -o br-262d4fd8db28 -j DOCKER
-A FORWARD -i br-262d4fd8db28 ! -o br-262d4fd8db28 -j ACCEPT
-A FORWARD -i br-262d4fd8db28 -o br-262d4fd8db28 -j ACCEPT
-A FORWARD -o br-1d1e6a77ebf5 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A FORWARD -o br-1d1e6a77ebf5 -j DOCKER
-A FORWARD -i br-1d1e6a77ebf5 ! -o br-1d1e6a77ebf5 -j ACCEPT
-A FORWARD -i br-1d1e6a77ebf5 -o br-1d1e6a77ebf5 -j ACCEPT
-A FORWARD -i docker0 -j DOCKER_FILTER
-A OUTPUT -s **********/16 -j DOCKER_FILTER
-A DOCKER -d **********/32 ! -i docker0 -o docker0 -p tcp -m tcp --dport 80 -j ACCEPT
-A DOCKER-ISOLATION-STAGE-1 -i docker0 ! -o docker0 -j DOCKER-ISOLATION-STAGE-2
-A DOCKER-ISOLATION-STAGE-1 -i br-bcd66ed44013 ! -o br-bcd66ed44013 -j DOCKER-ISOLATION-STAGE-2
-A DOCKER-ISOLATION-STAGE-1 -i br-262d4fd8db28 ! -o br-262d4fd8db28 -j DOCKER-ISOLATION-STAGE-2
-A DOCKER-ISOLATION-STAGE-1 -i br-1d1e6a77ebf5 ! -o br-1d1e6a77ebf5 -j DOCKER-ISOLATION-STAGE-2
-A DOCKER-ISOLATION-STAGE-1 -j RETURN
-A DOCKER-ISOLATION-STAGE-2 -o docker0 -j DROP
-A DOCKER-ISOLATION-STAGE-2 -o br-bcd66ed44013 -j DROP
-A DOCKER-ISOLATION-STAGE-2 -o br-262d4fd8db28 -j DROP
-A DOCKER-ISOLATION-STAGE-2 -o br-1d1e6a77ebf5 -j DROP
-A DOCKER-ISOLATION-STAGE-2 -j RETURN
-A DOCKER-USER -j RETURN
-A DOCKER_FILTER -d 0.0.0.0/8 -j ACCEPT
-A DOCKER_FILTER -d 10.0.0.0/8 -j ACCEPT
-A DOCKER_FILTER -d *********/8 -j ACCEPT
-A DOCKER_FILTER -d ***********/16 -j ACCEPT
-A DOCKER_FILTER -d **********/12 -j ACCEPT
-A DOCKER_FILTER -d ***********/16 -j ACCEPT
-A DOCKER_FILTER -d *********/4 -j ACCEPT
-A DOCKER_FILTER -d 240.0.0.0/4 -j ACCEPT
-A DOCKER_FILTER -d 127.0.0.1/32 -p tcp -m tcp --dport 1080 -j ACCEPT
-A DOCKER_FILTER -d 127.0.0.1/32 -p tcp -m tcp --dport 1080 -j ACCEPT
-A DOCKER_FILTER -j ACCEPT
COMMIT
# Completed on Sun May  4 07:41:09 2025
# Generated by iptables-save v1.8.7 on Sun May  4 07:41:09 2025
*nat
:PREROUTING ACCEPT [576496:********]
:INPUT ACCEPT [71474:4277174]
:OUTPUT ACCEPT [751952:********]
:POSTROUTING ACCEPT [751952:********]
:DOCKER - [0:0]
:DOCKER_PROXY - [0:0]
-A PREROUTING -m addrtype --dst-type LOCAL -j DOCKER
-A PREROUTING -i docker0 -p tcp -j DOCKER_PROXY
-A OUTPUT ! -d *********/8 -m addrtype --dst-type LOCAL -j DOCKER
-A OUTPUT -s **********/16 -p tcp -j DOCKER_PROXY
-A POSTROUTING -s **********/16 ! -o docker0 -j MASQUERADE
-A POSTROUTING -s **********/16 ! -o br-bcd66ed44013 -j MASQUERADE
-A POSTROUTING -s **********/16 ! -o br-262d4fd8db28 -j MASQUERADE
-A POSTROUTING -s **********/16 ! -o br-1d1e6a77ebf5 -j MASQUERADE
-A POSTROUTING -s **********/32 -d **********/32 -p tcp -m tcp --dport 80 -j MASQUERADE
-A DOCKER -i docker0 -j RETURN
-A DOCKER -i br-bcd66ed44013 -j RETURN
-A DOCKER -i br-262d4fd8db28 -j RETURN
-A DOCKER -i br-1d1e6a77ebf5 -j RETURN
-A DOCKER ! -i docker0 -p tcp -m tcp --dport 8081 -j DNAT --to-destination **********:80
-A DOCKER_PROXY -d 0.0.0.0/8 -j RETURN
-A DOCKER_PROXY -d 10.0.0.0/8 -j RETURN
-A DOCKER_PROXY -d *********/8 -j RETURN
-A DOCKER_PROXY -d ***********/16 -j RETURN
-A DOCKER_PROXY -d **********/12 -j RETURN
-A DOCKER_PROXY -d ***********/16 -j RETURN
-A DOCKER_PROXY -d *********/4 -j RETURN
-A DOCKER_PROXY -d 240.0.0.0/4 -j RETURN
-A DOCKER_PROXY -p tcp -j REDIRECT --to-ports 1080
COMMIT
# Completed on Sun May  4 07:41:09 2025
