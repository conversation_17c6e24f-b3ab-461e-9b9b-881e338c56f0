# Docker容器使用代理的方法

## 方法一：通过环境变量设置代理

在启动Docker容器时，可以通过`-e`参数设置环境变量，让容器内的应用程序使用代理：

```bash
docker run -e "HTTP_PROXY=socks5://host.docker.internal:1080" \
           -e "HTTPS_PROXY=socks5://host.docker.internal:1080" \
           -e "ALL_PROXY=socks5://host.docker.internal:1080" \
           -e "NO_PROXY=localhost,127.0.0.1" \
           --name my-container my-image
```

注意：`host.docker.internal`是Docker提供的特殊DNS名称，指向宿主机。如果这不起作用，可以使用宿主机的实际IP地址。

## 方法二：使用Docker网络设置

### 1. 创建自定义网络

```bash
docker network create proxy-network
```

### 2. 运行代理容器

```bash
docker run -d --name proxy-container \
           --network proxy-network \
           -p 1080:1080 \
           -v /home/<USER>/local-singbox:/local-singbox \
           ubuntu:20.04 \
           /local-singbox/bin/sing-box run -c /local-singbox/config/config.json
```

### 3. 将应用容器连接到同一网络

```bash
docker run -d --name app-container \
           --network proxy-network \
           -e "HTTP_PROXY=socks5://proxy-container:1080" \
           -e "HTTPS_PROXY=socks5://proxy-container:1080" \
           my-image
```

## 方法三：使用Docker Compose

创建`docker-compose.yml`文件：

```yaml
version: '3'

services:
  app:
    image: my-image
    container_name: my-app
    environment:
      - HTTP_PROXY=socks5://host.docker.internal:1080
      - HTTPS_PROXY=socks5://host.docker.internal:1080
      - ALL_PROXY=socks5://host.docker.internal:1080
      - NO_PROXY=localhost,127.0.0.1
    # 其他配置...

networks:
  default:
    driver: bridge
```

然后运行：

```bash
docker-compose up -d
```

## 方法四：使用透明代理（高级）

这种方法使用iptables将容器内的所有流量重定向到代理，无需在容器内配置代理设置。

### 1. 创建Docker网络

```bash
docker network create --subnet=**********/16 proxy-net
```

### 2. 设置iptables规则

```bash
# 获取Docker网络接口名称
DOCKER_INTERFACE=$(ip -o link show | grep docker0 | awk '{print $2}' | cut -d':' -f1)

# 设置iptables规则
sudo iptables -t nat -A PREROUTING -i $DOCKER_INTERFACE -p tcp -j REDIRECT --to-port 1080
```

### 3. 启动容器使用该网络

```bash
docker run --network proxy-net --ip ********** my-image
```

## 方法五：使用代理客户端容器

使用专门的代理客户端容器，如`privoxy`，将SOCKS5代理转换为HTTP代理：

```bash
# 运行Privoxy容器
docker run -d --name privoxy \
           -p 8118:8118 \
           -v /path/to/privoxy/config:/etc/privoxy \
           privoxy/privoxy

# 在应用容器中使用Privoxy
docker run -e "HTTP_PROXY=http://host.docker.internal:8118" \
           -e "HTTPS_PROXY=http://host.docker.internal:8118" \
           my-image
```

## 验证代理是否生效

在容器内运行以下命令检查代理是否生效：

```bash
# 进入容器
docker exec -it my-container bash

# 安装curl
apt-get update && apt-get install -y curl

# 检查当前IP
curl https://api.ipify.org

# 或者使用更详细的信息
curl https://ipinfo.io
```

## 注意事项

1. 确保宿主机上的代理服务允许来自Docker网络的连接
2. 某些应用程序可能不遵循环境变量设置的代理
3. 对于透明代理方法，需要root权限设置iptables规则
4. 容器重启后，某些设置可能需要重新应用
