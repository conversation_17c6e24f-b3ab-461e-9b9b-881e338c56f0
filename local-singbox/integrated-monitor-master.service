[Unit]
Description=Integrated Monitor Master Service
After=network.target singbox.service Douyin_TikTok_Download_API_New.service
Wants=singbox.service Douyin_TikTok_Download_API_New.service

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/API/local-singbox/scripts
ExecStart=/usr/bin/python3 /home/<USER>/API/local-singbox/scripts/integrated_monitor.py
Restart=always
RestartSec=10

# 环境变量
Environment="PYTHONUNBUFFERED=1"

# 日志
StandardOutput=journal
StandardError=journal

# 资源限制
LimitNOFILE=65535

[Install]
WantedBy=multi-user.target