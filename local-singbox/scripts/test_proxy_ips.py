#!/usr/bin/env python3
"""
测试四个singbox代理端口的出口IP
确保每个代理端口使用不同的IP地址
"""

import requests
import json
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import subprocess

# 代理配置
PROXY_PORTS = [1080, 1081, 1082, 1083]
PROXY_GROUPS = ["proxy0", "proxy1", "proxy2", "proxy3"]

# IP检测服务
IP_CHECK_URLS = [
    "https://api.ipify.org?format=json",
    "https://api.ip.sb/geoip",
    "https://ipinfo.io/json",
]

# Clash API配置
CLASH_API_URL = "http://127.0.0.1:9090"

def get_ip_via_proxy(port, timeout=10):
    """通过指定的代理端口获取出口IP"""
    proxies = {
        'http': f'socks5://127.0.0.1:{port}',
        'https': f'socks5://127.0.0.1:{port}'
    }
    
    results = []
    for url in IP_CHECK_URLS:
        try:
            response = requests.get(url, proxies=proxies, timeout=timeout)
            if response.status_code == 200:
                data = response.json()
                # 统一处理不同API的响应格式
                if 'ip' in data:
                    ip = data['ip']
                elif 'query' in data:
                    ip = data['query']
                else:
                    ip = str(data)
                
                results.append({
                    'source': url,
                    'ip': ip,
                    'data': data
                })
        except Exception as e:
            results.append({
                'source': url,
                'error': str(e)
            })
    
    return results

def get_clash_proxy_info():
    """获取Clash API中的代理信息"""
    try:
        response = requests.get(f"{CLASH_API_URL}/proxies", timeout=5)
        if response.status_code == 200:
            data = response.json()
            proxy_info = {}
            
            for group in PROXY_GROUPS:
                if group in data['proxies']:
                    proxy = data['proxies'][group]
                    proxy_info[group] = {
                        'now': proxy.get('now', 'N/A'),
                        'type': proxy.get('type', 'N/A')
                    }
            
            return proxy_info
    except Exception as e:
        print(f"获取Clash API信息失败: {e}")
        return {}

def test_proxy_via_curl(port):
    """使用curl命令测试代理"""
    cmd = f"curl -x socks5://127.0.0.1:{port} -s https://api.ipify.org?format=json --max-time 10"
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            data = json.loads(result.stdout)
            return data.get('ip', 'Unknown')
        else:
            return f"Error: {result.stderr}"
    except Exception as e:
        return f"Exception: {str(e)}"

def main():
    print("=" * 80)
    print(f"Singbox代理IP测试工具")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 获取Clash代理信息
    print("\n1. 获取Clash API代理组信息:")
    clash_info = get_clash_proxy_info()
    for group, info in clash_info.items():
        print(f"   {group}: 当前节点={info['now']}, 类型={info['type']}")
    
    # 并发测试所有代理端口
    print("\n2. 测试各代理端口的出口IP:")
    print("-" * 80)
    
    all_ips = {}
    with ThreadPoolExecutor(max_workers=len(PROXY_PORTS)) as executor:
        # 提交所有任务
        future_to_port = {
            executor.submit(get_ip_via_proxy, port): port 
            for port in PROXY_PORTS
        }
        
        # 处理完成的任务
        for future in as_completed(future_to_port):
            port = future_to_port[future]
            proxy_group = PROXY_GROUPS[PROXY_PORTS.index(port)]
            
            try:
                results = future.result()
                
                print(f"\n代理端口 {port} (组: {proxy_group}):")
                
                # 提取成功获取的IP
                successful_ips = []
                for result in results:
                    if 'ip' in result:
                        successful_ips.append(result['ip'])
                        print(f"   ✓ {result['source']}: {result['ip']}")
                    else:
                        print(f"   ✗ {result['source']}: {result.get('error', 'Unknown error')}")
                
                # 确定最终IP（取出现最多的）
                if successful_ips:
                    final_ip = max(set(successful_ips), key=successful_ips.count)
                    all_ips[port] = final_ip
                else:
                    all_ips[port] = None
                    
            except Exception as e:
                print(f"\n代理端口 {port}: 测试失败 - {e}")
                all_ips[port] = None
    
    # 使用curl再次验证
    print("\n3. 使用curl命令验证:")
    print("-" * 80)
    for port in PROXY_PORTS:
        proxy_group = PROXY_GROUPS[PROXY_PORTS.index(port)]
        curl_ip = test_proxy_via_curl(port)
        print(f"代理端口 {port} (组: {proxy_group}): {curl_ip}")
    
    # 分析结果
    print("\n4. 结果分析:")
    print("-" * 80)
    
    # 检查IP唯一性
    valid_ips = [ip for ip in all_ips.values() if ip]
    unique_ips = set(valid_ips)
    
    print(f"成功获取IP的代理数: {len(valid_ips)}/{len(PROXY_PORTS)}")
    print(f"唯一IP数量: {len(unique_ips)}")
    
    if len(unique_ips) == len(valid_ips) and len(valid_ips) == len(PROXY_PORTS):
        print("✅ 所有代理端口都使用不同的IP地址！")
    else:
        print("⚠️  存在问题:")
        
        # 查找重复的IP
        ip_count = {}
        for port, ip in all_ips.items():
            if ip:
                if ip not in ip_count:
                    ip_count[ip] = []
                ip_count[ip].append(port)
        
        for ip, ports in ip_count.items():
            if len(ports) > 1:
                print(f"   IP {ip} 被以下端口共享: {ports}")
        
        # 查找失败的代理
        for port, ip in all_ips.items():
            if not ip:
                print(f"   端口 {port} 无法获取IP")
    
    # 显示完整的IP映射
    print("\n5. 完整的端口-IP映射:")
    print("-" * 80)
    for port in PROXY_PORTS:
        proxy_group = PROXY_GROUPS[PROXY_PORTS.index(port)]
        ip = all_ips.get(port, "N/A")
        node = clash_info.get(proxy_group, {}).get('now', 'N/A')
        print(f"端口 {port} ({proxy_group}) -> 节点: {node} -> IP: {ip}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    main()