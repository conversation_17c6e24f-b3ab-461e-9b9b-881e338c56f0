#!/bin/bash
# 验证Docker容器是否使用我们的代理服务并且IP每2分钟切换一次

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 容器名称
CONTAINER_NAME="dlr-douyin-api"

# 检查次数
CHECK_COUNT=5

# 检查间隔（秒）
CHECK_INTERVAL=60

# 检查容器是否存在
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${RED}错误: 容器 $CONTAINER_NAME 不存在或未运行${NC}"
    exit 1
fi

echo -e "${BLUE}开始验证容器 $CONTAINER_NAME 的代理设置...${NC}"

# 检查容器环境变量
echo -e "${YELLOW}检查容器环境变量:${NC}"
docker exec $CONTAINER_NAME env | grep -i proxy
echo ""

# 安装必要的工具
echo -e "${YELLOW}确保容器内有必要的工具...${NC}"
docker exec $CONTAINER_NAME apt-get update -qq > /dev/null
docker exec $CONTAINER_NAME apt-get install -y curl jq -qq > /dev/null

# 获取宿主机当前IP
HOST_IP=$(curl -s --socks5 127.0.0.1:1080 https://api.ipify.org)
echo -e "${GREEN}宿主机当前代理IP: $HOST_IP${NC}"

# 获取容器当前IP
CONTAINER_IP=$(docker exec $CONTAINER_NAME curl -s https://api.ipify.org)
echo -e "${GREEN}容器当前IP: $CONTAINER_IP${NC}"

# 比较IP
if [ "$HOST_IP" = "$CONTAINER_IP" ]; then
    echo -e "${GREEN}✓ 验证成功: 容器使用了我们的代理服务${NC}"
else
    echo -e "${RED}✗ 验证失败: 容器未使用我们的代理服务${NC}"
    echo -e "${YELLOW}容器可能使用了其他代理或直接连接网络${NC}"
    exit 1
fi

# 循环检查IP变化
echo -e "\n${BLUE}开始监控IP变化 (将检查 $CHECK_COUNT 次，每次间隔 $CHECK_INTERVAL 秒)...${NC}"
echo -e "${YELLOW}时间\t\t容器IP\t\t状态${NC}"

previous_ip=$CONTAINER_IP
for i in $(seq 1 $CHECK_COUNT); do
    # 等待指定时间
    sleep $CHECK_INTERVAL
    
    # 获取当前时间
    current_time=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 获取容器当前IP
    current_ip=$(docker exec $CONTAINER_NAME curl -s https://api.ipify.org)
    
    # 检查IP是否变化
    if [ "$current_ip" != "$previous_ip" ]; then
        status="${GREEN}IP已变化${NC}"
    else
        status="${YELLOW}IP未变化${NC}"
    fi
    
    echo -e "$current_time\t$current_ip\t$status"
    previous_ip=$current_ip
done

echo -e "\n${BLUE}验证完成${NC}"

# 获取更详细的IP信息
echo -e "\n${YELLOW}获取当前IP的详细信息:${NC}"
docker exec $CONTAINER_NAME curl -s https://ipinfo.io | jq .

echo -e "\n${GREEN}提示: 如果想要持续监控IP变化，可以使用以下命令:${NC}"
echo -e "${BLUE}watch -n 60 'docker exec $CONTAINER_NAME curl -s https://api.ipify.org'${NC}"
