#!/bin/bash

# 安装集成监控服务的JS版本

SERVICE_NAME="integrated-monitor-multi-js"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
SCRIPT_DIR="/home/<USER>/API/local-singbox/scripts"
NODE_PATH="/usr/bin/node"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then 
    echo "请使用sudo运行此脚本"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "Node.js未安装，正在安装..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
    apt-get install -y nodejs
fi

# 安装npm依赖
echo "安装npm依赖..."
cd "$SCRIPT_DIR"
npm install

# 创建systemd服务文件
echo "创建systemd服务文件..."
cat > "$SERVICE_FILE" << EOF
[Unit]
Description=Integrated Monitor Service (Multi-Instance JavaScript Version)
After=network.target singbox.service

[Service]
Type=simple
User=ubuntu
WorkingDirectory=$SCRIPT_DIR
ExecStart=$NODE_PATH $SCRIPT_DIR/integrated_monitor_multi.js
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=integrated-monitor-js

# 环境变量
Environment="NODE_ENV=production"
Environment="PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"

# 如果使用环境变量配置Supabase
# Environment="SUPABASE_URL=your_supabase_url"
# Environment="SUPABASE_KEY=your_supabase_key"
# Environment="DOES_TABLE_ID=1"

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd配置
echo "重新加载systemd配置..."
systemctl daemon-reload

# 停止旧的Python版本服务（如果存在）
if systemctl is-active --quiet integrated-monitor-multi.service; then
    echo "停止旧的Python版本服务..."
    systemctl stop integrated-monitor-multi.service
    systemctl disable integrated-monitor-multi.service
fi

# 启用并启动新服务
echo "启用并启动新服务..."
systemctl enable "$SERVICE_NAME"
systemctl start "$SERVICE_NAME"

# 检查服务状态
echo ""
echo "服务状态："
systemctl status "$SERVICE_NAME" --no-pager

echo ""
echo "安装完成！"
echo ""
echo "有用的命令："
echo "  查看状态: sudo systemctl status $SERVICE_NAME"
echo "  查看日志: sudo journalctl -u $SERVICE_NAME -f"
echo "  重启服务: sudo systemctl restart $SERVICE_NAME"
echo "  停止服务: sudo systemctl stop $SERVICE_NAME"