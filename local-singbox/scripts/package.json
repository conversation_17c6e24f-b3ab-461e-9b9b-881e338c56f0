{"name": "integrated-monitor-multi", "version": "2.0.0", "description": "Integrated monitoring service for multiple API instances with Supabase integration", "main": "integrated_monitor_multi.js", "scripts": {"start": "node integrated_monitor_multi.js", "dev": "DEBUG=1 node integrated_monitor_multi.js", "install-service": "sudo bash install_integrated_service_js.sh"}, "keywords": ["monitoring", "api", "proxy", "singbox"], "author": "Cascade AI", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "axios": "^1.6.0", "js-yaml": "^4.1.0"}, "engines": {"node": ">=14.0.0"}}