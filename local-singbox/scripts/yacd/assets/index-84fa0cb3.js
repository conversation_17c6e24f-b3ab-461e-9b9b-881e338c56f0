function c(e,a){if(a.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+a.length+" present")}function y(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?y=function(t){return typeof t}:y=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(e)}function s(e){c(1,arguments);var a=Object.prototype.toString.call(e);return e instanceof Date||y(e)==="object"&&a==="[object Date]"?new Date(e.getTime()):typeof e=="number"||a==="[object Number]"?new Date(e):((typeof e=="string"||a==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}var C={};function A(){return C}function S(e){var a=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return a.setUTCFullYear(e.getFullYear()),e.getTime()-a.getTime()}function M(e,a){c(2,arguments);var t=s(e),n=s(a),i=t.getTime()-n.getTime();return i<0?-1:i>0?1:i}function _(e,a){c(2,arguments);var t=s(e),n=s(a),i=t.getFullYear()-n.getFullYear(),o=t.getMonth()-n.getMonth();return i*12+o}function X(e,a){return c(2,arguments),s(e).getTime()-s(a).getTime()}var T={ceil:Math.ceil,round:Math.round,floor:Math.floor,trunc:function(a){return a<0?Math.ceil(a):Math.floor(a)}},I="trunc";function R(e){return e?T[e]:T[I]}function E(e){c(1,arguments);var a=s(e);return a.setHours(23,59,59,999),a}function Y(e){c(1,arguments);var a=s(e),t=a.getMonth();return a.setFullYear(a.getFullYear(),t+1,0),a.setHours(23,59,59,999),a}function j(e){c(1,arguments);var a=s(e);return E(a).getTime()===Y(a).getTime()}function z(e,a){c(2,arguments);var t=s(e),n=s(a),i=M(t,n),o=Math.abs(_(t,n)),r;if(o<1)r=0;else{t.getMonth()===1&&t.getDate()>27&&t.setDate(30),t.setMonth(t.getMonth()-i*o);var l=M(t,n)===-i;j(s(e))&&o===1&&M(e,n)===1&&(l=!1),r=i*(o-Number(l))}return r===0?0:r}function V(e,a,t){c(2,arguments);var n=X(e,a)/1e3;return R(t==null?void 0:t.roundingMethod)(n)}var q={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},L=function(a,t,n){var i,o=q[a];return typeof o=="string"?i=o:t===1?i=o.one:i=o.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+i:i+" ago":i};const H=L;function p(e){return function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=a.width?String(a.width):e.defaultWidth,n=e.formats[t]||e.formats[e.defaultWidth];return n}}var J={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},U={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},$={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Q={date:p({formats:J,defaultWidth:"full"}),time:p({formats:U,defaultWidth:"full"}),dateTime:p({formats:$,defaultWidth:"full"})};const B=Q;var G={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},K=function(a,t,n,i){return G[a]};const Z=K;function g(e){return function(a,t){var n=t!=null&&t.context?String(t.context):"standalone",i;if(n==="formatting"&&e.formattingValues){var o=e.defaultFormattingWidth||e.defaultWidth,r=t!=null&&t.width?String(t.width):o;i=e.formattingValues[r]||e.formattingValues[o]}else{var l=e.defaultWidth,u=t!=null&&t.width?String(t.width):e.defaultWidth;i=e.values[u]||e.values[l]}var f=e.argumentCallback?e.argumentCallback(a):a;return i[f]}}var ee={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},te={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},ae={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},ne={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},re={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},ie={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},oe=function(a,t){var n=Number(a),i=n%100;if(i>20||i<10)switch(i%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},ue={ordinalNumber:oe,era:g({values:ee,defaultWidth:"wide"}),quarter:g({values:te,defaultWidth:"wide",argumentCallback:function(a){return a-1}}),month:g({values:ae,defaultWidth:"wide"}),day:g({values:ne,defaultWidth:"wide"}),dayPeriod:g({values:re,defaultWidth:"wide",formattingValues:ie,defaultFormattingWidth:"wide"})};const se=ue;function b(e){return function(a){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.width,i=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],o=a.match(i);if(!o)return null;var r=o[0],l=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(l)?de(l,function(m){return m.test(r)}):le(l,function(m){return m.test(r)}),f;f=e.valueCallback?e.valueCallback(u):u,f=t.valueCallback?t.valueCallback(f):f;var h=a.slice(r.length);return{value:f,rest:h}}}function le(e,a){for(var t in e)if(e.hasOwnProperty(t)&&a(e[t]))return t}function de(e,a){for(var t=0;t<e.length;t++)if(a(e[t]))return t}function fe(e){return function(a){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=a.match(e.matchPattern);if(!n)return null;var i=n[0],o=a.match(e.parsePattern);if(!o)return null;var r=e.valueCallback?e.valueCallback(o[0]):o[0];r=t.valueCallback?t.valueCallback(r):r;var l=a.slice(i.length);return{value:r,rest:l}}}var me=/^(\d+)(th|st|nd|rd)?/i,ce=/\d+/i,he={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},ve={any:[/^b/i,/^(a|c)/i]},ge={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},be={any:[/1/i,/2/i,/3/i,/4/i]},ye={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Me={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},we={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},pe={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},De={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Pe={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Se={ordinalNumber:fe({matchPattern:me,parsePattern:ce,valueCallback:function(a){return parseInt(a,10)}}),era:b({matchPatterns:he,defaultMatchWidth:"wide",parsePatterns:ve,defaultParseWidth:"any"}),quarter:b({matchPatterns:ge,defaultMatchWidth:"wide",parsePatterns:be,defaultParseWidth:"any",valueCallback:function(a){return a+1}}),month:b({matchPatterns:ye,defaultMatchWidth:"wide",parsePatterns:Me,defaultParseWidth:"any"}),day:b({matchPatterns:we,defaultMatchWidth:"wide",parsePatterns:pe,defaultParseWidth:"any"}),dayPeriod:b({matchPatterns:De,defaultMatchWidth:"any",parsePatterns:Pe,defaultParseWidth:"any"})};const Te=Se;var We={code:"en-US",formatDistance:H,formatLong:B,formatRelative:Z,localize:se,match:Te,options:{weekStartsOn:0,firstWeekContainsDate:1}};const Ne=We;function N(e,a){if(e==null)throw new TypeError("assign requires that input parameter not be null or undefined");for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t]);return e}function ke(e){return N({},e)}var W=1440,Oe=2520,D=43200,Fe=86400;function xe(e,a,t){var n,i;c(2,arguments);var o=A(),r=(n=(i=t==null?void 0:t.locale)!==null&&i!==void 0?i:o.locale)!==null&&n!==void 0?n:Ne;if(!r.formatDistance)throw new RangeError("locale must contain formatDistance property");var l=M(e,a);if(isNaN(l))throw new RangeError("Invalid time value");var u=N(ke(t),{addSuffix:Boolean(t==null?void 0:t.addSuffix),comparison:l}),f,h;l>0?(f=s(a),h=s(e)):(f=s(e),h=s(a));var m=V(h,f),k=(S(h)-S(f))/1e3,d=Math.round((m-k)/60),v;if(d<2)return t!=null&&t.includeSeconds?m<5?r.formatDistance("lessThanXSeconds",5,u):m<10?r.formatDistance("lessThanXSeconds",10,u):m<20?r.formatDistance("lessThanXSeconds",20,u):m<40?r.formatDistance("halfAMinute",0,u):m<60?r.formatDistance("lessThanXMinutes",1,u):r.formatDistance("xMinutes",1,u):d===0?r.formatDistance("lessThanXMinutes",1,u):r.formatDistance("xMinutes",d,u);if(d<45)return r.formatDistance("xMinutes",d,u);if(d<90)return r.formatDistance("aboutXHours",1,u);if(d<W){var O=Math.round(d/60);return r.formatDistance("aboutXHours",O,u)}else{if(d<Oe)return r.formatDistance("xDays",1,u);if(d<D){var F=Math.round(d/W);return r.formatDistance("xDays",F,u)}else if(d<Fe)return v=Math.round(d/D),r.formatDistance("aboutXMonths",v,u)}if(v=z(h,f),v<12){var x=Math.round(d/D);return r.formatDistance("xMonths",x,u)}else{var P=v%12,w=Math.floor(v/12);return P<3?r.formatDistance("aboutXYears",w,u):P<9?r.formatDistance("overXYears",w,u):r.formatDistance("almostXYears",w+1,u)}}export{g as a,p as b,fe as c,b as d,Ne as e,xe as f,A as g,c as r,s as t};
