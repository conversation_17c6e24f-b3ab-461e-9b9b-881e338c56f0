import{r as p,R as nn}from"./index-1a05af9b.js";const K=p.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),re=p.createContext({});function ti(){return p.useContext(re).visualElement}const mt=p.createContext(null),ae=typeof document<"u",Q=ae?p.useLayoutEffect:p.useEffect,sn=p.createContext({strict:!1});function jo(t,e,n,s){const i=ti(),r=p.useContext(sn),o=p.useContext(mt),a=p.useContext(K).reducedMotion,c=p.useRef();s=s||r.renderer,!c.current&&s&&(c.current=s(t,{visualState:e,parent:i,props:n,presenceId:o?o.id:void 0,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:a}));const l=c.current;return Q(()=>{l&&l.render()}),(window.HandoffAppearAnimations?Q:p.useEffect)(()=>{l&&l.animationState&&l.animationState.animateChanges()}),l}function ut(t){return typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function _o(t,e,n){return p.useCallback(s=>{s&&t.mount&&t.mount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):ut(n)&&(n.current=s))},[e])}function Rt(t){return typeof t=="string"||Array.isArray(t)}function ce(t){return typeof t=="object"&&typeof t.start=="function"}const Uo=["initial","animate","exit","whileHover","whileDrag","whileTap","whileFocus","whileInView"];function le(t){return ce(t.animate)||Uo.some(e=>Rt(t[e]))}function ei(t){return Boolean(le(t)||t.variants)}function zo(t,e){if(le(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Rt(n)?n:void 0,animate:Rt(s)?s:void 0}}return t.inherit!==!1?e:{}}function No(t){const{initial:e,animate:n}=zo(t,p.useContext(re));return p.useMemo(()=>({initial:e,animate:n}),[Nn(e),Nn(n)])}function Nn(t){return Array.isArray(t)?t.join(" "):t}const G=t=>({isEnabled:e=>t.some(n=>!!e[n])}),Et={measureLayout:G(["layout","layoutId","drag"]),animation:G(["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"]),exit:G(["exit"]),drag:G(["drag","dragControls"]),focus:G(["whileFocus"]),hover:G(["whileHover","onHoverStart","onHoverEnd"]),tap:G(["whileTap","onTap","onTapStart","onTapCancel"]),pan:G(["onPan","onPanStart","onPanSessionStart","onPanEnd"]),inView:G(["whileInView","onViewportEnter","onViewportLeave"])};function De(t){for(const e in t)e==="projectionNodeConstructor"?Et.projectionNodeConstructor=t[e]:Et[e].Component=t[e]}function D(t){const e=p.useRef(null);return e.current===null&&(e.current=t()),e.current}const Vt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};let $o=1;function Wo(){return D(()=>{if(Vt.hasEverUpdated)return $o++})}const Lt=p.createContext({});class Go extends nn.Component{getSnapshotBeforeUpdate(){const{visualElement:e,props:n}=this.props;return e&&e.setProps(n),null}componentDidUpdate(){}render(){return this.props.children}}const ni=p.createContext({}),on=Symbol.for("motionComponentSymbol");function si({preloadedFeatures:t,createVisualElement:e,projectionNodeConstructor:n,useRender:s,useVisualState:i,Component:r}){t&&De(t);function o(c,l){const u={...p.useContext(K),...c,layoutId:Ho(c)},{isStatic:d}=u;let f=null;const h=No(c),m=d?void 0:Wo(),g=i(c,d);if(!d&&ae){h.visualElement=jo(r,g,u,e);const b=p.useContext(sn).strict,v=p.useContext(ni);h.visualElement&&(f=h.visualElement.loadFeatures(u,b,t,m,n||Et.projectionNodeConstructor,v))}return p.createElement(Go,{visualElement:h.visualElement,props:u},f,p.createElement(re.Provider,{value:h},s(r,c,m,_o(g,h.visualElement,l),g,d,h.visualElement)))}const a=p.forwardRef(o);return a[on]=r,a}function Ho({layoutId:t}){const e=p.useContext(Lt).id;return e&&t!==void 0?e+"-"+t:t}function ii(t){function e(s,i={}){return si(t(s,i))}if(typeof Proxy>"u")return e;const n=new Map;return new Proxy(e,{get:(s,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const Ko=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rn(t){return typeof t!="string"||t.includes("-")?!1:!!(Ko.indexOf(t)>-1||/[A-Z]/.test(t))}const Xt={};function Xo(t){Object.assign(Xt,t)}const Yt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],X=new Set(Yt);function oi(t,{layout:e,layoutId:n}){return X.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Xt[t]||t==="opacity")}const E=t=>!!(t!=null&&t.getVelocity),Yo={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},qo=(t,e)=>Yt.indexOf(t)-Yt.indexOf(e);function Zo({transform:t,transformKeys:e},{enableHardwareAcceleration:n=!0,allowTransformNone:s=!0},i,r){let o="";e.sort(qo);for(const a of e)o+=`${Yo[a]||a}(${t[a]}) `;return n&&!t.z&&(o+="translateZ(0)"),o=o.trim(),r?o=r(t,i?"":o):s&&i&&(o="none"),o}function an(t){return t.startsWith("--")}const Jo=(t,e)=>e&&typeof t=="number"?e.transform(t):t,pt=(t,e,n)=>Math.min(Math.max(n,t),e),ct={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Pt={...ct,transform:t=>pt(0,1,t)},Ut={...ct,default:1},Ct=t=>Math.round(t*1e5)/1e5,Dt=/(-)?([\d]*\.?[\d])+/g,Ie=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Qo=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function kt(t){return typeof t=="string"}const jt=t=>({test:e=>kt(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Y=jt("deg"),$=jt("%"),V=jt("px"),tr=jt("vh"),er=jt("vw"),$n={...$,parse:t=>$.parse(t)/100,transform:t=>$.transform(t*100)},Wn={...ct,transform:Math.round},ri={borderWidth:V,borderTopWidth:V,borderRightWidth:V,borderBottomWidth:V,borderLeftWidth:V,borderRadius:V,radius:V,borderTopLeftRadius:V,borderTopRightRadius:V,borderBottomRightRadius:V,borderBottomLeftRadius:V,width:V,maxWidth:V,height:V,maxHeight:V,size:V,top:V,right:V,bottom:V,left:V,padding:V,paddingTop:V,paddingRight:V,paddingBottom:V,paddingLeft:V,margin:V,marginTop:V,marginRight:V,marginBottom:V,marginLeft:V,rotate:Y,rotateX:Y,rotateY:Y,rotateZ:Y,scale:Ut,scaleX:Ut,scaleY:Ut,scaleZ:Ut,skew:Y,skewX:Y,skewY:Y,distance:V,translateX:V,translateY:V,translateZ:V,x:V,y:V,z:V,perspective:V,transformPerspective:V,opacity:Pt,originX:$n,originY:$n,originZ:V,zIndex:Wn,fillOpacity:Pt,strokeOpacity:Pt,numOctaves:Wn};function cn(t,e,n,s){const{style:i,vars:r,transform:o,transformKeys:a,transformOrigin:c}=t;a.length=0;let l=!1,u=!1,d=!0;for(const f in e){const h=e[f];if(an(f)){r[f]=h;continue}const m=ri[f],g=Jo(h,m);if(X.has(f)){if(l=!0,o[f]=g,a.push(f),!d)continue;h!==(m.default||0)&&(d=!1)}else f.startsWith("origin")?(u=!0,c[f]=g):i[f]=g}if(e.transform||(l||s?i.transform=Zo(t,n,d,s):i.transform&&(i.transform="none")),u){const{originX:f="50%",originY:h="50%",originZ:m=0}=c;i.transformOrigin=`${f} ${h} ${m}`}}const ln=()=>({style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}});function ai(t,e,n){for(const s in e)!E(e[s])&&!oi(s,n)&&(t[s]=e[s])}function nr({transformTemplate:t},e,n){return p.useMemo(()=>{const s=ln();return cn(s,e,{enableHardwareAcceleration:!n},t),Object.assign({},s.vars,s.style)},[e])}function sr(t,e,n){const s=t.style||{},i={};return ai(i,s,t),Object.assign(i,nr(t,e,n)),t.transformValues?t.transformValues(i):i}function ir(t,e,n){const s={},i=sr(t,e,n);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),s.style=i,s}const or=["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"],rr=["whileTap","onTap","onTapStart","onTapCancel"],ar=["onPan","onPanStart","onPanSessionStart","onPanEnd"],cr=["whileInView","onViewportEnter","onViewportLeave","viewport"],lr=new Set(["initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","layout","layoutId","layoutDependency","layoutScroll","layoutRoot","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","drag","dragControls","dragListener","dragConstraints","dragDirectionLock","dragSnapToOrigin","_dragX","_dragY","dragElastic","dragMomentum","dragPropagation","dragTransition","onHoverStart","onHoverEnd",...cr,...rr,...or,...ar]);function qt(t){return lr.has(t)}let ci=t=>!qt(t);function li(t){t&&(ci=e=>e.startsWith("on")?!qt(e):t(e))}try{li(require("@emotion/is-prop-valid").default)}catch{}function ur(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(ci(i)||n===!0&&qt(i)||!e&&!qt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function Gn(t,e,n){return typeof t=="string"?t:V.transform(e+n*t)}function fr(t,e,n){const s=Gn(e,t.x,t.width),i=Gn(n,t.y,t.height);return`${s} ${i}`}const dr={offset:"stroke-dashoffset",array:"stroke-dasharray"},hr={offset:"strokeDashoffset",array:"strokeDasharray"};function pr(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?dr:hr;t[r.offset]=V.transform(-s);const o=V.transform(e),a=V.transform(n);t[r.array]=`${o} ${a}`}function un(t,{attrX:e,attrY:n,originX:s,originY:i,pathLength:r,pathSpacing:o=1,pathOffset:a=0,...c},l,u,d){if(cn(t,c,l,d),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:f,style:h,dimensions:m}=t;f.transform&&(m&&(h.transform=f.transform),delete f.transform),m&&(s!==void 0||i!==void 0||h.transform)&&(h.transformOrigin=fr(m,s!==void 0?s:.5,i!==void 0?i:.5)),e!==void 0&&(f.x=e),n!==void 0&&(f.y=n),r!==void 0&&pr(f,r,o,a,!1)}const ui=()=>({...ln(),attrs:{}}),fn=t=>typeof t=="string"&&t.toLowerCase()==="svg";function mr(t,e,n,s){const i=p.useMemo(()=>{const r=ui();return un(r,e,{enableHardwareAcceleration:!1},fn(s),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){const r={};ai(r,t.style,t),i.style={...r,...i.style}}return i}function gr(t=!1){return(n,s,i,r,{latestValues:o},a)=>{const l=(rn(n)?mr:ir)(s,o,a,n),d={...ur(s,typeof n=="string",t),...l,ref:r},{children:f}=s,h=p.useMemo(()=>E(f)?f.get():f,[f]);return i&&(d["data-projection-id"]=i),p.createElement(n,{...d,children:h})}}const It=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function fi(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const r in n)t.style.setProperty(r,n[r])}const di=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function hi(t,e,n,s){fi(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(di.has(i)?i:It(i),e.attrs[i])}function dn(t,e){const{style:n}=t,s={};for(const i in n)(E(n[i])||e.style&&E(e.style[i])||oi(i,t))&&(s[i]=n[i]);return s}function pi(t,e){const n=dn(t,e);for(const s in t)if(E(t[s])||E(e[s])){const i=s==="x"||s==="y"?"attr"+s.toUpperCase():s;n[i]=t[s]}return n}function hn(t,e,n,s={},i={}){return typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),e}const Zt=t=>Array.isArray(t),yr=t=>Boolean(t&&typeof t=="object"&&t.mix&&t.toValue),vr=t=>Zt(t)?t[t.length-1]||0:t;function Wt(t){const e=E(t)?t.get():t;return yr(e)?e.toValue():e}function xr({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},s,i,r){const o={latestValues:br(s,i,r,t),renderState:e()};return n&&(o.mount=a=>n(s,a,o)),o}const pn=t=>(e,n)=>{const s=p.useContext(re),i=p.useContext(mt),r=()=>xr(t,e,s,i);return n?r():D(r)};function br(t,e,n,s){const i={},r=s(t,{});for(const f in r)i[f]=Wt(r[f]);let{initial:o,animate:a}=t;const c=le(t),l=ei(t);e&&l&&!c&&t.inherit!==!1&&(o===void 0&&(o=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||o===!1;const d=u?a:o;return d&&typeof d!="boolean"&&!ce(d)&&(Array.isArray(d)?d:[d]).forEach(h=>{const m=hn(t,h);if(!m)return;const{transitionEnd:g,transition:b,...v}=m;for(const T in v){let x=v[T];if(Array.isArray(x)){const y=u?x.length-1:0;x=x[y]}x!==null&&(i[T]=x)}for(const T in g)i[T]=g[T]}),i}const Tr={useVisualState:pn({scrapeMotionValuesFromProps:pi,createRenderState:ui,onMount:(t,e,{renderState:n,latestValues:s})=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}un(n,s,{enableHardwareAcceleration:!1},fn(e.tagName),t.transformTemplate),hi(e,n)}})},Vr={useVisualState:pn({scrapeMotionValuesFromProps:dn,createRenderState:ln})};function mn(t,{forwardMotionProps:e=!1},n,s,i){return{...rn(t)?Tr:Vr,preloadedFeatures:n,useRender:gr(e),createVisualElement:s,projectionNodeConstructor:i,Component:t}}var S;(function(t){t.Animate="animate",t.Hover="whileHover",t.Tap="whileTap",t.Drag="whileDrag",t.Focus="whileFocus",t.InView="whileInView",t.Exit="exit"})(S||(S={}));function ue(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function Oe(t,e,n,s){p.useEffect(()=>{const i=t.current;if(n&&i)return ue(i,e,n,s)},[t,e,n,s])}function Pr({whileFocus:t,visualElement:e}){const{animationState:n}=e,s=p.useCallback(()=>{n&&n.setActive(S.Focus,!0)},[n]),i=p.useCallback(()=>{n&&n.setActive(S.Focus,!1)},[n]);Oe(e,"focus",t?s:void 0),Oe(e,"blur",t?i:void 0)}const mi=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function gn(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}const gi=t=>e=>mi(e)&&t(e,gn(e));function ht(t,e,n,s){return ue(t,e,gi(n),s)}function Jt(t,e,n,s){return Oe(t,e,n&&gi(n),s)}function yi(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const Hn=yi("dragHorizontal"),Kn=yi("dragVertical");function vi(t){let e=!1;if(t==="y")e=Kn();else if(t==="x")e=Hn();else{const n=Hn(),s=Kn();n&&s?e=()=>{n(),s()}:(n&&n(),s&&s())}return e}function xi(){const t=vi(!0);return t?(t(),!1):!0}function Xn(t,e,n,s){return(i,r)=>{i.type==="touch"||xi()||(n&&t.animationState&&t.animationState.setActive(S.Hover,e),s&&s(i,r))}}function Cr({onHoverStart:t,onHoverEnd:e,whileHover:n,visualElement:s}){Jt(s,"pointerenter",p.useMemo(()=>t||n?Xn(s,!0,Boolean(n),t):void 0,[t,Boolean(n),s]),{passive:!t}),Jt(s,"pointerleave",p.useMemo(()=>e||n?Xn(s,!1,Boolean(n),e):void 0,[t,Boolean(n),s]),{passive:!e})}const bi=(t,e)=>e?t===e?!0:bi(t,e.parentElement):!1;function yn(t){return p.useEffect(()=>()=>t(),[])}const Sr=(t,e)=>n=>e(t(n)),fe=(...t)=>t.reduce(Sr);function wr({onTap:t,onTapStart:e,onTapCancel:n,whileTap:s,visualElement:i,...r}){const o=t||e||n||s,a=p.useRef(!1),c=p.useRef(null),l={passive:!(e||t||n||r.onPointerDown)};function u(){c.current&&c.current(),c.current=null}function d(){return u(),a.current=!1,i.getProps().whileTap&&i.animationState&&i.animationState.setActive(S.Tap,!1),!xi()}function f(g,b){var v,T,x,y;d()&&(bi(i.current,g.target)?(y=(x=i.getProps()).onTap)===null||y===void 0||y.call(x,g,b):(T=(v=i.getProps()).onTapCancel)===null||T===void 0||T.call(v,g,b))}function h(g,b){var v,T;d()&&((T=(v=i.getProps()).onTapCancel)===null||T===void 0||T.call(v,g,b))}const m=p.useCallback((g,b)=>{var v;if(u(),a.current)return;a.current=!0,c.current=fe(ht(window,"pointerup",f,l),ht(window,"pointercancel",h,l));const T=i.getProps();T.whileTap&&i.animationState&&i.animationState.setActive(S.Tap,!0),(v=T.onTapStart)===null||v===void 0||v.call(T,g,b)},[Boolean(e),i]);Jt(i,"pointerdown",o?m:void 0,l),yn(u)}const Be=new WeakMap,ve=new WeakMap,Ar=t=>{const e=Be.get(t.target);e&&e(t)},Mr=t=>{t.forEach(Ar)};function Rr({root:t,...e}){const n=t||document;ve.has(n)||ve.set(n,{});const s=ve.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Mr,{root:t,...e})),s[i]}function Er(t,e,n){const s=Rr(e);return Be.set(t,n),s.observe(t),()=>{Be.delete(t),s.unobserve(t)}}function Lr({visualElement:t,whileInView:e,onViewportEnter:n,onViewportLeave:s,viewport:i={}}){const r=p.useRef({hasEnteredView:!1,isInView:!1});let o=Boolean(e||n||s);i.once&&r.current.hasEnteredView&&(o=!1),(typeof IntersectionObserver>"u"?Or:Ir)(o,r.current,t,i)}const Dr={some:0,all:1};function Ir(t,e,n,{root:s,margin:i,amount:r="some",once:o}){p.useEffect(()=>{if(!t||!n.current)return;const a={root:s==null?void 0:s.current,rootMargin:i,threshold:typeof r=="number"?r:Dr[r]},c=l=>{const{isIntersecting:u}=l;if(e.isInView===u||(e.isInView=u,o&&!u&&e.hasEnteredView))return;u&&(e.hasEnteredView=!0),n.animationState&&n.animationState.setActive(S.InView,u);const d=n.getProps(),f=u?d.onViewportEnter:d.onViewportLeave;f&&f(l)};return Er(n.current,a,c)},[t,s,i,r])}function Or(t,e,n,{fallback:s=!0}){p.useEffect(()=>{!t||!s||requestAnimationFrame(()=>{e.hasEnteredView=!0;const{onViewportEnter:i}=n.getProps();i&&i(null),n.animationState&&n.animationState.setActive(S.InView,!0)})},[t])}const J=t=>e=>(t(e),null),Ti={inView:J(Lr),tap:J(wr),focus:J(Pr),hover:J(Cr)};function Vi(){const t=p.useContext(mt);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,i=p.useId();return p.useEffect(()=>s(i),[]),!e&&n?[!1,()=>n&&n(i)]:[!0]}function Uu(){return Br(p.useContext(mt))}function Br(t){return t===null?!0:t.isPresent}function Pi(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const Fr=t=>/^\-?\d*\.?\d+$/.test(t),kr=t=>/^0[^.\s]+$/.test(t),H={delta:0,timestamp:0},Ci=1/60*1e3,jr=typeof performance<"u"?()=>performance.now():()=>Date.now(),Si=typeof window<"u"?t=>window.requestAnimationFrame(t):t=>setTimeout(()=>t(jr()),Ci);function _r(t){let e=[],n=[],s=0,i=!1,r=!1;const o=new WeakSet,a={schedule:(c,l=!1,u=!1)=>{const d=u&&i,f=d?e:n;return l&&o.add(c),f.indexOf(c)===-1&&(f.push(c),d&&i&&(s=e.length)),c},cancel:c=>{const l=n.indexOf(c);l!==-1&&n.splice(l,1),o.delete(c)},process:c=>{if(i){r=!0;return}if(i=!0,[e,n]=[n,e],n.length=0,s=e.length,s)for(let l=0;l<s;l++){const u=e[l];u(c),o.has(u)&&(a.schedule(u),t())}i=!1,r&&(r=!1,a.process(c))}};return a}const Ur=40;let Fe=!0,Ot=!1,ke=!1;const _t=["read","update","preRender","render","postRender"],de=_t.reduce((t,e)=>(t[e]=_r(()=>Ot=!0),t),{}),R=_t.reduce((t,e)=>{const n=de[e];return t[e]=(s,i=!1,r=!1)=>(Ot||Nr(),n.schedule(s,i,r)),t},{}),W=_t.reduce((t,e)=>(t[e]=de[e].cancel,t),{}),xe=_t.reduce((t,e)=>(t[e]=()=>de[e].process(H),t),{}),zr=t=>de[t].process(H),wi=t=>{Ot=!1,H.delta=Fe?Ci:Math.max(Math.min(t-H.timestamp,Ur),1),H.timestamp=t,ke=!0,_t.forEach(zr),ke=!1,Ot&&(Fe=!1,Si(wi))},Nr=()=>{Ot=!0,Fe=!0,ke||Si(wi)};function he(t,e){t.indexOf(e)===-1&&t.push(e)}function Bt(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}function $r([...t],e,n){const s=e<0?t.length+e:e;if(s>=0&&s<t.length){const i=n<0?t.length+n:n,[r]=t.splice(e,1);t.splice(i,0,r)}return t}class vn{constructor(){this.subscriptions=[]}add(e){return he(this.subscriptions,e),()=>Bt(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const o=this.subscriptions[r];o&&o(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function xn(t,e){return e?t*(1e3/e):0}const Wr=t=>!isNaN(parseFloat(t));class Ai{constructor(e,n={}){this.version="8.5.3",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(s,i=!0)=>{this.prev=this.current,this.current=s;const{delta:r,timestamp:o}=H;this.lastUpdated!==o&&(this.timeDelta=r,this.lastUpdated=o,R.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>R.postRender(this.velocityCheck),this.velocityCheck=({timestamp:s})=>{s!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=Wr(this.current),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new vn);const s=this.events[e].add(n);return e==="change"?()=>{s(),R.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=e,this.timeDelta=s}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?xn(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n)||null,this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){this.animation=null}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function z(t,e){return new Ai(t,e)}const bn=(t,e)=>n=>Boolean(kt(n)&&Qo.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),Mi=(t,e,n)=>s=>{if(!kt(s))return s;const[i,r,o,a]=s.match(Dt);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},Gr=t=>pt(0,255,t),be={...ct,transform:t=>Math.round(Gr(t))},ot={test:bn("rgb","red"),parse:Mi("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+be.transform(t)+", "+be.transform(e)+", "+be.transform(n)+", "+Ct(Pt.transform(s))+")"};function Hr(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const je={test:bn("#"),parse:Hr,transform:ot.transform},ft={test:bn("hsl","hue"),parse:Mi("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+$.transform(Ct(e))+", "+$.transform(Ct(n))+", "+Ct(Pt.transform(s))+")"},O={test:t=>ot.test(t)||je.test(t)||ft.test(t),parse:t=>ot.test(t)?ot.parse(t):ft.test(t)?ft.parse(t):je.parse(t),transform:t=>kt(t)?t:t.hasOwnProperty("red")?ot.transform(t):ft.transform(t)},Ri="${c}",Ei="${n}";function Kr(t){var e,n;return isNaN(t)&&kt(t)&&(((e=t.match(Dt))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(Ie))===null||n===void 0?void 0:n.length)||0)>0}function Qt(t){typeof t=="number"&&(t=`${t}`);const e=[];let n=0,s=0;const i=t.match(Ie);i&&(n=i.length,t=t.replace(Ie,Ri),e.push(...i.map(O.parse)));const r=t.match(Dt);return r&&(s=r.length,t=t.replace(Dt,Ei),e.push(...r.map(ct.parse))),{values:e,numColors:n,numNumbers:s,tokenised:t}}function Li(t){return Qt(t).values}function Di(t){const{values:e,numColors:n,tokenised:s}=Qt(t),i=e.length;return r=>{let o=s;for(let a=0;a<i;a++)o=o.replace(a<n?Ri:Ei,a<n?O.transform(r[a]):Ct(r[a]));return o}}const Xr=t=>typeof t=="number"?0:t;function Yr(t){const e=Li(t);return Di(t)(e.map(Xr))}const tt={test:Kr,parse:Li,createTransformer:Di,getAnimatableNone:Yr},qr=new Set(["brightness","contrast","saturate","opacity"]);function Zr(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Dt)||[];if(!s)return t;const i=n.replace(s,"");let r=qr.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Jr=/([a-z-]*)\(.*?\)/g,_e={...tt,getAnimatableNone:t=>{const e=t.match(Jr);return e?e.map(Zr).join(" "):t}},Qr={...ri,color:O,backgroundColor:O,outlineColor:O,fill:O,stroke:O,borderColor:O,borderTopColor:O,borderRightColor:O,borderBottomColor:O,borderLeftColor:O,filter:_e,WebkitFilter:_e},Tn=t=>Qr[t];function Vn(t,e){var n;let s=Tn(t);return s!==_e&&(s=tt),(n=s.getAnimatableNone)===null||n===void 0?void 0:n.call(s,e)}const Ii=t=>e=>e.test(t),ta={test:t=>t==="auto",parse:t=>t},Oi=[ct,V,$,Y,er,tr,ta],vt=t=>Oi.find(Ii(t)),ea=[...Oi,O,tt],na=t=>ea.find(Ii(t));function sa(t){const e={};return t.values.forEach((n,s)=>e[s]=n.get()),e}function ia(t){const e={};return t.values.forEach((n,s)=>e[s]=n.getVelocity()),e}function pe(t,e,n){const s=t.getProps();return hn(s,e,n!==void 0?n:s.custom,sa(t),ia(t))}function oa(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,z(n))}function Pn(t,e){const n=pe(t,e);let{transitionEnd:s={},transition:i={},...r}=n?t.makeTargetAnimatable(n,!1):{};r={...r,...s};for(const o in r){const a=vr(r[o]);oa(t,o,a)}}function Ue(t,e){[...e].reverse().forEach(s=>{var i;const r=t.getVariant(s);r&&Pn(t,r),(i=t.variantChildren)===null||i===void 0||i.forEach(o=>{Ue(o,e)})})}function ra(t,e){if(Array.isArray(e))return Ue(t,e);if(typeof e=="string")return Ue(t,[e]);Pn(t,e)}function Bi(t,e,n){var s,i;const r=Object.keys(e).filter(a=>!t.hasValue(a)),o=r.length;if(o)for(let a=0;a<o;a++){const c=r[a],l=e[c];let u=null;Array.isArray(l)&&(u=l[0]),u===null&&(u=(i=(s=n[c])!==null&&s!==void 0?s:t.readValue(c))!==null&&i!==void 0?i:e[c]),u!=null&&(typeof u=="string"&&(Fr(u)||kr(u))?u=parseFloat(u):!na(u)&&tt.test(l)&&(u=Vn(c,l)),t.addValue(c,z(u,{owner:t})),n[c]===void 0&&(n[c]=u),u!==null&&t.setBaseTarget(c,u))}}function aa(t,e){return e?(e[t]||e.default||e).from:void 0}function Fi(t,e,n){var s;const i={};for(const r in t){const o=aa(r,e);i[r]=o!==void 0?o:(s=n.getValue(r))===null||s===void 0?void 0:s.get()}return i}function te(t){return Boolean(E(t)&&t.add)}const ki="framerAppearId",ca="data-"+It(ki);var ji=function(){},ee=function(){};const Gt=t=>t*1e3,ze={current:!1},Cn=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Sn=t=>e=>1-t(1-e),wn=t=>t*t,la=Sn(wn),An=Cn(wn),w=(t,e,n)=>-n*t+n*e+t;function Te(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function ua({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,o=0;if(!e)i=r=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,c=2*n-a;i=Te(c,a,t+1/3),r=Te(c,a,t),o=Te(c,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:s}}const Ve=(t,e,n)=>{const s=t*t;return Math.sqrt(Math.max(0,n*(e*e-s)+s))},fa=[je,ot,ft],da=t=>fa.find(e=>e.test(t));function Yn(t){const e=da(t);let n=e.parse(t);return e===ft&&(n=ua(n)),n}const _i=(t,e)=>{const n=Yn(t),s=Yn(e),i={...n};return r=>(i.red=Ve(n.red,s.red,r),i.green=Ve(n.green,s.green,r),i.blue=Ve(n.blue,s.blue,r),i.alpha=w(n.alpha,s.alpha,r),ot.transform(i))};function Ui(t,e){return typeof t=="number"?n=>w(t,e,n):O.test(t)?_i(t,e):Ni(t,e)}const zi=(t,e)=>{const n=[...t],s=n.length,i=t.map((r,o)=>Ui(r,e[o]));return r=>{for(let o=0;o<s;o++)n[o]=i[o](r);return n}},ha=(t,e)=>{const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Ui(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}},Ni=(t,e)=>{const n=tt.createTransformer(e),s=Qt(t),i=Qt(e);return s.numColors===i.numColors&&s.numNumbers>=i.numNumbers?fe(zi(s.values,i.values),n):o=>`${o>0?e:t}`},ne=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},qn=(t,e)=>n=>w(t,e,n);function pa(t){return typeof t=="number"?qn:typeof t=="string"?O.test(t)?_i:Ni:Array.isArray(t)?zi:typeof t=="object"?ha:qn}function ma(t,e,n){const s=[],i=n||pa(t[0]),r=t.length-1;for(let o=0;o<r;o++){let a=i(t[o],t[o+1]);if(e){const c=Array.isArray(e)?e[o]:e;a=fe(c,a)}s.push(a)}return s}function Mn(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;ee(r===e.length),ee(!s||!Array.isArray(s)||s.length===r-1),t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=ma(e,s,i),a=o.length,c=l=>{let u=0;if(a>1)for(;u<t.length-2&&!(l<t[u+1]);u++);const d=ne(t[u],t[u+1],l);return o[u](d)};return n?l=>c(pt(t[0],t[r-1],l)):c}const me=t=>t,$i=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,ga=1e-7,ya=12;function va(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=$i(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>ga&&++a<ya);return o}function Wi(t,e,n,s){if(t===e&&n===s)return me;const i=r=>va(r,0,1,t,n);return r=>r===0||r===1?r:$i(i(r),e,s)}const Gi=t=>1-Math.sin(Math.acos(t)),Rn=Sn(Gi),xa=Cn(Rn),Hi=Wi(.33,1.53,.69,.99),En=Sn(Hi),ba=Cn(En),Ta=t=>(t*=2)<1?.5*En(t):.5*(2-Math.pow(2,-10*(t-1))),Va={linear:me,easeIn:wn,easeInOut:An,easeOut:la,circIn:Gi,circInOut:xa,circOut:Rn,backIn:En,backInOut:ba,backOut:Hi,anticipate:Ta},Zn=t=>{if(Array.isArray(t)){ee(t.length===4);const[e,n,s,i]=t;return Wi(e,n,s,i)}else if(typeof t=="string")return Va[t];return t},Pa=t=>Array.isArray(t)&&typeof t[0]!="number";function Ca(t,e){return t.map(()=>e||An).splice(0,t.length-1)}function Sa(t){const e=t.length;return t.map((n,s)=>s!==0?s/(e-1):0)}function wa(t,e){return t.map(n=>n*e)}function Ne({keyframes:t,ease:e=An,times:n,duration:s=300}){t=[...t];const i=Pa(e)?e.map(Zn):Zn(e),r={done:!1,value:t[0]},o=wa(n&&n.length===t.length?n:Sa(t),s);function a(){return Mn(o,t,{ease:Array.isArray(i)?i:Ca(t,i)})}let c=a();return{next:l=>(r.value=c(l),r.done=l>=s,r),flipTarget:()=>{t.reverse(),c=a()}}}const Pe=.001,Aa=.01,Jn=10,Ma=.05,Ra=1;function Ea({duration:t=800,bounce:e=.25,velocity:n=0,mass:s=1}){let i,r;ji(t<=Jn*1e3);let o=1-e;o=pt(Ma,Ra,o),t=pt(Aa,Jn,t/1e3),o<1?(i=l=>{const u=l*o,d=u*t,f=u-n,h=$e(l,o),m=Math.exp(-d);return Pe-f/h*m},r=l=>{const d=l*o*t,f=d*n+n,h=Math.pow(o,2)*Math.pow(l,2)*t,m=Math.exp(-d),g=$e(Math.pow(l,2),o);return(-i(l)+Pe>0?-1:1)*((f-h)*m)/g}):(i=l=>{const u=Math.exp(-l*t),d=(l-n)*t+1;return-Pe+u*d},r=l=>{const u=Math.exp(-l*t),d=(n-l)*(t*t);return u*d});const a=5/t,c=Da(i,r,a);if(t=t*1e3,isNaN(c))return{stiffness:100,damping:10,duration:t};{const l=Math.pow(c,2)*s;return{stiffness:l,damping:o*2*Math.sqrt(s*l),duration:t}}}const La=12;function Da(t,e,n){let s=n;for(let i=1;i<La;i++)s=s-t(s)/e(s);return s}function $e(t,e){return t*Math.sqrt(1-e*e)}const Ia=["duration","bounce"],Oa=["stiffness","damping","mass"];function Qn(t,e){return e.some(n=>t[n]!==void 0)}function Ba(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!Qn(t,Oa)&&Qn(t,Ia)){const n=Ea(t);e={...e,...n,velocity:0,mass:1},e.isResolvedFromDuration=!0}return e}const Fa=5;function Ki({keyframes:t,restDelta:e,restSpeed:n,...s}){let i=t[0],r=t[t.length-1];const o={done:!1,value:i},{stiffness:a,damping:c,mass:l,velocity:u,duration:d,isResolvedFromDuration:f}=Ba(s);let h=ka,m=u?-(u/1e3):0;const g=c/(2*Math.sqrt(a*l));function b(){const v=r-i,T=Math.sqrt(a/l)/1e3,x=Math.abs(v)<5;if(n||(n=x?.01:2),e||(e=x?.005:.5),g<1){const y=$e(T,g);h=P=>{const C=Math.exp(-g*T*P);return r-C*((m+g*T*v)/y*Math.sin(y*P)+v*Math.cos(y*P))}}else if(g===1)h=y=>r-Math.exp(-T*y)*(v+(m+T*v)*y);else{const y=T*Math.sqrt(g*g-1);h=P=>{const C=Math.exp(-g*T*P),L=Math.min(y*P,300);return r-C*((m+g*T*v)*Math.sinh(L)+y*v*Math.cosh(L))/y}}}return b(),{next:v=>{const T=h(v);if(f)o.done=v>=d;else{let x=m;if(v!==0)if(g<1){const C=Math.max(0,v-Fa);x=xn(T-h(C),v-C)}else x=0;const y=Math.abs(x)<=n,P=Math.abs(r-T)<=e;o.done=y&&P}return o.value=o.done?r:T,o},flipTarget:()=>{m=-m,[i,r]=[r,i],b()}}}Ki.needsInterpolation=(t,e)=>typeof t=="string"||typeof e=="string";const ka=t=>0;function ja({keyframes:t=[0],velocity:e=0,power:n=.8,timeConstant:s=350,restDelta:i=.5,modifyTarget:r}){const o=t[0],a={done:!1,value:o};let c=n*e;const l=o+c,u=r===void 0?l:r(l);return u!==l&&(c=u-o),{next:d=>{const f=-c*Math.exp(-d/s);return a.done=!(f>i||f<-i),a.value=a.done?u:u+f,a},flipTarget:()=>{}}}const _a={decay:ja,keyframes:Ne,tween:Ne,spring:Ki};function Xi(t,e,n=0){return t-e-n}function Ua(t,e=0,n=0,s=!0){return s?Xi(e+-t,e,n):e-(t-e)+n}function za(t,e,n,s){return s?t>=e+n:t<=-n}const Na=t=>{const e=({delta:n})=>t(n);return{start:()=>R.update(e,!0),stop:()=>W.update(e)}};function Ft({duration:t,driver:e=Na,elapsed:n=0,repeat:s=0,repeatType:i="loop",repeatDelay:r=0,keyframes:o,autoplay:a=!0,onPlay:c,onStop:l,onComplete:u,onRepeat:d,onUpdate:f,type:h="keyframes",...m}){var g,b;const v=n;let T,x=0,y=t,P=!1,C=!0,L;const F=_a[o.length>2?"keyframes":h]||Ne,k=o[0],I=o[o.length-1];let j={done:!1,value:k};!((b=(g=F).needsInterpolation)===null||b===void 0)&&b.call(g,k,I)&&(L=Mn([0,100],[k,I],{clamp:!1}),o=[0,100]);const gt=F({...m,duration:t,keyframes:o});function ge(){x++,i==="reverse"?(C=x%2===0,n=Ua(n,y,r,C)):(n=Xi(n,y,r),i==="mirror"&&gt.flipTarget()),P=!1,d&&d()}function yt(){T&&T.stop(),u&&u()}function A(_){C||(_=-_),n+=_,P||(j=gt.next(Math.max(0,n)),L&&(j.value=L(j.value)),P=C?j.done:n<=0),f&&f(j.value),P&&(x===0&&(y=y!==void 0?y:n),x<s?za(n,y,r,C)&&ge():yt())}function et(){c&&c(),T=e(A),T.start()}return a&&et(),{stop:()=>{l&&l(),T&&T.stop()},set currentTime(_){n=v,A(_)},sample:_=>{n=v;const zn=t&&typeof t=="number"?Math.max(t*.5,50):50;let ye=0;for(A(0);ye<=_;){const ko=_-ye;A(Math.min(ko,zn)),ye+=zn}return j}}}function $a(t){return!t||Array.isArray(t)||typeof t=="string"&&Yi[t]}const Tt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Yi={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Tt([0,.65,.55,1]),circOut:Tt([.55,0,1,.45]),backIn:Tt([.31,.01,.66,-.59]),backOut:Tt([.33,1.53,.69,.99])};function Wa(t){if(t)return Array.isArray(t)?Tt(t):Yi[t]}function We(t,e,n,{delay:s=0,duration:i,repeat:r=0,repeatType:o="loop",ease:a,times:c}={}){return t.animate({[e]:n,offset:c},{delay:s,duration:i,easing:Wa(a),fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"})}const ts={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},Ce={},qi={};for(const t in ts)qi[t]=()=>(Ce[t]===void 0&&(Ce[t]=ts[t]()),Ce[t]);function Ga(t,{repeat:e,repeatType:n="loop"}){const s=e&&n!=="loop"&&e%2===1?0:t.length-1;return t[s]}const Ha=new Set(["opacity"]),zt=10;function Ka(t,e,{onUpdate:n,onComplete:s,...i}){if(!(qi.waapi()&&Ha.has(e)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0))return!1;let{keyframes:o,duration:a=300,elapsed:c=0,ease:l}=i;if(i.type==="spring"||!$a(i.ease)){if(i.repeat===1/0)return;const d=Ft({...i,elapsed:0});let f={done:!1,value:o[0]};const h=[];let m=0;for(;!f.done&&m<2e4;)f=d.sample(m),h.push(f.value),m+=zt;o=h,a=m-zt,l="linear"}const u=We(t.owner.current,e,o,{...i,delay:-c,duration:a,ease:l});return u.onfinish=()=>{t.set(Ga(o,i)),s&&s()},{get currentTime(){return u.currentTime||0},set currentTime(d){u.currentTime=d},stop:()=>{const{currentTime:d}=u;if(d){const f=Ft({...i,autoplay:!1});t.setWithVelocity(f.sample(d-zt).value,f.sample(d).value,zt)}R.update(()=>u.cancel())}}}function Zi(t,e){const n=performance.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(W.read(s),t(r-e))};return R.read(s,!0),()=>W.read(s)}function Xa({keyframes:t,elapsed:e,onUpdate:n,onComplete:s}){const i=()=>{n&&n(t[t.length-1]),s&&s()};return e?{stop:Zi(i,-e)}:i()}function Ya({keyframes:t,velocity:e=0,min:n,max:s,power:i=.8,timeConstant:r=750,bounceStiffness:o=500,bounceDamping:a=10,restDelta:c=1,modifyTarget:l,driver:u,onUpdate:d,onComplete:f,onStop:h}){const m=t[0];let g;function b(y){return n!==void 0&&y<n||s!==void 0&&y>s}function v(y){return n===void 0?s:s===void 0||Math.abs(n-y)<Math.abs(s-y)?n:s}function T(y){g==null||g.stop(),g=Ft({keyframes:[0,1],velocity:0,...y,driver:u,onUpdate:P=>{var C;d==null||d(P),(C=y.onUpdate)===null||C===void 0||C.call(y,P)},onComplete:f,onStop:h})}function x(y){T({type:"spring",stiffness:o,damping:a,restDelta:c,...y})}if(b(m))x({velocity:e,keyframes:[m,v(m)]});else{let y=i*e+m;typeof l<"u"&&(y=l(y));const P=v(y),C=P===n?-1:1;let L,F;const k=I=>{L=F,F=I,e=xn(I-L,H.delta),(C===1&&I>P||C===-1&&I<P)&&x({keyframes:[I,P],velocity:e})};T({type:"decay",keyframes:[m,0],velocity:e,timeConstant:r,power:i,restDelta:c,modifyTarget:l,onUpdate:b(y)?k:void 0})}return{stop:()=>g==null?void 0:g.stop()}}const nt=()=>({type:"spring",stiffness:500,damping:25,restSpeed:10}),Nt=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Se=()=>({type:"keyframes",ease:"linear",duration:.3}),qa={type:"keyframes",duration:.8},es={x:nt,y:nt,z:nt,rotate:nt,rotateX:nt,rotateY:nt,rotateZ:nt,scaleX:Nt,scaleY:Nt,scale:Nt,opacity:Se,backgroundColor:Se,color:Se,default:Nt},Za=(t,{keyframes:e})=>e.length>2?qa:(es[t]||es.default)(e[1]),Ge=(t,e)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&tt.test(e)&&!e.startsWith("url("));function Ja({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:c,elapsed:l,...u}){return!!Object.keys(u).length}function ns(t){return t===0||typeof t=="string"&&parseFloat(t)===0&&t.indexOf(" ")===-1}function ss(t){return typeof t=="number"?0:Vn("",t)}function Ji(t,e){return t[e]||t.default||t}function Qa(t,e,n,s){const i=Ge(e,n);let r=s.from!==void 0?s.from:t.get();return r==="none"&&i&&typeof n=="string"?r=Vn(e,n):ns(r)&&typeof n=="string"?r=ss(n):!Array.isArray(n)&&ns(n)&&typeof r=="string"&&(n=ss(r)),Array.isArray(n)?(n[0]===null&&(n[0]=r),n):[r,n]}const Ln=(t,e,n,s={})=>i=>{const r=Ji(s,t)||{},o=r.delay||s.delay||0;let{elapsed:a=0}=s;a=a-Gt(o);const c=Qa(e,t,n,r),l=c[0],u=c[c.length-1],d=Ge(t,l),f=Ge(t,u);let h={keyframes:c,velocity:e.getVelocity(),...r,elapsed:a,onUpdate:b=>{e.set(b),r.onUpdate&&r.onUpdate(b)},onComplete:()=>{i(),r.onComplete&&r.onComplete()}};if(!d||!f||ze.current||r.type===!1)return Xa(h);if(r.type==="inertia")return Ya(h);Ja(r)||(h={...h,...Za(t,h)}),h.duration&&(h.duration=Gt(h.duration)),h.repeatDelay&&(h.repeatDelay=Gt(h.repeatDelay));const m=e.owner,g=m&&m.current;if(m&&g instanceof HTMLElement&&!(m!=null&&m.getProps().onUpdate)){const b=Ka(e,t,h);if(b)return b}return Ft(h)};function Dn(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>He(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=He(t,e,n);else{const i=typeof e=="function"?pe(t,e,n.custom):e;s=Qi(t,i,n)}return s.then(()=>t.notify("AnimationComplete",e))}function He(t,e,n={}){var s;const i=pe(t,e,n.custom);let{transition:r=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(r=n.transitionOverride);const o=i?()=>Qi(t,i,n):()=>Promise.resolve(),a=!((s=t.variantChildren)===null||s===void 0)&&s.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:d,staggerDirection:f}=r;return tc(t,e,u+l,d,f,n)}:()=>Promise.resolve(),{when:c}=r;if(c){const[l,u]=c==="beforeChildren"?[o,a]:[a,o];return l().then(u)}else return Promise.all([o(),a(n.delay)])}function Qi(t,e,{delay:n=0,transitionOverride:s,type:i}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...c}=t.makeTargetAnimatable(e);const l=t.getValue("willChange");s&&(o=s);const u=[],d=i&&((r=t.animationState)===null||r===void 0?void 0:r.getState()[i]);for(const f in c){const h=t.getValue(f),m=c[f];if(!h||m===void 0||d&&sc(d,f))continue;const g={delay:n,elapsed:0,...o};if(window.HandoffAppearAnimations&&!h.hasAnimated){const v=t.getProps()[ca];v&&(g.elapsed=window.HandoffAppearAnimations(v,f,h,R))}let b=h.start(Ln(f,h,m,t.shouldReduceMotion&&X.has(f)?{type:!1}:g));te(l)&&(l.add(f),b=b.then(()=>l.remove(f))),u.push(b)}return Promise.all(u).then(()=>{a&&Pn(t,a)})}function tc(t,e,n=0,s=0,i=1,r){const o=[],a=(t.variantChildren.size-1)*s,c=i===1?(l=0)=>l*s:(l=0)=>a-l*s;return Array.from(t.variantChildren).sort(nc).forEach((l,u)=>{l.notify("AnimationStart",e),o.push(He(l,e,{...r,delay:n+c(u)}).then(()=>l.notify("AnimationComplete",e)))}),Promise.all(o)}function ec(t){t.values.forEach(e=>e.stop())}function nc(t,e){return t.sortNodePosition(e)}function sc({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}const In=[S.Animate,S.InView,S.Focus,S.Hover,S.Tap,S.Drag,S.Exit],ic=[...In].reverse(),oc=In.length;function rc(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Dn(t,n,s)))}function ac(t){let e=rc(t);const n=lc();let s=!0;const i=(c,l)=>{const u=pe(t,l);if(u){const{transition:d,transitionEnd:f,...h}=u;c={...c,...h,...f}}return c};function r(c){e=c(t)}function o(c,l){const u=t.getProps(),d=t.getVariantContext(!0)||{},f=[],h=new Set;let m={},g=1/0;for(let v=0;v<oc;v++){const T=ic[v],x=n[T],y=u[T]!==void 0?u[T]:d[T],P=Rt(y),C=T===l?x.isActive:null;C===!1&&(g=v);let L=y===d[T]&&y!==u[T]&&P;if(L&&s&&t.manuallyAnimateOnMount&&(L=!1),x.protectedKeys={...m},!x.isActive&&C===null||!y&&!x.prevProp||ce(y)||typeof y=="boolean")continue;const F=cc(x.prevProp,y);let k=F||T===l&&x.isActive&&!L&&P||v>g&&P;const I=Array.isArray(y)?y:[y];let j=I.reduce(i,{});C===!1&&(j={});const{prevResolvedValues:gt={}}=x,ge={...gt,...j},yt=A=>{k=!0,h.delete(A),x.needsAnimating[A]=!0};for(const A in ge){const et=j[A],_=gt[A];m.hasOwnProperty(A)||(et!==_?Zt(et)&&Zt(_)?!Pi(et,_)||F?yt(A):x.protectedKeys[A]=!0:et!==void 0?yt(A):h.add(A):et!==void 0&&h.has(A)?yt(A):x.protectedKeys[A]=!0)}x.prevProp=y,x.prevResolvedValues=j,x.isActive&&(m={...m,...j}),s&&t.blockInitialAnimation&&(k=!1),k&&!L&&f.push(...I.map(A=>({animation:A,options:{type:T,...c}})))}if(h.size){const v={};h.forEach(T=>{const x=t.getBaseTarget(T);x!==void 0&&(v[T]=x)}),f.push({animation:v})}let b=Boolean(f.length);return s&&u.initial===!1&&!t.manuallyAnimateOnMount&&(b=!1),s=!1,b?e(f):Promise.resolve()}function a(c,l,u){var d;if(n[c].isActive===l)return Promise.resolve();(d=t.variantChildren)===null||d===void 0||d.forEach(h=>{var m;return(m=h.animationState)===null||m===void 0?void 0:m.setActive(c,l)}),n[c].isActive=l;const f=o(u,c);for(const h in n)n[h].protectedKeys={};return f}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>n}}function cc(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Pi(e,t):!1}function st(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function lc(){return{[S.Animate]:st(!0),[S.InView]:st(),[S.Hover]:st(),[S.Tap]:st(),[S.Drag]:st(),[S.Focus]:st(),[S.Exit]:st()}}const to={animation:J(({visualElement:t,animate:e})=>{t.animationState||(t.animationState=ac(t)),ce(e)&&p.useEffect(()=>e.subscribe(t),[e])}),exit:J(t=>{const{custom:e,visualElement:n}=t,[s,i]=Vi(),r=p.useContext(mt);p.useEffect(()=>{n.isPresent=s;const o=n.animationState&&n.animationState.setActive(S.Exit,!s,{custom:r&&r.custom||e});o&&!s&&o.then(i)},[s])})},is=(t,e)=>Math.abs(t-e);function uc(t,e){const n=is(t.x,e.x),s=is(t.y,e.y);return Math.sqrt(n**2+s**2)}class eo{constructor(e,n,{transformPagePoint:s}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const l=Ae(this.lastMoveEventInfo,this.history),u=this.startEvent!==null,d=uc(l.offset,{x:0,y:0})>=3;if(!u&&!d)return;const{point:f}=l,{timestamp:h}=H;this.history.push({...f,timestamp:h});const{onStart:m,onMove:g}=this.handlers;u||(m&&m(this.lastMoveEvent,l),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,l)},this.handlePointerMove=(l,u)=>{this.lastMoveEvent=l,this.lastMoveEventInfo=we(u,this.transformPagePoint),R.update(this.updatePoint,!0)},this.handlePointerUp=(l,u)=>{if(this.end(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const{onEnd:d,onSessionEnd:f}=this.handlers,h=Ae(l.type==="pointercancel"?this.lastMoveEventInfo:we(u,this.transformPagePoint),this.history);this.startEvent&&d&&d(l,h),f&&f(l,h)},!mi(e))return;this.handlers=n,this.transformPagePoint=s;const i=gn(e),r=we(i,this.transformPagePoint),{point:o}=r,{timestamp:a}=H;this.history=[{...o,timestamp:a}];const{onSessionStart:c}=n;c&&c(e,Ae(r,this.history)),this.removeListeners=fe(ht(window,"pointermove",this.handlePointerMove),ht(window,"pointerup",this.handlePointerUp),ht(window,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),W.update(this.updatePoint)}}function we(t,e){return e?{point:e(t.point)}:t}function os(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ae({point:t},e){return{point:t,delta:os(t,no(e)),offset:os(t,fc(e)),velocity:dc(e,.1)}}function fc(t){return t[0]}function no(t){return t[t.length-1]}function dc(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=no(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>Gt(e)));)n--;if(!s)return{x:0,y:0};const r=(i.timestamp-s.timestamp)/1e3;if(r===0)return{x:0,y:0};const o={x:(i.x-s.x)/r,y:(i.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function B(t){return t.max-t.min}function Ke(t,e=0,n=.01){return Math.abs(t-e)<=n}function rs(t,e,n,s=.5){t.origin=s,t.originPoint=w(e.min,e.max,t.origin),t.scale=B(n)/B(e),(Ke(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=w(n.min,n.max,t.origin)-t.originPoint,(Ke(t.translate)||isNaN(t.translate))&&(t.translate=0)}function St(t,e,n,s){rs(t.x,e.x,n.x,s==null?void 0:s.originX),rs(t.y,e.y,n.y,s==null?void 0:s.originY)}function as(t,e,n){t.min=n.min+e.min,t.max=t.min+B(e)}function hc(t,e,n){as(t.x,e.x,n.x),as(t.y,e.y,n.y)}function cs(t,e,n){t.min=e.min-n.min,t.max=t.min+B(e)}function wt(t,e,n){cs(t.x,e.x,n.x),cs(t.y,e.y,n.y)}function pc(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?w(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?w(n,t,s.max):Math.min(t,n)),t}function ls(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function mc(t,{top:e,left:n,bottom:s,right:i}){return{x:ls(t.x,n,i),y:ls(t.y,e,s)}}function us(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function gc(t,e){return{x:us(t.x,e.x),y:us(t.y,e.y)}}function yc(t,e){let n=.5;const s=B(t),i=B(e);return i>s?n=ne(e.min,e.max-s,t.min):s>i&&(n=ne(t.min,t.max-i,e.min)),pt(0,1,n)}function vc(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Xe=.35;function xc(t=Xe){return t===!1?t=0:t===!0&&(t=Xe),{x:fs(t,"left","right"),y:fs(t,"top","bottom")}}function fs(t,e,n){return{min:ds(t,e),max:ds(t,n)}}function ds(t,e){return typeof t=="number"?t:t[e]||0}const hs=()=>({translate:0,scale:1,origin:0,originPoint:0}),At=()=>({x:hs(),y:hs()}),ps=()=>({min:0,max:0}),M=()=>({x:ps(),y:ps()});function N(t){return[t("x"),t("y")]}function so({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function bc({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Tc(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function Me(t){return t===void 0||t===1}function Ye({scale:t,scaleX:e,scaleY:n}){return!Me(t)||!Me(e)||!Me(n)}function it(t){return Ye(t)||io(t)||t.z||t.rotate||t.rotateX||t.rotateY}function io(t){return ms(t.x)||ms(t.y)}function ms(t){return t&&t!=="0%"}function se(t,e,n){const s=t-n,i=e*s;return n+i}function gs(t,e,n,s,i){return i!==void 0&&(t=se(t,i,s)),se(t,n,s)+e}function qe(t,e=0,n=1,s,i){t.min=gs(t.min,e,n,s,i),t.max=gs(t.max,e,n,s,i)}function oo(t,{x:e,y:n}){qe(t.x,e.translate,e.scale,e.originPoint),qe(t.y,n.translate,n.scale,n.originPoint)}function Vc(t,e,n,s=!1){var i,r;const o=n.length;if(!o)return;e.x=e.y=1;let a,c;for(let l=0;l<o;l++)a=n[l],c=a.projectionDelta,((r=(i=a.instance)===null||i===void 0?void 0:i.style)===null||r===void 0?void 0:r.display)!=="contents"&&(s&&a.options.layoutScroll&&a.scroll&&a!==a.root&&dt(t,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),c&&(e.x*=c.x.scale,e.y*=c.y.scale,oo(t,c)),s&&it(a.latestValues)&&dt(t,a.latestValues));e.x=ys(e.x),e.y=ys(e.y)}function ys(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function Z(t,e){t.min=t.min+e,t.max=t.max+e}function vs(t,e,[n,s,i]){const r=e[i]!==void 0?e[i]:.5,o=w(t.min,t.max,r);qe(t,e[n],e[s],o,e.scale)}const Pc=["x","scaleX","originX"],Cc=["y","scaleY","originY"];function dt(t,e){vs(t.x,e,Pc),vs(t.y,e,Cc)}function ro(t,e){return so(Tc(t.getBoundingClientRect(),e))}function Sc(t,e,n){const s=ro(t,n),{scroll:i}=e;return i&&(Z(s.x,i.offset.x),Z(s.y,i.offset.y)),s}const wc=new WeakMap;class Ac{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=M(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){if(this.visualElement.isPresent===!1)return;const s=a=>{this.stopAnimation(),n&&this.snapToCursor(gn(a,"page").point)},i=(a,c)=>{var l;const{drag:u,dragPropagation:d,onDragStart:f}=this.getProps();u&&!d&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=vi(u),!this.openGlobalLock)||(this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),N(h=>{var m,g;let b=this.getAxisMotionValue(h).get()||0;if($.test(b)){const v=(g=(m=this.visualElement.projection)===null||m===void 0?void 0:m.layout)===null||g===void 0?void 0:g.layoutBox[h];v&&(b=B(v)*(parseFloat(b)/100))}this.originPoint[h]=b}),f==null||f(a,c),(l=this.visualElement.animationState)===null||l===void 0||l.setActive(S.Drag,!0))},r=(a,c)=>{const{dragPropagation:l,dragDirectionLock:u,onDirectionLock:d,onDrag:f}=this.getProps();if(!l&&!this.openGlobalLock)return;const{offset:h}=c;if(u&&this.currentDirection===null){this.currentDirection=Mc(h),this.currentDirection!==null&&(d==null||d(this.currentDirection));return}this.updateAxis("x",c.point,h),this.updateAxis("y",c.point,h),this.visualElement.render(),f==null||f(a,c)},o=(a,c)=>this.stop(a,c);this.panSession=new eo(e,{onSessionStart:s,onStart:i,onMove:r,onSessionEnd:o},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:r}=this.getProps();r==null||r(e,n)}cancel(){var e,n;this.isDragging=!1,this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!1),(e=this.panSession)===null||e===void 0||e.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),(n=this.visualElement.animationState)===null||n===void 0||n.setActive(S.Drag,!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!$t(e,i,this.currentDirection))return;const r=this.getAxisMotionValue(e);let o=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(o=pc(o,this.constraints[e],this.elastic[e])),r.set(o)}resolveConstraints(){const{dragConstraints:e,dragElastic:n}=this.getProps(),{layout:s}=this.visualElement.projection||{},i=this.constraints;e&&ut(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=mc(s.layoutBox,e):this.constraints=!1,this.elastic=xc(n),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&N(r=>{this.getAxisMotionValue(r)&&(this.constraints[r]=vc(s.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!ut(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const r=Sc(s,i.root,this.visualElement.getTransformPagePoint());let o=gc(i.layout.layoutBox,r);if(n){const a=n(bc(o));this.hasMutatedConstraints=!!a,a&&(o=so(a))}return o}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),c=this.constraints||{},l=N(u=>{if(!$t(u,n,this.currentDirection))return;let d=(c==null?void 0:c[u])||{};o&&(d={min:0,max:0});const f=i?200:1e6,h=i?40:1e7,m={type:"inertia",velocity:s?e[u]:0,bounceStiffness:f,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...r,...d};return this.startAxisValueAnimation(u,m)});return Promise.all(l).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return s.start(Ln(e,s,0,n))}stopAnimation(){N(e=>this.getAxisMotionValue(e).stop())}getAxisMotionValue(e){var n;const s="_drag"+e.toUpperCase(),i=this.visualElement.getProps()[s];return i||this.visualElement.getValue(e,((n=this.visualElement.getProps().initial)===null||n===void 0?void 0:n[e])||0)}snapToCursor(e){N(n=>{const{drag:s}=this.getProps();if(!$t(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,r=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];r.set(e[n]-w(o,a,.5))}})}scalePositionWithinConstraints(){var e;if(!this.visualElement.current)return;const{drag:n,dragConstraints:s}=this.getProps(),{projection:i}=this.visualElement;if(!ut(s)||!i||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};N(a=>{const c=this.getAxisMotionValue(a);if(c){const l=c.get();r[a]=yc({min:l,max:l},this.constraints[a])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",(e=i.root)===null||e===void 0||e.updateScroll(),i.updateLayout(),this.resolveConstraints(),N(a=>{if(!$t(a,n,null))return;const c=this.getAxisMotionValue(a),{min:l,max:u}=this.constraints[a];c.set(w(l,u,r[a]))})}addListeners(){var e;if(!this.visualElement.current)return;wc.set(this.visualElement,this);const n=this.visualElement.current,s=ht(n,"pointerdown",l=>{const{drag:u,dragListener:d=!0}=this.getProps();u&&d&&this.start(l)}),i=()=>{const{dragConstraints:l}=this.getProps();ut(l)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,o=r.addEventListener("measure",i);r&&!r.layout&&((e=r.root)===null||e===void 0||e.updateScroll(),r.updateLayout()),i();const a=ue(window,"resize",()=>this.scalePositionWithinConstraints()),c=r.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(N(d=>{const f=this.getAxisMotionValue(d);f&&(this.originPoint[d]+=l[d].translate,f.set(f.get()+l[d].translate))}),this.visualElement.render())});return()=>{a(),s(),o(),c==null||c()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:o=Xe,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function $t(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Mc(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}function Rc(t){const{dragControls:e,visualElement:n}=t,s=D(()=>new Ac(n));p.useEffect(()=>e&&e.subscribe(s),[s,e]),p.useEffect(()=>s.addListeners(),[s])}function Ec({onPan:t,onPanStart:e,onPanEnd:n,onPanSessionStart:s,visualElement:i}){const r=t||e||n||s,o=p.useRef(null),{transformPagePoint:a}=p.useContext(K),c={onSessionStart:s,onStart:e,onMove:t,onEnd:(u,d)=>{o.current=null,n&&n(u,d)}};p.useEffect(()=>{o.current!==null&&o.current.updateHandlers(c)});function l(u){o.current=new eo(u,c,{transformPagePoint:a})}Jt(i,"pointerdown",r&&l),yn(()=>o.current&&o.current.end())}const ao={pan:J(Ec),drag:J(Rc)};function Ze(t){return typeof t=="string"&&t.startsWith("var(--")}const co=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Lc(t){const e=co.exec(t);if(!e)return[,];const[,n,s]=e;return[n,s]}function Je(t,e,n=1){const[s,i]=Lc(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);return r?r.trim():Ze(i)?Je(i,e,n+1):i}function Dc(t,{...e},n){const s=t.current;if(!(s instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(i=>{const r=i.get();if(!Ze(r))return;const o=Je(r,s);o&&i.set(o)});for(const i in e){const r=e[i];if(!Ze(r))continue;const o=Je(r,s);o&&(e[i]=o,n&&n[i]===void 0&&(n[i]=r))}return{target:e,transitionEnd:n}}const Ic=new Set(["width","height","top","left","right","bottom","x","y"]),lo=t=>Ic.has(t),Oc=t=>Object.keys(t).some(lo),xs=t=>t===ct||t===V;var bs;(function(t){t.width="width",t.height="height",t.left="left",t.right="right",t.top="top",t.bottom="bottom"})(bs||(bs={}));const Ts=(t,e)=>parseFloat(t.split(", ")[e]),Vs=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/);if(i)return Ts(i[1],e);{const r=s.match(/^matrix\((.+)\)$/);return r?Ts(r[1],t):0}},Bc=new Set(["x","y","z"]),Fc=Yt.filter(t=>!Bc.has(t));function kc(t){const e=[];return Fc.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}const Ps={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:Vs(4,13),y:Vs(5,14)},jc=(t,e,n)=>{const s=e.measureViewportBox(),i=e.current,r=getComputedStyle(i),{display:o}=r,a={};o==="none"&&e.setStaticValue("display",t.display||"block"),n.forEach(l=>{a[l]=Ps[l](s,r)}),e.render();const c=e.measureViewportBox();return n.forEach(l=>{const u=e.getValue(l);u&&u.jump(a[l]),t[l]=Ps[l](c,r)}),t},_c=(t,e,n={},s={})=>{e={...e},s={...s};const i=Object.keys(e).filter(lo);let r=[],o=!1;const a=[];if(i.forEach(c=>{const l=t.getValue(c);if(!t.hasValue(c))return;let u=n[c],d=vt(u);const f=e[c];let h;if(Zt(f)){const m=f.length,g=f[0]===null?1:0;u=f[g],d=vt(u);for(let b=g;b<m;b++)h?ee(vt(f[b])===h):h=vt(f[b])}else h=vt(f);if(d!==h)if(xs(d)&&xs(h)){const m=l.get();typeof m=="string"&&l.set(parseFloat(m)),typeof f=="string"?e[c]=parseFloat(f):Array.isArray(f)&&h===V&&(e[c]=f.map(parseFloat))}else d!=null&&d.transform&&(h!=null&&h.transform)&&(u===0||f===0)?u===0?l.set(h.transform(u)):e[c]=d.transform(f):(o||(r=kc(t),o=!0),a.push(c),s[c]=s[c]!==void 0?s[c]:e[c],l.jump(f))}),a.length){const c=a.indexOf("height")>=0?window.pageYOffset:null,l=jc(e,t,a);return r.length&&r.forEach(([u,d])=>{t.getValue(u).set(d)}),t.render(),ae&&c!==null&&window.scrollTo({top:c}),{target:l,transitionEnd:s}}else return{target:e,transitionEnd:s}};function Uc(t,e,n,s){return Oc(e)?_c(t,e,n,s):{target:e,transitionEnd:s}}const zc=(t,e,n,s)=>{const i=Dc(t,e,s);return e=i.target,s=i.transitionEnd,Uc(t,e,n,s)},ie={current:null},On={current:!1};function uo(){if(On.current=!0,!!ae)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ie.current=t.matches;t.addListener(e),e()}else ie.current=!1}function Nc(t,e,n){const{willChange:s}=e;for(const i in e){const r=e[i],o=n[i];if(E(r))t.addValue(i,r),te(s)&&s.add(i);else if(E(o))t.addValue(i,z(r,{owner:t})),te(s)&&s.remove(i);else if(o!==r)if(t.hasValue(i)){const a=t.getValue(i);!a.hasAnimated&&a.set(r)}else{const a=t.getStaticValue(i);t.addValue(i,z(a!==void 0?a:r,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const fo=Object.keys(Et),$c=fo.length,Cs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ho{constructor({parent:e,props:n,reducedMotionConfig:s,visualState:i},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.isPresent=!0,this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>R.render(this.render,!1,!0);const{latestValues:o,renderState:a}=i;this.latestValues=o,this.baseTarget={...o},this.initialValues=n.initial?{...o}:{},this.renderState=a,this.parent=e,this.props=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=s,this.options=r,this.isControllingVariants=le(n),this.isVariantNode=ei(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(e&&e.current);const{willChange:c,...l}=this.scrapeMotionValuesFromProps(n,{});for(const u in l){const d=l[u];o[u]!==void 0&&E(d)&&(d.set(o[u],!1),te(c)&&c.add(u))}}scrapeMotionValuesFromProps(e,n){return{}}mount(e){var n;this.current=e,this.projection&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=(n=this.parent)===null||n===void 0?void 0:n.addVariantChild(this)),this.values.forEach((s,i)=>this.bindToMotionValue(i,s)),On.current||uo(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ie.current,this.parent&&this.parent.children.add(this),this.setProps(this.props)}unmount(){var e,n,s;(e=this.projection)===null||e===void 0||e.unmount(),W.update(this.notifyUpdate),W.render(this.render),this.valueSubscriptions.forEach(i=>i()),(n=this.removeFromVariantTree)===null||n===void 0||n.call(this),(s=this.parent)===null||s===void 0||s.children.delete(this);for(const i in this.events)this.events[i].clear();this.current=null}bindToMotionValue(e,n){const s=X.has(e),i=n.on("change",o=>{this.latestValues[e]=o,this.props.onUpdate&&R.update(this.notifyUpdate,!1,!0),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),r()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...n},s,i,r,o,a){const c=[];for(let l=0;l<$c;l++){const u=fo[l],{isEnabled:d,Component:f}=Et[u];d(n)&&f&&c.push(p.createElement(f,{key:u,...n,visualElement:this}))}if(!this.projection&&o){this.projection=new o(r,this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:d,dragConstraints:f,layoutScroll:h,layoutRoot:m}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:Boolean(d)||f&&ut(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:a,layoutScroll:h,layoutRoot:m})}return c}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):M()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}makeTargetAnimatable(e,n=!0){return this.makeTargetAnimatableFromInstance(e,this.props,n)}setProps(e){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender();const n=this.props;this.props=e;for(let s=0;s<Cs.length;s++){const i=Cs[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=Nc(this,this.scrapeMotionValuesFromProps(e,n),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){var n;return(n=this.props.variants)===null||n===void 0?void 0:n[e]}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){var e;return this.isVariantNode?this:(e=this.parent)===null||e===void 0?void 0:e.getClosestVariantNode()}getVariantContext(e=!1){var n,s;if(e)return(n=this.parent)===null||n===void 0?void 0:n.getVariantContext();if(!this.isControllingVariants){const r=((s=this.parent)===null||s===void 0?void 0:s.getVariantContext())||{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const i={};for(let r=0;r<Wc;r++){const o=po[r],a=this.props[o];(Rt(a)||a===!1)&&(i[o]=a)}return i}addVariantChild(e){var n;const s=this.getClosestVariantNode();if(s)return(n=s.variantChildren)===null||n===void 0||n.add(e),()=>s.variantChildren.delete(e)}addValue(e,n){n!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,n)),this.values.set(e,n),this.latestValues[e]=n.get()}removeValue(e){var n;this.values.delete(e),(n=this.valueSubscriptions.get(e))===null||n===void 0||n(),this.valueSubscriptions.delete(e),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=z(n,{owner:this}),this.addValue(e,s)),s}readValue(e){return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props,i=typeof s=="string"||typeof s=="object"?(n=hn(this.props,s))===null||n===void 0?void 0:n[e]:void 0;if(s&&i!==void 0)return i;const r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!E(r)?r:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new vn),this.events[e].add(n)}notify(e,...n){var s;(s=this.events[e])===null||s===void 0||s.notify(...n)}}const po=["initial",...In],Wc=po.length;class mo extends ho{sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){var s;return(s=e.style)===null||s===void 0?void 0:s[n]}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:n,...s},{transformValues:i},r){let o=Fi(s,e||{},this);if(i&&(n&&(n=i(n)),s&&(s=i(s)),o&&(o=i(o))),r){Bi(this,s,o);const a=zc(this,s,o,n);n=a.transitionEnd,s=a.target}return{transition:e,transitionEnd:n,...s}}}function Gc(t){return window.getComputedStyle(t)}class Hc extends mo{readValueFromInstance(e,n){if(X.has(n)){const s=Tn(n);return s&&s.default||0}else{const s=Gc(e),i=(an(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return ro(e,n)}build(e,n,s,i){cn(e,n,s,i.transformTemplate)}scrapeMotionValuesFromProps(e,n){return dn(e,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;E(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(e,n,s,i){fi(e,n,s,i)}}class Kc extends mo{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){var s;return X.has(n)?((s=Tn(n))===null||s===void 0?void 0:s.default)||0:(n=di.has(n)?n:It(n),e.getAttribute(n))}measureInstanceViewportBox(){return M()}scrapeMotionValuesFromProps(e,n){return pi(e,n)}build(e,n,s,i){un(e,n,s,this.isSVGTag,i.transformTemplate)}renderInstance(e,n,s,i){hi(e,n,s,i)}mount(e){this.isSVGTag=fn(e.tagName),super.mount(e)}}const Bn=(t,e)=>rn(t)?new Kc(e,{enableHardwareAcceleration:!1}):new Hc(e,{enableHardwareAcceleration:!0});function Ss(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const xt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(V.test(t))t=parseFloat(t);else return t;const n=Ss(t,e.target.x),s=Ss(t,e.target.y);return`${n}% ${s}%`}},ws="_$css",Xc={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=t.includes("var("),r=[];i&&(t=t.replace(co,h=>(r.push(h),ws)));const o=tt.parse(t);if(o.length>5)return s;const a=tt.createTransformer(t),c=typeof o[0]!="number"?1:0,l=n.x.scale*e.x,u=n.y.scale*e.y;o[0+c]/=l,o[1+c]/=u;const d=w(l,u,.5);typeof o[2+c]=="number"&&(o[2+c]/=d),typeof o[3+c]=="number"&&(o[3+c]/=d);let f=a(o);if(i){let h=0;f=f.replace(ws,()=>{const m=r[h];return h++,m})}return f}};class Yc extends nn.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:r}=e;Xo(Zc),r&&(n.group&&n.group.add(r),s&&s.register&&i&&s.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),Vt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:r}=this.props,o=s.projection;return o&&(o.isPresent=r,i||e.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?o.promote():o.relegate()||R.postRender(()=>{var a;!((a=o.getStack())===null||a===void 0)&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),!e.currentAnimation&&e.isLead()&&this.safeToRemove())}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n!=null&&n.group&&n.group.remove(i),s!=null&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e==null||e()}render(){return null}}function qc(t){const[e,n]=Vi(),s=p.useContext(Lt);return nn.createElement(Yc,{...t,layoutGroup:s,switchLayoutGroup:p.useContext(ni),isPresent:e,safeToRemove:n})}const Zc={borderRadius:{...xt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:xt,borderTopRightRadius:xt,borderBottomLeftRadius:xt,borderBottomRightRadius:xt,boxShadow:Xc},go={measureLayout:qc};function Jc(t,e,n={}){const s=E(t)?t:z(t);return s.start(Ln("",s,e,n)),{stop:()=>s.stop(),isAnimating:()=>s.isAnimating()}}const yo=["TopLeft","TopRight","BottomLeft","BottomRight"],Qc=yo.length,As=t=>typeof t=="string"?parseFloat(t):t,Ms=t=>typeof t=="number"||V.test(t);function tl(t,e,n,s,i,r){i?(t.opacity=w(0,n.opacity!==void 0?n.opacity:1,el(s)),t.opacityExit=w(e.opacity!==void 0?e.opacity:1,0,nl(s))):r&&(t.opacity=w(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let o=0;o<Qc;o++){const a=`border${yo[o]}Radius`;let c=Rs(e,a),l=Rs(n,a);if(c===void 0&&l===void 0)continue;c||(c=0),l||(l=0),c===0||l===0||Ms(c)===Ms(l)?(t[a]=Math.max(w(As(c),As(l),s),0),($.test(l)||$.test(c))&&(t[a]+="%")):t[a]=l}(e.rotate||n.rotate)&&(t.rotate=w(e.rotate||0,n.rotate||0,s))}function Rs(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const el=vo(0,.5,Rn),nl=vo(.5,.95,me);function vo(t,e,n){return s=>s<t?0:s>e?1:n(ne(t,e,s))}function Es(t,e){t.min=e.min,t.max=e.max}function U(t,e){Es(t.x,e.x),Es(t.y,e.y)}function Ls(t,e,n,s,i){return t-=e,t=se(t,1/n,s),i!==void 0&&(t=se(t,1/i,s)),t}function sl(t,e=0,n=1,s=.5,i,r=t,o=t){if($.test(e)&&(e=parseFloat(e),e=w(o.min,o.max,e/100)-o.min),typeof e!="number")return;let a=w(r.min,r.max,s);t===r&&(a-=e),t.min=Ls(t.min,e,n,a,i),t.max=Ls(t.max,e,n,a,i)}function Ds(t,e,[n,s,i],r,o){sl(t,e[n],e[s],e[i],e.scale,r,o)}const il=["x","scaleX","originX"],ol=["y","scaleY","originY"];function Is(t,e,n,s){Ds(t.x,e,il,n==null?void 0:n.x,s==null?void 0:s.x),Ds(t.y,e,ol,n==null?void 0:n.y,s==null?void 0:s.y)}function Os(t){return t.translate===0&&t.scale===1}function xo(t){return Os(t.x)&&Os(t.y)}function bo(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function Bs(t){return B(t.x)/B(t.y)}class rl{constructor(){this.members=[]}add(e){he(this.members,e),e.scheduleRender()}remove(e){if(Bt(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const r=this.members[i];if(r.isPresent!==!1){s=r;break}}return s?(this.promote(s),!0):!1}promote(e,n){var s;const i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,n&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),!((s=e.root)===null||s===void 0)&&s.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;r===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{var n,s,i,r,o;(s=(n=e.options).onExitComplete)===null||s===void 0||s.call(n),(o=(i=e.resumingFrom)===null||i===void 0?void 0:(r=i.options).onExitComplete)===null||o===void 0||o.call(r)})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Fs(t,e,n){let s="";const i=t.x.translate/e.x,r=t.y.translate/e.y;if((i||r)&&(s=`translate3d(${i}px, ${r}px, 0) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:c,rotateX:l,rotateY:u}=n;c&&(s+=`rotate(${c}deg) `),l&&(s+=`rotateX(${l}deg) `),u&&(s+=`rotateY(${u}deg) `)}const o=t.x.scale*e.x,a=t.y.scale*e.y;return(o!==1||a!==1)&&(s+=`scale(${o}, ${a})`),s||"none"}const al=(t,e)=>t.depth-e.depth;class cl{constructor(){this.children=[],this.isDirty=!1}add(e){he(this.children,e),this.isDirty=!0}remove(e){Bt(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(al),this.isDirty=!1,this.children.forEach(e)}}const ks=["","X","Y","Z"],js=1e3;let ll=0;function To({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(o,a={},c=e==null?void 0:e()){this.id=ll++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isTransformDirty=!1,this.isProjectionDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.potentialNodes=new Map,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.nodes.forEach(dl),this.nodes.forEach(ml),this.nodes.forEach(gl)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.elementId=o,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0,o&&this.root.registerPotentialNode(o,this);for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new cl)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new vn),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const c=this.eventHandlers.get(o);c==null||c.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}registerPotentialNode(o,a){this.potentialNodes.set(o,a)}mount(o,a=!1){var c;if(this.instance)return;this.isSVG=o instanceof SVGElement&&o.tagName!=="svg",this.instance=o;const{layoutId:l,layout:u,visualElement:d}=this.options;if(d&&!d.current&&d.mount(o),this.root.nodes.add(this),(c=this.parent)===null||c===void 0||c.children.add(this),this.elementId&&this.root.potentialNodes.delete(this.elementId),a&&(u||l)&&(this.isLayoutDirty=!0),t){let f;const h=()=>this.root.updateBlockedByResize=!1;t(o,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=Zi(h,250),Vt.hasAnimatedSinceResize&&(Vt.hasAnimatedSinceResize=!1,this.nodes.forEach(Us))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&d&&(l||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:h,hasRelativeTargetChanged:m,layout:g})=>{var b,v,T,x,y;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const P=(v=(b=this.options.transition)!==null&&b!==void 0?b:d.getDefaultTransition())!==null&&v!==void 0?v:Tl,{onLayoutAnimationStart:C,onLayoutAnimationComplete:L}=d.getProps(),F=!this.targetLayout||!bo(this.targetLayout,g)||m,k=!h&&m;if(this.options.layoutRoot||!((T=this.resumeFrom)===null||T===void 0)&&T.instance||k||h&&(F||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,k);const I={...Ji(P,"layout"),onPlay:C,onComplete:L};(d.shouldReduceMotion||this.options.layoutRoot)&&(I.delay=0,I.type=!1),this.startAnimation(I)}else!h&&this.animationProgress===0&&Us(this),this.isLead()&&((y=(x=this.options).onExitComplete)===null||y===void 0||y.call(x));this.targetLayout=g})}unmount(){var o,a;this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this),(o=this.getStack())===null||o===void 0||o.remove(this),(a=this.parent)===null||a===void 0||a.children.delete(this),this.instance=void 0,W.preRender(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){var o;return this.isAnimationBlocked||((o=this.parent)===null||o===void 0?void 0:o.isTreeAnimationBlocked())||!1}startUpdate(){var o;this.isUpdateBlocked()||(this.isUpdating=!0,(o=this.nodes)===null||o===void 0||o.forEach(yl),this.animationId++)}getTransformTemplate(){var o;return(o=this.options.visualElement)===null||o===void 0?void 0:o.getProps().transformTemplate}willUpdate(o=!0){var a,c,l;if(this.root.isUpdateBlocked()){(c=(a=this.options).onExitComplete)===null||c===void 0||c.call(a);return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let f=0;f<this.path.length;f++){const h=this.path[f];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:u,layout:d}=this.options;u===void 0&&!d||(this.prevTransformTemplateValue=(l=this.getTransformTemplate())===null||l===void 0?void 0:l(this.latestValues,""),this.updateSnapshot(),o&&this.notifyListeners("willUpdate"))}didUpdate(){if(this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(_s);return}this.isUpdating&&(this.isUpdating=!1,this.potentialNodes.size&&(this.potentialNodes.forEach(Vl),this.potentialNodes.clear()),this.nodes.forEach(pl),this.nodes.forEach(ul),this.nodes.forEach(fl),this.clearAllSnapshots(),xe.update(),xe.preRender(),xe.render())}clearAllSnapshots(){this.nodes.forEach(hl),this.sharedNodes.forEach(vl)}scheduleUpdateProjection(){R.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){R.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){var o;if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=M(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox),(o=this.options.visualElement)===null||o===void 0||o.notify("LayoutMeasure",this.layout.layoutBox,a==null?void 0:a.layoutBox)}updateScroll(o="measure"){let a=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:s(this.instance),offset:n(this.instance)})}resetTransform(){var o;if(!i)return;const a=this.isLayoutDirty||this.shouldResetTransform,c=this.projectionDelta&&!xo(this.projectionDelta),l=(o=this.getTransformTemplate())===null||o===void 0?void 0:o(this.latestValues,""),u=l!==this.prevTransformTemplateValue;a&&(c||it(this.latestValues)||u)&&(i(this.instance,l),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let c=this.removeElementScroll(a);return o&&(c=this.removeTransform(c)),Pl(c),{animationId:this.root.animationId,measuredBox:a,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return M();const a=o.measureViewportBox(),{scroll:c}=this.root;return c&&(Z(a.x,c.offset.x),Z(a.y,c.offset.y)),a}removeElementScroll(o){const a=M();U(a,o);for(let c=0;c<this.path.length;c++){const l=this.path[c],{scroll:u,options:d}=l;if(l!==this.root&&u&&d.layoutScroll){if(u.isRoot){U(a,o);const{scroll:f}=this.root;f&&(Z(a.x,-f.offset.x),Z(a.y,-f.offset.y))}Z(a.x,u.offset.x),Z(a.y,u.offset.y)}}return a}applyTransform(o,a=!1){const c=M();U(c,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&dt(c,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),it(u.latestValues)&&dt(c,u.latestValues)}return it(this.latestValues)&&dt(c,this.latestValues),c}removeTransform(o){var a;const c=M();U(c,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!it(u.latestValues))continue;Ye(u.latestValues)&&u.updateSnapshot();const d=M(),f=u.measurePageBox();U(d,f),Is(c,u.latestValues,(a=u.snapshot)===null||a===void 0?void 0:a.layoutBox,d)}return it(this.latestValues)&&Is(c,this.latestValues),c}setTargetDelta(o){this.targetDelta=o,this.isProjectionDirty=!0,this.root.scheduleUpdateProjection()}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}resolveTargetDelta(){var o;const a=this.getLead();if(this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),!this.isProjectionDirty&&!this.attemptToResolveRelativeTarget)return;const{layout:c,layoutId:l}=this.options;if(!(!this.layout||!(c||l))){if(!this.targetDelta&&!this.relativeTarget){const u=this.getClosestProjectingParent();u&&u.layout?(this.relativeParent=u,this.relativeTarget=M(),this.relativeTargetOrigin=M(),wt(this.relativeTargetOrigin,this.layout.layoutBox,u.layout.layoutBox),U(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=M(),this.targetWithTransforms=M()),this.relativeTarget&&this.relativeTargetOrigin&&(!((o=this.relativeParent)===null||o===void 0)&&o.target)?hc(this.target,this.relativeTarget,this.relativeParent.target):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):U(this.target,this.layout.layoutBox),oo(this.target,this.targetDelta)):U(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const u=this.getClosestProjectingParent();u&&Boolean(u.resumingFrom)===Boolean(this.resumingFrom)&&!u.options.layoutScroll&&u.target?(this.relativeParent=u,this.relativeTarget=M(),this.relativeTargetOrigin=M(),wt(this.relativeTargetOrigin,this.target,u.target),U(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Ye(this.parent.latestValues)||io(this.parent.latestValues)))return(this.parent.relativeTarget||this.parent.targetDelta||this.parent.options.layoutRoot)&&this.parent.layout?this.parent:this.parent.getClosestProjectingParent()}calcProjection(){var o;const{isProjectionDirty:a,isTransformDirty:c}=this;this.isProjectionDirty=this.isTransformDirty=!1;const l=this.getLead(),u=Boolean(this.resumingFrom)||this!==l;let d=!0;if(a&&(d=!1),u&&c&&(d=!1),d)return;const{layout:f,layoutId:h}=this.options;if(this.isTreeAnimating=Boolean(((o=this.parent)===null||o===void 0?void 0:o.isTreeAnimating)||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(f||h))return;U(this.layoutCorrected,this.layout.layoutBox),Vc(this.layoutCorrected,this.treeScale,this.path,u);const{target:m}=l;if(!m)return;this.projectionDelta||(this.projectionDelta=At(),this.projectionDeltaWithTransform=At());const g=this.treeScale.x,b=this.treeScale.y,v=this.projectionTransform;St(this.projectionDelta,this.layoutCorrected,m,this.latestValues),this.projectionTransform=Fs(this.projectionDelta,this.treeScale),(this.projectionTransform!==v||this.treeScale.x!==g||this.treeScale.y!==b)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a,c,l;(c=(a=this.options).scheduleRender)===null||c===void 0||c.call(a),o&&((l=this.getStack())===null||l===void 0||l.scheduleRender()),this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,a=!1){var c,l;const u=this.snapshot,d=(u==null?void 0:u.latestValues)||{},f={...this.latestValues},h=At();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const m=M(),g=(u==null?void 0:u.source)!==((c=this.layout)===null||c===void 0?void 0:c.source),b=(((l=this.getStack())===null||l===void 0?void 0:l.members.length)||0)<=1,v=Boolean(g&&!b&&this.options.crossfade===!0&&!this.path.some(bl));this.animationProgress=0,this.mixTargetDelta=T=>{var x;const y=T/1e3;zs(h.x,o.x,y),zs(h.y,o.y,y),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&(!((x=this.relativeParent)===null||x===void 0)&&x.layout)&&(wt(m,this.layout.layoutBox,this.relativeParent.layout.layoutBox),xl(this.relativeTarget,this.relativeTargetOrigin,m,y)),g&&(this.animationValues=f,tl(f,d,this.latestValues,y,v,b)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=y},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){var a,c;this.notifyListeners("animationStart"),(a=this.currentAnimation)===null||a===void 0||a.stop(),this.resumingFrom&&((c=this.resumingFrom.currentAnimation)===null||c===void 0||c.stop()),this.pendingAnimation&&(W.update(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=R.update(()=>{Vt.hasAnimatedSinceResize=!0,this.currentAnimation=Jc(0,js,{...o,onUpdate:l=>{var u;this.mixTargetDelta(l),(u=o.onUpdate)===null||u===void 0||u.call(o,l)},onComplete:()=>{var l;(l=o.onComplete)===null||l===void 0||l.call(o),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){var o;this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0),(o=this.getStack())===null||o===void 0||o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){var o;this.currentAnimation&&((o=this.mixTargetDelta)===null||o===void 0||o.call(this,js),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:c,layout:l,latestValues:u}=o;if(!(!a||!c||!l)){if(this!==o&&this.layout&&l&&Vo(this.options.animationType,this.layout.layoutBox,l.layoutBox)){c=this.target||M();const d=B(this.layout.layoutBox.x);c.x.min=o.target.x.min,c.x.max=c.x.min+d;const f=B(this.layout.layoutBox.y);c.y.min=o.target.y.min,c.y.max=c.y.min+f}U(a,c),dt(a,u),St(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(o,a){var c,l,u;this.sharedNodes.has(o)||this.sharedNodes.set(o,new rl),this.sharedNodes.get(o).add(a),a.promote({transition:(c=a.options.initialPromotionConfig)===null||c===void 0?void 0:c.transition,preserveFollowOpacity:(u=(l=a.options.initialPromotionConfig)===null||l===void 0?void 0:l.shouldPreserveFollowOpacity)===null||u===void 0?void 0:u.call(l,a)})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:c}={}){const l=this.getStack();l&&l.promote(this,c),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:c}=o;if((c.rotate||c.rotateX||c.rotateY||c.rotateZ)&&(a=!0),!a)return;const l={};for(let u=0;u<ks.length;u++){const d="rotate"+ks[u];c[d]&&(l[d]=c[d],o.setStaticValue(d,0))}o==null||o.render();for(const u in l)o.setStaticValue(u,l[u]);o.scheduleRender()}getProjectionStyles(o={}){var a,c;const l={};if(!this.instance||this.isSVG)return l;if(this.isVisible)l.visibility="";else return{visibility:"hidden"};const u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,l.opacity="",l.pointerEvents=Wt(o.pointerEvents)||"",l.transform=u?u(this.latestValues,""):"none",l;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const g={};return this.options.layoutId&&(g.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,g.pointerEvents=Wt(o.pointerEvents)||""),this.hasProjected&&!it(this.latestValues)&&(g.transform=u?u({},""):"none",this.hasProjected=!1),g}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),l.transform=Fs(this.projectionDeltaWithTransform,this.treeScale,f),u&&(l.transform=u(f,l.transform));const{x:h,y:m}=this.projectionDelta;l.transformOrigin=`${h.origin*100}% ${m.origin*100}% 0`,d.animationValues?l.opacity=d===this?(c=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&c!==void 0?c:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:l.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const g in Xt){if(f[g]===void 0)continue;const{correct:b,applyTo:v}=Xt[g],T=l.transform==="none"?f[g]:b(f[g],d);if(v){const x=v.length;for(let y=0;y<x;y++)l[v[y]]=T}else l[g]=T}return this.options.layoutId&&(l.pointerEvents=d===this?Wt(o.pointerEvents)||"":"none"),l}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(_s),this.root.sharedNodes.clear()}}}function ul(t){t.updateLayout()}function fl(t){var e,n,s;const i=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=t.layout,{animationType:a}=t.options,c=i.source!==t.layout.source;a==="size"?N(h=>{const m=c?i.measuredBox[h]:i.layoutBox[h],g=B(m);m.min=r[h].min,m.max=m.min+g}):Vo(a,i.layoutBox,r)&&N(h=>{const m=c?i.measuredBox[h]:i.layoutBox[h],g=B(r[h]);m.max=m.min+g});const l=At();St(l,r,i.layoutBox);const u=At();c?St(u,t.applyTransform(o,!0),i.measuredBox):St(u,r,i.layoutBox);const d=!xo(l);let f=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:m,layout:g}=h;if(m&&g){const b=M();wt(b,i.layoutBox,m.layoutBox);const v=M();wt(v,r,g.layoutBox),bo(b,v)||(f=!0),h.options.layoutRoot&&(t.relativeTarget=v,t.relativeTargetOrigin=b,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:r,snapshot:i,delta:u,layoutDelta:l,hasLayoutChanged:d,hasRelativeTargetChanged:f})}else t.isLead()&&((s=(n=t.options).onExitComplete)===null||s===void 0||s.call(n));t.options.transition=void 0}function dl(t){t.isProjectionDirty||(t.isProjectionDirty=Boolean(t.parent&&t.parent.isProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=Boolean(t.parent&&t.parent.isTransformDirty))}function hl(t){t.clearSnapshot()}function _s(t){t.clearMeasurements()}function pl(t){const{visualElement:e}=t.options;e!=null&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Us(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0}function ml(t){t.resolveTargetDelta()}function gl(t){t.calcProjection()}function yl(t){t.resetRotation()}function vl(t){t.removeLeadSnapshot()}function zs(t,e,n){t.translate=w(e.translate,0,n),t.scale=w(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Ns(t,e,n,s){t.min=w(e.min,n.min,s),t.max=w(e.max,n.max,s)}function xl(t,e,n,s){Ns(t.x,e.x,n.x,s),Ns(t.y,e.y,n.y,s)}function bl(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Tl={duration:.45,ease:[.4,0,.1,1]};function Vl(t,e){let n=t.root;for(let r=t.path.length-1;r>=0;r--)if(Boolean(t.path[r].instance)){n=t.path[r];break}const i=(n&&n!==t.root?n.instance:document).querySelector(`[data-projection-id="${e}"]`);i&&t.mount(i,!0)}function $s(t){t.min=Math.round(t.min),t.max=Math.round(t.max)}function Pl(t){$s(t.x),$s(t.y)}function Vo(t,e,n){return t==="position"||t==="preserve-aspect"&&!Ke(Bs(e),Bs(n),.2)}const Cl=To({attachResizeListener:(t,e)=>ue(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rt={current:void 0},Fn=To({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rt.current){const t=new Cl(0,{});t.mount(window),t.setOptions({layoutScroll:!0}),rt.current=t}return rt.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>Boolean(window.getComputedStyle(t).position==="fixed")}),Po={...to,...Ti,...ao,...go},Co=ii((t,e)=>mn(t,e,Po,Bn,Fn));function Nu(t){return si(mn(t,{forwardMotionProps:!1},Po,Bn,Fn))}const $u=ii(mn);function So(){const t=p.useRef(!1);return Q(()=>(t.current=!0,()=>{t.current=!1}),[]),t}function kn(){const t=So(),[e,n]=p.useState(0),s=p.useCallback(()=>{t.current&&n(e+1)},[e]);return[p.useCallback(()=>R.postRender(s),[s]),e]}class Sl extends p.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const s=this.props.sizeRef.current;s.height=n.offsetHeight||0,s.width=n.offsetWidth||0,s.top=n.offsetTop,s.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function wl({children:t,isPresent:e}){const n=p.useId(),s=p.useRef(null),i=p.useRef({width:0,height:0,top:0,left:0});return p.useInsertionEffect(()=>{const{width:r,height:o,top:a,left:c}=i.current;if(e||!s.current||!r||!o)return;s.current.dataset.motionPopId=n;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${r}px !important;
            height: ${o}px !important;
            top: ${a}px !important;
            left: ${c}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[e]),p.createElement(Sl,{isPresent:e,childRef:s,sizeRef:i},p.cloneElement(t,{ref:s}))}const Re=({children:t,initial:e,isPresent:n,onExitComplete:s,custom:i,presenceAffectsLayout:r,mode:o})=>{const a=D(Al),c=p.useId(),l=p.useMemo(()=>({id:c,initial:e,isPresent:n,custom:i,onExitComplete:u=>{a.set(u,!0);for(const d of a.values())if(!d)return;s&&s()},register:u=>(a.set(u,!1),()=>a.delete(u))}),r?void 0:[n]);return p.useMemo(()=>{a.forEach((u,d)=>a.set(d,!1))},[n]),p.useEffect(()=>{!n&&!a.size&&s&&s()},[n]),o==="popLayout"&&(t=p.createElement(wl,{isPresent:n},t)),p.createElement(mt.Provider,{value:l},t)};function Al(){return new Map}const lt=t=>t.key||"";function Ml(t,e){t.forEach(n=>{const s=lt(n);e.set(s,n)})}function Rl(t){const e=[];return p.Children.forEach(t,n=>{p.isValidElement(n)&&e.push(n)}),e}const Wu=({children:t,custom:e,initial:n=!0,onExitComplete:s,exitBeforeEnter:i,presenceAffectsLayout:r=!0,mode:o="sync"})=>{i&&(o="wait");let[a]=kn();const c=p.useContext(Lt).forceRender;c&&(a=c);const l=So(),u=Rl(t);let d=u;const f=new Set,h=p.useRef(d),m=p.useRef(new Map).current,g=p.useRef(!0);if(Q(()=>{g.current=!1,Ml(u,m),h.current=d}),yn(()=>{g.current=!0,m.clear(),f.clear()}),g.current)return p.createElement(p.Fragment,null,d.map(x=>p.createElement(Re,{key:lt(x),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:r,mode:o},x)));d=[...d];const b=h.current.map(lt),v=u.map(lt),T=b.length;for(let x=0;x<T;x++){const y=b[x];v.indexOf(y)===-1&&f.add(y)}return o==="wait"&&f.size&&(d=[]),f.forEach(x=>{if(v.indexOf(x)!==-1)return;const y=m.get(x);if(!y)return;const P=b.indexOf(x),C=()=>{m.delete(x),f.delete(x);const L=h.current.findIndex(F=>F.key===x);if(h.current.splice(L,1),!f.size){if(h.current=u,l.current===!1)return;a(),s&&s()}};d.splice(P,0,p.createElement(Re,{key:lt(y),isPresent:!1,onExitComplete:C,custom:e,presenceAffectsLayout:r,mode:o},y))}),d=d.map(x=>{const y=x.key;return f.has(y)?x:p.createElement(Re,{key:lt(x),isPresent:!0,presenceAffectsLayout:r,mode:o},x)}),p.createElement(p.Fragment,null,f.size?d:d.map(x=>p.cloneElement(x)))},El=p.createContext(null),Ll=t=>!t.isLayoutDirty&&t.willUpdate(!1);function Ws(){const t=new Set,e=new WeakMap,n=()=>t.forEach(Ll);return{add:s=>{t.add(s),e.set(s,s.addEventListener("willUpdate",n))},remove:s=>{var i;t.delete(s),(i=e.get(s))===null||i===void 0||i(),e.delete(s),n()},dirty:n}}const wo=t=>t===!0,Dl=t=>wo(t===!0)||t==="id",Il=({children:t,id:e,inheritId:n,inherit:s=!0})=>{n!==void 0&&(s=n);const i=p.useContext(Lt),r=p.useContext(El),[o,a]=kn(),c=p.useRef(null),l=i.id||r;c.current===null&&(Dl(s)&&l&&(e=e?l+"-"+e:l),c.current={id:e,group:wo(s)&&i.group||Ws()});const u=p.useMemo(()=>({...c.current,forceRender:o}),[a]);return p.createElement(Lt.Provider,{value:u},t)};let Ol=0;const Gu=({children:t})=>(p.useEffect(()=>{},[]),p.createElement(Il,{id:D(()=>`asl-${Ol++}`)},t));function Hu({children:t,isValidProp:e,...n}){e&&li(e),n={...p.useContext(K),...n},n.isStatic=D(()=>n.isStatic);const s=p.useMemo(()=>n,[JSON.stringify(n.transition),n.transformPagePoint,n.reducedMotion]);return p.createElement(K.Provider,{value:s},t)}function Ku({children:t,features:e,strict:n=!1}){const[,s]=p.useState(!Ee(e)),i=p.useRef(void 0);if(!Ee(e)){const{renderer:r,...o}=e;i.current=r,De(o)}return p.useEffect(()=>{Ee(e)&&e().then(({renderer:r,...o})=>{De(o),i.current=r,s(!0)})},[]),p.createElement(sn.Provider,{value:{renderer:i.current,strict:n}},t)}function Ee(t){return typeof t=="function"}const Ao=p.createContext(null);function Bl(t,e,n,s){if(!s)return t;const i=t.findIndex(u=>u.value===e);if(i===-1)return t;const r=s>0?1:-1,o=t[i+r];if(!o)return t;const a=t[i],c=o.layout,l=w(c.min,c.max,.5);return r===1&&a.layout.max+n>l||r===-1&&a.layout.min+n<l?$r(t,i,i+r):t}function Fl({children:t,as:e="ul",axis:n="y",onReorder:s,values:i,...r},o){const a=D(()=>Co(e)),c=[],l=p.useRef(!1),u={axis:n,registerItem:(d,f)=>{f&&c.findIndex(h=>d===h.value)===-1&&(c.push({value:d,layout:f[n]}),c.sort(_l))},updateOrder:(d,f,h)=>{if(l.current)return;const m=Bl(c,d,f,h);c!==m&&(l.current=!0,s(m.map(jl).filter(g=>i.indexOf(g)!==-1)))}};return p.useEffect(()=>{l.current=!1}),p.createElement(a,{...r,ref:o},p.createElement(Ao.Provider,{value:u},t))}const kl=p.forwardRef(Fl);function jl(t){return t.value}function _l(t,e){return t.layout.min-e.layout.min}function at(t){const e=D(()=>z(t)),{isStatic:n}=p.useContext(K);if(n){const[,s]=p.useState(t);p.useEffect(()=>e.on("change",s),[])}return e}const Ul=t=>typeof t=="object"&&t.mix,zl=t=>Ul(t)?t.mix:void 0;function Nl(...t){const e=!Array.isArray(t[0]),n=e?0:-1,s=t[0+n],i=t[1+n],r=t[2+n],o=t[3+n],a=Mn(i,r,{mixer:zl(r[0]),...o});return e?a(s):a}function Mo(t,e){const n=at(e()),s=()=>n.set(e());return s(),Q(()=>{const i=()=>R.update(s,!1,!0),r=t.map(o=>o.on("change",i));return()=>{r.forEach(o=>o()),W.update(s)}}),n}function Qe(t,e,n,s){const i=typeof e=="function"?e:Nl(e,n,s);return Array.isArray(t)?Gs(t,i):Gs([t],([r])=>i(r))}function Gs(t,e){const n=D(()=>[]);return Mo(t,()=>{n.length=0;const s=t.length;for(let i=0;i<s;i++)n[i]=t[i].get();return e(n)})}function Hs(t,e=0){return E(t)?t:at(e)}function $l({children:t,style:e={},value:n,as:s="li",onDrag:i,layout:r=!0,...o},a){const c=D(()=>Co(s)),l=p.useContext(Ao),u={x:Hs(e.x),y:Hs(e.y)},d=Qe([u.x,u.y],([b,v])=>b||v?1:"unset"),f=p.useRef(null),{axis:h,registerItem:m,updateOrder:g}=l;return p.useEffect(()=>{m(n,f.current)},[l]),p.createElement(c,{drag:h,...o,dragSnapToOrigin:!0,style:{...e,x:u.x,y:u.y,zIndex:d},layout:r,onDrag:(b,v)=>{const{velocity:T}=v;T[h]&&g(n,u[h].get(),T[h]),i&&i(b,v)},onLayoutMeasure:b=>{f.current=b},ref:a},t)}const Wl=p.forwardRef($l),Xu={Group:kl,Item:Wl},Gl={renderer:Bn,...to,...Ti},Yu={...Gl,...ao,...go,projectionNodeConstructor:Fn};function qu(t,...e){const n=t.length;function s(){let i="";for(let r=0;r<n;r++){i+=t[r];const o=e[r];o&&(i+=E(o)?o.get():o)}return i}return Mo(e.filter(E),s)}function Zu(t,e={}){const{isStatic:n}=p.useContext(K),s=p.useRef(null),i=at(E(t)?t.get():t),r=()=>{s.current&&s.current.stop()};return p.useInsertionEffect(()=>i.attach((o,a)=>n?a(o):(r(),s.current=Ft({keyframes:[i.get(),o],velocity:i.getVelocity(),type:"spring",...e,onUpdate:a}),i.get()),r),[JSON.stringify(e)]),Q(()=>{if(E(t))return t.on("change",o=>i.set(parseFloat(o)))},[i]),i}function Ju(t){const e=at(t.getVelocity());return p.useEffect(()=>t.on("velocityChange",n=>{e.set(n)}),[t]),e}const Hl=(t,e,n)=>Math.min(Math.max(n,t),e),jn=t=>typeof t=="number",Kl=t=>Array.isArray(t)&&!jn(t[0]),Xl=(t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t};function Yl(t,e){return Kl(t)?t[Xl(0,t.length,e)]:t}const Ro=(t,e,n)=>-n*t+n*e+t,Eo=t=>t,_n=(t,e,n)=>e-t===0?1:(n-t)/(e-t);function Lo(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=_n(0,e,s);t.push(Ro(n,1,i))}}function Do(t){const e=[0];return Lo(e,t-1),e}function ql(t,e=Do(t.length),n=Eo){const s=t.length,i=s-e.length;return i>0&&Lo(e,i),r=>{let o=0;for(;o<s-2&&!(r<e[o+1]);o++);let a=Hl(0,1,_n(e[o],e[o+1],r));return a=Yl(n,o)(a),Ro(t[o],t[o+1],a)}}const Un=t=>typeof t=="function",Io=t=>typeof t=="string";function Zl(t,e){return e?t*(1e3/e):0}function Oo(t,e){var n;return typeof t=="string"?e?((n=e[t])!==null&&n!==void 0||(e[t]=document.querySelectorAll(t)),t=e[t]):t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}function Jl(t,e){var n={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(n[s]=t[s]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,s=Object.getOwnPropertySymbols(t);i<s.length;i++)e.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(t,s[i])&&(n[s[i]]=t[s[i]]);return n}const Ql={any:0,all:1};function tu(t,e,{root:n,margin:s,amount:i="any"}={}){if(typeof IntersectionObserver>"u")return()=>{};const r=Oo(t),o=new WeakMap,a=l=>{l.forEach(u=>{const d=o.get(u.target);if(u.isIntersecting!==Boolean(d))if(u.isIntersecting){const f=e(u);Un(f)?o.set(u.target,f):c.unobserve(u.target)}else d&&(d(u),o.delete(u.target))})},c=new IntersectionObserver(a,{root:n,rootMargin:s,threshold:typeof i=="number"?i:Ql[i]});return r.forEach(l=>c.observe(l)),()=>c.disconnect()}const Ht=new WeakMap;let q;function eu(t,e){if(e){const{inlineSize:n,blockSize:s}=e[0];return{width:n,height:s}}else return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}function nu({target:t,contentRect:e,borderBoxSize:n}){var s;(s=Ht.get(t))===null||s===void 0||s.forEach(i=>{i({target:t,contentSize:e,get size(){return eu(t,n)}})})}function su(t){t.forEach(nu)}function iu(){typeof ResizeObserver>"u"||(q=new ResizeObserver(su))}function ou(t,e){q||iu();const n=Oo(t);return n.forEach(s=>{let i=Ht.get(s);i||(i=new Set,Ht.set(s,i)),i.add(e),q==null||q.observe(s)}),()=>{n.forEach(s=>{const i=Ht.get(s);i==null||i.delete(e),i!=null&&i.size||q==null||q.unobserve(s)})}}const Kt=new Set;let Mt;function ru(){Mt=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Kt.forEach(n=>n(e))},window.addEventListener("resize",Mt)}function au(t){return Kt.add(t),Mt||ru(),()=>{Kt.delete(t),!Kt.size&&Mt&&(Mt=void 0)}}function cu(t,e){return Un(t)?au(t):ou(t,e)}const lu=50,Ks=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),uu=()=>({time:0,x:Ks(),y:Ks()}),fu={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Xs(t,e,n,s){const i=n[e],{length:r,position:o}=fu[e],a=i.current,c=n.time;i.current=t[`scroll${o}`],i.scrollLength=t[`scroll${r}`]-t[`client${r}`],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=_n(0,i.scrollLength,i.current);const l=s-c;i.velocity=l>lu?0:Zl(i.current-a,l)}function du(t,e,n){Xs(t,"x",e,n),Xs(t,"y",e,n),e.time=n}function hu(t,e){let n={x:0,y:0},s=t;for(;s&&s!==e;)if(s instanceof HTMLElement)n.x+=s.offsetLeft,n.y+=s.offsetTop,s=s.offsetParent;else if(s instanceof SVGGraphicsElement&&"getBBox"in s){const{top:i,left:r}=s.getBBox();for(n.x+=r,n.y+=i;s&&s.tagName!=="svg";)s=s.parentNode}return n}const pu={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},tn={start:0,center:.5,end:1};function Ys(t,e,n=0){let s=0;if(tn[t]!==void 0&&(t=tn[t]),Io(t)){const i=parseFloat(t);t.endsWith("px")?s=i:t.endsWith("%")?t=i/100:t.endsWith("vw")?s=i/100*document.documentElement.clientWidth:t.endsWith("vh")?s=i/100*document.documentElement.clientHeight:t=i}return jn(t)&&(s=e*t),n+s}const mu=[0,0];function gu(t,e,n,s){let i=Array.isArray(t)?t:mu,r=0,o=0;return jn(t)?i=[t,t]:Io(t)&&(t=t.trim(),t.includes(" ")?i=t.split(" "):i=[t,tn[t]?t:"0"]),r=Ys(i[0],n,s),o=Ys(i[1],e),r-o}const yu={x:0,y:0};function vu(t,e,n){let{offset:s=pu.All}=n;const{target:i=t,axis:r="y"}=n,o=r==="y"?"height":"width",a=i!==t?hu(i,t):yu,c=i===t?{width:t.scrollWidth,height:t.scrollHeight}:{width:i.clientWidth,height:i.clientHeight},l={width:t.clientWidth,height:t.clientHeight};e[r].offset.length=0;let u=!e[r].interpolate;const d=s.length;for(let f=0;f<d;f++){const h=gu(s[f],l[o],c[o],a[r]);!u&&h!==e[r].interpolatorOffsets[f]&&(u=!0),e[r].offset[f]=h}u&&(e[r].interpolate=ql(Do(d),e[r].offset),e[r].interpolatorOffsets=[...e[r].offset]),e[r].progress=e[r].interpolate(e[r].current)}function xu(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let s=e;for(;s&&s!=t;)n.x.targetOffset+=s.offsetLeft,n.y.targetOffset+=s.offsetTop,s=s.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}function bu(t,e,n,s={}){const i=s.axis||"y";return{measure:()=>xu(t,s.target,n),update:r=>{du(t,n,r),(s.offset||s.target)&&vu(t,n,s)},notify:Un(e)?()=>e(n):Tu(e,n[i])}}function Tu(t,e){return t.pause(),t.forEachNative((n,{easing:s})=>{var i,r;if(n.updateDuration)s||(n.easing=Eo),n.updateDuration(1);else{const o={duration:1e3};s||(o.easing="linear"),(r=(i=n.effect)===null||i===void 0?void 0:i.updateTiming)===null||r===void 0||r.call(i,o)}}),()=>{t.currentTime=e.progress}}const bt=new WeakMap,qs=new WeakMap,Le=new WeakMap,Zs=t=>t===document.documentElement?window:t;function Vu(t,e={}){var{container:n=document.documentElement}=e,s=Jl(e,["container"]);let i=Le.get(n);i||(i=new Set,Le.set(n,i));const r=uu(),o=bu(n,t,r,s);if(i.add(o),!bt.has(n)){const l=()=>{const d=performance.now();for(const f of i)f.measure();for(const f of i)f.update(d);for(const f of i)f.notify()};bt.set(n,l);const u=Zs(n);window.addEventListener("resize",l,{passive:!0}),n!==document.documentElement&&qs.set(n,cu(n,l)),u.addEventListener("scroll",l,{passive:!0})}const a=bt.get(n),c=requestAnimationFrame(a);return()=>{var l;typeof t!="function"&&t.stop(),cancelAnimationFrame(c);const u=Le.get(n);if(!u||(u.delete(o),u.size))return;const d=bt.get(n);bt.delete(n),d&&(Zs(n).removeEventListener("scroll",d),(l=qs.get(n))===null||l===void 0||l(),window.removeEventListener("resize",d))}}function Js(t,e){ji(Boolean(!e||e.current))}const Pu=()=>({scrollX:z(0),scrollY:z(0),scrollXProgress:z(0),scrollYProgress:z(0)});function Bo({container:t,target:e,layoutEffect:n=!0,...s}={}){const i=D(Pu);return(n?Q:p.useEffect)(()=>(Js("target",e),Js("container",t),Vu(({x:o,y:a})=>{i.scrollX.set(o.current),i.scrollXProgress.set(o.progress),i.scrollY.set(a.current),i.scrollYProgress.set(a.progress)},{...s,container:(t==null?void 0:t.current)||void 0,target:(e==null?void 0:e.current)||void 0})),[]),i}function Qu(t){return Bo({container:t})}function tf(){return Bo()}function Cu(t){const e=p.useRef(0),{isStatic:n}=p.useContext(K);p.useEffect(()=>{if(n)return;const s=({timestamp:i,delta:r})=>{e.current||(e.current=i),t(i-e.current,r)};return R.update(s,!0),()=>W.update(s)},[t])}function ef(){const t=at(0);return Cu(e=>t.set(e)),t}class Su extends Ai{constructor(){super(...arguments),this.members=[],this.transforms=new Set}add(e){let n;X.has(e)?(this.transforms.add(e),n="transform"):!e.startsWith("origin")&&!an(e)&&e!=="willChange"&&(n=It(e)),n&&(he(this.members,n),this.update())}remove(e){X.has(e)?(this.transforms.delete(e),this.transforms.size||Bt(this.members,"transform")):Bt(this.members,It(e)),this.update()}update(){this.set(this.members.length?this.members.join(", "):"auto")}}function nf(){return D(()=>new Su("auto"))}function sf(t,e,n){p.useInsertionEffect(()=>t.on(e,n),[t,e,n])}function wu(){!On.current&&uo();const[t]=p.useState(ie.current);return t}function of(){const t=wu(),{reducedMotion:e}=p.useContext(K);return e==="never"?!1:e==="always"?!0:t}function Au(){const t=new Set,e={subscribe(n){return t.add(n),()=>void t.delete(n)},start(n,s){const i=[];return t.forEach(r=>{i.push(Dn(r,n,{transitionOverride:s}))}),Promise.all(i)},set(n){return t.forEach(s=>{ra(s,n)})},stop(){t.forEach(n=>{ec(n)})},mount(){return()=>{e.stop()}}};return e}function Mu(){const t=D(Au);return Q(t.mount,[]),t}const rf=Mu,Ru=(t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t};function af(...t){const e=p.useRef(0),[n,s]=p.useState(t[e.current]),i=p.useCallback(r=>{e.current=typeof r!="number"?Ru(0,t.length,e.current+1):r,s(t[e.current])},[t.length,...t]);return[n,i]}function cf(t,{root:e,margin:n,amount:s,once:i=!1}={}){const[r,o]=p.useState(!1);return p.useEffect(()=>{if(!t.current||i&&r)return;const a=()=>(o(!0),i?void 0:()=>o(!1)),c={root:e&&e.current||void 0,margin:n,amount:s==="some"?"any":s};return tu(t.current,a,c)},[e,t,n,i]),r}class Eu{constructor(){this.componentControls=new Set}subscribe(e){return this.componentControls.add(e),()=>this.componentControls.delete(e)}start(e,n){this.componentControls.forEach(s=>{s.start(e.nativeEvent||e,n)})}}const Lu=()=>new Eu;function lf(){return D(Lu)}function Du(t){return t!==null&&typeof t=="object"&&on in t}function uf(t){if(Du(t))return t[on]}function Iu(){return Ou}function Ou(t){rt.current&&(rt.current.isUpdating=!1,rt.current.blockUpdate(),t&&t())}function ff(){const[t,e]=kn(),n=Iu();return p.useEffect(()=>{R.postRender(()=>R.postRender(()=>ze.current=!1))},[e]),s=>{n(()=>{ze.current=!0,t(),s()})}}function df(){return p.useCallback(()=>{const e=rt.current;e&&e.resetTree()},[])}const Fo=(t,e)=>`${t}: ${e}`,oe=new Map;function Bu(t,e,n,s){const i=Fo(t,X.has(e)?"transform":e),r=oe.get(i);if(!r)return 0;const{animation:o,startTime:a}=r,c=()=>{oe.delete(i);try{o.cancel()}catch{}};if(a!==null){const l=performance.now();return s.update(()=>{n.animation&&(n.animation.currentTime=performance.now()-l)}),s.render(c),l-a||0}else return c(),0}function hf(t,e,n,s,i){const r=t.dataset[ki];if(!r)return;window.HandoffAppearAnimations=Bu;const o=Fo(r,e),a=We(t,e,[n[0],n[0]],{duration:1e4,ease:"linear"});oe.set(o,{animation:a,startTime:null});const c=()=>{a.cancel();const l=We(t,e,n,s);document.timeline&&(l.startTime=document.timeline.currentTime),oe.set(o,{animation:l,startTime:performance.now()}),i&&i(l)};a.ready?a.ready.then(c).catch(me):c()}const en=()=>({});class Fu extends ho{build(){}measureInstanceViewportBox(){return M()}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return en()}getBaseTargetFromProps(){}readValueFromInstance(e,n,s){return s.initialState[n]||0}sortInstanceNodePosition(){return 0}makeTargetAnimatableFromInstance({transition:e,transitionEnd:n,...s}){const i=Fi(s,e||{},this);return Bi(this,s,i),{transition:e,transitionEnd:n,...s}}}const ku=pn({scrapeMotionValuesFromProps:en,createRenderState:en});function pf(t){const[e,n]=p.useState(t),s=ku({},!1),i=D(()=>new Fu({props:{},visualState:s},{initialState:t}));p.useEffect(()=>(i.mount({}),()=>i.unmount()),[i]),p.useEffect(()=>{i.setProps({onUpdate:o=>{n({...o})}})},[n,i]);const r=D(()=>o=>Dn(i,o));return[e,r]}const ju=1e5,Qs=t=>t>.001?1/t:ju;function mf(t){let e=at(1),n=at(1);const s=ti();t?(e=t.scaleX||e,n=t.scaleY||n):s&&(e=s.getValue("scaleX",1),n=s.getValue("scaleY",1));const i=Qe(e,Qs),r=Qe(n,Qs);return{scaleX:i,scaleY:r}}export{Wu as AnimatePresence,Gu as AnimateSharedLayout,S as AnimationType,El as DeprecatedLayoutGroupContext,Eu as DragControls,cl as FlatTree,Il as LayoutGroup,Lt as LayoutGroupContext,Ku as LazyMotion,Hu as MotionConfig,K as MotionConfigContext,re as MotionContext,Ai as MotionValue,mt as PresenceContext,Xu as Reorder,ni as SwitchLayoutGroupContext,ho as VisualElement,ht as addPointerEvent,gi as addPointerInfo,Xo as addScaleCorrector,Jc as animate,Dn as animateVisualElement,Au as animationControls,to as animations,Ta as anticipate,En as backIn,ba as backInOut,Hi as backOut,Zo as buildTransform,B as calcLength,Bi as checkTargetForNewValues,Gi as circIn,xa as circInOut,Rn as circOut,pt as clamp,M as createBox,Nu as createDomMotionComponent,si as createMotionComponent,Wi as cubicBezier,Zi as delay,is as distance,uc as distance2D,Gl as domAnimation,Yu as domMax,wn as easeIn,An as easeInOut,la as easeOut,ur as filterProps,H as frameData,ae as isBrowser,xi as isDragActive,Du as isMotionComponent,E as isMotionValue,qt as isValidMotionProp,$u as m,pn as makeUseVisualState,w as mix,Co as motion,z as motionValue,ca as optimizedAppearDataAttribute,fe as pipe,Wt as resolveMotionValue,Ki as spring,hf as startOptimizedAppearAnimation,R as sync,Nl as transform,uf as unwrapMotionComponent,rf as useAnimation,Mu as useAnimationControls,Cu as useAnimationFrame,af as useCycle,pf as useDeprecatedAnimatedState,mf as useDeprecatedInvertedScale,Oe as useDomEvent,lf as useDragControls,Qu as useElementScroll,kn as useForceUpdate,cf as useInView,Iu as useInstantLayoutTransition,ff as useInstantTransition,Uu as useIsPresent,Q as useIsomorphicLayoutEffect,qu as useMotionTemplate,at as useMotionValue,sf as useMotionValueEvent,Vi as usePresence,wu as useReducedMotion,of as useReducedMotionConfig,df as useResetProjection,Bo as useScroll,Zu as useSpring,ef as useTime,Qe as useTransform,yn as useUnmountEffect,Ju as useVelocity,tf as useViewportScroll,ti as useVisualElementContext,nf as useWillChange,Ru as wrap};
