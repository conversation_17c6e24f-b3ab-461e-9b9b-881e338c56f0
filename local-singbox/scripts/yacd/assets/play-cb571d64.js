import{r as g,R as s,p as a}from"./index-1a05af9b.js";function p(){return p=Object.assign||function(t){for(var o=1;o<arguments.length;o++){var r=arguments[o];for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t},p.apply(this,arguments)}function v(t,o){if(t==null)return{};var r=y(t,o),e,n;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)e=i[n],!(o.indexOf(e)>=0)&&Object.prototype.propertyIsEnumerable.call(t,e)&&(r[e]=t[e])}return r}function y(t,o){if(t==null)return{};var r={},e=Object.keys(t),n,i;for(i=0;i<e.length;i++)n=e[i],!(o.indexOf(n)>=0)&&(r[n]=t[n]);return r}var c=g.forwardRef(function(t,o){var r=t.color,e=r===void 0?"currentColor":r,n=t.size,i=n===void 0?24:n,l=v(t,["color","size"]);return s.createElement("svg",p({ref:o,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:e,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),s.createElement("rect",{x:"6",y:"4",width:"4",height:"16"}),s.createElement("rect",{x:"14",y:"4",width:"4",height:"16"}))});c.propTypes={color:a.string,size:a.oneOfType([a.string,a.number])};c.displayName="Pause";const b=c;function f(){return f=Object.assign||function(t){for(var o=1;o<arguments.length;o++){var r=arguments[o];for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t},f.apply(this,arguments)}function h(t,o){if(t==null)return{};var r=O(t,o),e,n;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)e=i[n],!(o.indexOf(e)>=0)&&Object.prototype.propertyIsEnumerable.call(t,e)&&(r[e]=t[e])}return r}function O(t,o){if(t==null)return{};var r={},e=Object.keys(t),n,i;for(i=0;i<e.length;i++)n=e[i],!(o.indexOf(n)>=0)&&(r[n]=t[n]);return r}var u=g.forwardRef(function(t,o){var r=t.color,e=r===void 0?"currentColor":r,n=t.size,i=n===void 0?24:n,l=h(t,["color","size"]);return s.createElement("svg",f({ref:o,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:e,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),s.createElement("polygon",{points:"5 3 19 12 5 21 5 3"}))});u.propTypes={color:a.string,size:a.oneOfType([a.string,a.number])};u.displayName="Play";const w=u;export{w as P,b as a};
