import{r as G,R as re,p as Ne,c as Ir,a as Iu,u as it,m as Oo,j as <PERSON>,M as Pu,b as j,B as Tt,d as Fi,e as <PERSON>,f as Ao,g as Wi,_ as Ru,h as ae,i as Du,k as Gi,l as Eu,S as Bu,n as Ou,o as Au,C as Tu,I as To,q as Mu}from"./index-1a05af9b.js";import{S as Nu}from"./Select-04258549.js";import{u as Fu}from"./useRemainingViewPortHeight-dbe2192e.js";import{C as Lu,B as ki}from"./BaseModal-f42f892a.js";import{r as $i,t as Wu,g as Gu,b as Wr,a as gr,c as Hi,d as mr,f as ku,e as $u}from"./index-84fa0cb3.js";import{I as kn}from"./Input-e46653b4.js";import{_ as Mt,m as ke}from"./memoize-one.esm-efa1e849.js";import{F as Mo,p as No,A as Br}from"./Fab-47e19297.js";import{P as Hu,a as zu}from"./play-cb571d64.js";function $n(){return $n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},$n.apply(this,arguments)}function ju(e,r){if(e==null)return{};var t=Vu(e,r),n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(t[n]=e[n])}return t}function Vu(e,r){if(e==null)return{};var t={},n=Object.keys(e),o,i;for(i=0;i<n.length;i++)o=n[i],!(r.indexOf(o)>=0)&&(t[o]=e[o]);return t}var na=G.forwardRef(function(e,r){var t=e.color,n=t===void 0?"currentColor":t,o=e.size,i=o===void 0?24:o,l=ju(e,["color","size"]);return re.createElement("svg",$n({ref:r,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),re.createElement("line",{x1:"3",y1:"12",x2:"21",y2:"12"}),re.createElement("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),re.createElement("line",{x1:"3",y1:"18",x2:"21",y2:"18"}))});na.propTypes={color:Ne.string,size:Ne.oneOfType([Ne.string,Ne.number])};na.displayName="Menu";const Uu=na;function Hn(){return Hn=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},Hn.apply(this,arguments)}function qu(e,r){if(e==null)return{};var t=_u(e,r),n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(t[n]=e[n])}return t}function _u(e,r){if(e==null)return{};var t={},n=Object.keys(e),o,i;for(i=0;i<n.length;i++)o=n[i],!(r.indexOf(o)>=0)&&(t[o]=e[o]);return t}var aa=G.forwardRef(function(e,r){var t=e.color,n=t===void 0?"currentColor":t,o=e.size,i=o===void 0?24:o,l=qu(e,["color","size"]);return re.createElement("svg",Hn({ref:r,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),re.createElement("polyline",{points:"1 4 1 10 7 10"}),re.createElement("polyline",{points:"23 20 23 14 17 14"}),re.createElement("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"}))});aa.propTypes={color:Ne.string,size:Ne.oneOfType([Ne.string,Ne.number])};aa.displayName="RefreshCcw";const Fo=aa;function zn(){return zn=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},zn.apply(this,arguments)}function Xu(e,r){if(e==null)return{};var t=Ku(e,r),n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(t[n]=e[n])}return t}function Ku(e,r){if(e==null)return{};var t={},n=Object.keys(e),o,i;for(i=0;i<n.length;i++)o=n[i],!(r.indexOf(o)>=0)&&(t[o]=e[o]);return t}var oa=G.forwardRef(function(e,r){var t=e.color,n=t===void 0?"currentColor":t,o=e.size,i=o===void 0?24:o,l=Xu(e,["color","size"]);return re.createElement("svg",zn({ref:r,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),re.createElement("circle",{cx:"12",cy:"12",r:"3"}),re.createElement("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"}))});oa.propTypes={color:Ne.string,size:Ne.oneOfType([Ne.string,Ne.number])};oa.displayName="Settings";const Lo=oa;function jn(){return jn=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},jn.apply(this,arguments)}function Yu(e,r){if(e==null)return{};var t=Ju(e,r),n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(t[n]=e[n])}return t}function Ju(e,r){if(e==null)return{};var t={},n=Object.keys(e),o,i;for(i=0;i<n.length;i++)o=n[i],!(r.indexOf(o)>=0)&&(t[o]=e[o]);return t}var ia=G.forwardRef(function(e,r){var t=e.color,n=t===void 0?"currentColor":t,o=e.size,i=o===void 0?24:o,l=Yu(e,["color","size"]);return re.createElement("svg",jn({ref:r,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),re.createElement("path",{d:"M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"}),re.createElement("line",{x1:"7",y1:"7",x2:"7.01",y2:"7"}))});ia.propTypes={color:Ne.string,size:Ne.oneOfType([Ne.string,Ne.number])};ia.displayName="Tag";const Wo=ia;function Vn(){return Vn=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},Vn.apply(this,arguments)}function Qu(e,r){if(e==null)return{};var t=Zu(e,r),n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(t[n]=e[n])}return t}function Zu(e,r){if(e==null)return{};var t={},n=Object.keys(e),o,i;for(i=0;i<n.length;i++)o=n[i],!(r.indexOf(o)>=0)&&(t[o]=e[o]);return t}var la=G.forwardRef(function(e,r){var t=e.color,n=t===void 0?"currentColor":t,o=e.size,i=o===void 0?24:o,l=Qu(e,["color","size"]);return re.createElement("svg",Vn({ref:r,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),re.createElement("circle",{cx:"12",cy:"12",r:"10"}),re.createElement("line",{x1:"15",y1:"9",x2:"9",y2:"15"}),re.createElement("line",{x1:"9",y1:"9",x2:"15",y2:"15"}))});la.propTypes={color:Ne.string,size:Ne.oneOfType([Ne.string,Ne.number])};la.displayName="XCircle";const ec=la;function sa(e){return r=>!!r.type&&r.type.tabsRole===e}const Ut=sa("Tab"),ua=sa("TabList"),ca=sa("TabPanel");function rc(e){return Ut(e)||ua(e)||ca(e)}function Un(e,r){return G.Children.map(e,t=>t===null?null:rc(t)?r(t):t.props&&t.props.children&&typeof t.props.children=="object"?G.cloneElement(t,{...t.props,children:Un(t.props.children,r)}):t)}function zi(e,r){return G.Children.forEach(e,t=>{t!==null&&(Ut(t)||ca(t)?r(t):t.props&&t.props.children&&typeof t.props.children=="object"&&(ua(t)&&r(t),zi(t.props.children,r)))})}function ji(e){let r=0;return zi(e,t=>{Ut(t)&&r++}),r}function Vi(e){return e&&"getAttribute"in e}function Go(e){return Vi(e)&&e.getAttribute("data-rttab")}function Or(e){return Vi(e)&&e.getAttribute("aria-disabled")==="true"}let Nt;function tc(e){const r=e||(typeof window<"u"?window:void 0);try{Nt=!!(typeof r<"u"&&r.document&&r.document.activeElement)}catch{Nt=!1}}const nc={className:"react-tabs",focus:!1},da=e=>{let r=G.useRef([]),t=G.useRef([]);const n=G.useRef();function o($,z){if($<0||$>=u())return;const{onSelect:ue,selectedIndex:Ie}=e;ue($,Ie,z)}function i($){const z=u();for(let ue=$+1;ue<z;ue++)if(!Or(m(ue)))return ue;for(let ue=0;ue<$;ue++)if(!Or(m(ue)))return ue;return $}function l($){let z=$;for(;z--;)if(!Or(m(z)))return z;for(z=u();z-- >$;)if(!Or(m(z)))return z;return $}function s(){const $=u();for(let z=0;z<$;z++)if(!Or(m(z)))return z;return null}function p(){let $=u();for(;$--;)if(!Or(m($)))return $;return null}function u(){const{children:$}=e;return ji($)}function m($){return r.current[`tabs-${$}`]}function g(){let $=0;const{children:z,disabledTabClassName:ue,focus:Ie,forceRenderTabPanel:Se,selectedIndex:we,selectedTabClassName:Ee,selectedTabPanelClassName:De,environment:Oe}=e;t.current=t.current||[];let Fe=t.current.length-u();const ur=G.useId();for(;Fe++<0;)t.current.push(`${ur}${t.current.length}`);return Un(z,Ve=>{let Ae=Ve;if(ua(Ve)){let We=0,cr=!1;Nt==null&&tc(Oe);const nr=Oe||(typeof window<"u"?window:void 0);Nt&&nr&&(cr=re.Children.toArray(Ve.props.children).filter(Ut).some((dr,Ke)=>nr.document.activeElement===m(Ke))),Ae=G.cloneElement(Ve,{children:Un(Ve.props.children,dr=>{const Ke=`tabs-${We}`,xe=we===We,Ue={tabRef:vr=>{r.current[Ke]=vr},id:t.current[We],selected:xe,focus:xe&&(Ie||cr)};return Ee&&(Ue.selectedClassName=Ee),ue&&(Ue.disabledClassName=ue),We++,G.cloneElement(dr,Ue)})})}else if(ca(Ve)){const We={id:t.current[$],selected:we===$};Se&&(We.forceRender=Se),De&&(We.selectedClassName=De),$++,Ae=G.cloneElement(Ve,We)}return Ae})}function f($){const{direction:z,disableUpDownKeys:ue,disableLeftRightKeys:Ie}=e;if(C($.target)){let{selectedIndex:Se}=e,we=!1,Ee=!1;($.code==="Space"||$.keyCode===32||$.code==="Enter"||$.keyCode===13)&&(we=!0,Ee=!1,b($)),!Ie&&($.keyCode===37||$.code==="ArrowLeft")||!ue&&($.keyCode===38||$.code==="ArrowUp")?(z==="rtl"?Se=i(Se):Se=l(Se),we=!0,Ee=!0):!Ie&&($.keyCode===39||$.code==="ArrowRight")||!ue&&($.keyCode===40||$.code==="ArrowDown")?(z==="rtl"?Se=l(Se):Se=i(Se),we=!0,Ee=!0):$.keyCode===35||$.code==="End"?(Se=p(),we=!0,Ee=!0):($.keyCode===36||$.code==="Home")&&(Se=s(),we=!0,Ee=!0),we&&$.preventDefault(),Ee&&o(Se,$)}}function b($){let z=$.target;do if(C(z)){if(Or(z))return;const ue=[].slice.call(z.parentNode.children).filter(Go).indexOf(z);o(ue,$);return}while((z=z.parentNode)!=null)}function C($){if(!Go($))return!1;let z=$.parentElement;do{if(z===n.current)return!0;if(z.getAttribute("data-rttabs"))break;z=z.parentElement}while(z);return!1}const{children:S,className:P,disabledTabClassName:D,domRef:B,focus:O,forceRenderTabPanel:A,onSelect:V,selectedIndex:q,selectedTabClassName:Y,selectedTabPanelClassName:se,environment:Q,disableUpDownKeys:be,disableLeftRightKeys:pe,...Le}=e;return re.createElement("div",Object.assign({},Le,{className:Ir(P),onClick:b,onKeyDown:f,ref:$=>{n.current=$,B&&B($)},"data-rttabs":!0}),g())};da.defaultProps=nc;da.propTypes={};const ac=0,Ot=1,oc={defaultFocus:!1,focusTabOnClick:!0,forceRenderTabPanel:!1,selectedIndex:null,defaultIndex:null,environment:null,disableUpDownKeys:!1,disableLeftRightKeys:!1},ic=e=>e.selectedIndex===null?Ot:ac,qt=e=>{const{children:r,defaultFocus:t,defaultIndex:n,focusTabOnClick:o,onSelect:i}=e,[l,s]=G.useState(t),[p]=G.useState(ic(e)),[u,m]=G.useState(p===Ot?n||0:null);if(G.useEffect(()=>{s(!1)},[]),p===Ot){const b=ji(r);G.useEffect(()=>{if(u!=null){const C=Math.max(0,b-1);m(Math.min(u,C))}},[b])}const g=(b,C,S)=>{typeof i=="function"&&i(b,C,S)===!1||(o&&s(!0),p===Ot&&m(b))};let f={...e};return f.focus=l,f.onSelect=g,u!=null&&(f.selectedIndex=u),delete f.defaultFocus,delete f.defaultIndex,delete f.focusTabOnClick,re.createElement(da,f,r)};qt.propTypes={};qt.defaultProps=oc;qt.tabsRole="Tabs";const lc={className:"react-tabs__tab-list"},_t=e=>{const{children:r,className:t,...n}=e;return re.createElement("ul",Object.assign({},n,{className:Ir(t),role:"tablist"}),r)};_t.tabsRole="TabList";_t.propTypes={};_t.defaultProps=lc;const xn="react-tabs__tab",sc={className:xn,disabledClassName:`${xn}--disabled`,focus:!1,id:null,selected:!1,selectedClassName:`${xn}--selected`},Zr=e=>{let r=G.useRef();const{children:t,className:n,disabled:o,disabledClassName:i,focus:l,id:s,selected:p,selectedClassName:u,tabIndex:m,tabRef:g,...f}=e;return G.useEffect(()=>{p&&l&&r.current.focus()},[p,l]),re.createElement("li",Object.assign({},f,{className:Ir(n,{[u]:p,[i]:o}),ref:b=>{r.current=b,g&&g(b)},role:"tab",id:`tab${s}`,"aria-selected":p?"true":"false","aria-disabled":o?"true":"false","aria-controls":`panel${s}`,tabIndex:m||(p?"0":null),"data-rttab":!0}),t)};Zr.propTypes={};Zr.tabsRole="Tab";Zr.defaultProps=sc;const ko="react-tabs__tab-panel",uc={className:ko,forceRender:!1,selectedClassName:`${ko}--selected`},et=e=>{const{children:r,className:t,forceRender:n,id:o,selected:i,selectedClassName:l,...s}=e;return re.createElement("div",Object.assign({},s,{className:Ir(t,{[l]:i}),role:"tabpanel",id:`panel${o}`,"aria-labelledby":`tab${o}`}),n||i?r:null)};et.tabsRole="TabPanel";et.propTypes={};et.defaultProps=uc;const cc="_placeHolder_1vhnb_1",dc="_connQty_1vhnb_16",fc="_header_1vhnb_28",pc="_inputWrapper_1vhnb_44",vc="_input_1vhnb_44",Lr={placeHolder:cc,connQty:dc,header:fc,inputWrapper:pc,input:vc};function gc(e){if(e===null||e===!0||e===!1)return NaN;var r=Number(e);return isNaN(r)?r:r<0?Math.ceil(r):Math.floor(r)}function $o(e,r){var t,n,o,i,l,s,p,u;$i(1,arguments);var m=Gu(),g=gc((t=(n=(o=(i=r==null?void 0:r.weekStartsOn)!==null&&i!==void 0?i:r==null||(l=r.locale)===null||l===void 0||(s=l.options)===null||s===void 0?void 0:s.weekStartsOn)!==null&&o!==void 0?o:m.weekStartsOn)!==null&&n!==void 0?n:(p=m.locale)===null||p===void 0||(u=p.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&t!==void 0?t:0);if(!(g>=0&&g<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var f=Wu(e),b=f.getUTCDay(),C=(b<g?7:0)+b-g;return f.setUTCDate(f.getUTCDate()-C),f.setUTCHours(0,0,0,0),f}function mc(e,r,t){$i(2,arguments);var n=$o(e,t),o=$o(r,t);return n.getTime()===o.getTime()}var hc={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}},bc=function(r,t,n){var o,i=hc[r];return typeof i=="string"?o=i:t===1?o=i.one:o=i.other.replace("{{count}}",String(t)),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?o+"内":o+"前":o};const yc=bc;var wc={full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},Cc={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},Sc={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},xc={date:Wr({formats:wc,defaultWidth:"full"}),time:Wr({formats:Cc,defaultWidth:"full"}),dateTime:Wr({formats:Sc,defaultWidth:"full"})};const Ic=xc;function Ho(e,r,t){var n="eeee p";return mc(e,r,t)?n:e.getTime()>r.getTime()?"'下个'"+n:"'上个'"+n}var Pc={lastWeek:Ho,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:Ho,other:"PP p"},Rc=function(r,t,n,o){var i=Pc[r];return typeof i=="function"?i(t,n,o):i};const Dc=Rc;var Ec={narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},Bc={narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},Oc={narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},Ac={narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},Tc={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},Mc={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},Nc=function(r,t){var n=Number(r);switch(t==null?void 0:t.unit){case"date":return n.toString()+"日";case"hour":return n.toString()+"时";case"minute":return n.toString()+"分";case"second":return n.toString()+"秒";default:return"第 "+n.toString()}},Fc={ordinalNumber:Nc,era:gr({values:Ec,defaultWidth:"wide"}),quarter:gr({values:Bc,defaultWidth:"wide",argumentCallback:function(r){return r-1}}),month:gr({values:Oc,defaultWidth:"wide"}),day:gr({values:Ac,defaultWidth:"wide"}),dayPeriod:gr({values:Tc,defaultWidth:"wide",formattingValues:Mc,defaultFormattingWidth:"wide"})};const Lc=Fc;var Wc=/^(第\s*)?\d+(日|时|分|秒)?/i,Gc=/\d+/i,kc={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},$c={any:[/^(前)/i,/^(公元)/i]},Hc={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},zc={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},jc={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},Vc={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},Uc={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},qc={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},_c={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},Xc={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},Kc={ordinalNumber:Hi({matchPattern:Wc,parsePattern:Gc,valueCallback:function(r){return parseInt(r,10)}}),era:mr({matchPatterns:kc,defaultMatchWidth:"wide",parsePatterns:$c,defaultParseWidth:"any"}),quarter:mr({matchPatterns:Hc,defaultMatchWidth:"wide",parsePatterns:zc,defaultParseWidth:"any",valueCallback:function(r){return r+1}}),month:mr({matchPatterns:jc,defaultMatchWidth:"wide",parsePatterns:Vc,defaultParseWidth:"any"}),day:mr({matchPatterns:Uc,defaultMatchWidth:"wide",parsePatterns:qc,defaultParseWidth:"any"}),dayPeriod:mr({matchPatterns:_c,defaultMatchWidth:"any",parsePatterns:Xc,defaultParseWidth:"any"})};const Yc=Kc;var Jc={code:"zh-CN",formatDistance:yc,formatLong:Ic,formatRelative:Dc,localize:Lc,match:Yc,options:{weekStartsOn:1,firstWeekContainsDate:4}};const Qc=Jc;var Zc={lessThanXSeconds:{one:"少於 1 秒",other:"少於 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分鐘",lessThanXMinutes:{one:"少於 1 分鐘",other:"少於 {{count}} 分鐘"},xMinutes:{one:"1 分鐘",other:"{{count}} 分鐘"},xHours:{one:"1 小時",other:"{{count}} 小時"},aboutXHours:{one:"大約 1 小時",other:"大約 {{count}} 小時"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大約 1 個星期",other:"大約 {{count}} 個星期"},xWeeks:{one:"1 個星期",other:"{{count}} 個星期"},aboutXMonths:{one:"大約 1 個月",other:"大約 {{count}} 個月"},xMonths:{one:"1 個月",other:"{{count}} 個月"},aboutXYears:{one:"大約 1 年",other:"大約 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超過 1 年",other:"超過 {{count}} 年"},almostXYears:{one:"將近 1 年",other:"將近 {{count}} 年"}},ed=function(r,t,n){var o,i=Zc[r];return typeof i=="string"?o=i:t===1?o=i.one:o=i.other.replace("{{count}}",String(t)),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?o+"內":o+"前":o};const rd=ed;var td={full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},nd={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},ad={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},od={date:Wr({formats:td,defaultWidth:"full"}),time:Wr({formats:nd,defaultWidth:"full"}),dateTime:Wr({formats:ad,defaultWidth:"full"})};const id=od;var ld={lastWeek:"'上個'eeee p",yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:"'下個'eeee p",other:"P"},sd=function(r,t,n,o){return ld[r]};const ud=sd;var cd={narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},dd={narrow:["1","2","3","4"],abbreviated:["第一刻","第二刻","第三刻","第四刻"],wide:["第一刻鐘","第二刻鐘","第三刻鐘","第四刻鐘"]},fd={narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},pd={narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["週日","週一","週二","週三","週四","週五","週六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},vd={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜間"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜間"}},gd={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜間"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜間"}},md=function(r,t){var n=Number(r);switch(t==null?void 0:t.unit){case"date":return n+"日";case"hour":return n+"時";case"minute":return n+"分";case"second":return n+"秒";default:return"第 "+n}},hd={ordinalNumber:md,era:gr({values:cd,defaultWidth:"wide"}),quarter:gr({values:dd,defaultWidth:"wide",argumentCallback:function(r){return r-1}}),month:gr({values:fd,defaultWidth:"wide"}),day:gr({values:pd,defaultWidth:"wide"}),dayPeriod:gr({values:vd,defaultWidth:"wide",formattingValues:gd,defaultFormattingWidth:"wide"})};const bd=hd;var yd=/^(第\s*)?\d+(日|時|分|秒)?/i,wd=/\d+/i,Cd={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},Sd={any:[/^(前)/i,/^(公元)/i]},xd={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻鐘/i},Id={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},Pd={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},Rd={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},Dd={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^週[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},Ed={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},Bd={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨)/i},Od={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},Ad={ordinalNumber:Hi({matchPattern:yd,parsePattern:wd,valueCallback:function(r){return parseInt(r,10)}}),era:mr({matchPatterns:Cd,defaultMatchWidth:"wide",parsePatterns:Sd,defaultParseWidth:"any"}),quarter:mr({matchPatterns:xd,defaultMatchWidth:"wide",parsePatterns:Id,defaultParseWidth:"any",valueCallback:function(r){return r+1}}),month:mr({matchPatterns:Pd,defaultMatchWidth:"wide",parsePatterns:Rd,defaultParseWidth:"any"}),day:mr({matchPatterns:Dd,defaultMatchWidth:"wide",parsePatterns:Ed,defaultParseWidth:"any"}),dayPeriod:mr({matchPatterns:Bd,defaultMatchWidth:"any",parsePatterns:Od,defaultParseWidth:"any"})};const Td=Ad;var Md={code:"zh-TW",formatDistance:rd,formatLong:id,formatRelative:ud,localize:bd,match:Td,options:{weekStartsOn:1,firstWeekContainsDate:4}};const Nd=Md;var Ft={},Fd={get exports(){return Ft},set exports(e){Ft=e}},Lt={},Ld={get exports(){return Lt},set exports(e){Lt=e}};(function(e,r){(function(t,n){n(r,G)})(Iu,function(t,n){function o(a,c,d,v,y,h,w){try{var x=a[h](w),I=x.value}catch(R){return void d(R)}x.done?c(I):Promise.resolve(I).then(v,y)}function i(a){return function(){var c=this,d=arguments;return new Promise(function(v,y){var h=a.apply(c,d);function w(I){o(h,v,y,w,x,"next",I)}function x(I){o(h,v,y,w,x,"throw",I)}w(void 0)})}}function l(){return(l=Object.assign||function(a){for(var c=1;c<arguments.length;c++){var d=arguments[c];for(var v in d)Object.prototype.hasOwnProperty.call(d,v)&&(a[v]=d[v])}return a}).apply(this,arguments)}function s(a,c){if(a==null)return{};var d,v,y={},h=Object.keys(a);for(v=0;v<h.length;v++)d=h[v],c.indexOf(d)>=0||(y[d]=a[d]);return y}function p(a){var c=function(d,v){if(typeof d!="object"||d===null)return d;var y=d[Symbol.toPrimitive];if(y!==void 0){var h=y.call(d,v||"default");if(typeof h!="object")return h;throw new TypeError("@@toPrimitive must return a primitive value.")}return(v==="string"?String:Number)(d)}(a,"string");return typeof c=="symbol"?c:String(c)}n=n&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n;var u={init:"init"},m=function(a){var c=a.value;return c===void 0?"":c},g=function(){return n.createElement(n.Fragment,null," ")},f={Cell:m,width:150,minWidth:0,maxWidth:Number.MAX_SAFE_INTEGER};function b(){for(var a=arguments.length,c=new Array(a),d=0;d<a;d++)c[d]=arguments[d];return c.reduce(function(v,y){var h=y.style,w=y.className;return v=l({},v,{},s(y,["style","className"])),h&&(v.style=v.style?l({},v.style||{},{},h||{}):h),w&&(v.className=v.className?v.className+" "+w:w),v.className===""&&delete v.className,v},{})}var C=function(a,c){return c===void 0&&(c={}),function(d){return d===void 0&&(d={}),[].concat(a,[d]).reduce(function(v,y){return function h(w,x,I){return typeof x=="function"?h({},x(w,I)):Array.isArray(x)?b.apply(void 0,[w].concat(x)):b(w,x)}(v,y,l({},c,{userProps:d}))},{})}},S=function(a,c,d,v){return d===void 0&&(d={}),a.reduce(function(y,h){return h(y,d)},c)},P=function(a,c,d){return d===void 0&&(d={}),a.forEach(function(v){v(c,d)})};function D(a,c,d,v){a.findIndex(function(y){return y.pluginName===d}),c.forEach(function(y){a.findIndex(function(h){return h.pluginName===y})})}function B(a,c){return typeof a=="function"?a(c):a}function O(a){var c=n.useRef();return c.current=a,n.useCallback(function(){return c.current},[])}var A=typeof document<"u"?n.useLayoutEffect:n.useEffect;function V(a,c){var d=n.useRef(!1);A(function(){d.current&&a(),d.current=!0},c)}function q(a,c,d){return d===void 0&&(d={}),function(v,y){y===void 0&&(y={});var h=typeof v=="string"?c[v]:v;if(h===void 0)throw console.info(c),new Error("Renderer Error ☝️");return Y(h,l({},a,{column:c},d,{},y))}}function Y(a,c){return function(v){return typeof v=="function"&&(y=Object.getPrototypeOf(v)).prototype&&y.prototype.isReactComponent;var y}(d=a)||typeof d=="function"||function(v){return typeof v=="object"&&typeof v.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(v.$$typeof.description)}(d)?n.createElement(a,c):a;var d}function se(a,c,d){return d===void 0&&(d=0),a.map(function(v){return be(v=l({},v,{parent:c,depth:d})),v.columns&&(v.columns=se(v.columns,v,d+1)),v})}function Q(a){return Ie(a,"columns")}function be(a){var c=a.id,d=a.accessor,v=a.Header;if(typeof d=="string"){c=c||d;var y=d.split(".");d=function(h){return function(w,x,I){if(!x)return w;var R,F=typeof x=="function"?x:JSON.stringify(x),T=$.get(F)||function(){var M=function(E){return function N(k,H){if(H===void 0&&(H=[]),Array.isArray(k))for(var X=0;X<k.length;X+=1)N(k[X],H);else H.push(k);return H}(E).map(function(N){return String(N).replace(".","_")}).join(".").replace(Fe,".").replace(ur,"").split(".")}(x);return $.set(F,M),M}();try{R=T.reduce(function(M,E){return M[E]},w)}catch{}return R!==void 0?R:I}(h,y)}}if(!c&&typeof v=="string"&&v&&(c=v),!c&&a.columns)throw console.error(a),new Error('A column ID (or unique "Header" value) is required!');if(!c)throw console.error(a),new Error("A column ID (or string accessor) is required!");return Object.assign(a,{id:c,accessor:d}),a}function pe(a,c){if(!c)throw new Error;return Object.assign(a,l({Header:g,Footer:g},f,{},c,{},a)),Object.assign(a,{originalWidth:a.width}),a}function Le(a,c,d){d===void 0&&(d=function(){return{}});for(var v=[],y=a,h=0,w=function(){return h++},x=function(){var I={headers:[]},R=[],F=y.some(function(T){return T.parent});y.forEach(function(T){var M,E=[].concat(R).reverse()[0];F&&(T.parent?M=l({},T.parent,{originalId:T.parent.id,id:T.parent.id+"_"+w(),headers:[T]},d(T)):M=pe(l({originalId:T.id+"_placeholder",id:T.id+"_placeholder_"+w(),placeholderOf:T,headers:[T]},d(T)),c),E&&E.originalId===M.originalId?E.headers.push(T):R.push(M)),I.headers.push(T)}),v.push(I),y=R};y.length;)x();return v.reverse()}var $=new Map;function z(){for(var a=arguments.length,c=new Array(a),d=0;d<a;d++)c[d]=arguments[d];for(var v=0;v<c.length;v+=1)if(c[v]!==void 0)return c[v]}function ue(a){if(typeof a=="function")return a}function Ie(a,c){var d=[];return function v(y){y.forEach(function(h){h[c]?v(h[c]):d.push(h)})}(a),d}function Se(a,c){var d=c.manualExpandedKey,v=c.expanded,y=c.expandSubRows,h=y===void 0||y,w=[];return a.forEach(function(x){return function I(R,F){F===void 0&&(F=!0),R.isExpanded=R.original&&R.original[d]||v[R.id],R.canExpand=R.subRows&&!!R.subRows.length,F&&w.push(R),R.subRows&&R.subRows.length&&R.isExpanded&&R.subRows.forEach(function(T){return I(T,h)})}(x)}),w}function we(a,c,d){return ue(a)||c[a]||d[a]||d.text}function Ee(a,c,d){return a?a(c,d):c===void 0}function De(){throw new Error("React-Table: You have not called prepareRow(row) one or more rows you are attempting to render.")}var Oe=null,Fe=/\[/g,ur=/\]/g,Ve=function(a){return l({role:"table"},a)},Ae=function(a){return l({role:"rowgroup"},a)},We=function(a,c){var d=c.column;return l({key:"header_"+d.id,colSpan:d.totalVisibleHeaderCount,role:"columnheader"},a)},cr=function(a,c){var d=c.column;return l({key:"footer_"+d.id,colSpan:d.totalVisibleHeaderCount},a)},nr=function(a,c){return l({key:"headerGroup_"+c.index,role:"row"},a)},dr=function(a,c){return l({key:"footerGroup_"+c.index},a)},Ke=function(a,c){return l({key:"row_"+c.row.id,role:"row"},a)},xe=function(a,c){var d=c.cell;return l({key:"cell_"+d.row.id+"_"+d.column.id,role:"cell"},a)};function Ue(){return{useOptions:[],stateReducers:[],useControlledState:[],columns:[],columnsDeps:[],allColumns:[],allColumnsDeps:[],accessValue:[],materializedColumns:[],materializedColumnsDeps:[],useInstanceAfterData:[],visibleColumns:[],visibleColumnsDeps:[],headerGroups:[],headerGroupsDeps:[],useInstanceBeforeDimensions:[],useInstance:[],prepareRow:[],getTableProps:[Ve],getTableBodyProps:[Ae],getHeaderGroupProps:[nr],getFooterGroupProps:[dr],getHeaderProps:[We],getFooterProps:[cr],getRowProps:[Ke],getCellProps:[xe],useFinalInstance:[]}}u.resetHiddenColumns="resetHiddenColumns",u.toggleHideColumn="toggleHideColumn",u.setHiddenColumns="setHiddenColumns",u.toggleHideAllColumns="toggleHideAllColumns";var vr=function(a){a.getToggleHiddenProps=[Je],a.getToggleHideAllColumnsProps=[Dr],a.stateReducers.push(ar),a.useInstanceBeforeDimensions.push(Nr),a.headerGroupsDeps.push(function(c,d){var v=d.instance;return[].concat(c,[v.state.hiddenColumns])}),a.useInstance.push(Vr)};vr.pluginName="useColumnVisibility";var Je=function(a,c){var d=c.column;return[a,{onChange:function(v){d.toggleHidden(!v.target.checked)},style:{cursor:"pointer"},checked:d.isVisible,title:"Toggle Column Visible"}]},Dr=function(a,c){var d=c.instance;return[a,{onChange:function(v){d.toggleHideAllColumns(!v.target.checked)},style:{cursor:"pointer"},checked:!d.allColumnsHidden&&!d.state.hiddenColumns.length,title:"Toggle All Columns Hidden",indeterminate:!d.allColumnsHidden&&d.state.hiddenColumns.length}]};function ar(a,c,d,v){if(c.type===u.init)return l({hiddenColumns:[]},a);if(c.type===u.resetHiddenColumns)return l({},a,{hiddenColumns:v.initialState.hiddenColumns||[]});if(c.type===u.toggleHideColumn){var y=(c.value!==void 0?c.value:!a.hiddenColumns.includes(c.columnId))?[].concat(a.hiddenColumns,[c.columnId]):a.hiddenColumns.filter(function(h){return h!==c.columnId});return l({},a,{hiddenColumns:y})}return c.type===u.setHiddenColumns?l({},a,{hiddenColumns:B(c.value,a.hiddenColumns)}):c.type===u.toggleHideAllColumns?l({},a,{hiddenColumns:(c.value!==void 0?c.value:!a.hiddenColumns.length)?v.allColumns.map(function(h){return h.id}):[]}):void 0}function Nr(a){var c=a.headers,d=a.state.hiddenColumns;n.useRef(!1).current;var v=0;c.forEach(function(y){return v+=function h(w,x){w.isVisible=x&&!d.includes(w.id);var I=0;return w.headers&&w.headers.length?w.headers.forEach(function(R){return I+=h(R,w.isVisible)}):I=w.isVisible?1:0,w.totalVisibleHeaderCount=I,I}(y,!0)})}function Vr(a){var c=a.columns,d=a.flatHeaders,v=a.dispatch,y=a.allColumns,h=a.getHooks,w=a.state.hiddenColumns,x=a.autoResetHiddenColumns,I=x===void 0||x,R=O(a),F=y.length===w.length,T=n.useCallback(function(H,X){return v({type:u.toggleHideColumn,columnId:H,value:X})},[v]),M=n.useCallback(function(H){return v({type:u.setHiddenColumns,value:H})},[v]),E=n.useCallback(function(H){return v({type:u.toggleHideAllColumns,value:H})},[v]),N=C(h().getToggleHideAllColumnsProps,{instance:R()});d.forEach(function(H){H.toggleHidden=function(X){v({type:u.toggleHideColumn,columnId:H.id,value:X})},H.getToggleHiddenProps=C(h().getToggleHiddenProps,{instance:R(),column:H})});var k=O(I);V(function(){k()&&v({type:u.resetHiddenColumns})},[v,c]),Object.assign(a,{allColumnsHidden:F,toggleHideColumn:T,setHiddenColumns:M,toggleHideAllColumns:E,getToggleHideAllColumnsProps:N})}var Er={},Ur={},ss=function(a,c,d){return a},us=function(a,c){return a.subRows||[]},cs=function(a,c,d){return""+(d?[d.id,c].join("."):c)},ds=function(a){return a};function Ha(a){var c=a.initialState,d=c===void 0?Er:c,v=a.defaultColumn,y=v===void 0?Ur:v,h=a.getSubRows,w=h===void 0?us:h,x=a.getRowId,I=x===void 0?cs:x,R=a.stateReducer,F=R===void 0?ss:R,T=a.useControlledState,M=T===void 0?ds:T;return l({},s(a,["initialState","defaultColumn","getSubRows","getRowId","stateReducer","useControlledState"]),{initialState:d,defaultColumn:y,getSubRows:w,getRowId:I,stateReducer:F,useControlledState:M})}function za(a,c){c===void 0&&(c=0);var d=0,v=0,y=0,h=0;return a.forEach(function(w){var x=w.headers;if(w.totalLeft=c,x&&x.length){var I=za(x,c),R=I[0],F=I[1],T=I[2],M=I[3];w.totalMinWidth=R,w.totalWidth=F,w.totalMaxWidth=T,w.totalFlexWidth=M}else w.totalMinWidth=w.minWidth,w.totalWidth=Math.min(Math.max(w.minWidth,w.width),w.maxWidth),w.totalMaxWidth=w.maxWidth,w.totalFlexWidth=w.canResize?w.totalWidth:0;w.isVisible&&(c+=w.totalWidth,d+=w.totalMinWidth,v+=w.totalWidth,y+=w.totalMaxWidth,h+=w.totalFlexWidth)}),[d,v,y,h]}function fs(a){var c=a.data,d=a.rows,v=a.flatRows,y=a.rowsById,h=a.column,w=a.getRowId,x=a.getSubRows,I=a.accessValueHooks,R=a.getInstance;c.forEach(function(F,T){return function M(E,N,k,H,X){k===void 0&&(k=0);var le=E,_=w(E,N,H),L=y[_];if(L)L.subRows&&L.originalSubRows.forEach(function(U,ne){return M(U,ne,k+1,L)});else if((L={id:_,original:le,index:N,depth:k,cells:[{}]}).cells.map=De,L.cells.filter=De,L.cells.forEach=De,L.cells[0].getCellProps=De,L.values={},X.push(L),v.push(L),y[_]=L,L.originalSubRows=x(E,N),L.originalSubRows){var ie=[];L.originalSubRows.forEach(function(U,ne){return M(U,ne,k+1,L,ie)}),L.subRows=ie}h.accessor&&(L.values[h.id]=h.accessor(E,N,L,X,c)),L.values[h.id]=S(I,L.values[h.id],{row:L,column:h,instance:R()})}(F,T,0,void 0,d)})}u.resetExpanded="resetExpanded",u.toggleRowExpanded="toggleRowExpanded",u.toggleAllRowsExpanded="toggleAllRowsExpanded";var ja=function(a){a.getToggleAllRowsExpandedProps=[ps],a.getToggleRowExpandedProps=[vs],a.stateReducers.push(gs),a.useInstance.push(ms),a.prepareRow.push(hs)};ja.pluginName="useExpanded";var ps=function(a,c){var d=c.instance;return[a,{onClick:function(v){d.toggleAllRowsExpanded()},style:{cursor:"pointer"},title:"Toggle All Rows Expanded"}]},vs=function(a,c){var d=c.row;return[a,{onClick:function(){d.toggleRowExpanded()},style:{cursor:"pointer"},title:"Toggle Row Expanded"}]};function gs(a,c,d,v){if(c.type===u.init)return l({expanded:{}},a);if(c.type===u.resetExpanded)return l({},a,{expanded:v.initialState.expanded||{}});if(c.type===u.toggleAllRowsExpanded){var y=c.value,h=v.rowsById,w=Object.keys(h).length===Object.keys(a.expanded).length;if(y!==void 0?y:!w){var x={};return Object.keys(h).forEach(function(N){x[N]=!0}),l({},a,{expanded:x})}return l({},a,{expanded:{}})}if(c.type===u.toggleRowExpanded){var I,R=c.id,F=c.value,T=a.expanded[R],M=F!==void 0?F:!T;if(!T&&M)return l({},a,{expanded:l({},a.expanded,(I={},I[R]=!0,I))});if(T&&!M){var E=a.expanded;return E[R],l({},a,{expanded:s(E,[R].map(p))})}return a}}function ms(a){var c=a.data,d=a.rows,v=a.rowsById,y=a.manualExpandedKey,h=y===void 0?"expanded":y,w=a.paginateExpandedRows,x=w===void 0||w,I=a.expandSubRows,R=I===void 0||I,F=a.autoResetExpanded,T=F===void 0||F,M=a.getHooks,E=a.plugins,N=a.state.expanded,k=a.dispatch;D(E,["useSortBy","useGroupBy","usePivotColumns","useGlobalFilter"],"useExpanded");var H=O(T),X=Boolean(Object.keys(v).length&&Object.keys(N).length);X&&Object.keys(v).some(function(ce){return!N[ce]})&&(X=!1),V(function(){H()&&k({type:u.resetExpanded})},[k,c]);var le=n.useCallback(function(ce,Z){k({type:u.toggleRowExpanded,id:ce,value:Z})},[k]),_=n.useCallback(function(ce){return k({type:u.toggleAllRowsExpanded,value:ce})},[k]),L=n.useMemo(function(){return x?Se(d,{manualExpandedKey:h,expanded:N,expandSubRows:R}):d},[x,d,h,N,R]),ie=n.useMemo(function(){return function(ce){var Z=0;return Object.keys(ce).forEach(function(te){var ge=te.split(".");Z=Math.max(Z,ge.length)}),Z}(N)},[N]),U=O(a),ne=C(M().getToggleAllRowsExpandedProps,{instance:U()});Object.assign(a,{preExpandedRows:d,expandedRows:L,rows:L,expandedDepth:ie,isAllRowsExpanded:X,toggleRowExpanded:le,toggleAllRowsExpanded:_,getToggleAllRowsExpandedProps:ne})}function hs(a,c){var d=c.instance.getHooks,v=c.instance;a.toggleRowExpanded=function(y){return v.toggleRowExpanded(a.id,y)},a.getToggleRowExpandedProps=C(d().getToggleRowExpandedProps,{instance:v,row:a})}var Va=function(a,c,d){return a=a.filter(function(v){return c.some(function(y){var h=v.values[y];return String(h).toLowerCase().includes(String(d).toLowerCase())})})};Va.autoRemove=function(a){return!a};var Ua=function(a,c,d){return a.filter(function(v){return c.some(function(y){var h=v.values[y];return h===void 0||String(h).toLowerCase()===String(d).toLowerCase()})})};Ua.autoRemove=function(a){return!a};var qa=function(a,c,d){return a.filter(function(v){return c.some(function(y){var h=v.values[y];return h===void 0||String(h)===String(d)})})};qa.autoRemove=function(a){return!a};var _a=function(a,c,d){return a.filter(function(v){return c.some(function(y){return v.values[y].includes(d)})})};_a.autoRemove=function(a){return!a||!a.length};var Xa=function(a,c,d){return a.filter(function(v){return c.some(function(y){var h=v.values[y];return h&&h.length&&d.every(function(w){return h.includes(w)})})})};Xa.autoRemove=function(a){return!a||!a.length};var Ka=function(a,c,d){return a.filter(function(v){return c.some(function(y){var h=v.values[y];return h&&h.length&&d.some(function(w){return h.includes(w)})})})};Ka.autoRemove=function(a){return!a||!a.length};var Ya=function(a,c,d){return a.filter(function(v){return c.some(function(y){var h=v.values[y];return d.includes(h)})})};Ya.autoRemove=function(a){return!a||!a.length};var Ja=function(a,c,d){return a.filter(function(v){return c.some(function(y){return v.values[y]===d})})};Ja.autoRemove=function(a){return a===void 0};var Qa=function(a,c,d){return a.filter(function(v){return c.some(function(y){return v.values[y]==d})})};Qa.autoRemove=function(a){return a==null};var Za=function(a,c,d){var v=d||[],y=v[0],h=v[1];if((y=typeof y=="number"?y:-1/0)>(h=typeof h=="number"?h:1/0)){var w=y;y=h,h=w}return a.filter(function(x){return c.some(function(I){var R=x.values[I];return R>=y&&R<=h})})};Za.autoRemove=function(a){return!a||typeof a[0]!="number"&&typeof a[1]!="number"};var qr=Object.freeze({__proto__:null,text:Va,exactText:Ua,exactTextCase:qa,includes:_a,includesAll:Xa,includesSome:Ka,includesValue:Ya,exact:Ja,equals:Qa,between:Za});u.resetFilters="resetFilters",u.setFilter="setFilter",u.setAllFilters="setAllFilters";var eo=function(a){a.stateReducers.push(bs),a.useInstance.push(ys)};function bs(a,c,d,v){if(c.type===u.init)return l({filters:[]},a);if(c.type===u.resetFilters)return l({},a,{filters:v.initialState.filters||[]});if(c.type===u.setFilter){var y=c.columnId,h=c.filterValue,w=v.allColumns,x=v.filterTypes,I=w.find(function(k){return k.id===y});if(!I)throw new Error("React-Table: Could not find a column with id: "+y);var R=we(I.filter,x||{},qr),F=a.filters.find(function(k){return k.id===y}),T=B(h,F&&F.value);return Ee(R.autoRemove,T,I)?l({},a,{filters:a.filters.filter(function(k){return k.id!==y})}):l({},a,F?{filters:a.filters.map(function(k){return k.id===y?{id:y,value:T}:k})}:{filters:[].concat(a.filters,[{id:y,value:T}])})}if(c.type===u.setAllFilters){var M=c.filters,E=v.allColumns,N=v.filterTypes;return l({},a,{filters:B(M,a.filters).filter(function(k){var H=E.find(function(X){return X.id===k.id});return!Ee(we(H.filter,N||{},qr).autoRemove,k.value,H)})})}}function ys(a){var c=a.data,d=a.rows,v=a.flatRows,y=a.rowsById,h=a.allColumns,w=a.filterTypes,x=a.manualFilters,I=a.defaultCanFilter,R=I!==void 0&&I,F=a.disableFilters,T=a.state.filters,M=a.dispatch,E=a.autoResetFilters,N=E===void 0||E,k=n.useCallback(function(U,ne){M({type:u.setFilter,columnId:U,filterValue:ne})},[M]),H=n.useCallback(function(U){M({type:u.setAllFilters,filters:U})},[M]);h.forEach(function(U){var ne=U.id,ce=U.accessor,Z=U.defaultCanFilter,te=U.disableFilters;U.canFilter=ce?z(te!==!0&&void 0,F!==!0&&void 0,!0):z(Z,R,!1),U.setFilter=function(oe){return k(U.id,oe)};var ge=T.find(function(oe){return oe.id===ne});U.filterValue=ge&&ge.value});var X=n.useMemo(function(){if(x||!T.length)return[d,v,y];var U=[],ne={};return[function ce(Z,te){te===void 0&&(te=0);var ge=Z;return(ge=T.reduce(function(oe,ve){var fe=ve.id,ye=ve.value,K=h.find(function(Be){return Be.id===fe});if(!K)return oe;te===0&&(K.preFilteredRows=oe);var de=we(K.filter,w||{},qr);return de?(K.filteredRows=de(oe,[fe],ye),K.filteredRows):(console.warn("Could not find a valid 'column.filter' for column with the ID: "+K.id+"."),oe)},Z)).forEach(function(oe){U.push(oe),ne[oe.id]=oe,oe.subRows&&(oe.subRows=oe.subRows&&oe.subRows.length>0?ce(oe.subRows,te+1):oe.subRows)}),ge}(d),U,ne]},[x,T,d,v,y,h,w]),le=X[0],_=X[1],L=X[2];n.useMemo(function(){h.filter(function(U){return!T.find(function(ne){return ne.id===U.id})}).forEach(function(U){U.preFilteredRows=le,U.filteredRows=le})},[le,T,h]);var ie=O(N);V(function(){ie()&&M({type:u.resetFilters})},[M,x?null:c]),Object.assign(a,{preFilteredRows:d,preFilteredFlatRows:v,preFilteredRowsById:y,filteredRows:le,filteredFlatRows:_,filteredRowsById:L,rows:le,flatRows:_,rowsById:L,setFilter:k,setAllFilters:H})}eo.pluginName="useFilters",u.resetGlobalFilter="resetGlobalFilter",u.setGlobalFilter="setGlobalFilter";var ro=function(a){a.stateReducers.push(ws),a.useInstance.push(Cs)};function ws(a,c,d,v){if(c.type===u.resetGlobalFilter)return l({},a,{globalFilter:v.initialState.globalFilter||void 0});if(c.type===u.setGlobalFilter){var y=c.filterValue,h=v.userFilterTypes,w=we(v.globalFilter,h||{},qr),x=B(y,a.globalFilter);return Ee(w.autoRemove,x)?(a.globalFilter,s(a,["globalFilter"])):l({},a,{globalFilter:x})}}function Cs(a){var c=a.data,d=a.rows,v=a.flatRows,y=a.rowsById,h=a.allColumns,w=a.filterTypes,x=a.globalFilter,I=a.manualGlobalFilter,R=a.state.globalFilter,F=a.dispatch,T=a.autoResetGlobalFilter,M=T===void 0||T,E=a.disableGlobalFilter,N=n.useCallback(function(L){F({type:u.setGlobalFilter,filterValue:L})},[F]),k=n.useMemo(function(){if(I||R===void 0)return[d,v,y];var L=[],ie={},U=we(x,w||{},qr);if(!U)return console.warn("Could not find a valid 'globalFilter' option."),d;h.forEach(function(ce){var Z=ce.disableGlobalFilter;ce.canFilter=z(Z!==!0&&void 0,E!==!0&&void 0,!0)});var ne=h.filter(function(ce){return ce.canFilter===!0});return[function ce(Z){return(Z=U(Z,ne.map(function(te){return te.id}),R)).forEach(function(te){L.push(te),ie[te.id]=te,te.subRows=te.subRows&&te.subRows.length?ce(te.subRows):te.subRows}),Z}(d),L,ie]},[I,R,x,w,h,d,v,y,E]),H=k[0],X=k[1],le=k[2],_=O(M);V(function(){_()&&F({type:u.resetGlobalFilter})},[F,I?null:c]),Object.assign(a,{preGlobalFilteredRows:d,preGlobalFilteredFlatRows:v,preGlobalFilteredRowsById:y,globalFilteredRows:H,globalFilteredFlatRows:X,globalFilteredRowsById:le,rows:H,flatRows:X,rowsById:le,setGlobalFilter:N,disableGlobalFilter:E})}function to(a,c){return c.reduce(function(d,v){return d+(typeof v=="number"?v:0)},0)}ro.pluginName="useGlobalFilter";var no=Object.freeze({__proto__:null,sum:to,min:function(a){var c=a[0]||0;return a.forEach(function(d){typeof d=="number"&&(c=Math.min(c,d))}),c},max:function(a){var c=a[0]||0;return a.forEach(function(d){typeof d=="number"&&(c=Math.max(c,d))}),c},minMax:function(a){var c=a[0]||0,d=a[0]||0;return a.forEach(function(v){typeof v=="number"&&(c=Math.min(c,v),d=Math.max(d,v))}),c+".."+d},average:function(a){return to(0,a)/a.length},median:function(a){if(!a.length)return null;var c=Math.floor(a.length/2),d=[].concat(a).sort(function(v,y){return v-y});return a.length%2!=0?d[c]:(d[c-1]+d[c])/2},unique:function(a){return Array.from(new Set(a).values())},uniqueCount:function(a){return new Set(a).size},count:function(a){return a.length}}),Ss=[],xs={};u.resetGroupBy="resetGroupBy",u.setGroupBy="setGroupBy",u.toggleGroupBy="toggleGroupBy";var ao=function(a){a.getGroupByToggleProps=[Is],a.stateReducers.push(Ps),a.visibleColumnsDeps.push(function(c,d){var v=d.instance;return[].concat(c,[v.state.groupBy])}),a.visibleColumns.push(Rs),a.useInstance.push(Es),a.prepareRow.push(Bs)};ao.pluginName="useGroupBy";var Is=function(a,c){var d=c.header;return[a,{onClick:d.canGroupBy?function(v){v.persist(),d.toggleGroupBy()}:void 0,style:{cursor:d.canGroupBy?"pointer":void 0},title:"Toggle GroupBy"}]};function Ps(a,c,d,v){if(c.type===u.init)return l({groupBy:[]},a);if(c.type===u.resetGroupBy)return l({},a,{groupBy:v.initialState.groupBy||[]});if(c.type===u.setGroupBy)return l({},a,{groupBy:c.value});if(c.type===u.toggleGroupBy){var y=c.columnId,h=c.value,w=h!==void 0?h:!a.groupBy.includes(y);return l({},a,w?{groupBy:[].concat(a.groupBy,[y])}:{groupBy:a.groupBy.filter(function(x){return x!==y})})}}function Rs(a,c){var d=c.instance.state.groupBy,v=d.map(function(h){return a.find(function(w){return w.id===h})}).filter(Boolean),y=a.filter(function(h){return!d.includes(h.id)});return(a=[].concat(v,y)).forEach(function(h){h.isGrouped=d.includes(h.id),h.groupedIndex=d.indexOf(h.id)}),a}var Ds={};function Es(a){var c=a.data,d=a.rows,v=a.flatRows,y=a.rowsById,h=a.allColumns,w=a.flatHeaders,x=a.groupByFn,I=x===void 0?oo:x,R=a.manualGroupBy,F=a.aggregations,T=F===void 0?Ds:F,M=a.plugins,E=a.state.groupBy,N=a.dispatch,k=a.autoResetGroupBy,H=k===void 0||k,X=a.disableGroupBy,le=a.defaultCanGroupBy,_=a.getHooks;D(M,["useColumnOrder","useFilters"],"useGroupBy");var L=O(a);h.forEach(function(K){var de=K.accessor,Be=K.defaultGroupBy,qe=K.disableGroupBy;K.canGroupBy=de?z(K.canGroupBy,qe!==!0&&void 0,X!==!0&&void 0,!0):z(K.canGroupBy,Be,le,!1),K.canGroupBy&&(K.toggleGroupBy=function(){return a.toggleGroupBy(K.id)}),K.Aggregated=K.Aggregated||K.Cell});var ie=n.useCallback(function(K,de){N({type:u.toggleGroupBy,columnId:K,value:de})},[N]),U=n.useCallback(function(K){N({type:u.setGroupBy,value:K})},[N]);w.forEach(function(K){K.getGroupByToggleProps=C(_().getGroupByToggleProps,{instance:L(),header:K})});var ne=n.useMemo(function(){if(R||!E.length)return[d,v,y,Ss,xs,v,y];var K=E.filter(function(Ge){return h.find(function(br){return br.id===Ge})}),de=[],Be={},qe=[],ee={},Pe=[],Te={},_e=function Ge(br,hr,Io){if(hr===void 0&&(hr=0),hr===K.length)return br.map(function(yt){return l({},yt,{depth:hr})});var wn=K[hr],hu=I(br,wn);return Object.entries(hu).map(function(yt,bu){var Po=yt[0],wt=yt[1],Ct=wn+":"+Po,Ro=Ge(wt,hr+1,Ct=Io?Io+">"+Ct:Ct),Do=hr?Ie(wt,"leafRows"):wt,yu=function(or,Cn,Cu){var St={};return h.forEach(function(Me){if(K.includes(Me.id))St[Me.id]=Cn[0]?Cn[0].values[Me.id]:null;else{var Eo=typeof Me.aggregate=="function"?Me.aggregate:T[Me.aggregate]||no[Me.aggregate];if(Eo){var Su=Cn.map(function(xt){return xt.values[Me.id]}),xu=or.map(function(xt){var Sn=xt.values[Me.id];if(!Cu&&Me.aggregateValue){var Bo=typeof Me.aggregateValue=="function"?Me.aggregateValue:T[Me.aggregateValue]||no[Me.aggregateValue];if(!Bo)throw console.info({column:Me}),new Error("React Table: Invalid column.aggregateValue option for column listed above");Sn=Bo(Sn,xt,Me)}return Sn});St[Me.id]=Eo(xu,Su)}else{if(Me.aggregate)throw console.info({column:Me}),new Error("React Table: Invalid column.aggregate option for column listed above");St[Me.id]=null}}}),St}(Do,wt,hr),wu={id:Ct,isGrouped:!0,groupByID:wn,groupByVal:Po,values:yu,subRows:Ro,leafRows:Do,depth:hr,index:bu};return Ro.forEach(function(or){de.push(or),Be[or.id]=or,or.isGrouped?(qe.push(or),ee[or.id]=or):(Pe.push(or),Te[or.id]=or)}),wu})}(d);return _e.forEach(function(Ge){de.push(Ge),Be[Ge.id]=Ge,Ge.isGrouped?(qe.push(Ge),ee[Ge.id]=Ge):(Pe.push(Ge),Te[Ge.id]=Ge)}),[_e,de,Be,qe,ee,Pe,Te]},[R,E,d,v,y,h,T,I]),ce=ne[0],Z=ne[1],te=ne[2],ge=ne[3],oe=ne[4],ve=ne[5],fe=ne[6],ye=O(H);V(function(){ye()&&N({type:u.resetGroupBy})},[N,R?null:c]),Object.assign(a,{preGroupedRows:d,preGroupedFlatRow:v,preGroupedRowsById:y,groupedRows:ce,groupedFlatRows:Z,groupedRowsById:te,onlyGroupedFlatRows:ge,onlyGroupedRowsById:oe,nonGroupedFlatRows:ve,nonGroupedRowsById:fe,rows:ce,flatRows:Z,rowsById:te,toggleGroupBy:ie,setGroupBy:U})}function Bs(a){a.allCells.forEach(function(c){var d;c.isGrouped=c.column.isGrouped&&c.column.id===a.groupByID,c.isPlaceholder=!c.isGrouped&&c.column.isGrouped,c.isAggregated=!c.isGrouped&&!c.isPlaceholder&&((d=a.subRows)==null?void 0:d.length)})}function oo(a,c){return a.reduce(function(d,v,y){var h=""+v.values[c];return d[h]=Array.isArray(d[h])?d[h]:[],d[h].push(v),d},{})}var io=/([0-9]+)/gm;function vn(a,c){return a===c?0:a>c?1:-1}function _r(a,c,d){return[a.values[d],c.values[d]]}function lo(a){return typeof a=="number"?isNaN(a)||a===1/0||a===-1/0?"":String(a):typeof a=="string"?a:""}var Os=Object.freeze({__proto__:null,alphanumeric:function(a,c,d){var v=_r(a,c,d),y=v[0],h=v[1];for(y=lo(y),h=lo(h),y=y.split(io).filter(Boolean),h=h.split(io).filter(Boolean);y.length&&h.length;){var w=y.shift(),x=h.shift(),I=parseInt(w,10),R=parseInt(x,10),F=[I,R].sort();if(isNaN(F[0])){if(w>x)return 1;if(x>w)return-1}else{if(isNaN(F[1]))return isNaN(I)?-1:1;if(I>R)return 1;if(R>I)return-1}}return y.length-h.length},datetime:function(a,c,d){var v=_r(a,c,d),y=v[0],h=v[1];return vn(y=y.getTime(),h=h.getTime())},basic:function(a,c,d){var v=_r(a,c,d);return vn(v[0],v[1])},string:function(a,c,d){var v=_r(a,c,d),y=v[0],h=v[1];for(y=y.split("").filter(Boolean),h=h.split("").filter(Boolean);y.length&&h.length;){var w=y.shift(),x=h.shift(),I=w.toLowerCase(),R=x.toLowerCase();if(I>R)return 1;if(R>I)return-1;if(w>x)return 1;if(x>w)return-1}return y.length-h.length},number:function(a,c,d){var v=_r(a,c,d),y=v[0],h=v[1],w=/[^0-9.]/gi;return vn(y=Number(String(y).replace(w,"")),h=Number(String(h).replace(w,"")))}});u.resetSortBy="resetSortBy",u.setSortBy="setSortBy",u.toggleSortBy="toggleSortBy",u.clearSortBy="clearSortBy",f.sortType="alphanumeric",f.sortDescFirst=!1;var so=function(a){a.getSortByToggleProps=[As],a.stateReducers.push(Ts),a.useInstance.push(Ms)};so.pluginName="useSortBy";var As=function(a,c){var d=c.instance,v=c.column,y=d.isMultiSortEvent,h=y===void 0?function(w){return w.shiftKey}:y;return[a,{onClick:v.canSort?function(w){w.persist(),v.toggleSortBy(void 0,!d.disableMultiSort&&h(w))}:void 0,style:{cursor:v.canSort?"pointer":void 0},title:v.canSort?"Toggle SortBy":void 0}]};function Ts(a,c,d,v){if(c.type===u.init)return l({sortBy:[]},a);if(c.type===u.resetSortBy)return l({},a,{sortBy:v.initialState.sortBy||[]});if(c.type===u.clearSortBy)return l({},a,{sortBy:a.sortBy.filter(function(L){return L.id!==c.columnId})});if(c.type===u.setSortBy)return l({},a,{sortBy:c.sortBy});if(c.type===u.toggleSortBy){var y,h=c.columnId,w=c.desc,x=c.multi,I=v.allColumns,R=v.disableMultiSort,F=v.disableSortRemove,T=v.disableMultiRemove,M=v.maxMultiSortColCount,E=M===void 0?Number.MAX_SAFE_INTEGER:M,N=a.sortBy,k=I.find(function(L){return L.id===h}).sortDescFirst,H=N.find(function(L){return L.id===h}),X=N.findIndex(function(L){return L.id===h}),le=w!=null,_=[];return(y=!R&&x?H?"toggle":"add":X!==N.length-1||N.length!==1?"replace":H?"toggle":"replace")!="toggle"||F||le||x&&T||!(H&&H.desc&&!k||!H.desc&&k)||(y="remove"),y==="replace"?_=[{id:h,desc:le?w:k}]:y==="add"?(_=[].concat(N,[{id:h,desc:le?w:k}])).splice(0,_.length-E):y==="toggle"?_=N.map(function(L){return L.id===h?l({},L,{desc:le?w:!H.desc}):L}):y==="remove"&&(_=N.filter(function(L){return L.id!==h})),l({},a,{sortBy:_})}}function Ms(a){var c=a.data,d=a.rows,v=a.flatRows,y=a.allColumns,h=a.orderByFn,w=h===void 0?uo:h,x=a.sortTypes,I=a.manualSortBy,R=a.defaultCanSort,F=a.disableSortBy,T=a.flatHeaders,M=a.state.sortBy,E=a.dispatch,N=a.plugins,k=a.getHooks,H=a.autoResetSortBy,X=H===void 0||H;D(N,["useFilters","useGlobalFilter","useGroupBy","usePivotColumns"],"useSortBy");var le=n.useCallback(function(Z){E({type:u.setSortBy,sortBy:Z})},[E]),_=n.useCallback(function(Z,te,ge){E({type:u.toggleSortBy,columnId:Z,desc:te,multi:ge})},[E]),L=O(a);T.forEach(function(Z){var te=Z.accessor,ge=Z.canSort,oe=Z.disableSortBy,ve=Z.id,fe=te?z(oe!==!0&&void 0,F!==!0&&void 0,!0):z(R,ge,!1);Z.canSort=fe,Z.canSort&&(Z.toggleSortBy=function(K,de){return _(Z.id,K,de)},Z.clearSortBy=function(){E({type:u.clearSortBy,columnId:Z.id})}),Z.getSortByToggleProps=C(k().getSortByToggleProps,{instance:L(),column:Z});var ye=M.find(function(K){return K.id===ve});Z.isSorted=!!ye,Z.sortedIndex=M.findIndex(function(K){return K.id===ve}),Z.isSortedDesc=Z.isSorted?ye.desc:void 0});var ie=n.useMemo(function(){if(I||!M.length)return[d,v];var Z=[],te=M.filter(function(ge){return y.find(function(oe){return oe.id===ge.id})});return[function ge(oe){var ve=w(oe,te.map(function(fe){var ye=y.find(function(Be){return Be.id===fe.id});if(!ye)throw new Error("React-Table: Could not find a column with id: "+fe.id+" while sorting");var K=ye.sortType,de=ue(K)||(x||{})[K]||Os[K];if(!de)throw new Error("React-Table: Could not find a valid sortType of '"+K+"' for column '"+fe.id+"'.");return function(Be,qe){return de(Be,qe,fe.id,fe.desc)}}),te.map(function(fe){var ye=y.find(function(K){return K.id===fe.id});return ye&&ye.sortInverted?fe.desc:!fe.desc}));return ve.forEach(function(fe){Z.push(fe),fe.subRows&&fe.subRows.length!==0&&(fe.subRows=ge(fe.subRows))}),ve}(d),Z]},[I,M,d,v,y,w,x]),U=ie[0],ne=ie[1],ce=O(X);V(function(){ce()&&E({type:u.resetSortBy})},[I?null:c]),Object.assign(a,{preSortedRows:d,preSortedFlatRows:v,sortedRows:U,sortedFlatRows:ne,rows:U,flatRows:ne,setSortBy:le,toggleSortBy:_})}function uo(a,c,d){return[].concat(a).sort(function(v,y){for(var h=0;h<c.length;h+=1){var w=c[h],x=d[h]===!1||d[h]==="desc",I=w(v,y);if(I!==0)return x?-I:I}return d[0]?v.index-y.index:y.index-v.index})}u.resetPage="resetPage",u.gotoPage="gotoPage",u.setPageSize="setPageSize";var co=function(a){a.stateReducers.push(Ns),a.useInstance.push(Fs)};function Ns(a,c,d,v){if(c.type===u.init)return l({pageSize:10,pageIndex:0},a);if(c.type===u.resetPage)return l({},a,{pageIndex:v.initialState.pageIndex||0});if(c.type===u.gotoPage){var y=v.pageCount,h=v.page,w=B(c.pageIndex,a.pageIndex),x=!1;return w>a.pageIndex?x=y===-1?h.length>=a.pageSize:w<y:w<a.pageIndex&&(x=w>-1),x?l({},a,{pageIndex:w}):a}if(c.type===u.setPageSize){var I=c.pageSize,R=a.pageSize*a.pageIndex;return l({},a,{pageIndex:Math.floor(R/I),pageSize:I})}}function Fs(a){var c=a.rows,d=a.autoResetPage,v=d===void 0||d,y=a.manualExpandedKey,h=y===void 0?"expanded":y,w=a.plugins,x=a.pageCount,I=a.paginateExpandedRows,R=I===void 0||I,F=a.expandSubRows,T=F===void 0||F,M=a.state,E=M.pageSize,N=M.pageIndex,k=M.expanded,H=M.globalFilter,X=M.filters,le=M.groupBy,_=M.sortBy,L=a.dispatch,ie=a.data,U=a.manualPagination;D(w,["useGlobalFilter","useFilters","useGroupBy","useSortBy","useExpanded"],"usePagination");var ne=O(v);V(function(){ne()&&L({type:u.resetPage})},[L,U?null:ie,H,X,le,_]);var ce=U?x:Math.ceil(c.length/E),Z=n.useMemo(function(){return ce>0?[].concat(new Array(ce)).fill(null).map(function(de,Be){return Be}):[]},[ce]),te=n.useMemo(function(){var de;if(U)de=c;else{var Be=E*N,qe=Be+E;de=c.slice(Be,qe)}return R?de:Se(de,{manualExpandedKey:h,expanded:k,expandSubRows:T})},[T,k,h,U,N,E,R,c]),ge=N>0,oe=ce===-1?te.length>=E:N<ce-1,ve=n.useCallback(function(de){L({type:u.gotoPage,pageIndex:de})},[L]),fe=n.useCallback(function(){return ve(function(de){return de-1})},[ve]),ye=n.useCallback(function(){return ve(function(de){return de+1})},[ve]),K=n.useCallback(function(de){L({type:u.setPageSize,pageSize:de})},[L]);Object.assign(a,{pageOptions:Z,pageCount:ce,page:te,canPreviousPage:ge,canNextPage:oe,gotoPage:ve,previousPage:fe,nextPage:ye,setPageSize:K})}co.pluginName="usePagination",u.resetPivot="resetPivot",u.togglePivot="togglePivot";var fo=function(a){a.getPivotToggleProps=[Ls],a.stateReducers.push(Ws),a.useInstanceAfterData.push(Gs),a.allColumns.push(ks),a.accessValue.push($s),a.materializedColumns.push(Hs),a.materializedColumnsDeps.push(zs),a.visibleColumns.push(js),a.visibleColumnsDeps.push(Vs),a.useInstance.push(Us),a.prepareRow.push(qs)};fo.pluginName="usePivotColumns";var po=[],Ls=function(a,c){var d=c.header;return[a,{onClick:d.canPivot?function(v){v.persist(),d.togglePivot()}:void 0,style:{cursor:d.canPivot?"pointer":void 0},title:"Toggle Pivot"}]};function Ws(a,c,d,v){if(c.type===u.init)return l({pivotColumns:po},a);if(c.type===u.resetPivot)return l({},a,{pivotColumns:v.initialState.pivotColumns||po});if(c.type===u.togglePivot){var y=c.columnId,h=c.value,w=h!==void 0?h:!a.pivotColumns.includes(y);return l({},a,w?{pivotColumns:[].concat(a.pivotColumns,[y])}:{pivotColumns:a.pivotColumns.filter(function(x){return x!==y})})}}function Gs(a){a.allColumns.forEach(function(c){c.isPivotSource=a.state.pivotColumns.includes(c.id)})}function ks(a,c){var d=c.instance;return a.forEach(function(v){v.isPivotSource=d.state.pivotColumns.includes(v.id),v.uniqueValues=new Set}),a}function $s(a,c){var d=c.column;return d.uniqueValues&&a!==void 0&&d.uniqueValues.add(a),a}function Hs(a,c){var d=c.instance,v=d.allColumns,y=d.state;if(!y.pivotColumns.length||!y.groupBy||!y.groupBy.length)return a;var h=y.pivotColumns.map(function(I){return v.find(function(R){return R.id===I})}).filter(Boolean),w=v.filter(function(I){return!I.isPivotSource&&!y.groupBy.includes(I.id)&&!y.pivotColumns.includes(I.id)}),x=Q(function I(R,F,T){R===void 0&&(R=0),T===void 0&&(T=[]);var M=h[R];return M?Array.from(M.uniqueValues).sort().map(function(E){var N=l({},M,{Header:M.PivotHeader||typeof M.header=="string"?M.Header+": "+E:E,isPivotGroup:!0,parent:F,depth:R,id:F?F.id+"."+M.id+"."+E:M.id+"."+E,pivotValue:E});return N.columns=I(R+1,N,[].concat(T,[function(k){return k.values[M.id]===E}])),N}):w.map(function(E){return l({},E,{canPivot:!1,isPivoted:!0,parent:F,depth:R,id:""+(F?F.id+"."+E.id:E.id),accessor:function(N,k,H){if(T.every(function(X){return X(H)}))return H.values[E.id]}})})}());return[].concat(a,x)}function zs(a,c){var d=c.instance.state,v=d.pivotColumns,y=d.groupBy;return[].concat(a,[v,y])}function js(a,c){var d=c.instance.state;return a=a.filter(function(v){return!v.isPivotSource}),d.pivotColumns.length&&d.groupBy&&d.groupBy.length&&(a=a.filter(function(v){return v.isGrouped||v.isPivoted})),a}function Vs(a,c){var d=c.instance;return[].concat(a,[d.state.pivotColumns,d.state.groupBy])}function Us(a){var c=a.columns,d=a.allColumns,v=a.flatHeaders,y=a.getHooks,h=a.plugins,w=a.dispatch,x=a.autoResetPivot,I=x===void 0||x,R=a.manaulPivot,F=a.disablePivot,T=a.defaultCanPivot;D(h,["useGroupBy"],"usePivotColumns");var M=O(a);d.forEach(function(N){var k=N.accessor,H=N.defaultPivot,X=N.disablePivot;N.canPivot=k?z(N.canPivot,X!==!0&&void 0,F!==!0&&void 0,!0):z(N.canPivot,H,T,!1),N.canPivot&&(N.togglePivot=function(){return a.togglePivot(N.id)}),N.Aggregated=N.Aggregated||N.Cell}),v.forEach(function(N){N.getPivotToggleProps=C(y().getPivotToggleProps,{instance:M(),header:N})});var E=O(I);V(function(){E()&&w({type:u.resetPivot})},[w,R?null:c]),Object.assign(a,{togglePivot:function(N,k){w({type:u.togglePivot,columnId:N,value:k})}})}function qs(a){a.allCells.forEach(function(c){c.isPivoted=c.column.isPivoted})}u.resetSelectedRows="resetSelectedRows",u.toggleAllRowsSelected="toggleAllRowsSelected",u.toggleRowSelected="toggleRowSelected",u.toggleAllPageRowsSelected="toggleAllPageRowsSelected";var vo=function(a){a.getToggleRowSelectedProps=[_s],a.getToggleAllRowsSelectedProps=[Xs],a.getToggleAllPageRowsSelectedProps=[Ks],a.stateReducers.push(Ys),a.useInstance.push(Js),a.prepareRow.push(Qs)};vo.pluginName="useRowSelect";var _s=function(a,c){var d=c.instance,v=c.row,y=d.manualRowSelectedKey,h=y===void 0?"isSelected":y;return[a,{onChange:function(w){v.toggleRowSelected(w.target.checked)},style:{cursor:"pointer"},checked:!(!v.original||!v.original[h])||v.isSelected,title:"Toggle Row Selected",indeterminate:v.isSomeSelected}]},Xs=function(a,c){var d=c.instance;return[a,{onChange:function(v){d.toggleAllRowsSelected(v.target.checked)},style:{cursor:"pointer"},checked:d.isAllRowsSelected,title:"Toggle All Rows Selected",indeterminate:Boolean(!d.isAllRowsSelected&&Object.keys(d.state.selectedRowIds).length)}]},Ks=function(a,c){var d=c.instance;return[a,{onChange:function(v){d.toggleAllPageRowsSelected(v.target.checked)},style:{cursor:"pointer"},checked:d.isAllPageRowsSelected,title:"Toggle All Current Page Rows Selected",indeterminate:Boolean(!d.isAllPageRowsSelected&&d.page.some(function(v){var y=v.id;return d.state.selectedRowIds[y]}))}]};function Ys(a,c,d,v){if(c.type===u.init)return l({selectedRowIds:{}},a);if(c.type===u.resetSelectedRows)return l({},a,{selectedRowIds:v.initialState.selectedRowIds||{}});if(c.type===u.toggleAllRowsSelected){var y=c.value,h=v.isAllRowsSelected,w=v.rowsById,x=v.nonGroupedRowsById,I=x===void 0?w:x,R=y!==void 0?y:!h,F=Object.assign({},a.selectedRowIds);return R?Object.keys(I).forEach(function(ve){F[ve]=!0}):Object.keys(I).forEach(function(ve){delete F[ve]}),l({},a,{selectedRowIds:F})}if(c.type===u.toggleRowSelected){var T=c.id,M=c.value,E=v.rowsById,N=v.selectSubRows,k=N===void 0||N,H=v.getSubRows,X=a.selectedRowIds[T],le=M!==void 0?M:!X;if(X===le)return a;var _=l({},a.selectedRowIds);return function ve(fe){var ye=E[fe];if(ye&&(ye.isGrouped||(le?_[fe]=!0:delete _[fe]),k&&H(ye)))return H(ye).forEach(function(K){return ve(K.id)})}(T),l({},a,{selectedRowIds:_})}if(c.type===u.toggleAllPageRowsSelected){var L=c.value,ie=v.page,U=v.rowsById,ne=v.selectSubRows,ce=ne===void 0||ne,Z=v.isAllPageRowsSelected,te=v.getSubRows,ge=L!==void 0?L:!Z,oe=l({},a.selectedRowIds);return ie.forEach(function(ve){return function fe(ye){var K=U[ye];if(K.isGrouped||(ge?oe[ye]=!0:delete oe[ye]),ce&&te(K))return te(K).forEach(function(de){return fe(de.id)})}(ve.id)}),l({},a,{selectedRowIds:oe})}return a}function Js(a){var c=a.data,d=a.rows,v=a.getHooks,y=a.plugins,h=a.rowsById,w=a.nonGroupedRowsById,x=w===void 0?h:w,I=a.autoResetSelectedRows,R=I===void 0||I,F=a.state.selectedRowIds,T=a.selectSubRows,M=T===void 0||T,E=a.dispatch,N=a.page,k=a.getSubRows;D(y,["useFilters","useGroupBy","useSortBy","useExpanded","usePagination"],"useRowSelect");var H=n.useMemo(function(){var te=[];return d.forEach(function(ge){var oe=M?function ve(fe,ye,K){if(ye[fe.id])return!0;var de=K(fe);if(de&&de.length){var Be=!0,qe=!1;return de.forEach(function(ee){qe&&!Be||(ve(ee,ye,K)?qe=!0:Be=!1)}),!!Be||!!qe&&null}return!1}(ge,F,k):!!F[ge.id];ge.isSelected=!!oe,ge.isSomeSelected=oe===null,oe&&te.push(ge)}),te},[d,M,F,k]),X=Boolean(Object.keys(x).length&&Object.keys(F).length),le=X;X&&Object.keys(x).some(function(te){return!F[te]})&&(X=!1),X||N&&N.length&&N.some(function(te){var ge=te.id;return!F[ge]})&&(le=!1);var _=O(R);V(function(){_()&&E({type:u.resetSelectedRows})},[E,c]);var L=n.useCallback(function(te){return E({type:u.toggleAllRowsSelected,value:te})},[E]),ie=n.useCallback(function(te){return E({type:u.toggleAllPageRowsSelected,value:te})},[E]),U=n.useCallback(function(te,ge){return E({type:u.toggleRowSelected,id:te,value:ge})},[E]),ne=O(a),ce=C(v().getToggleAllRowsSelectedProps,{instance:ne()}),Z=C(v().getToggleAllPageRowsSelectedProps,{instance:ne()});Object.assign(a,{selectedFlatRows:H,isAllRowsSelected:X,isAllPageRowsSelected:le,toggleRowSelected:U,toggleAllRowsSelected:L,getToggleAllRowsSelectedProps:ce,getToggleAllPageRowsSelectedProps:Z,toggleAllPageRowsSelected:ie})}function Qs(a,c){var d=c.instance;a.toggleRowSelected=function(v){return d.toggleRowSelected(a.id,v)},a.getToggleRowSelectedProps=C(d.getHooks().getToggleRowSelectedProps,{instance:d,row:a})}var go=function(a){return{}},mo=function(a){return{}};u.setRowState="setRowState",u.setCellState="setCellState",u.resetRowState="resetRowState";var ho=function(a){a.stateReducers.push(Zs),a.useInstance.push(eu),a.prepareRow.push(ru)};function Zs(a,c,d,v){var y=v.initialRowStateAccessor,h=y===void 0?go:y,w=v.initialCellStateAccessor,x=w===void 0?mo:w,I=v.rowsById;if(c.type===u.init)return l({rowState:{}},a);if(c.type===u.resetRowState)return l({},a,{rowState:v.initialState.rowState||{}});if(c.type===u.setRowState){var R,F=c.rowId,T=c.value,M=a.rowState[F]!==void 0?a.rowState[F]:h(I[F]);return l({},a,{rowState:l({},a.rowState,(R={},R[F]=B(T,M),R))})}if(c.type===u.setCellState){var E,N,k,H,X,le=c.rowId,_=c.columnId,L=c.value,ie=a.rowState[le]!==void 0?a.rowState[le]:h(I[le]),U=(ie==null||(E=ie.cellState)==null?void 0:E[_])!==void 0?ie.cellState[_]:x((N=I[le])==null||(k=N.cells)==null?void 0:k.find(function(ne){return ne.column.id===_}));return l({},a,{rowState:l({},a.rowState,(X={},X[le]=l({},ie,{cellState:l({},ie.cellState||{},(H={},H[_]=B(L,U),H))}),X))})}}function eu(a){var c=a.autoResetRowState,d=c===void 0||c,v=a.data,y=a.dispatch,h=n.useCallback(function(I,R){return y({type:u.setRowState,rowId:I,value:R})},[y]),w=n.useCallback(function(I,R,F){return y({type:u.setCellState,rowId:I,columnId:R,value:F})},[y]),x=O(d);V(function(){x()&&y({type:u.resetRowState})},[v]),Object.assign(a,{setRowState:h,setCellState:w})}function ru(a,c){var d=c.instance,v=d.initialRowStateAccessor,y=v===void 0?go:v,h=d.initialCellStateAccessor,w=h===void 0?mo:h,x=d.state.rowState;a&&(a.state=x[a.id]!==void 0?x[a.id]:y(a),a.setState=function(I){return d.setRowState(a.id,I)},a.cells.forEach(function(I){a.state.cellState||(a.state.cellState={}),I.state=a.state.cellState[I.column.id]!==void 0?a.state.cellState[I.column.id]:w(I),I.setState=function(R){return d.setCellState(a.id,I.column.id,R)}}))}ho.pluginName="useRowState",u.resetColumnOrder="resetColumnOrder",u.setColumnOrder="setColumnOrder";var bo=function(a){a.stateReducers.push(tu),a.visibleColumnsDeps.push(function(c,d){var v=d.instance;return[].concat(c,[v.state.columnOrder])}),a.visibleColumns.push(nu),a.useInstance.push(au)};function tu(a,c,d,v){return c.type===u.init?l({columnOrder:[]},a):c.type===u.resetColumnOrder?l({},a,{columnOrder:v.initialState.columnOrder||[]}):c.type===u.setColumnOrder?l({},a,{columnOrder:B(c.columnOrder,a.columnOrder)}):void 0}function nu(a,c){var d=c.instance.state.columnOrder;if(!d||!d.length)return a;for(var v=[].concat(d),y=[].concat(a),h=[],w=function(){var x=v.shift(),I=y.findIndex(function(R){return R.id===x});I>-1&&h.push(y.splice(I,1)[0])};y.length&&v.length;)w();return[].concat(h,y)}function au(a){var c=a.dispatch;a.setColumnOrder=n.useCallback(function(d){return c({type:u.setColumnOrder,columnOrder:d})},[c])}bo.pluginName="useColumnOrder",f.canResize=!0,u.columnStartResizing="columnStartResizing",u.columnResizing="columnResizing",u.columnDoneResizing="columnDoneResizing",u.resetResize="resetResize";var yo=function(a){a.getResizerProps=[ou],a.getHeaderProps.push({style:{position:"relative"}}),a.stateReducers.push(iu),a.useInstance.push(su),a.useInstanceBeforeDimensions.push(lu)},ou=function(a,c){var d=c.instance,v=c.header,y=d.dispatch,h=function(w,x){var I=!1;if(w.type==="touchstart"){if(w.touches&&w.touches.length>1)return;I=!0}var R,F,T=function(_){var L=[];return function ie(U){U.columns&&U.columns.length&&U.columns.map(ie),L.push(U)}(_),L}(x).map(function(_){return[_.id,_.totalWidth]}),M=I?Math.round(w.touches[0].clientX):w.clientX,E=function(){window.cancelAnimationFrame(R),R=null,y({type:u.columnDoneResizing})},N=function(){window.cancelAnimationFrame(R),R=null,y({type:u.columnResizing,clientX:F})},k=function(_){F=_,R||(R=window.requestAnimationFrame(N))},H={mouse:{moveEvent:"mousemove",moveHandler:function(_){return k(_.clientX)},upEvent:"mouseup",upHandler:function(_){document.removeEventListener("mousemove",H.mouse.moveHandler),document.removeEventListener("mouseup",H.mouse.upHandler),E()}},touch:{moveEvent:"touchmove",moveHandler:function(_){return _.cancelable&&(_.preventDefault(),_.stopPropagation()),k(_.touches[0].clientX),!1},upEvent:"touchend",upHandler:function(_){document.removeEventListener(H.touch.moveEvent,H.touch.moveHandler),document.removeEventListener(H.touch.upEvent,H.touch.moveHandler),E()}}},X=I?H.touch:H.mouse,le=!!function(){if(typeof Oe=="boolean")return Oe;var _=!1;try{var L={get passive(){return _=!0,!1}};window.addEventListener("test",null,L),window.removeEventListener("test",null,L)}catch{_=!1}return Oe=_}()&&{passive:!1};document.addEventListener(X.moveEvent,X.moveHandler,le),document.addEventListener(X.upEvent,X.upHandler,le),y({type:u.columnStartResizing,columnId:x.id,columnWidth:x.totalWidth,headerIdWidths:T,clientX:M})};return[a,{onMouseDown:function(w){return w.persist()||h(w,v)},onTouchStart:function(w){return w.persist()||h(w,v)},style:{cursor:"col-resize"},draggable:!1,role:"separator"}]};function iu(a,c){if(c.type===u.init)return l({columnResizing:{columnWidths:{}}},a);if(c.type===u.resetResize)return l({},a,{columnResizing:{columnWidths:{}}});if(c.type===u.columnStartResizing){var d=c.clientX,v=c.columnId,y=c.columnWidth,h=c.headerIdWidths;return l({},a,{columnResizing:l({},a.columnResizing,{startX:d,headerIdWidths:h,columnWidth:y,isResizingColumn:v})})}if(c.type===u.columnResizing){var w=c.clientX,x=a.columnResizing,I=x.startX,R=x.columnWidth,F=x.headerIdWidths,T=(w-I)/R,M={};return(F===void 0?[]:F).forEach(function(E){var N=E[0],k=E[1];M[N]=Math.max(k+k*T,0)}),l({},a,{columnResizing:l({},a.columnResizing,{columnWidths:l({},a.columnResizing.columnWidths,{},M)})})}return c.type===u.columnDoneResizing?l({},a,{columnResizing:l({},a.columnResizing,{startX:null,isResizingColumn:null})}):void 0}yo.pluginName="useResizeColumns";var lu=function(a){var c=a.flatHeaders,d=a.disableResizing,v=a.getHooks,y=a.state.columnResizing,h=O(a);c.forEach(function(w){var x=z(w.disableResizing!==!0&&void 0,d!==!0&&void 0,!0);w.canResize=x,w.width=y.columnWidths[w.id]||w.originalWidth||w.width,w.isResizing=y.isResizingColumn===w.id,x&&(w.getResizerProps=C(v().getResizerProps,{instance:h(),header:w}))})};function su(a){var c=a.plugins,d=a.dispatch,v=a.autoResetResize,y=v===void 0||v,h=a.columns;D(c,["useAbsoluteLayout"],"useResizeColumns");var w=O(y);V(function(){w()&&d({type:u.resetResize})},[h]);var x=n.useCallback(function(){return d({type:u.resetResize})},[d]);Object.assign(a,{resetResizing:x})}var gn={position:"absolute",top:0},wo=function(a){a.getTableBodyProps.push(bt),a.getRowProps.push(bt),a.getHeaderGroupProps.push(bt),a.getFooterGroupProps.push(bt),a.getHeaderProps.push(function(c,d){var v=d.column;return[c,{style:l({},gn,{left:v.totalLeft+"px",width:v.totalWidth+"px"})}]}),a.getCellProps.push(function(c,d){var v=d.cell;return[c,{style:l({},gn,{left:v.column.totalLeft+"px",width:v.column.totalWidth+"px"})}]}),a.getFooterProps.push(function(c,d){var v=d.column;return[c,{style:l({},gn,{left:v.totalLeft+"px",width:v.totalWidth+"px"})}]})};wo.pluginName="useAbsoluteLayout";var bt=function(a,c){return[a,{style:{position:"relative",width:c.instance.totalColumnsWidth+"px"}}]},mn={display:"inline-block",boxSizing:"border-box"},hn=function(a,c){return[a,{style:{display:"flex",width:c.instance.totalColumnsWidth+"px"}}]},Co=function(a){a.getRowProps.push(hn),a.getHeaderGroupProps.push(hn),a.getFooterGroupProps.push(hn),a.getHeaderProps.push(function(c,d){var v=d.column;return[c,{style:l({},mn,{width:v.totalWidth+"px"})}]}),a.getCellProps.push(function(c,d){var v=d.cell;return[c,{style:l({},mn,{width:v.column.totalWidth+"px"})}]}),a.getFooterProps.push(function(c,d){var v=d.column;return[c,{style:l({},mn,{width:v.totalWidth+"px"})}]})};function So(a){a.getTableProps.push(uu),a.getRowProps.push(bn),a.getHeaderGroupProps.push(bn),a.getFooterGroupProps.push(bn),a.getHeaderProps.push(cu),a.getCellProps.push(du),a.getFooterProps.push(fu)}Co.pluginName="useBlockLayout",So.pluginName="useFlexLayout";var uu=function(a,c){return[a,{style:{minWidth:c.instance.totalColumnsMinWidth+"px"}}]},bn=function(a,c){return[a,{style:{display:"flex",flex:"1 0 auto",minWidth:c.instance.totalColumnsMinWidth+"px"}}]},cu=function(a,c){var d=c.column;return[a,{style:{boxSizing:"border-box",flex:d.totalFlexWidth?d.totalFlexWidth+" 0 auto":void 0,minWidth:d.totalMinWidth+"px",width:d.totalWidth+"px"}}]},du=function(a,c){var d=c.cell;return[a,{style:{boxSizing:"border-box",flex:d.column.totalFlexWidth+" 0 auto",minWidth:d.column.totalMinWidth+"px",width:d.column.totalWidth+"px"}}]},fu=function(a,c){var d=c.column;return[a,{style:{boxSizing:"border-box",flex:d.totalFlexWidth?d.totalFlexWidth+" 0 auto":void 0,minWidth:d.totalMinWidth+"px",width:d.totalWidth+"px"}}]};function xo(a){a.stateReducers.push(mu),a.getTableProps.push(pu),a.getHeaderProps.push(vu),a.getRowProps.push(gu)}u.columnStartResizing="columnStartResizing",u.columnResizing="columnResizing",u.columnDoneResizing="columnDoneResizing",u.resetResize="resetResize",xo.pluginName="useGridLayout";var pu=function(a,c){var d=c.instance;return[a,{style:{display:"grid",gridTemplateColumns:d.visibleColumns.map(function(v){var y;return d.state.gridLayout.columnWidths[v.id]?d.state.gridLayout.columnWidths[v.id]+"px":(y=d.state.columnResizing)!=null&&y.isResizingColumn?d.state.gridLayout.startWidths[v.id]+"px":typeof v.width=="number"?v.width+"px":v.width}).join(" ")}}]},vu=function(a,c){var d=c.column;return[a,{id:"header-cell-"+d.id,style:{position:"sticky",gridColumn:"span "+d.totalVisibleHeaderCount}}]},gu=function(a,c){var d=c.row;return d.isExpanded?[a,{style:{gridColumn:"1 / "+(d.cells.length+1)}}]:[a,{}]};function mu(a,c,d,v){if(c.type===u.init)return l({gridLayout:{columnWidths:{}}},a);if(c.type===u.resetResize)return l({},a,{gridLayout:{columnWidths:{}}});if(c.type===u.columnStartResizing){var y=c.columnId,h=c.headerIdWidths,w=yn(y);if(w!==void 0){var x=v.visibleColumns.reduce(function(L,ie){var U;return l({},L,((U={})[ie.id]=yn(ie.id),U))},{}),I=v.visibleColumns.reduce(function(L,ie){var U;return l({},L,((U={})[ie.id]=ie.minWidth,U))},{}),R=v.visibleColumns.reduce(function(L,ie){var U;return l({},L,((U={})[ie.id]=ie.maxWidth,U))},{}),F=h.map(function(L){var ie=L[0];return[ie,yn(ie)]});return l({},a,{gridLayout:l({},a.gridLayout,{startWidths:x,minWidths:I,maxWidths:R,headerIdGridWidths:F,columnWidth:w})})}return a}if(c.type===u.columnResizing){var T=c.clientX,M=a.columnResizing.startX,E=a.gridLayout,N=E.columnWidth,k=E.minWidths,H=E.maxWidths,X=E.headerIdGridWidths,le=(T-M)/N,_={};return(X===void 0?[]:X).forEach(function(L){var ie=L[0],U=L[1];_[ie]=Math.min(Math.max(k[ie],U+U*le),H[ie])}),l({},a,{gridLayout:l({},a.gridLayout,{columnWidths:l({},a.gridLayout.columnWidths,{},_)})})}return c.type===u.columnDoneResizing?l({},a,{gridLayout:l({},a.gridLayout,{startWidths:{},minWidths:{},maxWidths:{}})}):void 0}function yn(a){var c,d=(c=document.getElementById("header-cell-"+a))==null?void 0:c.offsetWidth;if(d!==void 0)return d}t._UNSTABLE_usePivotColumns=fo,t.actions=u,t.defaultColumn=f,t.defaultGroupByFn=oo,t.defaultOrderByFn=uo,t.defaultRenderer=m,t.emptyRenderer=g,t.ensurePluginOrder=D,t.flexRender=Y,t.functionalUpdate=B,t.loopHooks=P,t.makePropGetter=C,t.makeRenderer=q,t.reduceHooks=S,t.safeUseLayoutEffect=A,t.useAbsoluteLayout=wo,t.useAsyncDebounce=function(a,c){c===void 0&&(c=0);var d=n.useRef({}),v=O(a),y=O(c);return n.useCallback(function(){var h=i(regeneratorRuntime.mark(function w(){var x,I,R,F=arguments;return regeneratorRuntime.wrap(function(T){for(;;)switch(T.prev=T.next){case 0:for(x=F.length,I=new Array(x),R=0;R<x;R++)I[R]=F[R];return d.current.promise||(d.current.promise=new Promise(function(M,E){d.current.resolve=M,d.current.reject=E})),d.current.timeout&&clearTimeout(d.current.timeout),d.current.timeout=setTimeout(i(regeneratorRuntime.mark(function M(){return regeneratorRuntime.wrap(function(E){for(;;)switch(E.prev=E.next){case 0:return delete d.current.timeout,E.prev=1,E.t0=d.current,E.next=5,v().apply(void 0,I);case 5:E.t1=E.sent,E.t0.resolve.call(E.t0,E.t1),E.next=12;break;case 9:E.prev=9,E.t2=E.catch(1),d.current.reject(E.t2);case 12:return E.prev=12,delete d.current.promise,E.finish(12);case 15:case"end":return E.stop()}},M,null,[[1,9,12,15]])})),y()),T.abrupt("return",d.current.promise);case 5:case"end":return T.stop()}},w)}));return function(){return h.apply(this,arguments)}}(),[v,y])},t.useBlockLayout=Co,t.useColumnOrder=bo,t.useExpanded=ja,t.useFilters=eo,t.useFlexLayout=So,t.useGetLatest=O,t.useGlobalFilter=ro,t.useGridLayout=xo,t.useGroupBy=ao,t.useMountedLayoutEffect=V,t.usePagination=co,t.useResizeColumns=yo,t.useRowSelect=vo,t.useRowState=ho,t.useSortBy=so,t.useTable=function(a){for(var c=arguments.length,d=new Array(c>1?c-1:0),v=1;v<c;v++)d[v-1]=arguments[v];a=Ha(a),d=[vr].concat(d);var y=n.useRef({}),h=O(y.current);Object.assign(h(),l({},a,{plugins:d,hooks:Ue()})),d.filter(Boolean).forEach(function(ee){ee(h().hooks)});var w=O(h().hooks);h().getHooks=w,delete h().hooks,Object.assign(h(),S(w().useOptions,Ha(a)));var x=h(),I=x.data,R=x.columns,F=x.initialState,T=x.defaultColumn,M=x.getSubRows,E=x.getRowId,N=x.stateReducer,k=x.useControlledState,H=O(N),X=n.useCallback(function(ee,Pe){if(!Pe.type)throw console.info({action:Pe}),new Error("Unknown Action 👆");return[].concat(w().stateReducers,Array.isArray(H())?H():[H()]).reduce(function(Te,_e){return _e(Te,Pe,ee,h())||Te},ee)},[w,H,h]),le=n.useReducer(X,void 0,function(){return X(F,{type:u.init})}),_=le[0],L=le[1],ie=S([].concat(w().useControlledState,[k]),_,{instance:h()});Object.assign(h(),{state:ie,dispatch:L});var U=n.useMemo(function(){return se(S(w().columns,R,{instance:h()}))},[w,h,R].concat(S(w().columnsDeps,[],{instance:h()})));h().columns=U;var ne=n.useMemo(function(){return S(w().allColumns,Q(U),{instance:h()}).map(be)},[U,w,h].concat(S(w().allColumnsDeps,[],{instance:h()})));h().allColumns=ne;var ce=n.useMemo(function(){for(var ee=[],Pe=[],Te={},_e=[].concat(ne);_e.length;){var Ge=_e.shift();fs({data:I,rows:ee,flatRows:Pe,rowsById:Te,column:Ge,getRowId:E,getSubRows:M,accessValueHooks:w().accessValue,getInstance:h})}return[ee,Pe,Te]},[ne,I,E,M,w,h]),Z=ce[0],te=ce[1],ge=ce[2];Object.assign(h(),{rows:Z,initialRows:[].concat(Z),flatRows:te,rowsById:ge}),P(w().useInstanceAfterData,h());var oe=n.useMemo(function(){return S(w().visibleColumns,ne,{instance:h()}).map(function(ee){return pe(ee,T)})},[w,ne,h,T].concat(S(w().visibleColumnsDeps,[],{instance:h()})));ne=n.useMemo(function(){var ee=[].concat(oe);return ne.forEach(function(Pe){ee.find(function(Te){return Te.id===Pe.id})||ee.push(Pe)}),ee},[ne,oe]),h().allColumns=ne;var ve=n.useMemo(function(){return S(w().headerGroups,Le(oe,T),h())},[w,oe,T,h].concat(S(w().headerGroupsDeps,[],{instance:h()})));h().headerGroups=ve;var fe=n.useMemo(function(){return ve.length?ve[0].headers:[]},[ve]);h().headers=fe,h().flatHeaders=ve.reduce(function(ee,Pe){return[].concat(ee,Pe.headers)},[]),P(w().useInstanceBeforeDimensions,h());var ye=oe.filter(function(ee){return ee.isVisible}).map(function(ee){return ee.id}).sort().join("_");oe=n.useMemo(function(){return oe.filter(function(ee){return ee.isVisible})},[oe,ye]),h().visibleColumns=oe;var K=za(fe),de=K[0],Be=K[1],qe=K[2];return h().totalColumnsMinWidth=de,h().totalColumnsWidth=Be,h().totalColumnsMaxWidth=qe,P(w().useInstance,h()),[].concat(h().flatHeaders,h().allColumns).forEach(function(ee){ee.render=q(h(),ee),ee.getHeaderProps=C(w().getHeaderProps,{instance:h(),column:ee}),ee.getFooterProps=C(w().getFooterProps,{instance:h(),column:ee})}),h().headerGroups=n.useMemo(function(){return ve.filter(function(ee,Pe){return ee.headers=ee.headers.filter(function(Te){return Te.headers?function _e(Ge){return Ge.filter(function(br){return br.headers?_e(br.headers):br.isVisible}).length}(Te.headers):Te.isVisible}),!!ee.headers.length&&(ee.getHeaderGroupProps=C(w().getHeaderGroupProps,{instance:h(),headerGroup:ee,index:Pe}),ee.getFooterGroupProps=C(w().getFooterGroupProps,{instance:h(),headerGroup:ee,index:Pe}),!0)})},[ve,h,w]),h().footerGroups=[].concat(h().headerGroups).reverse(),h().prepareRow=n.useCallback(function(ee){ee.getRowProps=C(w().getRowProps,{instance:h(),row:ee}),ee.allCells=ne.map(function(Pe){var Te=ee.values[Pe.id],_e={column:Pe,row:ee,value:Te};return _e.getCellProps=C(w().getCellProps,{instance:h(),cell:_e}),_e.render=q(h(),Pe,{row:ee,cell:_e,value:Te}),_e}),ee.cells=oe.map(function(Pe){return ee.allCells.find(function(Te){return Te.column.id===Pe.id})}),P(w().prepareRow,ee,{instance:h()})},[w,h,ne,oe]),h().getTableProps=C(w().getTableProps,{instance:h()}),h().getTableBodyProps=C(w().getTableBodyProps,{instance:h()}),P(w().useFinalInstance,h()),h()},Object.defineProperty(t,"__esModule",{value:!0})})})(Ld,Lt);(function(e){e.exports=Lt})(Fd);const Wd="_tr_12ddc_2",Gd="_th_12ddc_6",kd="_btnSection_12ddc_18",$d="_td_12ddc_29",Hd="_overlay_12ddc_44",zd="_modal_12ddc_48",jd="_table_12ddc_52",Vd="_odd_12ddc_56",Ud="_center_12ddc_61",qd="_sortIconContainer_12ddc_66",_d="_rotate180_12ddc_72",yr={tr:Wd,th:Gd,btnSection:kd,break:"_break_12ddc_22",td:$d,overlay:Hd,modal:zd,table:jd,odd:Vd,center:Ud,sortIconContainer:qd,rotate180:_d},Xd="_overlay_1cbjw_1",Kd="_cnt_1cbjw_5",Yd="_afterOpen_1cbjw_15",Jd="_btngrp_1cbjw_20",It={overlay:Xd,cnt:Kd,afterOpen:Yd,btngrp:Jd},{useRef:Qd,useCallback:Zd,useMemo:ef}=re;function qn({confirm:e="close_all_confirm",isOpen:r,onRequestClose:t,primaryButtonOnTap:n}){const{t:o}=it(),i=Qd(null),l=Zd(()=>{i.current.focus()},[]),s=ef(()=>({base:Ir(Oo.content,It.cnt),afterOpen:It.afterOpen,beforeClose:""}),[]);return Re(Pu,{isOpen:r,onRequestClose:t,onAfterOpen:l,className:s,overlayClassName:Ir(Oo.overlay,It.overlay),children:[j("p",{children:o(e)}),Re("div",{className:It.btngrp,children:[j(Tt,{onClick:n,ref:i,children:o("close_all_confirm_yes")}),j("div",{style:{width:20}}),j(Tt,{onClick:t,children:o("close_all_confirm_no")})]})]})}const rf={id:"id",desc:!0};function tf({data:e,columns:r,hiddenColumns:t,apiConfig:n}){const[o,i]=G.useState(""),[l,s]=G.useState(!1),u={sortBy:JSON.parse(localStorage.getItem("tableSortBy"))||[rf],hiddenColumns:t},m=Ft.useTable({columns:r,data:e,initialState:u,autoResetSortBy:!1},Ft.useSortBy),{getTableProps:g,setHiddenColumns:f,headerGroups:b,rows:C,prepareRow:S}=m,P=m.state;G.useEffect(()=>{f(t)},[f,t]);const{t:D,i18n:B}=it();let O;B.language==="zh-CN"?O=Qc:B.language==="zh-TW"?O=Nd:O=$u;const A=()=>{Li(n,o),s(!1)},V=Y=>{i(Y),s(!0)},q=(Y,se)=>{switch(Y.column.id){case"ctrl":return j(ec,{style:{cursor:"pointer"},onClick:()=>V(Y.row.original.id)});case"start":return ku(Y.value,0,{locale:se});case"download":case"upload":return Ao(Y.value);case"downloadSpeedCurr":case"uploadSpeedCurr":return Ao(Y.value)+"/s";default:return Y.value}};return G.useEffect(()=>{localStorage.setItem("tableSortBy",JSON.stringify(P.sortBy))},[P.sortBy]),Re("div",{style:{marginTop:"5px"},children:[Re("table",{...g(),className:Ir(yr.table,"connections-table"),children:[j("thead",{children:b.map((Y,se)=>G.createElement("tr",{...Y.getHeaderGroupProps(),className:yr.tr,key:se},Y.headers.map(Q=>Re("th",{...Q.getHeaderProps(Q.getSortByToggleProps()),className:yr.th,children:[j("span",{children:D(Q.render("Header"))}),Q.id!=="ctrl"?j("span",{className:yr.sortIconContainer,children:Q.isSorted?j(Lu,{size:16,className:Q.isSortedDesc?"":yr.rotate180}):null}):null]}))))}),j("tbody",{children:C.map((Y,se)=>(S(Y),j("tr",{className:yr.tr,children:Y.cells.map(Q=>j("td",{...Q.getCellProps(),className:Ir(yr.td,se%2===0?yr.odd:!1,Q.column.id),children:q(Q,O)}))},se)))})]}),j(qn,{confirm:"disconnect",isOpen:l,onRequestClose:()=>s(!1),primaryButtonOnTap:A})]})}const nf=e=>({apiConfig:Wi(e)}),af=Fi(nf)(tf);function zo(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,n)}return t}function jo(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?zo(Object(t),!0).forEach(function(n){Ru(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):zo(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Ye(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Vo=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),In=function(){return Math.random().toString(36).substring(7).split("").join(".")},Uo={INIT:"@@redux/INIT"+In(),REPLACE:"@@redux/REPLACE"+In(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+In()}};function of(e){if(typeof e!="object"||e===null)return!1;for(var r=e;Object.getPrototypeOf(r)!==null;)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(e)===r}function Ui(e,r,t){var n;if(typeof r=="function"&&typeof t=="function"||typeof t=="function"&&typeof arguments[3]=="function")throw new Error(Ye(0));if(typeof r=="function"&&typeof t>"u"&&(t=r,r=void 0),typeof t<"u"){if(typeof t!="function")throw new Error(Ye(1));return t(Ui)(e,r)}if(typeof e!="function")throw new Error(Ye(2));var o=e,i=r,l=[],s=l,p=!1;function u(){s===l&&(s=l.slice())}function m(){if(p)throw new Error(Ye(3));return i}function g(S){if(typeof S!="function")throw new Error(Ye(4));if(p)throw new Error(Ye(5));var P=!0;return u(),s.push(S),function(){if(P){if(p)throw new Error(Ye(6));P=!1,u();var B=s.indexOf(S);s.splice(B,1),l=null}}}function f(S){if(!of(S))throw new Error(Ye(7));if(typeof S.type>"u")throw new Error(Ye(8));if(p)throw new Error(Ye(9));try{p=!0,i=o(i,S)}finally{p=!1}for(var P=l=s,D=0;D<P.length;D++){var B=P[D];B()}return S}function b(S){if(typeof S!="function")throw new Error(Ye(10));o=S,f({type:Uo.REPLACE})}function C(){var S,P=g;return S={subscribe:function(B){if(typeof B!="object"||B===null)throw new Error(Ye(11));function O(){B.next&&B.next(m())}O();var A=P(O);return{unsubscribe:A}}},S[Vo]=function(){return this},S}return f({type:Uo.INIT}),n={dispatch:f,subscribe:g,getState:m,replaceReducer:b},n[Vo]=C,n}function qo(e,r){return function(){return r(e.apply(this,arguments))}}function _o(e,r){if(typeof e=="function")return qo(e,r);if(typeof e!="object"||e===null)throw new Error(Ye(16));var t={};for(var n in e){var o=e[n];typeof o=="function"&&(t[n]=qo(o,r))}return t}function qi(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.length===0?function(n){return n}:r.length===1?r[0]:r.reduce(function(n,o){return function(){return n(o.apply(void 0,arguments))}})}function lf(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(n){return function(){var o=n.apply(void 0,arguments),i=function(){throw new Error(Ye(15))},l={getState:o.getState,dispatch:function(){return i.apply(void 0,arguments)}},s=r.map(function(p){return p(l)});return i=qi.apply(void 0,s)(o.dispatch),jo(jo({},o),{},{dispatch:i})}}}var _i=re.createContext(null);function sf(e){e()}var Xi=sf,uf=function(r){return Xi=r},cf=function(){return Xi};function df(){var e=cf(),r=null,t=null;return{clear:function(){r=null,t=null},notify:function(){e(function(){for(var o=r;o;)o.callback(),o=o.next})},get:function(){for(var o=[],i=r;i;)o.push(i),i=i.next;return o},subscribe:function(o){var i=!0,l=t={callback:o,next:null,prev:t};return l.prev?l.prev.next=l:r=l,function(){!i||r===null||(i=!1,l.next?l.next.prev=l.prev:t=l.prev,l.prev?l.prev.next=l.next:r=l.next)}}}}var Xo={notify:function(){},get:function(){return[]}};function Ki(e,r){var t,n=Xo;function o(g){return p(),n.subscribe(g)}function i(){n.notify()}function l(){m.onStateChange&&m.onStateChange()}function s(){return Boolean(t)}function p(){t||(t=r?r.addNestedSub(l):e.subscribe(l),n=df())}function u(){t&&(t(),t=void 0,n.clear(),n=Xo)}var m={addNestedSub:o,notifyNestedSubs:i,handleChangeWrapper:l,isSubscribed:s,trySubscribe:p,tryUnsubscribe:u,getListeners:function(){return n}};return m}var Yi=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?G.useLayoutEffect:G.useEffect;function ff(e){var r=e.store,t=e.context,n=e.children,o=G.useMemo(function(){var s=Ki(r);return{store:r,subscription:s}},[r]),i=G.useMemo(function(){return r.getState()},[r]);Yi(function(){var s=o.subscription;return s.onStateChange=s.notifyNestedSubs,s.trySubscribe(),i!==r.getState()&&s.notifyNestedSubs(),function(){s.tryUnsubscribe(),s.onStateChange=null}},[o,i]);var l=t||_i;return re.createElement(l.Provider,{value:o},n)}var _n={},pf={get exports(){return _n},set exports(e){_n=e}},he={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var He=typeof Symbol=="function"&&Symbol.for,fa=He?Symbol.for("react.element"):60103,pa=He?Symbol.for("react.portal"):60106,Xt=He?Symbol.for("react.fragment"):60107,Kt=He?Symbol.for("react.strict_mode"):60108,Yt=He?Symbol.for("react.profiler"):60114,Jt=He?Symbol.for("react.provider"):60109,Qt=He?Symbol.for("react.context"):60110,va=He?Symbol.for("react.async_mode"):60111,Zt=He?Symbol.for("react.concurrent_mode"):60111,en=He?Symbol.for("react.forward_ref"):60112,rn=He?Symbol.for("react.suspense"):60113,vf=He?Symbol.for("react.suspense_list"):60120,tn=He?Symbol.for("react.memo"):60115,nn=He?Symbol.for("react.lazy"):60116,gf=He?Symbol.for("react.block"):60121,mf=He?Symbol.for("react.fundamental"):60117,hf=He?Symbol.for("react.responder"):60118,bf=He?Symbol.for("react.scope"):60119;function tr(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case fa:switch(e=e.type,e){case va:case Zt:case Xt:case Yt:case Kt:case rn:return e;default:switch(e=e&&e.$$typeof,e){case Qt:case en:case nn:case tn:case Jt:return e;default:return r}}case pa:return r}}}function Ji(e){return tr(e)===Zt}he.AsyncMode=va;he.ConcurrentMode=Zt;he.ContextConsumer=Qt;he.ContextProvider=Jt;he.Element=fa;he.ForwardRef=en;he.Fragment=Xt;he.Lazy=nn;he.Memo=tn;he.Portal=pa;he.Profiler=Yt;he.StrictMode=Kt;he.Suspense=rn;he.isAsyncMode=function(e){return Ji(e)||tr(e)===va};he.isConcurrentMode=Ji;he.isContextConsumer=function(e){return tr(e)===Qt};he.isContextProvider=function(e){return tr(e)===Jt};he.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===fa};he.isForwardRef=function(e){return tr(e)===en};he.isFragment=function(e){return tr(e)===Xt};he.isLazy=function(e){return tr(e)===nn};he.isMemo=function(e){return tr(e)===tn};he.isPortal=function(e){return tr(e)===pa};he.isProfiler=function(e){return tr(e)===Yt};he.isStrictMode=function(e){return tr(e)===Kt};he.isSuspense=function(e){return tr(e)===rn};he.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Xt||e===Zt||e===Yt||e===Kt||e===rn||e===vf||typeof e=="object"&&e!==null&&(e.$$typeof===nn||e.$$typeof===tn||e.$$typeof===Jt||e.$$typeof===Qt||e.$$typeof===en||e.$$typeof===mf||e.$$typeof===hf||e.$$typeof===bf||e.$$typeof===gf)};he.typeOf=tr;(function(e){e.exports=he})(pf);var ga=_n,yf={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},wf={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Cf={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Qi={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},ma={};ma[ga.ForwardRef]=Cf;ma[ga.Memo]=Qi;function Ko(e){return ga.isMemo(e)?Qi:ma[e.$$typeof]||yf}var Sf=Object.defineProperty,xf=Object.getOwnPropertyNames,Yo=Object.getOwnPropertySymbols,If=Object.getOwnPropertyDescriptor,Pf=Object.getPrototypeOf,Jo=Object.prototype;function Zi(e,r,t){if(typeof r!="string"){if(Jo){var n=Pf(r);n&&n!==Jo&&Zi(e,n,t)}var o=xf(r);Yo&&(o=o.concat(Yo(r)));for(var i=Ko(e),l=Ko(r),s=0;s<o.length;++s){var p=o[s];if(!wf[p]&&!(t&&t[p])&&!(l&&l[p])&&!(i&&i[p])){var u=If(r,p);try{Sf(e,p,u)}catch{}}}}return e}var Qo=Zi,Xn={},Rf={get exports(){return Xn},set exports(e){Xn=e}},Ce={};/** @license React v17.0.2
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var an=60103,on=60106,lt=60107,st=60108,ut=60114,ct=60109,dt=60110,ft=60112,pt=60113,ha=60120,vt=60115,gt=60116,el=60121,rl=60122,tl=60117,nl=60129,al=60131;if(typeof Symbol=="function"&&Symbol.for){var ze=Symbol.for;an=ze("react.element"),on=ze("react.portal"),lt=ze("react.fragment"),st=ze("react.strict_mode"),ut=ze("react.profiler"),ct=ze("react.provider"),dt=ze("react.context"),ft=ze("react.forward_ref"),pt=ze("react.suspense"),ha=ze("react.suspense_list"),vt=ze("react.memo"),gt=ze("react.lazy"),el=ze("react.block"),rl=ze("react.server.block"),tl=ze("react.fundamental"),nl=ze("react.debug_trace_mode"),al=ze("react.legacy_hidden")}function pr(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case an:switch(e=e.type,e){case lt:case ut:case st:case pt:case ha:return e;default:switch(e=e&&e.$$typeof,e){case dt:case ft:case gt:case vt:case ct:return e;default:return r}}case on:return r}}}var Df=ct,Ef=an,Bf=ft,Of=lt,Af=gt,Tf=vt,Mf=on,Nf=ut,Ff=st,Lf=pt;Ce.ContextConsumer=dt;Ce.ContextProvider=Df;Ce.Element=Ef;Ce.ForwardRef=Bf;Ce.Fragment=Of;Ce.Lazy=Af;Ce.Memo=Tf;Ce.Portal=Mf;Ce.Profiler=Nf;Ce.StrictMode=Ff;Ce.Suspense=Lf;Ce.isAsyncMode=function(){return!1};Ce.isConcurrentMode=function(){return!1};Ce.isContextConsumer=function(e){return pr(e)===dt};Ce.isContextProvider=function(e){return pr(e)===ct};Ce.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===an};Ce.isForwardRef=function(e){return pr(e)===ft};Ce.isFragment=function(e){return pr(e)===lt};Ce.isLazy=function(e){return pr(e)===gt};Ce.isMemo=function(e){return pr(e)===vt};Ce.isPortal=function(e){return pr(e)===on};Ce.isProfiler=function(e){return pr(e)===ut};Ce.isStrictMode=function(e){return pr(e)===st};Ce.isSuspense=function(e){return pr(e)===pt};Ce.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===lt||e===ut||e===nl||e===st||e===pt||e===ha||e===al||typeof e=="object"&&e!==null&&(e.$$typeof===gt||e.$$typeof===vt||e.$$typeof===ct||e.$$typeof===dt||e.$$typeof===ft||e.$$typeof===tl||e.$$typeof===el||e[0]===rl)};Ce.typeOf=pr;(function(e){e.exports=Ce})(Rf);var Wf=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],Gf=["reactReduxForwardedRef"],kf=[],$f=[null,null];function Hf(e,r){var t=e[1];return[r.payload,t+1]}function Zo(e,r,t){Yi(function(){return e.apply(void 0,r)},t)}function zf(e,r,t,n,o,i,l){e.current=n,r.current=o,t.current=!1,i.current&&(i.current=null,l())}function jf(e,r,t,n,o,i,l,s,p,u){if(e){var m=!1,g=null,f=function(){if(!m){var S=r.getState(),P,D;try{P=n(S,o.current)}catch(B){D=B,g=B}D||(g=null),P===i.current?l.current||p():(i.current=P,s.current=P,l.current=!0,u({type:"STORE_UPDATED",payload:{error:D}}))}};t.onStateChange=f,t.trySubscribe(),f();var b=function(){if(m=!0,t.tryUnsubscribe(),t.onStateChange=null,g)throw g};return b}}var Vf=function(){return[null,0]};function Uf(e,r){r===void 0&&(r={});var t=r,n=t.getDisplayName,o=n===void 0?function(O){return"ConnectAdvanced("+O+")"}:n,i=t.methodName,l=i===void 0?"connectAdvanced":i,s=t.renderCountProp,p=s===void 0?void 0:s,u=t.shouldHandleStateChanges,m=u===void 0?!0:u,g=t.storeKey,f=g===void 0?"store":g;t.withRef;var b=t.forwardRef,C=b===void 0?!1:b,S=t.context,P=S===void 0?_i:S,D=Mt(t,Wf),B=P;return function(A){var V=A.displayName||A.name||"Component",q=o(V),Y=ae({},D,{getDisplayName:o,methodName:l,renderCountProp:p,shouldHandleStateChanges:m,storeKey:f,displayName:q,wrappedComponentName:V,WrappedComponent:A}),se=D.pure;function Q(z){return e(z.dispatch,Y)}var be=se?G.useMemo:function(z){return z()};function pe(z){var ue=G.useMemo(function(){var Er=z.reactReduxForwardedRef,Ur=Mt(z,Gf);return[z.context,Er,Ur]},[z]),Ie=ue[0],Se=ue[1],we=ue[2],Ee=G.useMemo(function(){return Ie&&Ie.Consumer&&Xn.isContextConsumer(re.createElement(Ie.Consumer,null))?Ie:B},[Ie,B]),De=G.useContext(Ee),Oe=Boolean(z.store)&&Boolean(z.store.getState)&&Boolean(z.store.dispatch);Boolean(De)&&Boolean(De.store);var Fe=Oe?z.store:De.store,ur=G.useMemo(function(){return Q(Fe)},[Fe]),Ve=G.useMemo(function(){if(!m)return $f;var Er=Ki(Fe,Oe?null:De.subscription),Ur=Er.notifyNestedSubs.bind(Er);return[Er,Ur]},[Fe,Oe,De]),Ae=Ve[0],We=Ve[1],cr=G.useMemo(function(){return Oe?De:ae({},De,{subscription:Ae})},[Oe,De,Ae]),nr=G.useReducer(Hf,kf,Vf),dr=nr[0],Ke=dr[0],xe=nr[1];if(Ke&&Ke.error)throw Ke.error;var Ue=G.useRef(),vr=G.useRef(we),Je=G.useRef(),Dr=G.useRef(!1),ar=be(function(){return Je.current&&we===vr.current?Je.current:ur(Fe.getState(),we)},[Fe,Ke,we]);Zo(zf,[vr,Ue,Dr,we,ar,Je,We]),Zo(jf,[m,Fe,Ae,ur,vr,Ue,Dr,Je,We,xe],[Fe,Ae,ur]);var Nr=G.useMemo(function(){return re.createElement(A,ae({},ar,{ref:Se}))},[Se,A,ar]),Vr=G.useMemo(function(){return m?re.createElement(Ee.Provider,{value:cr},Nr):Nr},[Ee,Nr,cr]);return Vr}var Le=se?re.memo(pe):pe;if(Le.WrappedComponent=A,Le.displayName=pe.displayName=q,C){var $=re.forwardRef(function(ue,Ie){return re.createElement(Le,ae({},ue,{reactReduxForwardedRef:Ie}))});return $.displayName=q,$.WrappedComponent=A,Qo($,A)}return Qo(Le,A)}}function ei(e,r){return e===r?e!==0||r!==0||1/e===1/r:e!==e&&r!==r}function Pn(e,r){if(ei(e,r))return!0;if(typeof e!="object"||e===null||typeof r!="object"||r===null)return!1;var t=Object.keys(e),n=Object.keys(r);if(t.length!==n.length)return!1;for(var o=0;o<t.length;o++)if(!Object.prototype.hasOwnProperty.call(r,t[o])||!ei(e[t[o]],r[t[o]]))return!1;return!0}function qf(e,r){var t={},n=function(l){var s=e[l];typeof s=="function"&&(t[l]=function(){return r(s.apply(void 0,arguments))})};for(var o in e)n(o);return t}function ba(e){return function(t,n){var o=e(t,n);function i(){return o}return i.dependsOnOwnProps=!1,i}}function ri(e){return e.dependsOnOwnProps!==null&&e.dependsOnOwnProps!==void 0?Boolean(e.dependsOnOwnProps):e.length!==1}function ol(e,r){return function(n,o){o.displayName;var i=function(s,p){return i.dependsOnOwnProps?i.mapToProps(s,p):i.mapToProps(s)};return i.dependsOnOwnProps=!0,i.mapToProps=function(s,p){i.mapToProps=e,i.dependsOnOwnProps=ri(e);var u=i(s,p);return typeof u=="function"&&(i.mapToProps=u,i.dependsOnOwnProps=ri(u),u=i(s,p)),u},i}}function _f(e){return typeof e=="function"?ol(e):void 0}function Xf(e){return e?void 0:ba(function(r){return{dispatch:r}})}function Kf(e){return e&&typeof e=="object"?ba(function(r){return qf(e,r)}):void 0}const Yf=[_f,Xf,Kf];function Jf(e){return typeof e=="function"?ol(e):void 0}function Qf(e){return e?void 0:ba(function(){return{}})}const Zf=[Jf,Qf];function ep(e,r,t){return ae({},t,e,r)}function rp(e){return function(t,n){n.displayName;var o=n.pure,i=n.areMergedPropsEqual,l=!1,s;return function(u,m,g){var f=e(u,m,g);return l?(!o||!i(f,s))&&(s=f):(l=!0,s=f),s}}}function tp(e){return typeof e=="function"?rp(e):void 0}function np(e){return e?void 0:function(){return ep}}const ap=[tp,np];var op=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function ip(e,r,t,n){return function(i,l){return t(e(i,l),r(n,l),l)}}function lp(e,r,t,n,o){var i=o.areStatesEqual,l=o.areOwnPropsEqual,s=o.areStatePropsEqual,p=!1,u,m,g,f,b;function C(O,A){return u=O,m=A,g=e(u,m),f=r(n,m),b=t(g,f,m),p=!0,b}function S(){return g=e(u,m),r.dependsOnOwnProps&&(f=r(n,m)),b=t(g,f,m),b}function P(){return e.dependsOnOwnProps&&(g=e(u,m)),r.dependsOnOwnProps&&(f=r(n,m)),b=t(g,f,m),b}function D(){var O=e(u,m),A=!s(O,g);return g=O,A&&(b=t(g,f,m)),b}function B(O,A){var V=!l(A,m),q=!i(O,u,A,m);return u=O,m=A,V&&q?S():V?P():q?D():b}return function(A,V){return p?B(A,V):C(A,V)}}function sp(e,r){var t=r.initMapStateToProps,n=r.initMapDispatchToProps,o=r.initMergeProps,i=Mt(r,op),l=t(e,i),s=n(e,i),p=o(e,i),u=i.pure?lp:ip;return u(l,s,p,e,i)}var up=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function Rn(e,r,t){for(var n=r.length-1;n>=0;n--){var o=r[n](e);if(o)return o}return function(i,l){throw new Error("Invalid value of type "+typeof e+" for "+t+" argument when connecting component "+l.wrappedComponentName+".")}}function cp(e,r){return e===r}function dp(e){var r=e===void 0?{}:e,t=r.connectHOC,n=t===void 0?Uf:t,o=r.mapStateToPropsFactories,i=o===void 0?Zf:o,l=r.mapDispatchToPropsFactories,s=l===void 0?Yf:l,p=r.mergePropsFactories,u=p===void 0?ap:p,m=r.selectorFactory,g=m===void 0?sp:m;return function(b,C,S,P){P===void 0&&(P={});var D=P,B=D.pure,O=B===void 0?!0:B,A=D.areStatesEqual,V=A===void 0?cp:A,q=D.areOwnPropsEqual,Y=q===void 0?Pn:q,se=D.areStatePropsEqual,Q=se===void 0?Pn:se,be=D.areMergedPropsEqual,pe=be===void 0?Pn:be,Le=Mt(D,up),$=Rn(b,i,"mapStateToProps"),z=Rn(C,s,"mapDispatchToProps"),ue=Rn(S,u,"mergeProps");return n(g,ae({methodName:"connect",getDisplayName:function(Se){return"Connect("+Se+")"},shouldHandleStateChanges:Boolean(b),initMapStateToProps:$,initMapDispatchToProps:z,initMergeProps:ue,pure:O,areStatesEqual:V,areOwnPropsEqual:Y,areStatePropsEqual:Q,areMergedPropsEqual:pe},Le))}}const il=dp();uf(Du.unstable_batchedUpdates);function fp(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(e[t]!==r[t])return!1;return!0}function ll(e,r){var t=G.useState(function(){return{inputs:r,result:e()}})[0],n=G.useRef(!0),o=G.useRef(t),i=n.current||Boolean(r&&o.current.inputs&&fp(r,o.current.inputs)),l=i?o.current:{inputs:r,result:e()};return G.useEffect(function(){n.current=!1,o.current=l},[l]),l.result}function pp(e,r){return ll(function(){return e},r)}var me=ll,J=pp,vp=!0,Dn="Invariant failed";function gp(e,r){if(!e){if(vp)throw new Error(Dn);var t=typeof r=="function"?r():r,n=t?"".concat(Dn,": ").concat(t):Dn;throw new Error(n)}}var fr=function(r){var t=r.top,n=r.right,o=r.bottom,i=r.left,l=n-i,s=o-t,p={top:t,right:n,bottom:o,left:i,width:l,height:s,x:i,y:t,center:{x:(n+i)/2,y:(o+t)/2}};return p},ya=function(r,t){return{top:r.top-t.top,left:r.left-t.left,bottom:r.bottom+t.bottom,right:r.right+t.right}},ti=function(r,t){return{top:r.top+t.top,left:r.left+t.left,bottom:r.bottom-t.bottom,right:r.right-t.right}},mp=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},En={top:0,right:0,bottom:0,left:0},wa=function(r){var t=r.borderBox,n=r.margin,o=n===void 0?En:n,i=r.border,l=i===void 0?En:i,s=r.padding,p=s===void 0?En:s,u=fr(ya(t,o)),m=fr(ti(t,l)),g=fr(ti(m,p));return{marginBox:u,borderBox:fr(t),paddingBox:m,contentBox:g,margin:o,border:l,padding:p}},ir=function(r){var t=r.slice(0,-2),n=r.slice(-2);if(n!=="px")return 0;var o=Number(t);return isNaN(o)&&gp(!1),o},hp=function(){return{x:window.pageXOffset,y:window.pageYOffset}},Wt=function(r,t){var n=r.borderBox,o=r.border,i=r.margin,l=r.padding,s=mp(n,t);return wa({borderBox:s,border:o,margin:i,padding:l})},Gt=function(r,t){return t===void 0&&(t=hp()),Wt(r,t)},sl=function(r,t){var n={top:ir(t.marginTop),right:ir(t.marginRight),bottom:ir(t.marginBottom),left:ir(t.marginLeft)},o={top:ir(t.paddingTop),right:ir(t.paddingRight),bottom:ir(t.paddingBottom),left:ir(t.paddingLeft)},i={top:ir(t.borderTopWidth),right:ir(t.borderRightWidth),bottom:ir(t.borderBottomWidth),left:ir(t.borderLeftWidth)};return wa({borderBox:r,margin:n,padding:o,border:i})},ul=function(r){var t=r.getBoundingClientRect(),n=window.getComputedStyle(r);return sl(t,n)},bp=function(r){var t=[],n=null,o=function(){for(var l=arguments.length,s=new Array(l),p=0;p<l;p++)s[p]=arguments[p];t=s,!n&&(n=requestAnimationFrame(function(){n=null,r.apply(void 0,t)}))};return o.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},o};const rt=bp;function cl(e,r){}cl.bind(null,"warn");cl.bind(null,"error");function Cr(){}function yp(e,r){return ae({},e,{},r)}function lr(e,r,t){var n=r.map(function(o){var i=yp(t,o.options);return e.addEventListener(o.eventName,o.fn,i),function(){e.removeEventListener(o.eventName,o.fn,i)}});return function(){n.forEach(function(i){i()})}}var wp="Invariant failed";function kt(e){this.message=e}kt.prototype.toString=function(){return this.message};function W(e,r){if(!e)throw new kt(wp)}var Cp=function(e){Gi(r,e);function r(){for(var n,o=arguments.length,i=new Array(o),l=0;l<o;l++)i[l]=arguments[l];return n=e.call.apply(e,[this].concat(i))||this,n.callbacks=null,n.unbind=Cr,n.onWindowError=function(s){var p=n.getCallbacks();p.isDragging()&&p.tryAbort();var u=s.error;u instanceof kt&&s.preventDefault()},n.getCallbacks=function(){if(!n.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return n.callbacks},n.setCallbacks=function(s){n.callbacks=s},n}var t=r.prototype;return t.componentDidMount=function(){this.unbind=lr(window,[{eventName:"error",fn:this.onWindowError}])},t.componentDidCatch=function(o){if(o instanceof kt){this.setState({});return}throw o},t.componentWillUnmount=function(){this.unbind()},t.render=function(){return this.props.children(this.setCallbacks)},r}(re.Component),Sp=`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,$t=function(r){return r+1},xp=function(r){return`
  You have lifted an item in position `+$t(r.source.index)+`
`},dl=function(r,t){var n=r.droppableId===t.droppableId,o=$t(r.index),i=$t(t.index);return n?`
      You have moved the item from position `+o+`
      to position `+i+`
    `:`
    You have moved the item from position `+o+`
    in list `+r.droppableId+`
    to list `+t.droppableId+`
    in position `+i+`
  `},fl=function(r,t,n){var o=t.droppableId===n.droppableId;return o?`
      The item `+r+`
      has been combined with `+n.draggableId:`
      The item `+r+`
      in list `+t.droppableId+`
      has been combined with `+n.draggableId+`
      in list `+n.droppableId+`
    `},Ip=function(r){var t=r.destination;if(t)return dl(r.source,t);var n=r.combine;return n?fl(r.draggableId,r.source,n):"You are over an area that cannot be dropped on"},ni=function(r){return`
  The item has returned to its starting position
  of `+$t(r.index)+`
`},Pp=function(r){if(r.reason==="CANCEL")return`
      Movement cancelled.
      `+ni(r.source)+`
    `;var t=r.destination,n=r.combine;return t?`
      You have dropped the item.
      `+dl(r.source,t)+`
    `:n?`
      You have dropped the item.
      `+fl(r.draggableId,r.source,n)+`
    `:`
    The item has been dropped while not over a drop area.
    `+ni(r.source)+`
  `},At={dragHandleUsageInstructions:Sp,onDragStart:xp,onDragUpdate:Ip,onDragEnd:Pp},$e={x:0,y:0},je=function(r,t){return{x:r.x+t.x,y:r.y+t.y}},Ze=function(r,t){return{x:r.x-t.x,y:r.y-t.y}},Sr=function(r,t){return r.x===t.x&&r.y===t.y},Hr=function(r){return{x:r.x!==0?-r.x:0,y:r.y!==0?-r.y:0}},Mr=function(r,t,n){var o;return n===void 0&&(n=0),o={},o[r]=t,o[r==="x"?"y":"x"]=n,o},tt=function(r,t){return Math.sqrt(Math.pow(t.x-r.x,2)+Math.pow(t.y-r.y,2))},ai=function(r,t){return Math.min.apply(Math,t.map(function(n){return tt(r,n)}))},pl=function(r){return function(t){return{x:r(t.x),y:r(t.y)}}},Rp=function(e,r){var t=fr({top:Math.max(r.top,e.top),right:Math.min(r.right,e.right),bottom:Math.min(r.bottom,e.bottom),left:Math.max(r.left,e.left)});return t.width<=0||t.height<=0?null:t},mt=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},oi=function(r){return[{x:r.left,y:r.top},{x:r.right,y:r.top},{x:r.left,y:r.bottom},{x:r.right,y:r.bottom}]},Dp={top:0,right:0,bottom:0,left:0},Ep=function(r,t){return t?mt(r,t.scroll.diff.displacement):r},Bp=function(r,t,n){if(n&&n.increasedBy){var o;return ae({},r,(o={},o[t.end]=r[t.end]+n.increasedBy[t.line],o))}return r},Op=function(r,t){return t&&t.shouldClipSubject?Rp(t.pageMarginBox,r):fr(r)},Gr=function(e){var r=e.page,t=e.withPlaceholder,n=e.axis,o=e.frame,i=Ep(r.marginBox,o),l=Bp(i,n,t),s=Op(l,o);return{page:r,withPlaceholder:t,active:s}},Ca=function(e,r){e.frame||W(!1);var t=e.frame,n=Ze(r,t.scroll.initial),o=Hr(n),i=ae({},t,{scroll:{initial:t.scroll.initial,current:r,diff:{value:n,displacement:o},max:t.scroll.max}}),l=Gr({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i}),s=ae({},e,{frame:i,subject:l});return s};function Ht(e){return Object.values?Object.values(e):Object.keys(e).map(function(r){return e[r]})}function Sa(e,r){if(e.findIndex)return e.findIndex(r);for(var t=0;t<e.length;t++)if(r(e[t]))return t;return-1}function Rr(e,r){if(e.find)return e.find(r);var t=Sa(e,r);if(t!==-1)return e[t]}function vl(e){return Array.prototype.slice.call(e)}var gl=ke(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),ml=ke(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),ln=ke(function(e){return Ht(e)}),Ap=ke(function(e){return Ht(e)}),zr=ke(function(e,r){var t=Ap(r).filter(function(n){return e===n.descriptor.droppableId}).sort(function(n,o){return n.descriptor.index-o.descriptor.index});return t});function xa(e){return e.at&&e.at.type==="REORDER"?e.at.destination:null}function sn(e){return e.at&&e.at.type==="COMBINE"?e.at.combine:null}var un=ke(function(e,r){return r.filter(function(t){return t.descriptor.id!==e.descriptor.id})}),Tp=function(e){var r=e.isMovingForward,t=e.draggable,n=e.destination,o=e.insideDestination,i=e.previousImpact;if(!n.isCombineEnabled)return null;var l=xa(i);if(!l)return null;function s(S){var P={type:"COMBINE",combine:{draggableId:S,droppableId:n.descriptor.id}};return ae({},i,{at:P})}var p=i.displaced.all,u=p.length?p[0]:null;if(r)return u?s(u):null;var m=un(t,o);if(!u){if(!m.length)return null;var g=m[m.length-1];return s(g.descriptor.id)}var f=Sa(m,function(S){return S.descriptor.id===u});f===-1&&W(!1);var b=f-1;if(b<0)return null;var C=m[b];return s(C.descriptor.id)},jr=function(e,r){return e.descriptor.droppableId===r.descriptor.id},hl={point:$e,value:0},nt={invisible:{},visible:{},all:[]},Mp={displaced:nt,displacedBy:hl,at:null},sr=function(e,r){return function(t){return e<=t&&t<=r}},bl=function(e){var r=sr(e.top,e.bottom),t=sr(e.left,e.right);return function(n){var o=r(n.top)&&r(n.bottom)&&t(n.left)&&t(n.right);if(o)return!0;var i=r(n.top)||r(n.bottom),l=t(n.left)||t(n.right),s=i&&l;if(s)return!0;var p=n.top<e.top&&n.bottom>e.bottom,u=n.left<e.left&&n.right>e.right,m=p&&u;if(m)return!0;var g=p&&l||u&&i;return g}},Np=function(e){var r=sr(e.top,e.bottom),t=sr(e.left,e.right);return function(n){var o=r(n.top)&&r(n.bottom)&&t(n.left)&&t(n.right);return o}},Ia={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},yl={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},Fp=function(e){return function(r){var t=sr(r.top,r.bottom),n=sr(r.left,r.right);return function(o){return e===Ia?t(o.top)&&t(o.bottom):n(o.left)&&n(o.right)}}},Lp=function(r,t){var n=t.frame?t.frame.scroll.diff.displacement:$e;return mt(r,n)},Wp=function(r,t,n){return t.subject.active?n(t.subject.active)(r):!1},Gp=function(r,t,n){return n(t)(r)},Pa=function(r){var t=r.target,n=r.destination,o=r.viewport,i=r.withDroppableDisplacement,l=r.isVisibleThroughFrameFn,s=i?Lp(t,n):t;return Wp(s,n,l)&&Gp(s,o,l)},kp=function(r){return Pa(ae({},r,{isVisibleThroughFrameFn:bl}))},wl=function(r){return Pa(ae({},r,{isVisibleThroughFrameFn:Np}))},$p=function(r){return Pa(ae({},r,{isVisibleThroughFrameFn:Fp(r.destination.axis)}))},Hp=function(r,t,n){if(typeof n=="boolean")return n;if(!t)return!0;var o=t.invisible,i=t.visible;if(o[r])return!1;var l=i[r];return l?l.shouldAnimate:!0};function zp(e,r){var t=e.page.marginBox,n={top:r.point.y,right:0,bottom:0,left:r.point.x};return fr(ya(t,n))}function at(e){var r=e.afterDragging,t=e.destination,n=e.displacedBy,o=e.viewport,i=e.forceShouldAnimate,l=e.last;return r.reduce(function(p,u){var m=zp(u,n),g=u.descriptor.id;p.all.push(g);var f=kp({target:m,destination:t,viewport:o,withDroppableDisplacement:!0});if(!f)return p.invisible[u.descriptor.id]=!0,p;var b=Hp(g,l,i),C={draggableId:g,shouldAnimate:b};return p.visible[g]=C,p},{all:[],visible:{},invisible:{}})}function jp(e,r){if(!e.length)return 0;var t=e[e.length-1].descriptor.index;return r.inHomeList?t:t+1}function ii(e){var r=e.insideDestination,t=e.inHomeList,n=e.displacedBy,o=e.destination,i=jp(r,{inHomeList:t});return{displaced:nt,displacedBy:n,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:i}}}}function zt(e){var r=e.draggable,t=e.insideDestination,n=e.destination,o=e.viewport,i=e.displacedBy,l=e.last,s=e.index,p=e.forceShouldAnimate,u=jr(r,n);if(s==null)return ii({insideDestination:t,inHomeList:u,displacedBy:i,destination:n});var m=Rr(t,function(S){return S.descriptor.index===s});if(!m)return ii({insideDestination:t,inHomeList:u,displacedBy:i,destination:n});var g=un(r,t),f=t.indexOf(m),b=g.slice(f),C=at({afterDragging:b,destination:n,displacedBy:i,last:l,viewport:o.frame,forceShouldAnimate:p});return{displaced:C,displacedBy:i,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:s}}}}function Pr(e,r){return Boolean(r.effected[e])}var Vp=function(e){var r=e.isMovingForward,t=e.destination,n=e.draggables,o=e.combine,i=e.afterCritical;if(!t.isCombineEnabled)return null;var l=o.draggableId,s=n[l],p=s.descriptor.index,u=Pr(l,i);return u?r?p:p-1:r?p+1:p},Up=function(e){var r=e.isMovingForward,t=e.isInHomeList,n=e.insideDestination,o=e.location;if(!n.length)return null;var i=o.index,l=r?i+1:i-1,s=n[0].descriptor.index,p=n[n.length-1].descriptor.index,u=t?p:p+1;return l<s||l>u?null:l},qp=function(e){var r=e.isMovingForward,t=e.isInHomeList,n=e.draggable,o=e.draggables,i=e.destination,l=e.insideDestination,s=e.previousImpact,p=e.viewport,u=e.afterCritical,m=s.at;if(m||W(!1),m.type==="REORDER"){var g=Up({isMovingForward:r,isInHomeList:t,location:m.destination,insideDestination:l});return g==null?null:zt({draggable:n,insideDestination:l,destination:i,viewport:p,last:s.displaced,displacedBy:s.displacedBy,index:g})}var f=Vp({isMovingForward:r,destination:i,displaced:s.displaced,draggables:o,combine:m.combine,afterCritical:u});return f==null?null:zt({draggable:n,insideDestination:l,destination:i,viewport:p,last:s.displaced,displacedBy:s.displacedBy,index:f})},_p=function(e){var r=e.displaced,t=e.afterCritical,n=e.combineWith,o=e.displacedBy,i=Boolean(r.visible[n]||r.invisible[n]);return Pr(n,t)?i?$e:Hr(o.point):i?o.point:$e},Xp=function(e){var r=e.afterCritical,t=e.impact,n=e.draggables,o=sn(t);o||W(!1);var i=o.draggableId,l=n[i].page.borderBox.center,s=_p({displaced:t.displaced,afterCritical:r,combineWith:i,displacedBy:t.displacedBy});return je(l,s)},Cl=function(r,t){return t.margin[r.start]+t.borderBox[r.size]/2},Kp=function(r,t){return t.margin[r.end]+t.borderBox[r.size]/2},Ra=function(r,t,n){return t[r.crossAxisStart]+n.margin[r.crossAxisStart]+n.borderBox[r.crossAxisSize]/2},li=function(r){var t=r.axis,n=r.moveRelativeTo,o=r.isMoving;return Mr(t.line,n.marginBox[t.end]+Cl(t,o),Ra(t,n.marginBox,o))},si=function(r){var t=r.axis,n=r.moveRelativeTo,o=r.isMoving;return Mr(t.line,n.marginBox[t.start]-Kp(t,o),Ra(t,n.marginBox,o))},Yp=function(r){var t=r.axis,n=r.moveInto,o=r.isMoving;return Mr(t.line,n.contentBox[t.start]+Cl(t,o),Ra(t,n.contentBox,o))},Jp=function(e){var r=e.impact,t=e.draggable,n=e.draggables,o=e.droppable,i=e.afterCritical,l=zr(o.descriptor.id,n),s=t.page,p=o.axis;if(!l.length)return Yp({axis:p,moveInto:o.page,isMoving:s});var u=r.displaced,m=r.displacedBy,g=u.all[0];if(g){var f=n[g];if(Pr(g,i))return si({axis:p,moveRelativeTo:f.page,isMoving:s});var b=Wt(f.page,m.point);return si({axis:p,moveRelativeTo:b,isMoving:s})}var C=l[l.length-1];if(C.descriptor.id===t.descriptor.id)return s.borderBox.center;if(Pr(C.descriptor.id,i)){var S=Wt(C.page,Hr(i.displacedBy.point));return li({axis:p,moveRelativeTo:S,isMoving:s})}return li({axis:p,moveRelativeTo:C.page,isMoving:s})},Kn=function(e,r){var t=e.frame;return t?je(r,t.scroll.diff.displacement):r},Qp=function(r){var t=r.impact,n=r.draggable,o=r.droppable,i=r.draggables,l=r.afterCritical,s=n.page.borderBox.center,p=t.at;return!o||!p?s:p.type==="REORDER"?Jp({impact:t,draggable:n,draggables:i,droppable:o,afterCritical:l}):Xp({impact:t,draggables:i,afterCritical:l})},cn=function(e){var r=Qp(e),t=e.droppable,n=t?Kn(t,r):r;return n},Sl=function(e,r){var t=Ze(r,e.scroll.initial),n=Hr(t),o=fr({top:r.y,bottom:r.y+e.frame.height,left:r.x,right:r.x+e.frame.width}),i={frame:o,scroll:{initial:e.scroll.initial,max:e.scroll.max,current:r,diff:{value:t,displacement:n}}};return i};function ui(e,r){return e.map(function(t){return r[t]})}function Zp(e,r){for(var t=0;t<r.length;t++){var n=r[t].visible[e];if(n)return n}return null}var ev=function(e){var r=e.impact,t=e.viewport,n=e.destination,o=e.draggables,i=e.maxScrollChange,l=Sl(t,je(t.scroll.current,i)),s=n.frame?Ca(n,je(n.frame.scroll.current,i)):n,p=r.displaced,u=at({afterDragging:ui(p.all,o),destination:n,displacedBy:r.displacedBy,viewport:l.frame,last:p,forceShouldAnimate:!1}),m=at({afterDragging:ui(p.all,o),destination:s,displacedBy:r.displacedBy,viewport:t.frame,last:p,forceShouldAnimate:!1}),g={},f={},b=[p,u,m];p.all.forEach(function(S){var P=Zp(S,b);if(P){f[S]=P;return}g[S]=!0});var C=ae({},r,{displaced:{all:p.all,invisible:g,visible:f}});return C},rv=function(e,r){return je(e.scroll.diff.displacement,r)},Da=function(e){var r=e.pageBorderBoxCenter,t=e.draggable,n=e.viewport,o=rv(n,r),i=Ze(o,t.page.borderBox.center);return je(t.client.borderBox.center,i)},xl=function(e){var r=e.draggable,t=e.destination,n=e.newPageBorderBoxCenter,o=e.viewport,i=e.withDroppableDisplacement,l=e.onlyOnMainAxis,s=l===void 0?!1:l,p=Ze(n,r.page.borderBox.center),u=mt(r.page.borderBox,p),m={target:u,destination:t,withDroppableDisplacement:i,viewport:o};return s?$p(m):wl(m)},tv=function(e){var r=e.isMovingForward,t=e.draggable,n=e.destination,o=e.draggables,i=e.previousImpact,l=e.viewport,s=e.previousPageBorderBoxCenter,p=e.previousClientSelection,u=e.afterCritical;if(!n.isEnabled)return null;var m=zr(n.descriptor.id,o),g=jr(t,n),f=Tp({isMovingForward:r,draggable:t,destination:n,insideDestination:m,previousImpact:i})||qp({isMovingForward:r,isInHomeList:g,draggable:t,draggables:o,destination:n,insideDestination:m,previousImpact:i,viewport:l,afterCritical:u});if(!f)return null;var b=cn({impact:f,draggable:t,droppable:n,draggables:o,afterCritical:u}),C=xl({draggable:t,destination:n,newPageBorderBoxCenter:b,viewport:l.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});if(C){var S=Da({pageBorderBoxCenter:b,draggable:t,viewport:l});return{clientSelection:S,impact:f,scrollJumpRequest:null}}var P=Ze(b,s),D=ev({impact:f,viewport:l,destination:n,draggables:o,maxScrollChange:P});return{clientSelection:p,impact:D,scrollJumpRequest:P}},Xe=function(r){var t=r.subject.active;return t||W(!1),t},nv=function(e){var r=e.isMovingForward,t=e.pageBorderBoxCenter,n=e.source,o=e.droppables,i=e.viewport,l=n.subject.active;if(!l)return null;var s=n.axis,p=sr(l[s.start],l[s.end]),u=ln(o).filter(function(g){return g!==n}).filter(function(g){return g.isEnabled}).filter(function(g){return Boolean(g.subject.active)}).filter(function(g){return bl(i.frame)(Xe(g))}).filter(function(g){var f=Xe(g);return r?l[s.crossAxisEnd]<f[s.crossAxisEnd]:f[s.crossAxisStart]<l[s.crossAxisStart]}).filter(function(g){var f=Xe(g),b=sr(f[s.start],f[s.end]);return p(f[s.start])||p(f[s.end])||b(l[s.start])||b(l[s.end])}).sort(function(g,f){var b=Xe(g)[s.crossAxisStart],C=Xe(f)[s.crossAxisStart];return r?b-C:C-b}).filter(function(g,f,b){return Xe(g)[s.crossAxisStart]===Xe(b[0])[s.crossAxisStart]});if(!u.length)return null;if(u.length===1)return u[0];var m=u.filter(function(g){var f=sr(Xe(g)[s.start],Xe(g)[s.end]);return f(t[s.line])});return m.length===1?m[0]:m.length>1?m.sort(function(g,f){return Xe(g)[s.start]-Xe(f)[s.start]})[0]:u.sort(function(g,f){var b=ai(t,oi(Xe(g))),C=ai(t,oi(Xe(f)));return b!==C?b-C:Xe(g)[s.start]-Xe(f)[s.start]})[0]},ci=function(r,t){var n=r.page.borderBox.center;return Pr(r.descriptor.id,t)?Ze(n,t.displacedBy.point):n},av=function(r,t){var n=r.page.borderBox;return Pr(r.descriptor.id,t)?mt(n,Hr(t.displacedBy.point)):n},ov=function(e){var r=e.pageBorderBoxCenter,t=e.viewport,n=e.destination,o=e.insideDestination,i=e.afterCritical,l=o.filter(function(s){return wl({target:av(s,i),destination:n,viewport:t.frame,withDroppableDisplacement:!0})}).sort(function(s,p){var u=tt(r,Kn(n,ci(s,i))),m=tt(r,Kn(n,ci(p,i)));return u<m?-1:m<u?1:s.descriptor.index-p.descriptor.index});return l[0]||null},ht=ke(function(r,t){var n=t[r.line];return{value:n,point:Mr(r.line,n)}}),iv=function(r,t,n){var o=r.axis;if(r.descriptor.mode==="virtual")return Mr(o.line,t[o.line]);var i=r.subject.page.contentBox[o.size],l=zr(r.descriptor.id,n),s=l.reduce(function(m,g){return m+g.client.marginBox[o.size]},0),p=s+t[o.line],u=p-i;return u<=0?null:Mr(o.line,u)},Il=function(r,t){return ae({},r,{scroll:ae({},r.scroll,{max:t})})},Pl=function(r,t,n){var o=r.frame;jr(t,r)&&W(!1),r.subject.withPlaceholder&&W(!1);var i=ht(r.axis,t.displaceBy).point,l=iv(r,i,n),s={placeholderSize:i,increasedBy:l,oldFrameMaxScroll:r.frame?r.frame.scroll.max:null};if(!o){var p=Gr({page:r.subject.page,withPlaceholder:s,axis:r.axis,frame:r.frame});return ae({},r,{subject:p})}var u=l?je(o.scroll.max,l):o.scroll.max,m=Il(o,u),g=Gr({page:r.subject.page,withPlaceholder:s,axis:r.axis,frame:m});return ae({},r,{subject:g,frame:m})},lv=function(r){var t=r.subject.withPlaceholder;t||W(!1);var n=r.frame;if(!n){var o=Gr({page:r.subject.page,axis:r.axis,frame:null,withPlaceholder:null});return ae({},r,{subject:o})}var i=t.oldFrameMaxScroll;i||W(!1);var l=Il(n,i),s=Gr({page:r.subject.page,axis:r.axis,frame:l,withPlaceholder:null});return ae({},r,{subject:s,frame:l})},sv=function(e){var r=e.previousPageBorderBoxCenter,t=e.moveRelativeTo,n=e.insideDestination,o=e.draggable,i=e.draggables,l=e.destination,s=e.viewport,p=e.afterCritical;if(!t){if(n.length)return null;var u={displaced:nt,displacedBy:hl,at:{type:"REORDER",destination:{droppableId:l.descriptor.id,index:0}}},m=cn({impact:u,draggable:o,droppable:l,draggables:i,afterCritical:p}),g=jr(o,l)?l:Pl(l,o,i),f=xl({draggable:o,destination:g,newPageBorderBoxCenter:m,viewport:s.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});return f?u:null}var b=Boolean(r[l.axis.line]<=t.page.borderBox.center[l.axis.line]),C=function(){var P=t.descriptor.index;return t.descriptor.id===o.descriptor.id||b?P:P+1}(),S=ht(l.axis,o.displaceBy);return zt({draggable:o,insideDestination:n,destination:l,viewport:s,displacedBy:S,last:nt,index:C})},uv=function(e){var r=e.isMovingForward,t=e.previousPageBorderBoxCenter,n=e.draggable,o=e.isOver,i=e.draggables,l=e.droppables,s=e.viewport,p=e.afterCritical,u=nv({isMovingForward:r,pageBorderBoxCenter:t,source:o,droppables:l,viewport:s});if(!u)return null;var m=zr(u.descriptor.id,i),g=ov({pageBorderBoxCenter:t,viewport:s,destination:u,insideDestination:m,afterCritical:p}),f=sv({previousPageBorderBoxCenter:t,destination:u,draggable:n,draggables:i,moveRelativeTo:g,insideDestination:m,viewport:s,afterCritical:p});if(!f)return null;var b=cn({impact:f,draggable:n,droppable:u,draggables:i,afterCritical:p}),C=Da({pageBorderBoxCenter:b,draggable:n,viewport:s});return{clientSelection:C,impact:f,scrollJumpRequest:null}},er=function(e){var r=e.at;return r?r.type==="REORDER"?r.destination.droppableId:r.combine.droppableId:null},cv=function(r,t){var n=er(r);return n?t[n]:null},dv=function(e){var r=e.state,t=e.type,n=cv(r.impact,r.dimensions.droppables),o=Boolean(n),i=r.dimensions.droppables[r.critical.droppable.id],l=n||i,s=l.axis.direction,p=s==="vertical"&&(t==="MOVE_UP"||t==="MOVE_DOWN")||s==="horizontal"&&(t==="MOVE_LEFT"||t==="MOVE_RIGHT");if(p&&!o)return null;var u=t==="MOVE_DOWN"||t==="MOVE_RIGHT",m=r.dimensions.draggables[r.critical.draggable.id],g=r.current.page.borderBoxCenter,f=r.dimensions,b=f.draggables,C=f.droppables;return p?tv({isMovingForward:u,previousPageBorderBoxCenter:g,draggable:m,destination:l,draggables:b,viewport:r.viewport,previousClientSelection:r.current.client.selection,previousImpact:r.impact,afterCritical:r.afterCritical}):uv({isMovingForward:u,previousPageBorderBoxCenter:g,draggable:m,isOver:l,draggables:b,droppables:C,viewport:r.viewport,afterCritical:r.afterCritical})};function Tr(e){return e.phase==="DRAGGING"||e.phase==="COLLECTING"}function Rl(e){var r=sr(e.top,e.bottom),t=sr(e.left,e.right);return function(o){return r(o.y)&&t(o.x)}}function fv(e,r){return e.left<r.right&&e.right>r.left&&e.top<r.bottom&&e.bottom>r.top}function pv(e){var r=e.pageBorderBox,t=e.draggable,n=e.candidates,o=t.page.borderBox.center,i=n.map(function(l){var s=l.axis,p=Mr(l.axis.line,r.center[s.line],l.page.borderBox.center[s.crossAxisLine]);return{id:l.descriptor.id,distance:tt(o,p)}}).sort(function(l,s){return s.distance-l.distance});return i[0]?i[0].id:null}function vv(e){var r=e.pageBorderBox,t=e.draggable,n=e.droppables,o=ln(n).filter(function(i){if(!i.isEnabled)return!1;var l=i.subject.active;if(!l||!fv(r,l))return!1;if(Rl(l)(r.center))return!0;var s=i.axis,p=l.center[s.crossAxisLine],u=r[s.crossAxisStart],m=r[s.crossAxisEnd],g=sr(l[s.crossAxisStart],l[s.crossAxisEnd]),f=g(u),b=g(m);return!f&&!b?!0:f?u<p:m>p});return o.length?o.length===1?o[0].descriptor.id:pv({pageBorderBox:r,draggable:t,candidates:o}):null}var Dl=function(r,t){return fr(mt(r,t))},gv=function(e,r){var t=e.frame;return t?Dl(r,t.scroll.diff.value):r};function El(e){var r=e.displaced,t=e.id;return Boolean(r.visible[t]||r.invisible[t])}function mv(e){var r=e.draggable,t=e.closest,n=e.inHomeList;return t?n&&t.descriptor.index>r.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}var hv=function(e){var r=e.pageBorderBoxWithDroppableScroll,t=e.draggable,n=e.destination,o=e.insideDestination,i=e.last,l=e.viewport,s=e.afterCritical,p=n.axis,u=ht(n.axis,t.displaceBy),m=u.value,g=r[p.start],f=r[p.end],b=un(t,o),C=Rr(b,function(P){var D=P.descriptor.id,B=P.page.borderBox.center[p.line],O=Pr(D,s),A=El({displaced:i,id:D});return O?A?f<=B:g<B-m:A?f<=B+m:g<B}),S=mv({draggable:t,closest:C,inHomeList:jr(t,n)});return zt({draggable:t,insideDestination:o,destination:n,viewport:l,last:i,displacedBy:u,index:S})},bv=4,yv=function(e){var r=e.draggable,t=e.pageBorderBoxWithDroppableScroll,n=e.previousImpact,o=e.destination,i=e.insideDestination,l=e.afterCritical;if(!o.isCombineEnabled)return null;var s=o.axis,p=ht(o.axis,r.displaceBy),u=p.value,m=t[s.start],g=t[s.end],f=un(r,i),b=Rr(f,function(S){var P=S.descriptor.id,D=S.page.borderBox,B=D[s.size],O=B/bv,A=Pr(P,l),V=El({displaced:n.displaced,id:P});return A?V?g>D[s.start]+O&&g<D[s.end]-O:m>D[s.start]-u+O&&m<D[s.end]-u-O:V?g>D[s.start]+u+O&&g<D[s.end]+u-O:m>D[s.start]+O&&m<D[s.end]-O});if(!b)return null;var C={displacedBy:p,displaced:n.displaced,at:{type:"COMBINE",combine:{draggableId:b.descriptor.id,droppableId:o.descriptor.id}}};return C},Bl=function(e){var r=e.pageOffset,t=e.draggable,n=e.draggables,o=e.droppables,i=e.previousImpact,l=e.viewport,s=e.afterCritical,p=Dl(t.page.borderBox,r),u=vv({pageBorderBox:p,draggable:t,droppables:o});if(!u)return Mp;var m=o[u],g=zr(m.descriptor.id,n),f=gv(m,p);return yv({pageBorderBoxWithDroppableScroll:f,draggable:t,previousImpact:i,destination:m,insideDestination:g,afterCritical:s})||hv({pageBorderBoxWithDroppableScroll:f,draggable:t,destination:m,insideDestination:g,last:i.displaced,viewport:l,afterCritical:s})},Ea=function(e,r){var t;return ae({},e,(t={},t[r.descriptor.id]=r,t))},wv=function(r){var t=r.previousImpact,n=r.impact,o=r.droppables,i=er(t),l=er(n);if(!i||i===l)return o;var s=o[i];if(!s.subject.withPlaceholder)return o;var p=lv(s);return Ea(o,p)},Cv=function(e){var r=e.draggable,t=e.draggables,n=e.droppables,o=e.previousImpact,i=e.impact,l=wv({previousImpact:o,impact:i,droppables:n}),s=er(i);if(!s)return l;var p=n[s];if(jr(r,p)||p.subject.withPlaceholder)return l;var u=Pl(p,r,t);return Ea(l,u)},Jr=function(e){var r=e.state,t=e.clientSelection,n=e.dimensions,o=e.viewport,i=e.impact,l=e.scrollJumpRequest,s=o||r.viewport,p=n||r.dimensions,u=t||r.current.client.selection,m=Ze(u,r.initial.client.selection),g={offset:m,selection:u,borderBoxCenter:je(r.initial.client.borderBoxCenter,m)},f={selection:je(g.selection,s.scroll.current),borderBoxCenter:je(g.borderBoxCenter,s.scroll.current),offset:je(g.offset,s.scroll.diff.value)},b={client:g,page:f};if(r.phase==="COLLECTING")return ae({phase:"COLLECTING"},r,{dimensions:p,viewport:s,current:b});var C=p.draggables[r.critical.draggable.id],S=i||Bl({pageOffset:f.offset,draggable:C,draggables:p.draggables,droppables:p.droppables,previousImpact:r.impact,viewport:s,afterCritical:r.afterCritical}),P=Cv({draggable:C,impact:S,previousImpact:r.impact,draggables:p.draggables,droppables:p.droppables}),D=ae({},r,{current:b,dimensions:{draggables:p.draggables,droppables:P},impact:S,viewport:s,scrollJumpRequest:l||null,forceShouldAnimate:l?!1:null});return D};function Sv(e,r){return e.map(function(t){return r[t]})}var Ol=function(e){var r=e.impact,t=e.viewport,n=e.draggables,o=e.destination,i=e.forceShouldAnimate,l=r.displaced,s=Sv(l.all,n),p=at({afterDragging:s,destination:o,displacedBy:r.displacedBy,viewport:t.frame,forceShouldAnimate:i,last:l});return ae({},r,{displaced:p})},Al=function(e){var r=e.impact,t=e.draggable,n=e.droppable,o=e.draggables,i=e.viewport,l=e.afterCritical,s=cn({impact:r,draggable:t,draggables:o,droppable:n,afterCritical:l});return Da({pageBorderBoxCenter:s,draggable:t,viewport:i})},Tl=function(e){var r=e.state,t=e.dimensions,n=e.viewport;r.movementMode!=="SNAP"&&W(!1);var o=r.impact,i=n||r.viewport,l=t||r.dimensions,s=l.draggables,p=l.droppables,u=s[r.critical.draggable.id],m=er(o);m||W(!1);var g=p[m],f=Ol({impact:o,viewport:i,destination:g,draggables:s}),b=Al({impact:f,draggable:u,droppable:g,draggables:s,viewport:i,afterCritical:r.afterCritical});return Jr({impact:f,clientSelection:b,state:r,dimensions:l,viewport:i})},xv=function(e){return{index:e.index,droppableId:e.droppableId}},Ml=function(e){var r=e.draggable,t=e.home,n=e.draggables,o=e.viewport,i=ht(t.axis,r.displaceBy),l=zr(t.descriptor.id,n),s=l.indexOf(r);s===-1&&W(!1);var p=l.slice(s+1),u=p.reduce(function(b,C){return b[C.descriptor.id]=!0,b},{}),m={inVirtualList:t.descriptor.mode==="virtual",displacedBy:i,effected:u},g=at({afterDragging:p,destination:t,displacedBy:i,last:null,viewport:o.frame,forceShouldAnimate:!1}),f={displaced:g,displacedBy:i,at:{type:"REORDER",destination:xv(r.descriptor)}};return{impact:f,afterCritical:m}},Iv=function(e,r){return{draggables:e.draggables,droppables:Ea(e.droppables,r)}},Pv=function(e){var r=e.draggable,t=e.offset,n=e.initialWindowScroll,o=Wt(r.client,t),i=Gt(o,n),l=ae({},r,{placeholder:ae({},r.placeholder,{client:o}),client:o,page:i});return l},Rv=function(e){var r=e.frame;return r||W(!1),r},Dv=function(e){var r=e.additions,t=e.updatedDroppables,n=e.viewport,o=n.scroll.diff.value;return r.map(function(i){var l=i.descriptor.droppableId,s=t[l],p=Rv(s),u=p.scroll.diff.value,m=je(o,u),g=Pv({draggable:i,offset:m,initialWindowScroll:n.scroll.initial});return g})},Ev=function(e){var r=e.state,t=e.published,n=t.modified.map(function(O){var A=r.dimensions.droppables[O.droppableId],V=Ca(A,O.scroll);return V}),o=ae({},r.dimensions.droppables,{},gl(n)),i=ml(Dv({additions:t.additions,updatedDroppables:o,viewport:r.viewport})),l=ae({},r.dimensions.draggables,{},i);t.removals.forEach(function(O){delete l[O]});var s={droppables:o,draggables:l},p=er(r.impact),u=p?s.droppables[p]:null,m=s.draggables[r.critical.draggable.id],g=s.droppables[r.critical.droppable.id],f=Ml({draggable:m,home:g,draggables:l,viewport:r.viewport}),b=f.impact,C=f.afterCritical,S=u&&u.isCombineEnabled?r.impact:b,P=Bl({pageOffset:r.current.page.offset,draggable:s.draggables[r.critical.draggable.id],draggables:s.draggables,droppables:s.droppables,previousImpact:S,viewport:r.viewport,afterCritical:C}),D=ae({phase:"DRAGGING"},r,{phase:"DRAGGING",impact:P,onLiftImpact:b,dimensions:s,afterCritical:C,forceShouldAnimate:!1});if(r.phase==="COLLECTING")return D;var B=ae({phase:"DROP_PENDING"},D,{phase:"DROP_PENDING",reason:r.reason,isWaiting:!1});return B},Yn=function(r){return r.movementMode==="SNAP"},Bn=function(r,t,n){var o=Iv(r.dimensions,t);return!Yn(r)||n?Jr({state:r,dimensions:o}):Tl({state:r,dimensions:o})};function On(e){return e.isDragging&&e.movementMode==="SNAP"?ae({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}var di={phase:"IDLE",completed:null,shouldFlush:!1},Bv=function(e,r){if(e===void 0&&(e=di),r.type==="FLUSH")return ae({},di,{shouldFlush:!0});if(r.type==="INITIAL_PUBLISH"){e.phase!=="IDLE"&&W(!1);var t=r.payload,n=t.critical,o=t.clientSelection,i=t.viewport,l=t.dimensions,s=t.movementMode,p=l.draggables[n.draggable.id],u=l.droppables[n.droppable.id],m={selection:o,borderBoxCenter:p.client.borderBox.center,offset:$e},g={client:m,page:{selection:je(m.selection,i.scroll.initial),borderBoxCenter:je(m.selection,i.scroll.initial),offset:je(m.selection,i.scroll.diff.value)}},f=ln(l.droppables).every(function(xe){return!xe.isFixedOnPage}),b=Ml({draggable:p,home:u,draggables:l.draggables,viewport:i}),C=b.impact,S=b.afterCritical,P={phase:"DRAGGING",isDragging:!0,critical:n,movementMode:s,dimensions:l,initial:g,current:g,isWindowScrollAllowed:f,impact:C,afterCritical:S,onLiftImpact:C,viewport:i,scrollJumpRequest:null,forceShouldAnimate:null};return P}if(r.type==="COLLECTION_STARTING"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&W(!1);var D=ae({phase:"COLLECTING"},e,{phase:"COLLECTING"});return D}if(r.type==="PUBLISH_WHILE_DRAGGING")return e.phase==="COLLECTING"||e.phase==="DROP_PENDING"||W(!1),Ev({state:e,published:r.payload});if(r.type==="MOVE"){if(e.phase==="DROP_PENDING")return e;Tr(e)||W(!1);var B=r.payload.client;return Sr(B,e.current.client.selection)?e:Jr({state:e,clientSelection:B,impact:Yn(e)?e.impact:null})}if(r.type==="UPDATE_DROPPABLE_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="COLLECTING")return On(e);Tr(e)||W(!1);var O=r.payload,A=O.id,V=O.newScroll,q=e.dimensions.droppables[A];if(!q)return e;var Y=Ca(q,V);return Bn(e,Y,!1)}if(r.type==="UPDATE_DROPPABLE_IS_ENABLED"){if(e.phase==="DROP_PENDING")return e;Tr(e)||W(!1);var se=r.payload,Q=se.id,be=se.isEnabled,pe=e.dimensions.droppables[Q];pe||W(!1),pe.isEnabled===be&&W(!1);var Le=ae({},pe,{isEnabled:be});return Bn(e,Le,!0)}if(r.type==="UPDATE_DROPPABLE_IS_COMBINE_ENABLED"){if(e.phase==="DROP_PENDING")return e;Tr(e)||W(!1);var $=r.payload,z=$.id,ue=$.isCombineEnabled,Ie=e.dimensions.droppables[z];Ie||W(!1),Ie.isCombineEnabled===ue&&W(!1);var Se=ae({},Ie,{isCombineEnabled:ue});return Bn(e,Se,!0)}if(r.type==="MOVE_BY_WINDOW_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="DROP_ANIMATING")return e;Tr(e)||W(!1),e.isWindowScrollAllowed||W(!1);var we=r.payload.newScroll;if(Sr(e.viewport.scroll.current,we))return On(e);var Ee=Sl(e.viewport,we);return Yn(e)?Tl({state:e,viewport:Ee}):Jr({state:e,viewport:Ee})}if(r.type==="UPDATE_VIEWPORT_MAX_SCROLL"){if(!Tr(e))return e;var De=r.payload.maxScroll;if(Sr(De,e.viewport.scroll.max))return e;var Oe=ae({},e.viewport,{scroll:ae({},e.viewport.scroll,{max:De})});return ae({phase:"DRAGGING"},e,{viewport:Oe})}if(r.type==="MOVE_UP"||r.type==="MOVE_DOWN"||r.type==="MOVE_LEFT"||r.type==="MOVE_RIGHT"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&W(!1);var Fe=dv({state:e,type:r.type});return Fe?Jr({state:e,impact:Fe.impact,clientSelection:Fe.clientSelection,scrollJumpRequest:Fe.scrollJumpRequest}):e}if(r.type==="DROP_PENDING"){var ur=r.payload.reason;e.phase!=="COLLECTING"&&W(!1);var Ve=ae({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:ur});return Ve}if(r.type==="DROP_ANIMATE"){var Ae=r.payload,We=Ae.completed,cr=Ae.dropDuration,nr=Ae.newHomeClientOffset;e.phase==="DRAGGING"||e.phase==="DROP_PENDING"||W(!1);var dr={phase:"DROP_ANIMATING",completed:We,dropDuration:cr,newHomeClientOffset:nr,dimensions:e.dimensions};return dr}if(r.type==="DROP_COMPLETE"){var Ke=r.payload.completed;return{phase:"IDLE",completed:Ke,shouldFlush:!1}}return e},Ov=function(r){return{type:"BEFORE_INITIAL_CAPTURE",payload:r}},Av=function(r){return{type:"LIFT",payload:r}},Tv=function(r){return{type:"INITIAL_PUBLISH",payload:r}},Mv=function(r){return{type:"PUBLISH_WHILE_DRAGGING",payload:r}},Nv=function(){return{type:"COLLECTION_STARTING",payload:null}},Fv=function(r){return{type:"UPDATE_DROPPABLE_SCROLL",payload:r}},Lv=function(r){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:r}},Wv=function(r){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:r}},Nl=function(r){return{type:"MOVE",payload:r}},Gv=function(r){return{type:"MOVE_BY_WINDOW_SCROLL",payload:r}},kv=function(r){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:r}},$v=function(){return{type:"MOVE_UP",payload:null}},Hv=function(){return{type:"MOVE_DOWN",payload:null}},zv=function(){return{type:"MOVE_RIGHT",payload:null}},jv=function(){return{type:"MOVE_LEFT",payload:null}},Ba=function(){return{type:"FLUSH",payload:null}},Vv=function(r){return{type:"DROP_ANIMATE",payload:r}},Oa=function(r){return{type:"DROP_COMPLETE",payload:r}},Fl=function(r){return{type:"DROP",payload:r}},Uv=function(r){return{type:"DROP_PENDING",payload:r}},Ll=function(){return{type:"DROP_ANIMATION_FINISHED",payload:null}},qv=function(e){return function(r){var t=r.getState,n=r.dispatch;return function(o){return function(i){if(i.type!=="LIFT"){o(i);return}var l=i.payload,s=l.id,p=l.clientSelection,u=l.movementMode,m=t();m.phase==="DROP_ANIMATING"&&n(Oa({completed:m.completed})),t().phase!=="IDLE"&&W(!1),n(Ba()),n(Ov({draggableId:s,movementMode:u}));var g={shouldPublishImmediately:u==="SNAP"},f={draggableId:s,scrollOptions:g},b=e.startPublishing(f),C=b.critical,S=b.dimensions,P=b.viewport;n(Tv({critical:C,dimensions:S,clientSelection:p,movementMode:u,viewport:P}))}}}},_v=function(e){return function(){return function(r){return function(t){t.type==="INITIAL_PUBLISH"&&e.dragging(),t.type==="DROP_ANIMATE"&&e.dropping(t.payload.completed.result.reason),(t.type==="FLUSH"||t.type==="DROP_COMPLETE")&&e.resting(),r(t)}}}},Aa={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},ot={opacity:{drop:0,combining:.7},scale:{drop:.75}},Ta={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},Ar=Ta.outOfTheWay+"s "+Aa.outOfTheWay,Qr={fluid:"opacity "+Ar,snap:"transform "+Ar+", opacity "+Ar,drop:function(r){var t=r+"s "+Aa.drop;return"transform "+t+", opacity "+t},outOfTheWay:"transform "+Ar,placeholder:"height "+Ar+", width "+Ar+", margin "+Ar},fi=function(r){return Sr(r,$e)?null:"translate("+r.x+"px, "+r.y+"px)"},Jn={moveTo:fi,drop:function(r,t){var n=fi(r);return n?t?n+" scale("+ot.scale.drop+")":n:null}},Qn=Ta.minDropTime,Wl=Ta.maxDropTime,Xv=Wl-Qn,pi=1500,Kv=.6,Yv=function(e){var r=e.current,t=e.destination,n=e.reason,o=tt(r,t);if(o<=0)return Qn;if(o>=pi)return Wl;var i=o/pi,l=Qn+Xv*i,s=n==="CANCEL"?l*Kv:l;return Number(s.toFixed(2))},Jv=function(e){var r=e.impact,t=e.draggable,n=e.dimensions,o=e.viewport,i=e.afterCritical,l=n.draggables,s=n.droppables,p=er(r),u=p?s[p]:null,m=s[t.descriptor.droppableId],g=Al({impact:r,draggable:t,draggables:l,afterCritical:i,droppable:u||m,viewport:o}),f=Ze(g,t.client.borderBox.center);return f},Qv=function(e){var r=e.draggables,t=e.reason,n=e.lastImpact,o=e.home,i=e.viewport,l=e.onLiftImpact;if(!n.at||t!=="DROP"){var s=Ol({draggables:r,impact:l,destination:o,viewport:i,forceShouldAnimate:!0});return{impact:s,didDropInsideDroppable:!1}}if(n.at.type==="REORDER")return{impact:n,didDropInsideDroppable:!0};var p=ae({},n,{displaced:nt});return{impact:p,didDropInsideDroppable:!0}},Zv=function(e){var r=e.getState,t=e.dispatch;return function(n){return function(o){if(o.type!=="DROP"){n(o);return}var i=r(),l=o.payload.reason;if(i.phase==="COLLECTING"){t(Uv({reason:l}));return}if(i.phase!=="IDLE"){var s=i.phase==="DROP_PENDING"&&i.isWaiting;s&&W(!1),i.phase==="DRAGGING"||i.phase==="DROP_PENDING"||W(!1);var p=i.critical,u=i.dimensions,m=u.draggables[i.critical.draggable.id],g=Qv({reason:l,lastImpact:i.impact,afterCritical:i.afterCritical,onLiftImpact:i.onLiftImpact,home:i.dimensions.droppables[i.critical.droppable.id],viewport:i.viewport,draggables:i.dimensions.draggables}),f=g.impact,b=g.didDropInsideDroppable,C=b?xa(f):null,S=b?sn(f):null,P={index:p.draggable.index,droppableId:p.droppable.id},D={draggableId:m.descriptor.id,type:m.descriptor.type,source:P,reason:l,mode:i.movementMode,destination:C,combine:S},B=Jv({impact:f,draggable:m,dimensions:u,viewport:i.viewport,afterCritical:i.afterCritical}),O={critical:i.critical,afterCritical:i.afterCritical,result:D,impact:f},A=!Sr(i.current.client.offset,B)||Boolean(D.combine);if(!A){t(Oa({completed:O}));return}var V=Yv({current:i.current.client.offset,destination:B,reason:l}),q={newHomeClientOffset:B,dropDuration:V,completed:O};t(Vv(q))}}}},Gl=function(){return{x:window.pageXOffset,y:window.pageYOffset}};function eg(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(t){t.target!==window&&t.target!==window.document||e()}}}function rg(e){var r=e.onWindowScroll;function t(){r(Gl())}var n=rt(t),o=eg(n),i=Cr;function l(){return i!==Cr}function s(){l()&&W(!1),i=lr(window,[o])}function p(){l()||W(!1),n.cancel(),i(),i=Cr}return{start:s,stop:p,isActive:l}}var tg=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},ng=function(e){var r=rg({onWindowScroll:function(n){e.dispatch(Gv({newScroll:n}))}});return function(t){return function(n){!r.isActive()&&n.type==="INITIAL_PUBLISH"&&r.start(),r.isActive()&&tg(n)&&r.stop(),t(n)}}},ag=function(e){var r=!1,t=!1,n=setTimeout(function(){t=!0}),o=function(l){r||t||(r=!0,e(l),clearTimeout(n))};return o.wasCalled=function(){return r},o},og=function(){var e=[],r=function(i){var l=Sa(e,function(u){return u.timerId===i});l===-1&&W(!1);var s=e.splice(l,1),p=s[0];p.callback()},t=function(i){var l=setTimeout(function(){return r(l)}),s={timerId:l,callback:i};e.push(s)},n=function(){if(e.length){var i=[].concat(e);e.length=0,i.forEach(function(l){clearTimeout(l.timerId),l.callback()})}};return{add:t,flush:n}},ig=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.droppableId===t.droppableId&&r.index===t.index},lg=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.draggableId===t.draggableId&&r.droppableId===t.droppableId},sg=function(r,t){if(r===t)return!0;var n=r.draggable.id===t.draggable.id&&r.draggable.droppableId===t.draggable.droppableId&&r.draggable.type===t.draggable.type&&r.draggable.index===t.draggable.index,o=r.droppable.id===t.droppable.id&&r.droppable.type===t.droppable.type;return n&&o},Xr=function(r,t){t()},Pt=function(r,t){return{draggableId:r.draggable.id,type:r.droppable.type,source:{droppableId:r.droppable.id,index:r.draggable.index},mode:t}},An=function(r,t,n,o){if(!r){n(o(t));return}var i=ag(n),l={announce:i};r(t,l),i.wasCalled()||n(o(t))},ug=function(e,r){var t=og(),n=null,o=function(f,b){n&&W(!1),Xr("onBeforeCapture",function(){var C=e().onBeforeCapture;if(C){var S={draggableId:f,mode:b};C(S)}})},i=function(f,b){n&&W(!1),Xr("onBeforeDragStart",function(){var C=e().onBeforeDragStart;C&&C(Pt(f,b))})},l=function(f,b){n&&W(!1);var C=Pt(f,b);n={mode:b,lastCritical:f,lastLocation:C.source,lastCombine:null},t.add(function(){Xr("onDragStart",function(){return An(e().onDragStart,C,r,At.onDragStart)})})},s=function(f,b){var C=xa(b),S=sn(b);n||W(!1);var P=!sg(f,n.lastCritical);P&&(n.lastCritical=f);var D=!ig(n.lastLocation,C);D&&(n.lastLocation=C);var B=!lg(n.lastCombine,S);if(B&&(n.lastCombine=S),!(!P&&!D&&!B)){var O=ae({},Pt(f,n.mode),{combine:S,destination:C});t.add(function(){Xr("onDragUpdate",function(){return An(e().onDragUpdate,O,r,At.onDragUpdate)})})}},p=function(){n||W(!1),t.flush()},u=function(f){n||W(!1),n=null,Xr("onDragEnd",function(){return An(e().onDragEnd,f,r,At.onDragEnd)})},m=function(){if(n){var f=ae({},Pt(n.lastCritical,n.mode),{combine:null,destination:null,reason:"CANCEL"});u(f)}};return{beforeCapture:o,beforeStart:i,start:l,update:s,flush:p,drop:u,abort:m}},cg=function(e,r){var t=ug(e,r);return function(n){return function(o){return function(i){if(i.type==="BEFORE_INITIAL_CAPTURE"){t.beforeCapture(i.payload.draggableId,i.payload.movementMode);return}if(i.type==="INITIAL_PUBLISH"){var l=i.payload.critical;t.beforeStart(l,i.payload.movementMode),o(i),t.start(l,i.payload.movementMode);return}if(i.type==="DROP_COMPLETE"){var s=i.payload.completed.result;t.flush(),o(i),t.drop(s);return}if(o(i),i.type==="FLUSH"){t.abort();return}var p=n.getState();p.phase==="DRAGGING"&&t.update(p.critical,p.impact)}}}},dg=function(e){return function(r){return function(t){if(t.type!=="DROP_ANIMATION_FINISHED"){r(t);return}var n=e.getState();n.phase!=="DROP_ANIMATING"&&W(!1),e.dispatch(Oa({completed:n.completed}))}}},fg=function(e){var r=null,t=null;function n(){t&&(cancelAnimationFrame(t),t=null),r&&(r(),r=null)}return function(o){return function(i){if((i.type==="FLUSH"||i.type==="DROP_COMPLETE"||i.type==="DROP_ANIMATION_FINISHED")&&n(),o(i),i.type==="DROP_ANIMATE"){var l={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){var p=e.getState();p.phase==="DROP_ANIMATING"&&e.dispatch(Ll())}};t=requestAnimationFrame(function(){t=null,r=lr(window,[l])})}}}},pg=function(e){return function(){return function(r){return function(t){(t.type==="DROP_COMPLETE"||t.type==="FLUSH"||t.type==="DROP_ANIMATE")&&e.stopPublishing(),r(t)}}}},vg=function(e){var r=!1;return function(){return function(t){return function(n){if(n.type==="INITIAL_PUBLISH"){r=!0,e.tryRecordFocus(n.payload.critical.draggable.id),t(n),e.tryRestoreFocusRecorded();return}if(t(n),!!r){if(n.type==="FLUSH"){r=!1,e.tryRestoreFocusRecorded();return}if(n.type==="DROP_COMPLETE"){r=!1;var o=n.payload.completed.result;o.combine&&e.tryShiftRecord(o.draggableId,o.combine.draggableId),e.tryRestoreFocusRecorded()}}}}}},gg=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},mg=function(e){return function(r){return function(t){return function(n){if(gg(n)){e.stop(),t(n);return}if(n.type==="INITIAL_PUBLISH"){t(n);var o=r.getState();o.phase!=="DRAGGING"&&W(!1),e.start(o);return}t(n),e.scroll(r.getState())}}}},hg=function(e){return function(r){return function(t){if(r(t),t.type==="PUBLISH_WHILE_DRAGGING"){var n=e.getState();n.phase==="DROP_PENDING"&&(n.isWaiting||e.dispatch(Fl({reason:n.reason})))}}}},bg=qi,yg=function(e){var r=e.dimensionMarshal,t=e.focusMarshal,n=e.styleMarshal,o=e.getResponders,i=e.announce,l=e.autoScroller;return Ui(Bv,bg(lf(_v(n),pg(r),qv(r),Zv,dg,fg,hg,mg(l),ng,vg(t),cg(o,i))))},Tn=function(){return{additions:{},removals:{},modified:{}}};function wg(e){var r=e.registry,t=e.callbacks,n=Tn(),o=null,i=function(){o||(t.collectionStarting(),o=requestAnimationFrame(function(){o=null;var m=n,g=m.additions,f=m.removals,b=m.modified,C=Object.keys(g).map(function(D){return r.draggable.getById(D).getDimension($e)}).sort(function(D,B){return D.descriptor.index-B.descriptor.index}),S=Object.keys(b).map(function(D){var B=r.droppable.getById(D),O=B.callbacks.getScrollWhileDragging();return{droppableId:D,scroll:O}}),P={additions:C,removals:Object.keys(f),modified:S};n=Tn(),t.publish(P)}))},l=function(m){var g=m.descriptor.id;n.additions[g]=m,n.modified[m.descriptor.droppableId]=!0,n.removals[g]&&delete n.removals[g],i()},s=function(m){var g=m.descriptor;n.removals[g.id]=!0,n.modified[g.droppableId]=!0,n.additions[g.id]&&delete n.additions[g.id],i()},p=function(){o&&(cancelAnimationFrame(o),o=null,n=Tn())};return{add:l,remove:s,stop:p}}var kl=function(e){var r=e.scrollHeight,t=e.scrollWidth,n=e.height,o=e.width,i=Ze({x:t,y:r},{x:o,y:n}),l={x:Math.max(0,i.x),y:Math.max(0,i.y)};return l},$l=function(){var e=document.documentElement;return e||W(!1),e},Hl=function(){var e=$l(),r=kl({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight});return r},Cg=function(){var e=Gl(),r=Hl(),t=e.y,n=e.x,o=$l(),i=o.clientWidth,l=o.clientHeight,s=n+i,p=t+l,u=fr({top:t,left:n,right:s,bottom:p}),m={frame:u,scroll:{initial:e,current:e,max:r,diff:{value:$e,displacement:$e}}};return m},Sg=function(e){var r=e.critical,t=e.scrollOptions,n=e.registry,o=Cg(),i=o.scroll.current,l=r.droppable,s=n.droppable.getAllByType(l.type).map(function(g){return g.callbacks.getDimensionAndWatchScroll(i,t)}),p=n.draggable.getAllByType(r.draggable.type).map(function(g){return g.getDimension(i)}),u={draggables:ml(p),droppables:gl(s)},m={dimensions:u,critical:r,viewport:o};return m};function vi(e,r,t){if(t.descriptor.id===r.id||t.descriptor.type!==r.type)return!1;var n=e.droppable.getById(t.descriptor.droppableId);return n.descriptor.mode==="virtual"}var xg=function(e,r){var t=null,n=wg({callbacks:{publish:r.publishWhileDragging,collectionStarting:r.collectionStarting},registry:e}),o=function(b,C){e.droppable.exists(b)||W(!1),t&&r.updateDroppableIsEnabled({id:b,isEnabled:C})},i=function(b,C){t&&(e.droppable.exists(b)||W(!1),r.updateDroppableIsCombineEnabled({id:b,isCombineEnabled:C}))},l=function(b,C){t&&(e.droppable.exists(b)||W(!1),r.updateDroppableScroll({id:b,newScroll:C}))},s=function(b,C){t&&e.droppable.getById(b).callbacks.scroll(C)},p=function(){if(t){n.stop();var b=t.critical.droppable;e.droppable.getAllByType(b.type).forEach(function(C){return C.callbacks.dragStopped()}),t.unsubscribe(),t=null}},u=function(b){t||W(!1);var C=t.critical.draggable;b.type==="ADDITION"&&vi(e,C,b.value)&&n.add(b.value),b.type==="REMOVAL"&&vi(e,C,b.value)&&n.remove(b.value)},m=function(b){t&&W(!1);var C=e.draggable.getById(b.draggableId),S=e.droppable.getById(C.descriptor.droppableId),P={draggable:C.descriptor,droppable:S.descriptor},D=e.subscribe(u);return t={critical:P,unsubscribe:D},Sg({critical:P,registry:e,scrollOptions:b.scrollOptions})},g={updateDroppableIsEnabled:o,updateDroppableIsCombineEnabled:i,scrollDroppable:s,updateDroppableScroll:l,startPublishing:m,stopPublishing:p};return g},zl=function(e,r){return e.phase==="IDLE"?!0:e.phase!=="DROP_ANIMATING"||e.completed.result.draggableId===r?!1:e.completed.result.reason==="DROP"},Ig=function(e){window.scrollBy(e.x,e.y)},Pg=ke(function(e){return ln(e).filter(function(r){return!(!r.isEnabled||!r.frame)})}),Rg=function(r,t){var n=Rr(Pg(t),function(o){return o.frame||W(!1),Rl(o.frame.pageMarginBox)(r)});return n},Dg=function(e){var r=e.center,t=e.destination,n=e.droppables;if(t){var o=n[t];return o.frame?o:null}var i=Rg(r,n);return i},xr={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:function(r){return Math.pow(r,2)},durationDampening:{stopDampeningAt:1200,accelerateAt:360}},Eg=function(e,r){var t=e[r.size]*xr.startFromPercentage,n=e[r.size]*xr.maxScrollAtPercentage,o={startScrollingFrom:t,maxScrollValueAt:n};return o},jl=function(e){var r=e.startOfRange,t=e.endOfRange,n=e.current,o=t-r;if(o===0)return 0;var i=n-r,l=i/o;return l},Ma=1,Bg=function(e,r){if(e>r.startScrollingFrom)return 0;if(e<=r.maxScrollValueAt)return xr.maxPixelScroll;if(e===r.startScrollingFrom)return Ma;var t=jl({startOfRange:r.maxScrollValueAt,endOfRange:r.startScrollingFrom,current:e}),n=1-t,o=xr.maxPixelScroll*xr.ease(n);return Math.ceil(o)},gi=xr.durationDampening.accelerateAt,mi=xr.durationDampening.stopDampeningAt,Og=function(e,r){var t=r,n=mi,o=Date.now(),i=o-t;if(i>=mi)return e;if(i<gi)return Ma;var l=jl({startOfRange:gi,endOfRange:n,current:i}),s=e*xr.ease(l);return Math.ceil(s)},hi=function(e){var r=e.distanceToEdge,t=e.thresholds,n=e.dragStartTime,o=e.shouldUseTimeDampening,i=Bg(r,t);return i===0?0:o?Math.max(Og(i,n),Ma):i},bi=function(e){var r=e.container,t=e.distanceToEdges,n=e.dragStartTime,o=e.axis,i=e.shouldUseTimeDampening,l=Eg(r,o),s=t[o.end]<t[o.start];return s?hi({distanceToEdge:t[o.end],thresholds:l,dragStartTime:n,shouldUseTimeDampening:i}):-1*hi({distanceToEdge:t[o.start],thresholds:l,dragStartTime:n,shouldUseTimeDampening:i})},Ag=function(e){var r=e.container,t=e.subject,n=e.proposedScroll,o=t.height>r.height,i=t.width>r.width;return!i&&!o?n:i&&o?null:{x:i?0:n.x,y:o?0:n.y}},Tg=pl(function(e){return e===0?0:e}),Vl=function(e){var r=e.dragStartTime,t=e.container,n=e.subject,o=e.center,i=e.shouldUseTimeDampening,l={top:o.y-t.top,right:t.right-o.x,bottom:t.bottom-o.y,left:o.x-t.left},s=bi({container:t,distanceToEdges:l,dragStartTime:r,axis:Ia,shouldUseTimeDampening:i}),p=bi({container:t,distanceToEdges:l,dragStartTime:r,axis:yl,shouldUseTimeDampening:i}),u=Tg({x:p,y:s});if(Sr(u,$e))return null;var m=Ag({container:t,subject:n,proposedScroll:u});return m?Sr(m,$e)?null:m:null},Mg=pl(function(e){return e===0?0:e>0?1:-1}),Na=function(){var e=function(t,n){return t<0?t:t>n?t-n:0};return function(r){var t=r.current,n=r.max,o=r.change,i=je(t,o),l={x:e(i.x,n.x),y:e(i.y,n.y)};return Sr(l,$e)?null:l}}(),Ul=function(r){var t=r.max,n=r.current,o=r.change,i={x:Math.max(n.x,t.x),y:Math.max(n.y,t.y)},l=Mg(o),s=Na({max:i,current:n,change:l});return!s||l.x!==0&&s.x===0||l.y!==0&&s.y===0},Fa=function(r,t){return Ul({current:r.scroll.current,max:r.scroll.max,change:t})},Ng=function(r,t){if(!Fa(r,t))return null;var n=r.scroll.max,o=r.scroll.current;return Na({current:o,max:n,change:t})},La=function(r,t){var n=r.frame;return n?Ul({current:n.scroll.current,max:n.scroll.max,change:t}):!1},Fg=function(r,t){var n=r.frame;return!n||!La(r,t)?null:Na({current:n.scroll.current,max:n.scroll.max,change:t})},Lg=function(e){var r=e.viewport,t=e.subject,n=e.center,o=e.dragStartTime,i=e.shouldUseTimeDampening,l=Vl({dragStartTime:o,container:r.frame,subject:t,center:n,shouldUseTimeDampening:i});return l&&Fa(r,l)?l:null},Wg=function(e){var r=e.droppable,t=e.subject,n=e.center,o=e.dragStartTime,i=e.shouldUseTimeDampening,l=r.frame;if(!l)return null;var s=Vl({dragStartTime:o,container:l.pageMarginBox,subject:t,center:n,shouldUseTimeDampening:i});return s&&La(r,s)?s:null},yi=function(e){var r=e.state,t=e.dragStartTime,n=e.shouldUseTimeDampening,o=e.scrollWindow,i=e.scrollDroppable,l=r.current.page.borderBoxCenter,s=r.dimensions.draggables[r.critical.draggable.id],p=s.page.marginBox;if(r.isWindowScrollAllowed){var u=r.viewport,m=Lg({dragStartTime:t,viewport:u,subject:p,center:l,shouldUseTimeDampening:n});if(m){o(m);return}}var g=Dg({center:l,destination:er(r.impact),droppables:r.dimensions.droppables});if(g){var f=Wg({dragStartTime:t,droppable:g,subject:p,center:l,shouldUseTimeDampening:n});f&&i(g.descriptor.id,f)}},Gg=function(e){var r=e.scrollWindow,t=e.scrollDroppable,n=rt(r),o=rt(t),i=null,l=function(m){i||W(!1);var g=i,f=g.shouldUseTimeDampening,b=g.dragStartTime;yi({state:m,scrollWindow:n,scrollDroppable:o,dragStartTime:b,shouldUseTimeDampening:f})},s=function(m){i&&W(!1);var g=Date.now(),f=!1,b=function(){f=!0};yi({state:m,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:b,scrollDroppable:b}),i={dragStartTime:g,shouldUseTimeDampening:f},f&&l(m)},p=function(){i&&(n.cancel(),o.cancel(),i=null)};return{start:s,stop:p,scroll:l}},kg=function(e){var r=e.move,t=e.scrollDroppable,n=e.scrollWindow,o=function(u,m){var g=je(u.current.client.selection,m);r({client:g})},i=function(u,m){if(!La(u,m))return m;var g=Fg(u,m);if(!g)return t(u.descriptor.id,m),null;var f=Ze(m,g);t(u.descriptor.id,f);var b=Ze(m,f);return b},l=function(u,m,g){if(!u||!Fa(m,g))return g;var f=Ng(m,g);if(!f)return n(g),null;var b=Ze(g,f);n(b);var C=Ze(g,b);return C},s=function(u){var m=u.scrollJumpRequest;if(m){var g=er(u.impact);g||W(!1);var f=i(u.dimensions.droppables[g],m);if(f){var b=u.viewport,C=l(u.isWindowScrollAllowed,b,f);C&&o(u,C)}}};return s},$g=function(e){var r=e.scrollDroppable,t=e.scrollWindow,n=e.move,o=Gg({scrollWindow:t,scrollDroppable:r}),i=kg({move:n,scrollWindow:t,scrollDroppable:r}),l=function(u){if(u.phase==="DRAGGING"){if(u.movementMode==="FLUID"){o.scroll(u);return}u.scrollJumpRequest&&i(u)}},s={scroll:l,start:o.start,stop:o.stop};return s},kr="data-rbd",$r=function(){var e=kr+"-drag-handle";return{base:e,draggableId:e+"-draggable-id",contextId:e+"-context-id"}}(),Zn=function(){var e=kr+"-draggable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),Hg=function(){var e=kr+"-droppable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),wi={contextId:kr+"-scroll-container-context-id"},zg=function(r){return function(t){return"["+t+'="'+r+'"]'}},Kr=function(r,t){return r.map(function(n){var o=n.styles[t];return o?n.selector+" { "+o+" }":""}).join(" ")},jg="pointer-events: none;",Vg=function(e){var r=zg(e),t=function(){var s=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:r($r.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:s,dragging:jg,dropAnimating:s}}}(),n=function(){var s=`
      transition: `+Qr.outOfTheWay+`;
    `;return{selector:r(Zn.contextId),styles:{dragging:s,dropAnimating:s,userCancel:s}}}(),o={selector:r(Hg.contextId),styles:{always:"overflow-anchor: none;"}},i={selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}},l=[n,t,o,i];return{always:Kr(l,"always"),resting:Kr(l,"resting"),dragging:Kr(l,"dragging"),dropAnimating:Kr(l,"dropAnimating"),userCancel:Kr(l,"userCancel")}},rr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?G.useLayoutEffect:G.useEffect,Mn=function(){var r=document.querySelector("head");return r||W(!1),r},Ci=function(r){var t=document.createElement("style");return r&&t.setAttribute("nonce",r),t.type="text/css",t};function Ug(e,r){var t=me(function(){return Vg(e)},[e]),n=G.useRef(null),o=G.useRef(null),i=J(ke(function(g){var f=o.current;f||W(!1),f.textContent=g}),[]),l=J(function(g){var f=n.current;f||W(!1),f.textContent=g},[]);rr(function(){!n.current&&!o.current||W(!1);var g=Ci(r),f=Ci(r);return n.current=g,o.current=f,g.setAttribute(kr+"-always",e),f.setAttribute(kr+"-dynamic",e),Mn().appendChild(g),Mn().appendChild(f),l(t.always),i(t.resting),function(){var b=function(S){var P=S.current;P||W(!1),Mn().removeChild(P),S.current=null};b(n),b(o)}},[r,l,i,t.always,t.resting,e]);var s=J(function(){return i(t.dragging)},[i,t.dragging]),p=J(function(g){if(g==="DROP"){i(t.dropAnimating);return}i(t.userCancel)},[i,t.dropAnimating,t.userCancel]),u=J(function(){o.current&&i(t.resting)},[i,t.resting]),m=me(function(){return{dragging:s,dropping:p,resting:u}},[s,p,u]);return m}var ql=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function dn(e){return e instanceof ql(e).HTMLElement}function qg(e,r){var t="["+$r.contextId+'="'+e+'"]',n=vl(document.querySelectorAll(t));if(!n.length)return null;var o=Rr(n,function(i){return i.getAttribute($r.draggableId)===r});return!o||!dn(o)?null:o}function _g(e){var r=G.useRef({}),t=G.useRef(null),n=G.useRef(null),o=G.useRef(!1),i=J(function(f,b){var C={id:f,focus:b};return r.current[f]=C,function(){var P=r.current,D=P[f];D!==C&&delete P[f]}},[]),l=J(function(f){var b=qg(e,f);b&&b!==document.activeElement&&b.focus()},[e]),s=J(function(f,b){t.current===f&&(t.current=b)},[]),p=J(function(){n.current||o.current&&(n.current=requestAnimationFrame(function(){n.current=null;var f=t.current;f&&l(f)}))},[l]),u=J(function(f){t.current=null;var b=document.activeElement;b&&b.getAttribute($r.draggableId)===f&&(t.current=f)},[]);rr(function(){return o.current=!0,function(){o.current=!1;var f=n.current;f&&cancelAnimationFrame(f)}},[]);var m=me(function(){return{register:i,tryRecordFocus:u,tryRestoreFocusRecorded:p,tryShiftRecord:s}},[i,u,p,s]);return m}function Xg(){var e={draggables:{},droppables:{}},r=[];function t(g){return r.push(g),function(){var b=r.indexOf(g);b!==-1&&r.splice(b,1)}}function n(g){r.length&&r.forEach(function(f){return f(g)})}function o(g){return e.draggables[g]||null}function i(g){var f=o(g);return f||W(!1),f}var l={register:function(f){e.draggables[f.descriptor.id]=f,n({type:"ADDITION",value:f})},update:function(f,b){var C=e.draggables[b.descriptor.id];C&&C.uniqueId===f.uniqueId&&(delete e.draggables[b.descriptor.id],e.draggables[f.descriptor.id]=f)},unregister:function(f){var b=f.descriptor.id,C=o(b);C&&f.uniqueId===C.uniqueId&&(delete e.draggables[b],n({type:"REMOVAL",value:f}))},getById:i,findById:o,exists:function(f){return Boolean(o(f))},getAllByType:function(f){return Ht(e.draggables).filter(function(b){return b.descriptor.type===f})}};function s(g){return e.droppables[g]||null}function p(g){var f=s(g);return f||W(!1),f}var u={register:function(f){e.droppables[f.descriptor.id]=f},unregister:function(f){var b=s(f.descriptor.id);b&&f.uniqueId===b.uniqueId&&delete e.droppables[f.descriptor.id]},getById:p,findById:s,exists:function(f){return Boolean(s(f))},getAllByType:function(f){return Ht(e.droppables).filter(function(b){return b.descriptor.type===f})}};function m(){e.draggables={},e.droppables={},r.length=0}return{draggable:l,droppable:u,subscribe:t,clean:m}}function Kg(){var e=me(Xg,[]);return G.useEffect(function(){return function(){requestAnimationFrame(e.clean)}},[e]),e}var Wa=re.createContext(null),jt=function(){var e=document.body;return e||W(!1),e},Yg={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},Jg=function(r){return"rbd-announcement-"+r};function Qg(e){var r=me(function(){return Jg(e)},[e]),t=G.useRef(null);G.useEffect(function(){var i=document.createElement("div");return t.current=i,i.id=r,i.setAttribute("aria-live","assertive"),i.setAttribute("aria-atomic","true"),ae(i.style,Yg),jt().appendChild(i),function(){setTimeout(function(){var p=jt();p.contains(i)&&p.removeChild(i),i===t.current&&(t.current=null)})}},[r]);var n=J(function(o){var i=t.current;if(i){i.textContent=o;return}},[]);return n}var Zg=0,em={separator:"::"};function Ga(e,r){return r===void 0&&(r=em),me(function(){return""+e+r.separator+Zg++},[r.separator,e])}function rm(e){var r=e.contextId,t=e.uniqueId;return"rbd-hidden-text-"+r+"-"+t}function tm(e){var r=e.contextId,t=e.text,n=Ga("hidden-text",{separator:"-"}),o=me(function(){return rm({contextId:r,uniqueId:n})},[n,r]);return G.useEffect(function(){var l=document.createElement("div");return l.id=o,l.textContent=t,l.style.display="none",jt().appendChild(l),function(){var p=jt();p.contains(l)&&p.removeChild(l)}},[o,t]),o}var fn=re.createContext(null);function _l(e){var r=G.useRef(e);return G.useEffect(function(){r.current=e}),r}function nm(){var e=null;function r(){return Boolean(e)}function t(l){return l===e}function n(l){e&&W(!1);var s={abandon:l};return e=s,s}function o(){e||W(!1),e=null}function i(){e&&(e.abandon(),o())}return{isClaimed:r,isActive:t,claim:n,release:o,tryAbandon:i}}var am=9,om=13,ka=27,Xl=32,im=33,lm=34,sm=35,um=36,cm=37,dm=38,fm=39,pm=40,Rt,vm=(Rt={},Rt[om]=!0,Rt[am]=!0,Rt),Kl=function(e){vm[e.keyCode]&&e.preventDefault()},pn=function(){var e="visibilitychange";if(typeof document>"u")return e;var r=[e,"ms"+e,"webkit"+e,"moz"+e,"o"+e],t=Rr(r,function(n){return"on"+n in document});return t||e}(),Yl=0,Si=5;function gm(e,r){return Math.abs(r.x-e.x)>=Si||Math.abs(r.y-e.y)>=Si}var xi={type:"IDLE"};function mm(e){var r=e.cancel,t=e.completed,n=e.getPhase,o=e.setPhase;return[{eventName:"mousemove",fn:function(l){var s=l.button,p=l.clientX,u=l.clientY;if(s===Yl){var m={x:p,y:u},g=n();if(g.type==="DRAGGING"){l.preventDefault(),g.actions.move(m);return}g.type!=="PENDING"&&W(!1);var f=g.point;if(gm(f,m)){l.preventDefault();var b=g.actions.fluidLift(m);o({type:"DRAGGING",actions:b})}}}},{eventName:"mouseup",fn:function(l){var s=n();if(s.type!=="DRAGGING"){r();return}l.preventDefault(),s.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:function(l){n().type==="DRAGGING"&&l.preventDefault(),r()}},{eventName:"keydown",fn:function(l){var s=n();if(s.type==="PENDING"){r();return}if(l.keyCode===ka){l.preventDefault(),r();return}Kl(l)}},{eventName:"resize",fn:r},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){n().type==="PENDING"&&r()}},{eventName:"webkitmouseforcedown",fn:function(l){var s=n();if(s.type==="IDLE"&&W(!1),s.actions.shouldRespectForcePress()){r();return}l.preventDefault()}},{eventName:pn,fn:r}]}function hm(e){var r=G.useRef(xi),t=G.useRef(Cr),n=me(function(){return{eventName:"mousedown",fn:function(g){if(!g.defaultPrevented&&g.button===Yl&&!(g.ctrlKey||g.metaKey||g.shiftKey||g.altKey)){var f=e.findClosestDraggableId(g);if(f){var b=e.tryGetLock(f,l,{sourceEvent:g});if(b){g.preventDefault();var C={x:g.clientX,y:g.clientY};t.current(),u(b,C)}}}}}},[e]),o=me(function(){return{eventName:"webkitmouseforcewillbegin",fn:function(g){if(!g.defaultPrevented){var f=e.findClosestDraggableId(g);if(f){var b=e.findOptionsForDraggable(f);b&&(b.shouldRespectForcePress||e.canGetLock(f)&&g.preventDefault())}}}}},[e]),i=J(function(){var g={passive:!1,capture:!0};t.current=lr(window,[o,n],g)},[o,n]),l=J(function(){var m=r.current;m.type!=="IDLE"&&(r.current=xi,t.current(),i())},[i]),s=J(function(){var m=r.current;l(),m.type==="DRAGGING"&&m.actions.cancel({shouldBlockNextClick:!0}),m.type==="PENDING"&&m.actions.abort()},[l]),p=J(function(){var g={capture:!0,passive:!1},f=mm({cancel:s,completed:l,getPhase:function(){return r.current},setPhase:function(C){r.current=C}});t.current=lr(window,f,g)},[s,l]),u=J(function(g,f){r.current.type!=="IDLE"&&W(!1),r.current={type:"PENDING",point:f,actions:g},p()},[p]);rr(function(){return i(),function(){t.current()}},[i])}var Fr;function bm(){}var ym=(Fr={},Fr[lm]=!0,Fr[im]=!0,Fr[um]=!0,Fr[sm]=!0,Fr);function wm(e,r){function t(){r(),e.cancel()}function n(){r(),e.drop()}return[{eventName:"keydown",fn:function(i){if(i.keyCode===ka){i.preventDefault(),t();return}if(i.keyCode===Xl){i.preventDefault(),n();return}if(i.keyCode===pm){i.preventDefault(),e.moveDown();return}if(i.keyCode===dm){i.preventDefault(),e.moveUp();return}if(i.keyCode===fm){i.preventDefault(),e.moveRight();return}if(i.keyCode===cm){i.preventDefault(),e.moveLeft();return}if(ym[i.keyCode]){i.preventDefault();return}Kl(i)}},{eventName:"mousedown",fn:t},{eventName:"mouseup",fn:t},{eventName:"click",fn:t},{eventName:"touchstart",fn:t},{eventName:"resize",fn:t},{eventName:"wheel",fn:t,options:{passive:!0}},{eventName:pn,fn:t}]}function Cm(e){var r=G.useRef(bm),t=me(function(){return{eventName:"keydown",fn:function(i){if(i.defaultPrevented||i.keyCode!==Xl)return;var l=e.findClosestDraggableId(i);if(!l)return;var s=e.tryGetLock(l,m,{sourceEvent:i});if(!s)return;i.preventDefault();var p=!0,u=s.snapLift();r.current();function m(){p||W(!1),p=!1,r.current(),n()}r.current=lr(window,wm(u,m),{capture:!0,passive:!1})}}},[e]),n=J(function(){var i={passive:!1,capture:!0};r.current=lr(window,[t],i)},[t]);rr(function(){return n(),function(){r.current()}},[n])}var Nn={type:"IDLE"},Sm=120,xm=.15;function Im(e){var r=e.cancel,t=e.getPhase;return[{eventName:"orientationchange",fn:r},{eventName:"resize",fn:r},{eventName:"contextmenu",fn:function(o){o.preventDefault()}},{eventName:"keydown",fn:function(o){if(t().type!=="DRAGGING"){r();return}o.keyCode===ka&&o.preventDefault(),r()}},{eventName:pn,fn:r}]}function Pm(e){var r=e.cancel,t=e.completed,n=e.getPhase;return[{eventName:"touchmove",options:{capture:!1},fn:function(i){var l=n();if(l.type!=="DRAGGING"){r();return}l.hasMoved=!0;var s=i.touches[0],p=s.clientX,u=s.clientY,m={x:p,y:u};i.preventDefault(),l.actions.move(m)}},{eventName:"touchend",fn:function(i){var l=n();if(l.type!=="DRAGGING"){r();return}i.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:function(i){if(n().type!=="DRAGGING"){r();return}i.preventDefault(),r()}},{eventName:"touchforcechange",fn:function(i){var l=n();l.type==="IDLE"&&W(!1);var s=i.touches[0];if(s){var p=s.force>=xm;if(p){var u=l.actions.shouldRespectForcePress();if(l.type==="PENDING"){u&&r();return}if(u){if(l.hasMoved){i.preventDefault();return}r();return}i.preventDefault()}}}},{eventName:pn,fn:r}]}function Rm(e){var r=G.useRef(Nn),t=G.useRef(Cr),n=J(function(){return r.current},[]),o=J(function(b){r.current=b},[]),i=me(function(){return{eventName:"touchstart",fn:function(b){if(!b.defaultPrevented){var C=e.findClosestDraggableId(b);if(C){var S=e.tryGetLock(C,s,{sourceEvent:b});if(S){var P=b.touches[0],D=P.clientX,B=P.clientY,O={x:D,y:B};t.current(),g(S,O)}}}}}},[e]),l=J(function(){var b={capture:!0,passive:!1};t.current=lr(window,[i],b)},[i]),s=J(function(){var f=r.current;f.type!=="IDLE"&&(f.type==="PENDING"&&clearTimeout(f.longPressTimerId),o(Nn),t.current(),l())},[l,o]),p=J(function(){var f=r.current;s(),f.type==="DRAGGING"&&f.actions.cancel({shouldBlockNextClick:!0}),f.type==="PENDING"&&f.actions.abort()},[s]),u=J(function(){var b={capture:!0,passive:!1},C={cancel:p,completed:s,getPhase:n},S=lr(window,Pm(C),b),P=lr(window,Im(C),b);t.current=function(){S(),P()}},[p,n,s]),m=J(function(){var b=n();b.type!=="PENDING"&&W(!1);var C=b.actions.fluidLift(b.point);o({type:"DRAGGING",actions:C,hasMoved:!1})},[n,o]),g=J(function(b,C){n().type!=="IDLE"&&W(!1);var S=setTimeout(m,Sm);o({type:"PENDING",point:C,actions:b,longPressTimerId:S}),u()},[u,n,o,m]);rr(function(){return l(),function(){t.current();var C=n();C.type==="PENDING"&&(clearTimeout(C.longPressTimerId),o(Nn))}},[n,l,o]),rr(function(){var b=lr(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}]);return b},[])}var Dm={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function Jl(e,r){if(r==null)return!1;var t=Boolean(Dm[r.tagName.toLowerCase()]);if(t)return!0;var n=r.getAttribute("contenteditable");return n==="true"||n===""?!0:r===e?!1:Jl(e,r.parentElement)}function Em(e,r){var t=r.target;return dn(t)?Jl(e,t):!1}var Bm=function(e){return fr(e.getBoundingClientRect()).center};function Om(e){return e instanceof ql(e).Element}var Am=function(){var e="matches";if(typeof document>"u")return e;var r=[e,"msMatchesSelector","webkitMatchesSelector"],t=Rr(r,function(n){return n in Element.prototype});return t||e}();function Ql(e,r){return e==null?null:e[Am](r)?e:Ql(e.parentElement,r)}function Tm(e,r){return e.closest?e.closest(r):Ql(e,r)}function Mm(e){return"["+$r.contextId+'="'+e+'"]'}function Nm(e,r){var t=r.target;if(!Om(t))return null;var n=Mm(e),o=Tm(t,n);return!o||!dn(o)?null:o}function Fm(e,r){var t=Nm(e,r);return t?t.getAttribute($r.draggableId):null}function Lm(e,r){var t="["+Zn.contextId+'="'+e+'"]',n=vl(document.querySelectorAll(t)),o=Rr(n,function(i){return i.getAttribute(Zn.id)===r});return!o||!dn(o)?null:o}function Wm(e){e.preventDefault()}function Dt(e){var r=e.expected,t=e.phase,n=e.isLockActive;return e.shouldWarn,!(!n()||r!==t)}function Zl(e){var r=e.lockAPI,t=e.store,n=e.registry,o=e.draggableId;if(r.isClaimed())return!1;var i=n.draggable.findById(o);return!(!i||!i.options.isEnabled||!zl(t.getState(),o))}function Gm(e){var r=e.lockAPI,t=e.contextId,n=e.store,o=e.registry,i=e.draggableId,l=e.forceSensorStop,s=e.sourceEvent,p=Zl({lockAPI:r,store:n,registry:o,draggableId:i});if(!p)return null;var u=o.draggable.getById(i),m=Lm(t,u.descriptor.id);if(!m||s&&!u.options.canDragInteractiveElements&&Em(m,s))return null;var g=r.claim(l||Cr),f="PRE_DRAG";function b(){return u.options.shouldRespectForcePress}function C(){return r.isActive(g)}function S(q,Y){Dt({expected:q,phase:f,isLockActive:C,shouldWarn:!0})&&n.dispatch(Y())}var P=S.bind(null,"DRAGGING");function D(q){function Y(){r.release(),f="COMPLETED"}f!=="PRE_DRAG"&&(Y(),f!=="PRE_DRAG"&&W(!1)),n.dispatch(Av(q.liftActionArgs)),f="DRAGGING";function se(Q,be){if(be===void 0&&(be={shouldBlockNextClick:!1}),q.cleanup(),be.shouldBlockNextClick){var pe=lr(window,[{eventName:"click",fn:Wm,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(pe)}Y(),n.dispatch(Fl({reason:Q}))}return ae({isActive:function(){return Dt({expected:"DRAGGING",phase:f,isLockActive:C,shouldWarn:!1})},shouldRespectForcePress:b,drop:function(be){return se("DROP",be)},cancel:function(be){return se("CANCEL",be)}},q.actions)}function B(q){var Y=rt(function(Q){P(function(){return Nl({client:Q})})}),se=D({liftActionArgs:{id:i,clientSelection:q,movementMode:"FLUID"},cleanup:function(){return Y.cancel()},actions:{move:Y}});return ae({},se,{move:Y})}function O(){var q={moveUp:function(){return P($v)},moveRight:function(){return P(zv)},moveDown:function(){return P(Hv)},moveLeft:function(){return P(jv)}};return D({liftActionArgs:{id:i,clientSelection:Bm(m),movementMode:"SNAP"},cleanup:Cr,actions:q})}function A(){var q=Dt({expected:"PRE_DRAG",phase:f,isLockActive:C,shouldWarn:!0});q&&r.release()}var V={isActive:function(){return Dt({expected:"PRE_DRAG",phase:f,isLockActive:C,shouldWarn:!1})},shouldRespectForcePress:b,fluidLift:B,snapLift:O,abort:A};return V}var km=[hm,Cm,Rm];function $m(e){var r=e.contextId,t=e.store,n=e.registry,o=e.customSensors,i=e.enableDefaultSensors,l=[].concat(i?km:[],o||[]),s=G.useState(function(){return nm()})[0],p=J(function(B,O){B.isDragging&&!O.isDragging&&s.tryAbandon()},[s]);rr(function(){var B=t.getState(),O=t.subscribe(function(){var A=t.getState();p(B,A),B=A});return O},[s,t,p]),rr(function(){return s.tryAbandon},[s.tryAbandon]);for(var u=J(function(D){return Zl({lockAPI:s,registry:n,store:t,draggableId:D})},[s,n,t]),m=J(function(D,B,O){return Gm({lockAPI:s,registry:n,contextId:r,store:t,draggableId:D,forceSensorStop:B,sourceEvent:O&&O.sourceEvent?O.sourceEvent:null})},[r,s,n,t]),g=J(function(D){return Fm(r,D)},[r]),f=J(function(D){var B=n.draggable.findById(D);return B?B.options:null},[n.draggable]),b=J(function(){s.isClaimed()&&(s.tryAbandon(),t.getState().phase!=="IDLE"&&t.dispatch(Ba()))},[s,t]),C=J(s.isClaimed,[s]),S=me(function(){return{canGetLock:u,tryGetLock:m,findClosestDraggableId:g,findOptionsForDraggable:f,tryReleaseLock:b,isLockClaimed:C}},[u,m,g,f,b,C]),P=0;P<l.length;P++)l[P](S)}var Hm=function(r){return{onBeforeCapture:r.onBeforeCapture,onBeforeDragStart:r.onBeforeDragStart,onDragStart:r.onDragStart,onDragEnd:r.onDragEnd,onDragUpdate:r.onDragUpdate}};function Yr(e){return e.current||W(!1),e.current}function zm(e){var r=e.contextId,t=e.setCallbacks,n=e.sensors,o=e.nonce,i=e.dragHandleUsageInstructions,l=G.useRef(null),s=_l(e),p=J(function(){return Hm(s.current)},[s]),u=Qg(r),m=tm({contextId:r,text:i}),g=Ug(r,o),f=J(function(Q){Yr(l).dispatch(Q)},[]),b=me(function(){return _o({publishWhileDragging:Mv,updateDroppableScroll:Fv,updateDroppableIsEnabled:Lv,updateDroppableIsCombineEnabled:Wv,collectionStarting:Nv},f)},[f]),C=Kg(),S=me(function(){return xg(C,b)},[C,b]),P=me(function(){return $g(ae({scrollWindow:Ig,scrollDroppable:S.scrollDroppable},_o({move:Nl},f)))},[S.scrollDroppable,f]),D=_g(r),B=me(function(){return yg({announce:u,autoScroller:P,dimensionMarshal:S,focusMarshal:D,getResponders:p,styleMarshal:g})},[u,P,S,D,p,g]);l.current=B;var O=J(function(){var Q=Yr(l),be=Q.getState();be.phase!=="IDLE"&&Q.dispatch(Ba())},[]),A=J(function(){var Q=Yr(l).getState();return Q.isDragging||Q.phase==="DROP_ANIMATING"},[]),V=me(function(){return{isDragging:A,tryAbort:O}},[A,O]);t(V);var q=J(function(Q){return zl(Yr(l).getState(),Q)},[]),Y=J(function(){return Tr(Yr(l).getState())},[]),se=me(function(){return{marshal:S,focus:D,contextId:r,canLift:q,isMovementAllowed:Y,dragHandleUsageInstructionsId:m,registry:C}},[r,S,m,D,q,Y,C]);return $m({contextId:r,store:B,registry:C,customSensors:n,enableDefaultSensors:e.enableDefaultSensors!==!1}),G.useEffect(function(){return O},[O]),re.createElement(fn.Provider,{value:se},re.createElement(ff,{context:Wa,store:B},e.children))}var jm=0;function Vm(){return me(function(){return""+jm++},[])}function Um(e){var r=Vm(),t=e.dragHandleUsageInstructions||At.dragHandleUsageInstructions;return re.createElement(Cp,null,function(n){return re.createElement(zm,{nonce:e.nonce,contextId:r,setCallbacks:n,dragHandleUsageInstructions:t,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd},e.children)})}var es=function(r){return function(t){return r===t}},qm=es("scroll"),_m=es("auto"),Ii=function(r,t){return t(r.overflowX)||t(r.overflowY)},Xm=function(r){var t=window.getComputedStyle(r),n={overflowX:t.overflowX,overflowY:t.overflowY};return Ii(n,qm)||Ii(n,_m)},Km=function(){return!1},Ym=function e(r){return r==null?null:r===document.body?Km()?r:null:r===document.documentElement?null:Xm(r)?r:e(r.parentElement)},ea=function(e){return{x:e.scrollLeft,y:e.scrollTop}},Jm=function e(r){if(!r)return!1;var t=window.getComputedStyle(r);return t.position==="fixed"?!0:e(r.parentElement)},Qm=function(e){var r=Ym(e),t=Jm(e);return{closestScrollable:r,isFixedOnPage:t}},Zm=function(e){var r=e.descriptor,t=e.isEnabled,n=e.isCombineEnabled,o=e.isFixedOnPage,i=e.direction,l=e.client,s=e.page,p=e.closest,u=function(){if(!p)return null;var b=p.scrollSize,C=p.client,S=kl({scrollHeight:b.scrollHeight,scrollWidth:b.scrollWidth,height:C.paddingBox.height,width:C.paddingBox.width});return{pageMarginBox:p.page.marginBox,frameClient:C,scrollSize:b,shouldClipSubject:p.shouldClipSubject,scroll:{initial:p.scroll,current:p.scroll,max:S,diff:{value:$e,displacement:$e}}}}(),m=i==="vertical"?Ia:yl,g=Gr({page:s,withPlaceholder:null,axis:m,frame:u}),f={descriptor:r,isCombineEnabled:n,isFixedOnPage:o,axis:m,isEnabled:t,client:l,page:s,frame:u,subject:g};return f},eh=function(r,t){var n=ul(r);if(!t||r!==t)return n;var o=n.paddingBox.top-t.scrollTop,i=n.paddingBox.left-t.scrollLeft,l=o+t.scrollHeight,s=i+t.scrollWidth,p={top:o,right:s,bottom:l,left:i},u=ya(p,n.border),m=wa({borderBox:u,margin:n.margin,border:n.border,padding:n.padding});return m},rh=function(e){var r=e.ref,t=e.descriptor,n=e.env,o=e.windowScroll,i=e.direction,l=e.isDropDisabled,s=e.isCombineEnabled,p=e.shouldClipSubject,u=n.closestScrollable,m=eh(r,u),g=Gt(m,o),f=function(){if(!u)return null;var C=ul(u),S={scrollHeight:u.scrollHeight,scrollWidth:u.scrollWidth};return{client:C,page:Gt(C,o),scroll:ea(u),scrollSize:S,shouldClipSubject:p}}(),b=Zm({descriptor:t,isEnabled:!l,isCombineEnabled:s,isFixedOnPage:n.isFixedOnPage,direction:i,client:m,page:g,closest:f});return b},th={passive:!1},nh={passive:!0},Pi=function(e){return e.shouldPublishImmediately?th:nh};function Vt(e){var r=G.useContext(e);return r||W(!1),r}var Et=function(r){return r&&r.env.closestScrollable||null};function ah(e){var r=G.useRef(null),t=Vt(fn),n=Ga("droppable"),o=t.registry,i=t.marshal,l=_l(e),s=me(function(){return{id:e.droppableId,type:e.type,mode:e.mode}},[e.droppableId,e.mode,e.type]),p=G.useRef(s),u=me(function(){return ke(function(A,V){r.current||W(!1);var q={x:A,y:V};i.updateDroppableScroll(s.id,q)})},[s.id,i]),m=J(function(){var A=r.current;return!A||!A.env.closestScrollable?$e:ea(A.env.closestScrollable)},[]),g=J(function(){var A=m();u(A.x,A.y)},[m,u]),f=me(function(){return rt(g)},[g]),b=J(function(){var A=r.current,V=Et(A);A&&V||W(!1);var q=A.scrollOptions;if(q.shouldPublishImmediately){g();return}f()},[f,g]),C=J(function(A,V){r.current&&W(!1);var q=l.current,Y=q.getDroppableRef();Y||W(!1);var se=Qm(Y),Q={ref:Y,descriptor:s,env:se,scrollOptions:V};r.current=Q;var be=rh({ref:Y,descriptor:s,env:se,windowScroll:A,direction:q.direction,isDropDisabled:q.isDropDisabled,isCombineEnabled:q.isCombineEnabled,shouldClipSubject:!q.ignoreContainerClipping}),pe=se.closestScrollable;return pe&&(pe.setAttribute(wi.contextId,t.contextId),pe.addEventListener("scroll",b,Pi(Q.scrollOptions))),be},[t.contextId,s,b,l]),S=J(function(){var A=r.current,V=Et(A);return A&&V||W(!1),ea(V)},[]),P=J(function(){var A=r.current;A||W(!1);var V=Et(A);r.current=null,V&&(f.cancel(),V.removeAttribute(wi.contextId),V.removeEventListener("scroll",b,Pi(A.scrollOptions)))},[b,f]),D=J(function(A){var V=r.current;V||W(!1);var q=Et(V);q||W(!1),q.scrollTop+=A.y,q.scrollLeft+=A.x},[]),B=me(function(){return{getDimensionAndWatchScroll:C,getScrollWhileDragging:S,dragStopped:P,scroll:D}},[P,C,S,D]),O=me(function(){return{uniqueId:n,descriptor:s,callbacks:B}},[B,s,n]);rr(function(){return p.current=O.descriptor,o.droppable.register(O),function(){r.current&&P(),o.droppable.unregister(O)}},[B,s,P,O,i,o.droppable]),rr(function(){r.current&&i.updateDroppableIsEnabled(p.current.id,!e.isDropDisabled)},[e.isDropDisabled,i]),rr(function(){r.current&&i.updateDroppableIsCombineEnabled(p.current.id,e.isCombineEnabled)},[e.isCombineEnabled,i])}function Fn(){}var Ri={width:0,height:0,margin:Dp},oh=function(r){var t=r.isAnimatingOpenOnMount,n=r.placeholder,o=r.animate;return t||o==="close"?Ri:{height:n.client.borderBox.height,width:n.client.borderBox.width,margin:n.client.margin}},ih=function(r){var t=r.isAnimatingOpenOnMount,n=r.placeholder,o=r.animate,i=oh({isAnimatingOpenOnMount:t,placeholder:n,animate:o});return{display:n.display,boxSizing:"border-box",width:i.width,height:i.height,marginTop:i.margin.top,marginRight:i.margin.right,marginBottom:i.margin.bottom,marginLeft:i.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:o!=="none"?Qr.placeholder:null}};function lh(e){var r=G.useRef(null),t=J(function(){r.current&&(clearTimeout(r.current),r.current=null)},[]),n=e.animate,o=e.onTransitionEnd,i=e.onClose,l=e.contextId,s=G.useState(e.animate==="open"),p=s[0],u=s[1];G.useEffect(function(){return p?n!=="open"?(t(),u(!1),Fn):r.current?Fn:(r.current=setTimeout(function(){r.current=null,u(!1)}),t):Fn},[n,p,t]);var m=J(function(f){f.propertyName==="height"&&(o(),n==="close"&&i())},[n,i,o]),g=ih({isAnimatingOpenOnMount:p,animate:e.animate,placeholder:e.placeholder});return re.createElement(e.placeholder.tagName,{style:g,"data-rbd-placeholder-context-id":l,onTransitionEnd:m,ref:e.innerRef})}var sh=re.memo(lh),$a=re.createContext(null),uh=function(e){Gi(r,e);function r(){for(var n,o=arguments.length,i=new Array(o),l=0;l<o;l++)i[l]=arguments[l];return n=e.call.apply(e,[this].concat(i))||this,n.state={isVisible:Boolean(n.props.on),data:n.props.on,animate:n.props.shouldAnimate&&n.props.on?"open":"none"},n.onClose=function(){n.state.animate==="close"&&n.setState({isVisible:!1})},n}r.getDerivedStateFromProps=function(o,i){return o.shouldAnimate?o.on?{isVisible:!0,data:o.on,animate:"open"}:i.isVisible?{isVisible:!0,data:i.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(o.on),data:o.on,animate:"none"}};var t=r.prototype;return t.render=function(){if(!this.state.isVisible)return null;var o={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(o)},r}(re.PureComponent),Di={dragging:5e3,dropAnimating:4500},ch=function(r,t){return t?Qr.drop(t.duration):r?Qr.snap:Qr.fluid},dh=function(r,t){return r?t?ot.opacity.drop:ot.opacity.combining:null},fh=function(r){return r.forceShouldAnimate!=null?r.forceShouldAnimate:r.mode==="SNAP"};function ph(e){var r=e.dimension,t=r.client,n=e.offset,o=e.combineWith,i=e.dropping,l=Boolean(o),s=fh(e),p=Boolean(i),u=p?Jn.drop(n,l):Jn.moveTo(n),m={position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:ch(s,i),transform:u,opacity:dh(l,p),zIndex:p?Di.dropAnimating:Di.dragging,pointerEvents:"none"};return m}function vh(e){return{transform:Jn.moveTo(e.offset),transition:e.shouldAnimateDisplacement?null:"none"}}function gh(e){return e.type==="DRAGGING"?ph(e):vh(e)}function mh(e,r,t){t===void 0&&(t=$e);var n=window.getComputedStyle(r),o=r.getBoundingClientRect(),i=sl(o,n),l=Gt(i,t),s={client:i,tagName:r.tagName.toLowerCase(),display:n.display},p={x:i.marginBox.width,y:i.marginBox.height},u={descriptor:e,placeholder:s,displaceBy:p,client:i,page:l};return u}function hh(e){var r=Ga("draggable"),t=e.descriptor,n=e.registry,o=e.getDraggableRef,i=e.canDragInteractiveElements,l=e.shouldRespectForcePress,s=e.isEnabled,p=me(function(){return{canDragInteractiveElements:i,shouldRespectForcePress:l,isEnabled:s}},[i,s,l]),u=J(function(b){var C=o();return C||W(!1),mh(t,C,b)},[t,o]),m=me(function(){return{uniqueId:r,descriptor:t,options:p,getDimension:u}},[t,u,p,r]),g=G.useRef(m),f=G.useRef(!0);rr(function(){return n.draggable.register(g.current),function(){return n.draggable.unregister(g.current)}},[n.draggable]),rr(function(){if(f.current){f.current=!1;return}var b=g.current;g.current=m,n.draggable.update(m,b)},[m,n.draggable])}function bh(e){e.preventDefault()}function yh(e){var r=G.useRef(null),t=J(function(Q){r.current=Q},[]),n=J(function(){return r.current},[]),o=Vt(fn),i=o.contextId,l=o.dragHandleUsageInstructionsId,s=o.registry,p=Vt($a),u=p.type,m=p.droppableId,g=me(function(){return{id:e.draggableId,index:e.index,type:u,droppableId:m}},[e.draggableId,e.index,u,m]),f=e.children,b=e.draggableId,C=e.isEnabled,S=e.shouldRespectForcePress,P=e.canDragInteractiveElements,D=e.isClone,B=e.mapped,O=e.dropAnimationFinished;if(!D){var A=me(function(){return{descriptor:g,registry:s,getDraggableRef:n,canDragInteractiveElements:P,shouldRespectForcePress:S,isEnabled:C}},[g,s,n,P,S,C]);hh(A)}var V=me(function(){return C?{tabIndex:0,role:"button","aria-describedby":l,"data-rbd-drag-handle-draggable-id":b,"data-rbd-drag-handle-context-id":i,draggable:!1,onDragStart:bh}:null},[i,l,b,C]),q=J(function(Q){B.type==="DRAGGING"&&B.dropping&&Q.propertyName==="transform"&&O()},[O,B]),Y=me(function(){var Q=gh(B),be=B.type==="DRAGGING"&&B.dropping?q:null,pe={innerRef:t,draggableProps:{"data-rbd-draggable-context-id":i,"data-rbd-draggable-id":b,style:Q,onTransitionEnd:be},dragHandleProps:V};return pe},[i,V,b,B,q,t]),se=me(function(){return{draggableId:g.id,type:g.type,source:{index:g.index,droppableId:g.droppableId}}},[g.droppableId,g.id,g.index,g.type]);return f(Y,B.snapshot,se)}var rs=function(e,r){return e===r},ts=function(e){var r=e.combine,t=e.destination;return t?t.droppableId:r?r.droppableId:null},wh=function(r){return r.combine?r.combine.draggableId:null},Ch=function(r){return r.at&&r.at.type==="COMBINE"?r.at.combine.draggableId:null};function Sh(){var e=ke(function(o,i){return{x:o,y:i}}),r=ke(function(o,i,l,s,p){return{isDragging:!0,isClone:i,isDropAnimating:Boolean(p),dropAnimation:p,mode:o,draggingOver:l,combineWith:s,combineTargetFor:null}}),t=ke(function(o,i,l,s,p,u,m){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:p,combineWith:u,mode:i,offset:o,dimension:l,forceShouldAnimate:m,snapshot:r(i,s,p,u,null)}}}),n=function(i,l){if(i.isDragging){if(i.critical.draggable.id!==l.draggableId)return null;var s=i.current.client.offset,p=i.dimensions.draggables[l.draggableId],u=er(i.impact),m=Ch(i.impact),g=i.forceShouldAnimate;return t(e(s.x,s.y),i.movementMode,p,l.isClone,u,m,g)}if(i.phase==="DROP_ANIMATING"){var f=i.completed;if(f.result.draggableId!==l.draggableId)return null;var b=l.isClone,C=i.dimensions.draggables[l.draggableId],S=f.result,P=S.mode,D=ts(S),B=wh(S),O=i.dropDuration,A={duration:O,curve:Aa.drop,moveTo:i.newHomeClientOffset,opacity:B?ot.opacity.drop:null,scale:B?ot.scale.drop:null};return{mapped:{type:"DRAGGING",offset:i.newHomeClientOffset,dimension:C,dropping:A,draggingOver:D,combineWith:B,mode:P,forceShouldAnimate:null,snapshot:r(P,b,D,B,A)}}}return null};return n}function ns(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var xh={mapped:{type:"SECONDARY",offset:$e,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:ns(null)}};function Ih(){var e=ke(function(l,s){return{x:l,y:s}}),r=ke(ns),t=ke(function(l,s,p){return s===void 0&&(s=null),{mapped:{type:"SECONDARY",offset:l,combineTargetFor:s,shouldAnimateDisplacement:p,snapshot:r(s)}}}),n=function(s){return s?t($e,s,!0):null},o=function(s,p,u,m){var g=u.displaced.visible[s],f=Boolean(m.inVirtualList&&m.effected[s]),b=sn(u),C=b&&b.draggableId===s?p:null;if(!g){if(!f)return n(C);if(u.displaced.invisible[s])return null;var S=Hr(m.displacedBy.point),P=e(S.x,S.y);return t(P,C,!0)}if(f)return n(C);var D=u.displacedBy.point,B=e(D.x,D.y);return t(B,C,g.shouldAnimate)},i=function(s,p){if(s.isDragging)return s.critical.draggable.id===p.draggableId?null:o(p.draggableId,s.critical.draggable.id,s.impact,s.afterCritical);if(s.phase==="DROP_ANIMATING"){var u=s.completed;return u.result.draggableId===p.draggableId?null:o(p.draggableId,u.result.draggableId,u.impact,u.afterCritical)}return null};return i}var Ph=function(){var r=Sh(),t=Ih(),n=function(i,l){return r(i,l)||t(i,l)||xh};return n},Rh={dropAnimationFinished:Ll},Dh=il(Ph,Rh,null,{context:Wa,pure:!0,areStatePropsEqual:rs})(yh);function as(e){var r=Vt($a),t=r.isUsingCloneFor;return t===e.draggableId&&!e.isClone?null:re.createElement(Dh,e)}function Eh(e){var r=typeof e.isDragDisabled=="boolean"?!e.isDragDisabled:!0,t=Boolean(e.disableInteractiveElementBlocking),n=Boolean(e.shouldRespectForcePress);return re.createElement(as,ae({},e,{isClone:!1,isEnabled:r,canDragInteractiveElements:t,shouldRespectForcePress:n}))}function Bh(e){var r=G.useContext(fn);r||W(!1);var t=r.contextId,n=r.isMovementAllowed,o=G.useRef(null),i=G.useRef(null),l=e.children,s=e.droppableId,p=e.type,u=e.mode,m=e.direction,g=e.ignoreContainerClipping,f=e.isDropDisabled,b=e.isCombineEnabled,C=e.snapshot,S=e.useClone,P=e.updateViewportMaxScroll,D=e.getContainerForClone,B=J(function(){return o.current},[]),O=J(function(pe){o.current=pe},[]);J(function(){return i.current},[]);var A=J(function(pe){i.current=pe},[]),V=J(function(){n()&&P({maxScroll:Hl()})},[n,P]);ah({droppableId:s,type:p,mode:u,direction:m,isDropDisabled:f,isCombineEnabled:b,ignoreContainerClipping:g,getDroppableRef:B});var q=re.createElement(uh,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},function(pe){var Le=pe.onClose,$=pe.data,z=pe.animate;return re.createElement(sh,{placeholder:$,onClose:Le,innerRef:A,animate:z,contextId:t,onTransitionEnd:V})}),Y=me(function(){return{innerRef:O,placeholder:q,droppableProps:{"data-rbd-droppable-id":s,"data-rbd-droppable-context-id":t}}},[t,s,q,O]),se=S?S.dragging.draggableId:null,Q=me(function(){return{droppableId:s,type:p,isUsingCloneFor:se}},[s,se,p]);function be(){if(!S)return null;var pe=S.dragging,Le=S.render,$=re.createElement(as,{draggableId:pe.draggableId,index:pe.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},function(z,ue){return Le(z,ue,pe)});return Eu.createPortal($,D())}return re.createElement($a.Provider,{value:Q},l(Y,C),be())}var Ln=function(r,t){return r===t.droppable.type},Ei=function(r,t){return t.draggables[r.draggable.id]},Oh=function(){var r={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t=ae({},r,{shouldAnimatePlaceholder:!1}),n=ke(function(l){return{draggableId:l.id,type:l.type,source:{index:l.index,droppableId:l.droppableId}}}),o=ke(function(l,s,p,u,m,g){var f=m.descriptor.id,b=m.descriptor.droppableId===l;if(b){var C=g?{render:g,dragging:n(m.descriptor)}:null,S={isDraggingOver:p,draggingOverWith:p?f:null,draggingFromThisWith:f,isUsingPlaceholder:!0};return{placeholder:m.placeholder,shouldAnimatePlaceholder:!1,snapshot:S,useClone:C}}if(!s)return t;if(!u)return r;var P={isDraggingOver:p,draggingOverWith:f,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:m.placeholder,shouldAnimatePlaceholder:!0,snapshot:P,useClone:null}}),i=function(s,p){var u=p.droppableId,m=p.type,g=!p.isDropDisabled,f=p.renderClone;if(s.isDragging){var b=s.critical;if(!Ln(m,b))return t;var C=Ei(b,s.dimensions),S=er(s.impact)===u;return o(u,g,S,S,C,f)}if(s.phase==="DROP_ANIMATING"){var P=s.completed;if(!Ln(m,P.critical))return t;var D=Ei(P.critical,s.dimensions);return o(u,g,ts(P.result)===u,er(P.impact)===u,D,f)}if(s.phase==="IDLE"&&s.completed&&!s.shouldFlush){var B=s.completed;if(!Ln(m,B.critical))return t;var O=er(B.impact)===u,A=Boolean(B.impact.at&&B.impact.at.type==="COMBINE"),V=B.critical.droppable.id===u;return O?A?r:t:V?r:t}return t};return i},Ah={updateViewportMaxScroll:kv};function Th(){return document.body||W(!1),document.body}var Mh={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:Th},os=il(Oh,Ah,null,{context:Wa,pure:!0,areStatePropsEqual:rs})(Bh);os.defaultProps=Mh;const Nh="_columnManagerRow_e56pa_1",Fh="_columnManageLabel_e56pa_7",Lh="_columnManageSwitch_e56pa_11",Wn={columnManagerRow:Nh,columnManageLabel:Fh,columnManageSwitch:Lh},Wh=(e,r)=>({...r,...e&&{background:"transparent"}});function Gh({isOpen:e,onRequestClose:r,columns:t,hiddenColumns:n,setColumns:o,setHiddenColumns:i}){const{t:l}=it(),s=u=>{if(!u.destination)return;const m=Array.from(t),[g]=m.splice(u.source.index,1);m.splice(u.destination.index,0,g),o(m),localStorage.setItem("columns",JSON.stringify(m))},p=(u,m)=>{if(!m)n.push(u.accessor);else{const g=n.indexOf(u.accessor);n.splice(g,1)}i(Array.from(n)),localStorage.setItem("hiddenColumns",JSON.stringify(n))};return j(ki,{isOpen:e,onRequestClose:r,children:j("div",{children:j(Um,{onDragEnd:s,children:j(os,{droppableId:"droppable-modal",children:u=>Re("div",{...u.droppableProps,ref:u.innerRef,children:[t.filter(m=>m.accessor!=="id").map(m=>{const g=!n.includes(m.accessor);return j(Eh,{draggableId:m.accessor,index:t.findIndex(f=>f.accessor===m.accessor),children:(f,b)=>Re("div",{ref:f.innerRef,...f.draggableProps,...f.dragHandleProps,className:Wn.columnManagerRow,style:Wh(b.isDragging,f.draggableProps.style),children:[j(Uu,{}),j("span",{className:Wn.columnManageLabel,children:l(m.Header)}),j("div",{className:Wn.columnManageSwitch,children:j(Bu,{size:"mini",checked:g,onChange:C=>p(m,C)})})]})},m.accessor)}),u.placeholder]})})})})})}const kh="_sourceipTable_2lem6_1",$h="_iptableTipContainer_2lem6_5",Bi={sourceipTable:kh,iptableTipContainer:$h};function Hh({isOpen:e,onRequestClose:r,sourceMap:t,setSourceMap:n}){const{t:o}=it(),i=(l,s,p)=>{t[s][l]=p,n(Array.from(t))};return Re(ki,{isOpen:e,onRequestClose:r,children:[Re("table",{className:Bi.sourceipTable,children:[j("thead",{children:Re("tr",{children:[j("th",{children:o("c_source")}),j("th",{children:o("device_name")})]})}),j("tbody",{children:t.map((l,s)=>Re("tr",{children:[j("td",{children:j(kn,{type:"text",name:"reg",autoComplete:"off",value:l.reg,onChange:p=>i("reg",s,p.target.value)})}),j("td",{children:j(kn,{type:"text",name:"name",autoComplete:"off",value:l.name,onChange:p=>i("name",s,p.target.value)})}),j("td",{children:j(Tt,{onClick:()=>t.splice(s,1),children:o("delete")})})]},`${s}`))})]}),Re("div",{children:[j("div",{className:Bi.iptableTipContainer,children:o("sourceip_tip")}),j(Tt,{onClick:()=>t.push({reg:"",name:""}),children:o("add_tag")})]})]})}const{useEffect:zh,useState:Qe,useRef:jh,useCallback:wr}=re,ra="ALL_SOURCE_IP",Vh=localStorage.getItem("sourceMap")?JSON.parse(localStorage.getItem("sourceMap")):[],Uh=30;function qh(e){const r={};for(let t=0;t<e.length;t++){const n=e[t];r[n.id]=n}return r}function _h(e,r){return e.toLowerCase().includes(r.toLowerCase())}function Xh(e,r){return e.filter(t=>t.sourceIP===r)}function Oi(e,r,t){let n=e;return r!==""&&(n=e.filter(o=>[o.host,o.sourceIP,o.sourcePort,o.destinationIP,o.chains,o.rule,o.type,o.network,o.process].some(i=>_h(i,r)))),t!==ra&&(n=Xh(n,t)),n}function is(e,r,t){let n=t??e;return r.forEach(({reg:o,name:i})=>{o&&(o.startsWith("/")?new RegExp(o.replace("/",""),"g").test(e)&&i&&(n=`${i}(${e})`):e===o&&i&&(n=`${i}(${e})`))}),n}function Kh(e,r,t,n){const{id:o,metadata:i,upload:l,download:s,start:p,chains:u,rule:m,rulePayload:g}=e,{host:f,destinationPort:b,destinationIP:C,remoteDestination:S,network:P,type:D,sourceIP:B,sourcePort:O,process:A,sniffHost:V}=i;let q=f;q===""&&(q=C);const Y=r[o],se=`${B}:${O}`;return{id:o,upload:l,download:s,start:t-new Date(p).valueOf(),chains:Yh(u),rule:g?`${m} :: ${g}`:m,...i,host:`${q}:${b}`,sniffHost:V||"-",type:`${D}(${P})`,source:is(B,n,se),downloadSpeedCurr:s-(Y?Y.download:0),uploadSpeedCurr:l-(Y?Y.upload:0),process:A||"-",destinationIP:S||C||f}}function Yh(e){if(!Array.isArray(e)||e.length===0)return"";if(e.length===1)return e[0];if(e.length===2)return`${e[1]} -> ${e[0]}`;const r=e.pop(),t=e.shift();return`${r} -> ${t}`}function Ai(e,r,t){return t.length>0?j(af,{data:t,columns:e,hiddenColumns:r}):j("div",{className:Lr.placeHolder,children:j(Mu,{width:200,height:200,c1:"var(--color-text)"})})}function Ti({qty:e}){return e<100?""+e:"99+"}const Bt=!0,ls=["id"],ta=[{accessor:"id",show:!1},{Header:"c_type",accessor:"type"},{Header:"c_process",accessor:"process"},{Header:"c_host",accessor:"host"},{Header:"c_rule",accessor:"rule"},{Header:"c_chains",accessor:"chains"},{Header:"c_time",accessor:"start"},{Header:"c_dl_speed",accessor:"downloadSpeedCurr",sortDescFirst:Bt},{Header:"c_ul_speed",accessor:"uploadSpeedCurr",sortDescFirst:Bt},{Header:"c_dl",accessor:"download",sortDescFirst:Bt},{Header:"c_ul",accessor:"upload",sortDescFirst:Bt},{Header:"c_source",accessor:"source"},{Header:"c_destination_ip",accessor:"destinationIP"},{Header:"c_sni",accessor:"sniffHost"},{Header:"c_ctrl",accessor:"ctrl"}],Mi=localStorage.getItem("hiddenColumns"),Ni=localStorage.getItem("columns"),Jh=Mi?JSON.parse(Mi):[...ls],Gn=Ni?JSON.parse(Ni):null,Qh=Gn?[...ta].sort((e,r)=>{const t=Gn.findIndex(o=>o.accessor===e.accessor),n=Gn.findIndex(o=>o.accessor===r.accessor);return t===-1?1:n===-1?-1:t-n}):[...ta];function Zh({apiConfig:e}){const{t:r}=it(),[t,n]=Qe(!1),[o,i]=Qe(Jh),[l,s]=Qe(Qh),p=()=>{n(!1)},u=()=>{i([...ls]),s([...ta]),localStorage.removeItem("hiddenColumns"),localStorage.removeItem("columns")},[m,g]=Qe(!1),[f,b]=Qe(Vh),[C,S]=Fu(),[P,D]=Qe([]),[B,O]=Qe([]),[A,V]=Qe(""),[q,Y]=Qe(ra),se=Oi(P,A,q),Q=Oi(B,A,q),pe=(xe=>[[ra,r("All")],...Array.from(new Set(xe.map(Ue=>Ue.sourceIP))).sort().map(Ue=>[Ue,is(Ue,f).trim()||r("internel")])])(P),[Le,$]=Qe(!1),z=wr(()=>$(!0),[]),ue=wr(()=>$(!1),[]),Ie=wr(async()=>{for(const xe of se)await Li(e,xe.id);ue()},[e,se,ue]),[Se,we]=Qe(!1),Ee=wr(()=>we(!0),[]),De=wr(()=>we(!1),[]),[Oe,Fe]=Qe(!1),ur=wr(()=>{Fe(xe=>!xe)},[]),Ve=wr(()=>{Ou(e),De()},[e,De]),Ae=jh(P),We=wr(({connections:xe})=>{const Ue=qh(Ae.current),vr=Date.now(),Je=xe.map(ar=>Kh(ar,Ue,vr,f)),Dr=[];for(const ar of Ae.current)Je.findIndex(Vr=>Vr.id===ar.id)<0&&Dr.push(ar);O(ar=>[...Dr,...ar].slice(0,101)),Je&&(Je.length!==0||Ae.current.length!==0)&&!Oe?(Ae.current=Je,D(Je)):Ae.current=Je},[D,f,Oe]),[cr,nr]=Qe(0);zh(()=>Au(e,We,()=>{setTimeout(()=>{nr(xe=>xe+1)},1e3)}),[e,We,cr,nr]);const dr=()=>{f.length===0&&f.push({reg:"",name:""}),g(!0)},Ke=()=>{b(f.filter(xe=>xe.reg||xe.name)),localStorage.setItem("sourceMap",JSON.stringify(f)),g(!1)};return Re("div",{children:[Re("div",{className:Lr.header,children:[j(Tu,{title:r("Connections")}),j("div",{className:Lr.inputWrapper,children:j(kn,{type:"text",name:"filter",autoComplete:"off",className:Lr.input,placeholder:r("Search"),onChange:xe=>V(xe.target.value)})})]}),Re(qt,{children:[Re("div",{style:{display:"flex",flexWrap:"wrap",paddingLeft:"30px",justifyContent:"flex-start"},children:[Re(_t,{style:{padding:"0 15px 0 0"},children:[Re(Zr,{children:[j("span",{children:r("Active")}),j("span",{className:Lr.connQty,children:j(Ti,{qty:se.length})})]}),Re(Zr,{children:[j("span",{children:r("Closed")}),j("span",{className:Lr.connQty,children:j(Ti,{qty:Q.length})})]})]}),j(Nu,{options:pe,selected:q,style:{width:"unset"},onChange:xe=>Y(xe.target.value)})]}),j("div",{ref:C,style:{padding:30,paddingBottom:10,paddingTop:10},children:Re("div",{style:{height:S-Uh,overflow:"auto"},children:[Re(et,{children:[Ai(l,o,se),Re(Mo,{icon:Oe?j(Hu,{size:16}):j(zu,{size:16}),mainButtonStyles:Oe?{background:"#e74c3c"}:{},style:No,text:r(Oe?"Resume Refresh":"Pause Refresh"),onClick:ur,children:[j(Br,{text:r("close_all_connections"),onClick:Ee,children:j(To,{size:10})}),j(Br,{text:r("close_filter_connections"),onClick:z,children:j(To,{size:10})}),j(Br,{text:r("manage_column"),onClick:()=>n(!0),children:j(Lo,{size:10})}),j(Br,{text:r("reset_column"),onClick:u,children:j(Fo,{size:10})}),j(Br,{text:r("client_tag"),onClick:dr,children:j(Wo,{size:10})})]})]}),Re(et,{children:[Ai(l,o,Q),Re(Mo,{icon:j(Lo,{size:16}),style:No,text:r("manage_column"),onClick:()=>n(!0),children:[j(Br,{text:r("reset_column"),onClick:u,children:j(Fo,{size:10})}),j(Br,{text:r("client_tag"),onClick:dr,children:j(Wo,{size:10})})]})]})]})}),j(qn,{isOpen:Se,primaryButtonOnTap:Ve,onRequestClose:De}),j(qn,{confirm:"close_filter_connections",isOpen:Le,primaryButtonOnTap:Ie,onRequestClose:ue}),j(Gh,{isOpen:t,onRequestClose:p,columns:l,hiddenColumns:o,setColumns:s,setHiddenColumns:i}),j(Hh,{isOpen:m,onRequestClose:Ke,sourceMap:f,setSourceMap:b})]})]})}const eb=e=>({apiConfig:Wi(e)}),cb=Fi(eb)(Zh);export{cb as default};
