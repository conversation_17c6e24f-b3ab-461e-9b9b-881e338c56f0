#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API错误的脚本
用于验证监控服务是否能正确识别特定端口的错误
"""

import requests
import sys
import time

def test_api_error(port):
    """测试指定端口的API，触发错误"""
    print(f"测试端口 {port} 的API...")
    
    # 使用一个会导致Cookie失效错误的请求
    url = f"http://127.0.0.1:{port}/api/douyin/web/handler_user_profile"
    params = {
        "sec_user_id": "MS4wLjABAAAA_invalid_user_id_to_trigger_error"
    }
    
    try:
        # 发送多个请求以触发错误阈值
        for i in range(10):
            response = requests.get(url, params=params, timeout=10)
            print(f"请求 {i+1}: 状态码 {response.status_code}")
            
            # 如果收到400错误，说明可能触发了Cookie失效
            if response.status_code == 400:
                print("收到400错误，可能触发Cookie失效检测")
            
            time.sleep(1)
            
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        port = int(sys.argv[1])
    else:
        port = 8081  # 默认测试端口8081
    
    print(f"开始测试API端口 {port}...")
    print("注意：这个测试可能会触发监控服务的Cookie更新机制")
    test_api_error(port)
    print("测试完成")