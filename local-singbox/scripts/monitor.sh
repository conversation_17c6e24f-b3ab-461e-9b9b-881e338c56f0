#!/bin/bash
# Singbox 代理服务监控脚本

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置路径
SINGBOX_DIR="/home/<USER>/api/local-singbox"
LOG_FILE="$SINGBOX_DIR/logs/monitor.log"
SWITCH_LOG="$SINGBOX_DIR/logs/switch_node.log"
SINGBOX_LOG="$SINGBOX_DIR/logs/singbox.log"
PID_FILE="$SINGBOX_DIR/singbox.pid"
SWITCH_PID_FILE="$SINGBOX_DIR/switch_node.pid"

# Telegram 配置
TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_CHAT_ID="235196660"

# 记录日志
log() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 发送Telegram消息
send_telegram_message() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -d "chat_id=${TELEGRAM_CHAT_ID}" \
        -d "text=${message}" \
        -d "parse_mode=HTML" > /dev/null
    
    if [ $? -eq 0 ]; then
        log "${GREEN}Telegram消息发送成功${NC}"
    else
        log "${RED}Telegram消息发送失败${NC}"
    fi
}

# 检查服务状态
check_service() {
    log "${YELLOW}检查服务状态...${NC}"
    
    # 检查sing-box进程
    if [ -f "$PID_FILE" ]; then
        pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null; then
            log "${GREEN}Singbox 代理服务正在运行 (PID: $pid)${NC}"
            service_status="正常"
        else
            log "${RED}警告: Singbox 代理服务PID文件存在，但进程不存在${NC}"
            service_status="异常 - PID文件存在但进程不存在"
            return 1
        fi
    else
        # 尝试通过进程名查找
        pid=$(pgrep -f "/home/<USER>/api/local-singbox/bin/sing-box")
        if [ -n "$pid" ]; then
            log "${YELLOW}Singbox 代理服务正在运行，但PID文件不存在 (PID: $pid)${NC}"
            echo "$pid" > "$PID_FILE"
            service_status="正常 - 已修复PID文件"
        else
            log "${RED}错误: Singbox 代理服务未运行${NC}"
            service_status="未运行"
            return 1
        fi
    fi
    
    # 检查节点切换脚本
    if [ -f "$SWITCH_PID_FILE" ]; then
        pid=$(cat "$SWITCH_PID_FILE")
        if ps -p "$pid" > /dev/null; then
            log "${GREEN}节点切换脚本正在运行 (PID: $pid)${NC}"
            switch_status="正常"
        else
            log "${RED}警告: 节点切换脚本PID文件存在，但进程不存在${NC}"
            switch_status="异常 - PID文件存在但进程不存在"
            return 1
        fi
    else
        # 尝试通过进程名查找
        pid=$(pgrep -f "python3 /home/<USER>/api/local-singbox/scripts/switch_node.py")
        if [ -n "$pid" ]; then
            log "${YELLOW}节点切换脚本正在运行，但PID文件不存在 (PID: $pid)${NC}"
            echo "$pid" > "$SWITCH_PID_FILE"
            switch_status="正常 - 已修复PID文件"
        else
            log "${RED}错误: 节点切换脚本未运行${NC}"
            switch_status="未运行"
            return 1
        fi
    fi
    
    return 0
}

# 检查代理连接
check_proxy() {
    log "${YELLOW}检查代理连接...${NC}"
    
    # 测试SOCKS5代理连接
    timeout 10 curl -s --socks5 127.0.0.1:1080 https://www.google.com > /dev/null
    if [ $? -eq 0 ]; then
        log "${GREEN}代理连接正常，可以访问外网${NC}"
        proxy_status="正常"
    else
        log "${RED}错误: 代理连接异常，无法访问外网${NC}"
        proxy_status="异常 - 无法访问外网"
        return 1
    fi
    
    return 0
}

# 获取当前IP
get_current_ip() {
    log "${YELLOW}获取当前出口IP...${NC}"
    
    current_ip=$(timeout 10 curl -s --socks5 127.0.0.1:1080 https://api.ipify.org)
    if [ -n "$current_ip" ]; then
        log "${GREEN}当前出口IP: $current_ip${NC}"
        echo "$current_ip"
    else
        log "${RED}错误: 无法获取当前出口IP${NC}"
        echo "未知"
    fi
}

# 检查IP切换
check_ip_switching() {
    log "${YELLOW}检查IP切换情况...${NC}"
    
    # 获取初始IP
    initial_ip=$(get_current_ip)
    if [ "$initial_ip" == "未知" ]; then
        ip_switch_status="异常 - 无法获取当前IP"
        return 1
    fi
    
    # 检查最近的IP切换记录
    recent_switches=$(tail -n 50 "$SWITCH_LOG" | grep -c "IP已变化")
    recent_time=$(tail -n 50 "$SWITCH_LOG" | grep "IP已变化" | tail -n 1 | cut -d' ' -f1,2)
    
    if [ $recent_switches -gt 0 ]; then
        log "${GREEN}最近有 $recent_switches 次IP切换，最后一次切换时间: $recent_time${NC}"
        ip_switch_status="正常 - 最近有 $recent_switches 次切换"
    else
        log "${YELLOW}警告: 最近没有IP切换记录${NC}"
        ip_switch_status="警告 - 最近没有切换记录"
    fi
    
    # 检查节点切换间隔
    interval=$(grep -o "SWITCH_INTERVAL = [0-9]*" "$SINGBOX_DIR/scripts/switch_node.py" | awk '{print $3}')
    log "${BLUE}当前配置的节点切换间隔: ${interval}秒${NC}"
    
    return 0
}

# 检查订阅更新
check_subscription() {
    log "${YELLOW}检查订阅更新情况...${NC}"
    
    # 检查最近的订阅更新记录
    if [ -f "$SINGBOX_DIR/logs/update_subscription.log" ]; then
        last_update=$(grep -a "订阅更新完成" "$SINGBOX_DIR/logs/update_subscription.log" | tail -n 1)
        if [ -n "$last_update" ]; then
            update_time=$(echo "$last_update" | cut -d' ' -f1,2)
            log "${GREEN}最后一次订阅更新: $update_time${NC}"
            subscription_status="正常 - 最后更新: $update_time"
        else
            log "${YELLOW}警告: 没有找到成功的订阅更新记录${NC}"
            subscription_status="警告 - 没有成功记录"
        fi
        
        # 检查crontab设置
        cron_setting=$(crontab -l | grep "update_subscription.sh")
        if [ -n "$cron_setting" ]; then
            log "${GREEN}订阅更新定时任务已设置: $cron_setting${NC}"
            cron_status="已设置"
        else
            log "${RED}错误: 未找到订阅更新定时任务${NC}"
            cron_status="未设置"
        fi
    else
        log "${RED}错误: 订阅更新日志文件不存在${NC}"
        subscription_status="异常 - 日志文件不存在"
    fi
    
    return 0
}

# 检查延迟过滤
check_latency_filtering() {
    log "${YELLOW}检查延迟过滤情况...${NC}"
    
    # 获取延迟阈值
    latency_threshold=$(grep -o "MAX_LATENCY = [0-9]*" "$SINGBOX_DIR/scripts/switch_node.py" | awk '{print $3}')
    log "${BLUE}当前配置的延迟阈值: ${latency_threshold}ms${NC}"
    
    # 检查过滤记录
    filtered_nodes=$(tail -n 100 "$SWITCH_LOG" | grep -c "延迟.*超过阈值")
    if [ $filtered_nodes -gt 0 ]; then
        log "${GREEN}最近有 $filtered_nodes 个节点因延迟过高被过滤${NC}"
        
        # 显示最近被过滤的节点
        log "${BLUE}最近被过滤的节点示例:${NC}"
        recent_filtered=$(tail -n 100 "$SWITCH_LOG" | grep "延迟.*超过阈值" | tail -n 5)
        log "$recent_filtered"
        latency_status="正常 - 最近过滤了 $filtered_nodes 个高延迟节点"
    else
        log "${YELLOW}警告: 最近没有节点因延迟过高被过滤${NC}"
        latency_status="警告 - 没有节点被过滤"
    fi
    
    return 0
}

# 显示系统资源使用情况
show_resource_usage() {
    log "${YELLOW}系统资源使用情况:${NC}"
    
    # 获取sing-box进程的资源使用情况
    if [ -f "$PID_FILE" ]; then
        pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null; then
            cpu=$(ps -p "$pid" -o %cpu | tail -n 1 | tr -d ' ')
            mem=$(ps -p "$pid" -o %mem | tail -n 1 | tr -d ' ')
            log "${GREEN}Singbox CPU使用率: ${cpu}%, 内存使用率: ${mem}%${NC}"
            singbox_resource="CPU: ${cpu}%, 内存: ${mem}%"
        fi
    fi
    
    # 获取节点切换脚本的资源使用情况
    if [ -f "$SWITCH_PID_FILE" ]; then
        pid=$(cat "$SWITCH_PID_FILE")
        if ps -p "$pid" > /dev/null; then
            cpu=$(ps -p "$pid" -o %cpu | tail -n 1 | tr -d ' ')
            mem=$(ps -p "$pid" -o %mem | tail -n 1 | tr -d ' ')
            log "${GREEN}节点切换脚本 CPU使用率: ${cpu}%, 内存使用率: ${mem}%${NC}"
            switch_resource="CPU: ${cpu}%, 内存: ${mem}%"
        fi
    fi
    
    # 显示系统负载
    load=$(uptime | awk -F'load average:' '{print $2}' | tr -d ',')
    log "${BLUE}系统负载: $load${NC}"
    system_load="$load"
    
    return 0
}

# 尝试修复服务
fix_service() {
    if [ "$1" == "force" ] || [ "$service_status" == "未运行" ]; then
        log "${YELLOW}尝试启动 Singbox 代理服务...${NC}"
        "$SINGBOX_DIR/scripts/manage_service.sh" restart
        
        # 等待服务启动
        sleep 5
        
        # 重新检查服务状态
        check_service
        
        if [ $? -eq 0 ]; then
            log "${GREEN}服务已成功重启${NC}"
            fix_result="成功 - 服务已重启"
            
            # 发送修复成功通知
            send_telegram_message "🔧 <b>Singbox代理服务已修复</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
操作: 重启服务
结果: 成功
当前状态: 正常运行"
        else
            log "${RED}服务重启失败${NC}"
            fix_result="失败 - 无法重启服务"
            
            # 发送修复失败通知
            send_telegram_message "⚠️ <b>Singbox代理服务修复失败</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
操作: 重启服务
结果: 失败
当前状态: 服务未运行"
        fi
    else
        log "${GREEN}服务正在运行，无需修复${NC}"
        fix_result="跳过 - 服务正常"
    fi
}

# 主函数
main() {
    # 确保日志目录存在
    mkdir -p "$(dirname "$LOG_FILE")"
    
    log "===== 开始监控 Singbox 代理服务 ====="
    
    # 检查服务状态
    check_service
    service_check=$?
    
    # 如果服务正在运行，继续检查其他项目
    if [ $service_check -eq 0 ]; then
        # 检查代理连接
        check_proxy
        proxy_check=$?
        
        # 检查IP切换
        check_ip_switching
        ip_check=$?
        
        # 检查订阅更新
        check_subscription
        subscription_check=$?
        
        # 检查延迟过滤
        check_latency_filtering
        latency_check=$?
        
        # 显示资源使用情况
        show_resource_usage
        resource_check=$?
        
        # 构建状态消息
        status_message="📊 <b>Singbox代理服务状态报告</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')

<b>服务状态:</b>
- Singbox服务: $service_status
- 节点切换脚本: $switch_status
- 代理连接: $proxy_status
- 当前出口IP: $initial_ip

<b>IP切换状态:</b>
- $ip_switch_status
- 切换间隔: ${interval}秒

<b>订阅状态:</b>
- $subscription_status
- 定时任务: $cron_status

<b>延迟过滤:</b>
- $latency_status
- 阈值: ${latency_threshold}ms

<b>资源使用:</b>
- Singbox: $singbox_resource
- 切换脚本: $switch_resource
- 系统负载: $system_load"
        
        # 检查是否有异常
        if [ $proxy_check -ne 0 ] || [ $ip_check -ne 0 ] || [ "$ip_switch_status" == "警告 - 最近没有切换记录" ]; then
            log "${YELLOW}检测到异常，尝试修复...${NC}"
            fix_service "force"
            status_message="$status_message

修复操作:
- $fix_result"
        fi
        
        # 发送状态报告
        send_telegram_message "$status_message"
    else
        log "${RED}服务未正常运行，尝试修复${NC}"
        
        # 尝试修复服务
        fix_service
        
        # 发送异常通知
        send_telegram_message "🚨 <b>Singbox代理服务异常</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
问题: 服务未正常运行
修复: $fix_result

请检查服务状态并手动处理。"
    fi
    
    log "===== 监控完成 ====="
}

# 执行主函数
main
