#!/bin/bash
# 集成服务安装和迁移脚本
# 作者: Cascade AI
# 创建日期: 2025-04-25

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 基础目录
BASE_DIR="/home/<USER>/api/local-singbox"
SCRIPT_DIR="${BASE_DIR}/scripts"
CONFIG_DIR="${BASE_DIR}/config"
LOG_DIR="${BASE_DIR}/logs"
BIN_DIR="${BASE_DIR}/bin"
BACKUP_DIR="${BASE_DIR}/backup_$(date +%Y%m%d_%H%M%S)"

# 确保目录存在
mkdir -p ${LOG_DIR}

# 记录日志
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# 显示标题
echo -e "${BLUE}===============================================${NC}"
echo -e "${BLUE}      安装集成的Singbox代理和监控服务         ${NC}"
echo -e "${BLUE}===============================================${NC}"

# 步骤1: 创建备份
log "步骤1: 创建服务备份..."
mkdir -p ${BACKUP_DIR}
cp -r ${SCRIPT_DIR}/* ${BACKUP_DIR}/
cp ${BASE_DIR}/singbox-proxy.service ${BACKUP_DIR}/ 2>/dev/null || true
log "已创建备份目录: ${BACKUP_DIR}"

# 步骤2: 停止当前运行的服务
log "步骤2: 停止当前运行的服务..."
# 停止现有监控脚本
pkill -f "python3.*monitor_douyin_api.py" || true
pkill -f "python3.*switch_node.py" || true

# 查找并杀死singbox进程
if [ -f "${BASE_DIR}/singbox.pid" ]; then
    kill -15 $(cat ${BASE_DIR}/singbox.pid) 2>/dev/null || true
    rm -f ${BASE_DIR}/singbox.pid
fi

# 确保Docker容器正在运行
docker start dlr-douyin-api || true

log "所有旧服务已停止"

# 步骤3: 安装依赖
log "步骤3: 安装必要的Python依赖..."
pip3 install --user requests schedule pyyaml docker

# 步骤4: 设置执行权限
log "步骤4: 设置脚本执行权限..."
chmod +x ${SCRIPT_DIR}/singbox_service.sh
chmod +x ${SCRIPT_DIR}/integrated_monitor.py

# 步骤5: 创建服务链接
log "步骤5: 创建服务链接..."
# 创建服务链接至最新的集成脚本
ln -sf ${SCRIPT_DIR}/singbox_service.sh ${BIN_DIR}/singbox_service || true
ln -sf ${SCRIPT_DIR}/integrated_monitor.py ${BIN_DIR}/integrated_monitor || true

# 步骤6: 启动新服务
log "步骤6: 启动新的集成服务..."
${SCRIPT_DIR}/singbox_service.sh start

# 步骤7: 设置开机自启
log "步骤7: 设置系统服务..."

echo -e "${YELLOW}如需设置系统服务开机自启，请执行以下命令：${NC}"
echo -e "  sudo cp ${BASE_DIR}/integrated-singbox.service /etc/systemd/system/"
echo -e "  sudo systemctl daemon-reload"
echo -e "  sudo systemctl enable integrated-singbox.service"
echo -e "  sudo systemctl start integrated-singbox.service"

# 完成
echo -e "${GREEN}===============================================${NC}"
echo -e "${GREEN}       集成服务安装完成！                     ${NC}"
echo -e "${GREEN}===============================================${NC}"
echo 
echo -e "您可以使用以下命令管理服务："
echo -e "  ${YELLOW}${SCRIPT_DIR}/singbox_service.sh status${NC} - 显示当前服务状态"
echo -e "  ${YELLOW}${SCRIPT_DIR}/singbox_service.sh start${NC} - 启动所有服务"
echo -e "  ${YELLOW}${SCRIPT_DIR}/singbox_service.sh stop${NC} - 停止所有服务"
echo -e "  ${YELLOW}${SCRIPT_DIR}/singbox_service.sh restart${NC} - 重启所有服务"
echo 
echo -e "旧服务备份位于: ${BACKUP_DIR}"
echo -e "日志文件位于: ${LOG_DIR}/integrated_monitor.log"
echo 
echo -e "${BLUE}感谢您使用集成的Singbox服务！${NC}"
