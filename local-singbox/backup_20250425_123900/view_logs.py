#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import argparse
import subprocess
from datetime import datetime, timedelta
import re
import curses
import threading
import signal

# 日志文件路径
LOG_FILES = {
    "singbox": "/home/<USER>/api/local-singbox/logs/singbox.log",
    "switch_node": "/home/<USER>/api/local-singbox/logs/switch_node.log",
    "update_subscription": "/home/<USER>/api/local-singbox/logs/update_subscription.log",
    "monitor": "/home/<USER>/api/local-singbox/logs/monitor.log",
    "monitor_douyin_api": "/home/<USER>/api/local-singbox/logs/monitor_douyin_api.log",
    "douyin_api": "/home/<USER>/douyin-api/logs/api.log",
    "start_services": "/home/<USER>/api/local-singbox/logs/start_services.log"
}

# 颜色定义
COLORS = {
    "error": 1,     # 红色
    "warning": 3,   # 黄色
    "info": 2,      # 绿色
    "debug": 4,     # 蓝色
    "default": 7    # 白色
}

# 日志级别正则表达式
LOG_PATTERNS = {
    "error": re.compile(r'(error|错误|失败|异常|ERROR)', re.IGNORECASE),
    "warning": re.compile(r'(warning|警告|WARN)', re.IGNORECASE),
    "info": re.compile(r'(info|信息|成功|INFO)', re.IGNORECASE),
    "debug": re.compile(r'(debug|调试|DEBUG)', re.IGNORECASE)
}

# 全局变量
running = True
logs = []
current_filter = None
current_service = None
max_lines = 1000
auto_refresh = False
refresh_interval = 5  # 秒

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    global running
    running = False
    curses.endwin()
    sys.exit(0)

def get_log_level(line):
    """根据日志内容确定日志级别"""
    for level, pattern in LOG_PATTERNS.items():
        if pattern.search(line):
            return level
    return "default"

def tail_file(file_path, n=100):
    """读取文件最后n行"""
    try:
        result = subprocess.run(["tail", "-n", str(n), file_path], 
                               capture_output=True, text=True)
        return result.stdout.splitlines()
    except Exception as e:
        return [f"错误: 无法读取日志文件 {file_path}: {e}"]

def get_logs(service=None, n=100, filter_level=None):
    """获取指定服务的日志"""
    global logs
    logs = []
    
    if service and service in LOG_FILES:
        # 获取单个服务的日志
        file_path = LOG_FILES[service]
        if os.path.exists(file_path):
            lines = tail_file(file_path, n)
            for line in lines:
                level = get_log_level(line)
                if filter_level is None or level == filter_level:
                    logs.append((service, line, level))
    else:
        # 获取所有服务的日志
        for service_name, file_path in LOG_FILES.items():
            if os.path.exists(file_path):
                lines = tail_file(file_path, n // len(LOG_FILES))
                for line in lines:
                    level = get_log_level(line)
                    if filter_level is None or level == filter_level:
                        logs.append((service_name, line, level))
    
    # 按时间排序（尝试从日志中提取时间）
    def extract_time(log_entry):
        line = log_entry[1]
        # 尝试匹配常见的时间格式
        time_patterns = [
            r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})',  # 2023-04-24 12:34:56
            r'(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2})',  # 04/24/2023 12:34:56
            r'(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})'     # Apr 24 12:34:56
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, line)
            if match:
                try:
                    time_str = match.group(1)
                    # 根据不同格式解析时间
                    if re.match(r'\d{4}-\d{2}-\d{2}', time_str):
                        return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                    elif re.match(r'\d{2}/\d{2}/\d{4}', time_str):
                        return datetime.strptime(time_str, '%m/%d/%Y %H:%M:%S')
                    else:
                        # 对于没有年份的日志，添加当前年份
                        current_year = datetime.now().year
                        time_with_year = f"{time_str} {current_year}"
                        return datetime.strptime(time_with_year, '%b %d %H:%M:%S %Y')
                except:
                    pass
        
        # 如果无法提取时间，返回一个很早的时间
        return datetime(1970, 1, 1)
    
    try:
        logs.sort(key=extract_time)
    except:
        # 如果排序失败，不进行排序
        pass
    
    return logs

def refresh_logs():
    """刷新日志内容"""
    global logs
    logs = get_logs(current_service, max_lines, current_filter)

def auto_refresh_thread():
    """自动刷新线程"""
    global running
    while running and auto_refresh:
        time.sleep(refresh_interval)
        refresh_logs()
        # 通知主线程刷新界面
        curses.ungetch(ord('r'))

def display_help(stdscr):
    """显示帮助信息"""
    stdscr.clear()
    h, w = stdscr.getmaxyx()
    
    help_text = [
        "日志查看器帮助",
        "",
        "键盘快捷键:",
        "  q, Q, Esc - 退出程序",
        "  h, H, ? - 显示此帮助",
        "  r, R - 刷新日志",
        "  a, A - 切换自动刷新",
        "  f, F - 循环切换日志级别过滤器 (全部 -> 错误 -> 警告 -> 信息 -> 调试)",
        "  s, S - 循环切换服务过滤器",
        "  +, = - 增加显示的日志行数",
        "  -, _ - 减少显示的日志行数",
        "  上下箭头 - 滚动日志",
        "  Home - 跳转到第一行",
        "  End - 跳转到最后一行",
        "  PageUp - 向上翻页",
        "  PageDown - 向下翻页",
        "",
        "按任意键返回..."
    ]
    
    for i, line in enumerate(help_text):
        if i < h - 1:
            stdscr.addstr(i, 0, line)
    
    stdscr.refresh()
    stdscr.getch()

def main(stdscr):
    """主函数"""
    global running, logs, current_filter, current_service, max_lines, auto_refresh
    
    # 设置颜色
    curses.start_color()
    curses.use_default_colors()
    for name, color_id in COLORS.items():
        curses.init_pair(color_id, color_id, -1)
    
    # 隐藏光标
    curses.curs_set(0)
    
    # 获取初始日志
    refresh_logs()
    
    # 滚动位置
    scroll_pos = 0
    
    # 循环处理用户输入
    while running:
        stdscr.clear()
        h, w = stdscr.getmaxyx()
        
        # 显示标题和状态栏
        title = "日志查看器"
        status = f"服务: {current_service or '全部'} | 过滤: {current_filter or '全部'} | 行数: {max_lines} | 自动刷新: {'开' if auto_refresh else '关'}"
        help_text = "按 'h' 查看帮助, 'q' 退出"
        
        stdscr.addstr(0, 0, title.center(w)[:w-1], curses.A_REVERSE)
        stdscr.addstr(h-1, 0, status[:w//2-1])
        stdscr.addstr(h-1, w//2, help_text.center(w//2)[:w//2-1])
        
        # 显示日志内容
        display_lines = h - 2  # 减去标题和状态栏
        start_line = max(0, min(scroll_pos, len(logs) - display_lines))
        
        for i, (service, line, level) in enumerate(logs[start_line:start_line + display_lines]):
            if i < display_lines:
                # 确定颜色
                color = curses.color_pair(COLORS.get(level, COLORS["default"]))
                
                # 格式化输出
                prefix = f"[{service}] "
                line_display = prefix + line
                
                # 裁剪过长的行
                if len(line_display) > w - 1:
                    line_display = line_display[:w-4] + "..."
                
                try:
                    stdscr.addstr(i+1, 0, line_display, color)
                except:
                    # 忽略可能的编码错误
                    pass
        
        # 刷新屏幕
        stdscr.refresh()
        
        # 处理用户输入
        try:
            key = stdscr.getch()
        except:
            continue
        
        if key in [ord('q'), ord('Q'), 27]:  # q, Q, Esc
            running = False
            break
        elif key in [ord('h'), ord('H'), ord('?')]:  # h, H, ?
            display_help(stdscr)
        elif key in [ord('r'), ord('R')]:  # r, R
            refresh_logs()
        elif key in [ord('a'), ord('A')]:  # a, A
            auto_refresh = not auto_refresh
            if auto_refresh:
                # 启动自动刷新线程
                threading.Thread(target=auto_refresh_thread, daemon=True).start()
        elif key in [ord('f'), ord('F')]:  # f, F
            # 循环切换过滤器
            if current_filter is None:
                current_filter = "error"
            elif current_filter == "error":
                current_filter = "warning"
            elif current_filter == "warning":
                current_filter = "info"
            elif current_filter == "info":
                current_filter = "debug"
            else:
                current_filter = None
            refresh_logs()
        elif key in [ord('s'), ord('S')]:  # s, S
            # 循环切换服务
            services = list(LOG_FILES.keys()) + [None]
            if current_service not in services:
                current_service = None
            else:
                current_service = services[(services.index(current_service) + 1) % len(services)]
            refresh_logs()
            scroll_pos = 0
        elif key in [ord('+'), ord('=')]:  # +, =
            max_lines = min(10000, max_lines + 100)
            refresh_logs()
        elif key in [ord('-'), ord('_')]:  # -, _
            max_lines = max(100, max_lines - 100)
            refresh_logs()
        elif key == curses.KEY_UP:  # 上箭头
            scroll_pos = max(0, scroll_pos - 1)
        elif key == curses.KEY_DOWN:  # 下箭头
            scroll_pos = min(len(logs) - 1, scroll_pos + 1)
        elif key == curses.KEY_HOME:  # Home
            scroll_pos = 0
        elif key == curses.KEY_END:  # End
            scroll_pos = max(0, len(logs) - display_lines)
        elif key == curses.KEY_PPAGE:  # PageUp
            scroll_pos = max(0, scroll_pos - display_lines)
        elif key == curses.KEY_NPAGE:  # PageDown
            scroll_pos = min(len(logs) - 1, scroll_pos + display_lines)

if __name__ == "__main__":
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='查看服务日志')
    parser.add_argument('-s', '--service', choices=list(LOG_FILES.keys()), help='指定要查看的服务')
    parser.add_argument('-f', '--filter', choices=['error', 'warning', 'info', 'debug'], help='按日志级别过滤')
    parser.add_argument('-n', '--lines', type=int, default=1000, help='显示的日志行数')
    parser.add_argument('-a', '--auto-refresh', action='store_true', help='启用自动刷新')
    parser.add_argument('-i', '--interval', type=int, default=5, help='自动刷新间隔(秒)')
    
    args = parser.parse_args()
    
    # 设置全局变量
    current_service = args.service
    current_filter = args.filter
    max_lines = args.lines
    auto_refresh = args.auto_refresh
    refresh_interval = args.interval
    
    # 启动curses界面
    try:
        curses.wrapper(main)
    except KeyboardInterrupt:
        print("程序已退出")
    except Exception as e:
        print(f"发生错误: {e}")
