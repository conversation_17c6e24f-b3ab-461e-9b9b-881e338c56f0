#!/bin/bash
# singbox_service.sh
# 综合服务管理脚本，整合现有的多个管理脚本功能
# 作者: Cascade AI
# 创建日期: 2025-04-25

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 基础目录
BASE_DIR="/home/<USER>/api/local-singbox"
SCRIPT_DIR="${BASE_DIR}/scripts"
CONFIG_DIR="${BASE_DIR}/config"
LOG_DIR="${BASE_DIR}/logs"
BIN_DIR="${BASE_DIR}/bin"

# PID文件
SINGBOX_PID_FILE="${BASE_DIR}/singbox.pid"
SWITCH_NODE_PID_FILE="${BASE_DIR}/switch_node.pid"
MONITOR_API_PID_FILE="${BASE_DIR}/monitor_api.pid"

# 日志文件
MAIN_LOG="${LOG_DIR}/singbox_service.log"

# 确保日志目录存在
mkdir -p ${LOG_DIR}

# 记录日志的函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a ${MAIN_LOG}
}

# 显示状态的函数
status() {
    echo -e "${BLUE}=== Singbox服务状态 ===${NC}"
    
    # 检查singbox代理状态
    if [ -f "${SINGBOX_PID_FILE}" ] && ps -p $(cat ${SINGBOX_PID_FILE}) > /dev/null; then
        echo -e "${GREEN}✅ Singbox代理服务: 运行中 (PID: $(cat ${SINGBOX_PID_FILE}))${NC}"
    else
        echo -e "${RED}❌ Singbox代理服务: 未运行${NC}"
    fi
    
    # 检查IP切换脚本状态
    if [ -f "${SWITCH_NODE_PID_FILE}" ] && ps -p $(cat ${SWITCH_NODE_PID_FILE}) > /dev/null; then
        echo -e "${GREEN}✅ IP切换服务: 运行中 (PID: $(cat ${SWITCH_NODE_PID_FILE}))${NC}"
    else
        echo -e "${RED}❌ IP切换服务: 未运行${NC}"
    fi
    
    # 检查API监控脚本状态
    API_MONITOR_PID=$(ps aux | grep "python3.*monitor_douyin_api.py" | grep -v grep | awk '{print $2}')
    if [ ! -z "$API_MONITOR_PID" ]; then
        echo -e "${GREEN}✅ API监控服务: 运行中 (PID: ${API_MONITOR_PID})${NC}"
        echo "$API_MONITOR_PID" > ${MONITOR_API_PID_FILE}
    else
        echo -e "${RED}❌ API监控服务: 未运行${NC}"
    fi
    
    # 检查Docker容器状态
    CONTAINER_STATUS=$(docker ps -a | grep dlr-douyin-api | awk '{print $7}')
    if [ "$CONTAINER_STATUS" == "Up" ]; then
        echo -e "${GREEN}✅ Douyin API容器: 运行中${NC}"
    else
        echo -e "${RED}❌ Douyin API容器: ${CONTAINER_STATUS:-未运行}${NC}"
    fi
    
    # 显示当前IP
    CURRENT_IP=$(curl -s --socks5 127.0.0.1:1080 https://api.ipify.org)
    if [ ! -z "$CURRENT_IP" ]; then
        echo -e "${GREEN}✅ 当前代理IP: ${CURRENT_IP}${NC}"
    else
        echo -e "${RED}❌ 无法获取代理IP${NC}"
    fi
    
    echo -e "${BLUE}=== 服务状态检查完成 ===${NC}"
}

# 启动Singbox代理
start_proxy() {
    log "正在启动Singbox代理服务..."
    if [ -f "${SINGBOX_PID_FILE}" ] && ps -p $(cat ${SINGBOX_PID_FILE}) > /dev/null; then
        log "Singbox代理服务已经在运行中"
    else
        nohup ${BIN_DIR}/sing-box run -c ${CONFIG_DIR}/config.json > ${LOG_DIR}/singbox.log 2>&1 &
        echo $! > ${SINGBOX_PID_FILE}
        log "Singbox代理服务已启动, PID: $(cat ${SINGBOX_PID_FILE})"
    fi
}

# 启动IP切换服务
start_switch_node() {
    log "正在启动IP切换服务..."
    if [ -f "${SWITCH_NODE_PID_FILE}" ] && ps -p $(cat ${SWITCH_NODE_PID_FILE}) > /dev/null; then
        log "IP切换服务已经在运行中"
    else
        cd ${SCRIPT_DIR}
        nohup python3 ./switch_node.py > ${LOG_DIR}/switch_node.log 2>&1 &
        echo $! > ${SWITCH_NODE_PID_FILE}
        log "IP切换服务已启动, PID: $(cat ${SWITCH_NODE_PID_FILE})"
    fi
}

# 启动API监控服务
start_monitor() {
    log "正在启动API监控服务..."
    API_MONITOR_PID=$(ps aux | grep "python3.*monitor_douyin_api.py" | grep -v grep | awk '{print $2}')
    if [ ! -z "$API_MONITOR_PID" ]; then
        log "API监控服务已经在运行中"
    else
        cd ${SCRIPT_DIR}
        nohup python3 ./monitor_douyin_api.py > ${LOG_DIR}/monitor_douyin_api.log 2>&1 &
        API_MONITOR_PID=$!
        echo $API_MONITOR_PID > ${MONITOR_API_PID_FILE}
        log "API监控服务已启动, PID: ${API_MONITOR_PID}"
    fi
}

# 启动所有服务
start_all() {
    log "正在启动所有服务..."
    start_proxy
    sleep 2
    start_switch_node
    sleep 2
    start_monitor
    log "所有服务已启动"
    status
}

# 停止Singbox代理
stop_proxy() {
    log "正在停止Singbox代理服务..."
    if [ -f "${SINGBOX_PID_FILE}" ]; then
        kill -15 $(cat ${SINGBOX_PID_FILE}) 2>/dev/null
        rm -f ${SINGBOX_PID_FILE}
        log "Singbox代理服务已停止"
    else
        log "Singbox代理服务未运行"
    fi
}

# 停止IP切换服务
stop_switch_node() {
    log "正在停止IP切换服务..."
    if [ -f "${SWITCH_NODE_PID_FILE}" ]; then
        kill -15 $(cat ${SWITCH_NODE_PID_FILE}) 2>/dev/null
        rm -f ${SWITCH_NODE_PID_FILE}
        log "IP切换服务已停止"
    else
        log "IP切换服务未运行"
    fi
}

# 停止API监控服务
stop_monitor() {
    log "正在停止API监控服务..."
    API_MONITOR_PID=$(ps aux | grep "python3.*monitor_douyin_api.py" | grep -v grep | awk '{print $2}')
    if [ ! -z "$API_MONITOR_PID" ]; then
        kill -15 $API_MONITOR_PID 2>/dev/null
        rm -f ${MONITOR_API_PID_FILE}
        log "API监控服务已停止"
    else
        log "API监控服务未运行"
    fi
}

# 停止所有服务
stop_all() {
    log "正在停止所有服务..."
    stop_monitor
    stop_switch_node
    stop_proxy
    log "所有服务已停止"
}

# 重启所有服务
restart_all() {
    log "正在重启所有服务..."
    stop_all
    sleep 2
    start_all
    log "所有服务已重启"
}

# 更新代理订阅
update_subscription() {
    log "正在更新代理订阅..."
    ${SCRIPT_DIR}/update_subscription.py
    log "代理订阅更新完成"
}

# 测试代理连接
test_proxy() {
    log "正在测试代理连接..."
    ${SCRIPT_DIR}/proxy_test.py
    log "代理测试完成"
}

# 验证容器代理设置
verify_container() {
    log "正在验证容器代理设置..."
    ${SCRIPT_DIR}/verify_container_proxy.sh
    log "容器验证完成"
}

# 显示使用帮助
show_help() {
    echo -e "${BLUE}Singbox服务管理工具${NC}"
    echo -e "用法: $0 [命令]"
    echo 
    echo -e "可用命令:"
    echo -e "  ${GREEN}start${NC}        启动所有服务"
    echo -e "  ${GREEN}stop${NC}         停止所有服务"
    echo -e "  ${GREEN}restart${NC}      重启所有服务"
    echo -e "  ${GREEN}status${NC}       显示服务状态"
    echo -e "  ${GREEN}start-proxy${NC}  仅启动代理服务"
    echo -e "  ${GREEN}stop-proxy${NC}   仅停止代理服务"
    echo -e "  ${GREEN}start-switch${NC} 仅启动IP切换服务"
    echo -e "  ${GREEN}stop-switch${NC}  仅停止IP切换服务" 
    echo -e "  ${GREEN}start-monitor${NC} 仅启动API监控服务"
    echo -e "  ${GREEN}stop-monitor${NC}  仅停止API监控服务"
    echo -e "  ${GREEN}update${NC}       更新代理订阅"
    echo -e "  ${GREEN}test${NC}         测试代理连接"
    echo -e "  ${GREEN}verify${NC}       验证容器代理设置"
    echo -e "  ${GREEN}help${NC}         显示此帮助信息"
}

# 主要命令处理
case "$1" in
    start)
        start_all
        ;;
    stop)
        stop_all
        ;;
    restart)
        restart_all
        ;;
    status)
        status
        ;;
    start-proxy)
        start_proxy
        ;;
    stop-proxy)
        stop_proxy
        ;;
    start-switch)
        start_switch_node
        ;;
    stop-switch)
        stop_switch_node
        ;;
    start-monitor)
        start_monitor
        ;;
    stop-monitor)
        stop_monitor
        ;;
    update)
        update_subscription
        ;;
    test)
        test_proxy
        ;;
    verify)
        verify_container
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${YELLOW}未知命令: $1${NC}"
        show_help
        exit 1
        ;;
esac

exit 0
