#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import logging
import os
import time
import base64
import re
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    filename='/home/<USER>/api/local-singbox/logs/update_subscription.log',
    filemode='a'
)
logger = logging.getLogger(__name__)

# 配置
CONFIG_PATH = '/home/<USER>/api/local-singbox/config/config.json'
SUBSCRIPTION_URL = 'https://global.tagonline.asia/api/v1/client/subscribe?token=9fe0baab7e2b181e70e93b39aecdd3cb'  # 从原始脚本中获取的订阅URL
CLASH_API_URL = 'http://127.0.0.1:9090'  # sing-box 的 Clash API 地址

def restart_singbox():
    """重启 sing-box 服务"""
    try:
        # 获取 sing-box 进程 PID
        pid_cmd = "pgrep -f '/home/<USER>/api/local-singbox/bin/sing-box'"
        pid = os.popen(pid_cmd).read().strip()
        
        if pid:
            # 停止 sing-box
            logger.info(f"正在停止 sing-box 进程 (PID: {pid})...")
            os.system(f"kill {pid}")
            time.sleep(2)
        
        # 启动 sing-box
        logger.info("正在启动 sing-box...")
        cmd = f"nohup /home/<USER>/api/local-singbox/bin/sing-box run -c /home/<USER>/api/local-singbox/config/config.json > /home/<USER>/api/local-singbox/logs/singbox.log 2>&1 &"
        os.system(cmd)
        logger.info("sing-box 已重启")
        return True
    except Exception as e:
        logger.error(f"重启 sing-box 时出错: {e}")
        return False

def fetch_subscription():
    """获取订阅内容"""
    try:
        logger.info(f"正在从 {SUBSCRIPTION_URL} 获取订阅...")
        response = requests.get(SUBSCRIPTION_URL, timeout=30)
        if response.status_code == 200:
            content = response.text.strip()
            
            # 尝试 Base64 解码
            try:
                decoded = base64.b64decode(content).decode('utf-8')
                logger.info("成功获取并解码订阅内容")
                return decoded
            except Exception as e:
                logger.error(f"Base64解码失败: {e}")
                return None
        else:
            logger.error(f"获取订阅失败: HTTP {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"获取订阅时出错: {e}")
        return None

def parse_ss_url(ss_url):
    """解析 Shadowsocks URL"""
    try:
        if not ss_url.startswith('ss://'):
            return None
        
        # 移除 ss:// 前缀
        ss_url = ss_url[5:]
        
        # 检查URL格式
        if '@' in ss_url:
            # 格式: ss://BASE64(method:password)@server:port
            userinfo, server_info = ss_url.split('@', 1)
            
            try:
                # 解码用户信息
                decoded_userinfo = base64.b64decode(userinfo).decode('utf-8')
                if ':' in decoded_userinfo:
                    method, password = decoded_userinfo.split(':', 1)
                    
                    # 获取服务器和端口
                    if ':' in server_info:
                        server, port = server_info.split(':', 1)
                        
                        # 移除可能的额外参数
                        if '#' in port:
                            port = port.split('#', 1)[0]
                        
                        return {
                            'server': server,
                            'port': int(port),
                            'method': method,
                            'password': password
                        }
            except Exception as e:
                # 如果上面的方法失败，尝试另一种格式
                logger.warning(f"标准格式解析失败，尝试替代格式: {e}")
        
        # 尝试替代格式: ss://BASE64(method:password@server:port)
        try:
            # 解码整个URL
            decoded = base64.b64decode(ss_url).decode('utf-8')
            
            # 检查是否包含必要的部分
            if '@' in decoded and ':' in decoded:
                # 分割用户信息和服务器信息
                userpass, server_port = decoded.split('@', 1)
                
                if ':' in userpass and ':' in server_port:
                    method, password = userpass.split(':', 1)
                    server, port = server_port.split(':', 1)
                    
                    # 移除可能的额外参数
                    if '#' in port:
                        port = port.split('#', 1)[0]
                    
                    return {
                        'server': server,
                        'port': int(port),
                        'method': method,
                        'password': password
                    }
        except Exception as e:
            logger.error(f"替代格式解析失败: {e}")
        
        return None
    except Exception as e:
        logger.error(f"解析 SS URL 失败: {e}")
        return None

def update_config(subscription_content):
    """更新配置文件"""
    try:
        logger.info("正在更新配置文件...")
        
        # 读取当前配置
        with open(CONFIG_PATH, 'r') as f:
            config = json.load(f)
        
        # 备份当前配置
        backup_path = f"{CONFIG_PATH}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        with open(backup_path, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"已备份当前配置到 {backup_path}")
        
        # 提取所有 SS URL
        ss_urls = re.findall(r'ss://[^\s]+', subscription_content)
        if not ss_urls:
            logger.error("未找到有效的 Shadowsocks 服务器信息")
            return False
        
        logger.info(f"找到 {len(ss_urls)} 个 Shadowsocks 服务器")
        
        # 解析服务器信息
        servers = []
        server_tags = []
        for i, url in enumerate(ss_urls, 1):
            server_info = parse_ss_url(url)
            if server_info:
                tag = f"server{i}"
                server_tags.append(tag)
                servers.append({
                    "type": "shadowsocks",
                    "tag": tag,
                    "server": server_info['server'],
                    "server_port": server_info['port'],
                    "method": server_info['method'],
                    "password": server_info['password'],
                    "plugin": "obfs-local",
                    "plugin_opts": "obfs=http;obfs-host=d5004ce2e61aa5459306.microsoft.com"
                })
                logger.info(f"成功解析服务器 {tag}: {server_info['server']}:{server_info['port']}")
        
        if not servers:
            logger.error("未能成功解析任何服务器信息")
            return False
        
        logger.info(f"成功解析 {len(servers)} 个服务器")
        
        # 更新配置
        # 1. 保留特殊出站配置
        special_outbounds = [ob for ob in config["outbounds"] if ob["tag"] in ["direct", "dns-out"]]
        
        # 2. 更新 selector 和 urltest 出站配置
        selector_outbound = next((ob for ob in config["outbounds"] if ob["type"] == "selector"), None)
        urltest_outbound = next((ob for ob in config["outbounds"] if ob["type"] == "urltest"), None)
        
        if selector_outbound:
            selector_outbound["outbounds"] = server_tags + ["direct"]
            selector_outbound["default"] = server_tags[0]  # 设置第一个服务器为默认
        
        if urltest_outbound:
            urltest_outbound["outbounds"] = server_tags
        
        # 3. 组合新的出站配置
        new_outbounds = []
        if selector_outbound:
            new_outbounds.append(selector_outbound)
        if urltest_outbound:
            new_outbounds.append(urltest_outbound)
        new_outbounds.extend(servers)
        new_outbounds.extend(special_outbounds)
        
        config["outbounds"] = new_outbounds
        
        # 保存更新后的配置
        with open(CONFIG_PATH, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"配置文件已更新，共添加 {len(servers)} 个节点")
        return True
    except Exception as e:
        logger.error(f"更新配置文件时出错: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始更新订阅...")
    
    # 获取订阅内容
    subscription_content = fetch_subscription()
    if not subscription_content:
        logger.error("获取订阅失败，退出程序")
        return
    
    # 更新配置文件
    if update_config(subscription_content):
        # 重启 sing-box
        if restart_singbox():
            logger.info("订阅更新完成")
        else:
            logger.error("重启 sing-box 失败")
    else:
        logger.error("更新配置文件失败")

if __name__ == "__main__":
    main()
