#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import random
import logging
import json
from datetime import datetime
from collections import Counter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    filename='/home/<USER>/api/local-singbox/logs/switch_node.log',
    filemode='a'
)
logger = logging.getLogger(__name__)

# 配置
CLASH_API_URL = "http://127.0.0.1:9090"  # sing-box 的 Clash API 地址
PROXY_GROUPS = ["proxy"]  # 要切换的代理组名称
SWITCH_INTERVAL = 120  # 切换间隔（秒）
MAX_RETRIES = 3  # 最大重试次数
MAX_LATENCY = 300  # 最大延迟（毫秒），超过此值的节点将被过滤

# Telegram 配置
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "235196660"
TELEGRAM_API_URL = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"

# IP信息API
IP_INFO_API = "http://ip-api.com/json/{}"

# 节点使用计数器，用于确保平均切换
node_usage_counter = Counter()

# 记录当前IP
current_ip = None
current_location = None

# 获取IP地理位置信息
def get_ip_location(ip):
    try:
        response = requests.get(IP_INFO_API.format(ip), timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                country = data.get("country", "未知国家")
                city = data.get("city", "未知城市")
                isp = data.get("isp", "未知ISP")
                return f"{country} {city} ({isp})"
        return "未知"
    except Exception as e:
        logger.error(f"获取IP地理位置信息失败: {e}")
        return "未知"

# 发送Telegram消息
def send_telegram_message(message):
    try:
        data = {
            "chat_id": TELEGRAM_CHAT_ID,
            "text": message,
            "parse_mode": "HTML"
        }
        response = requests.post(TELEGRAM_API_URL, data=data, timeout=10)
        if response.status_code == 200:
            logger.info(f"Telegram消息发送成功")
        else:
            logger.error(f"Telegram消息发送失败: HTTP {response.status_code}, {response.text}")
    except Exception as e:
        logger.error(f"发送Telegram消息时出错: {e}")

# 获取当前IP
def get_current_ip():
    global current_ip, current_location
    for retry in range(MAX_RETRIES):
        try:
            # 使用代理获取当前IP
            session = requests.Session()
            session.proxies = {
                'http': 'socks5h://127.0.0.1:1080',
                'https': 'socks5h://127.0.0.1:1080'
            }
            
            # 尝试多个IP信息服务
            services = [
                "https://api.ipify.org?format=json",
                "https://ipinfo.io/json",
                "https://ifconfig.me/all.json"
            ]
            
            for service_url in services:
                try:
                    response = session.get(service_url, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if "ip" in data:
                            ip = data.get("ip", "未知")
                            location = get_ip_location(ip)
                            return ip, location
                        else:
                            continue
                except Exception as e:
                    logger.warning(f"服务 {service_url} 请求失败: {e}")
                    continue
            
            return "未知", "未知"
        except Exception as e:
            logger.error(f"获取当前IP失败 (尝试 {retry+1}/{MAX_RETRIES}): {e}")
            time.sleep(2)
    return "未知", "未知"

# 获取所有代理信息
def get_all_proxies():
    for retry in range(MAX_RETRIES):
        try:
            url = f"{CLASH_API_URL}/proxies"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取代理信息失败: HTTP {response.status_code}")
                time.sleep(1)
        except Exception as e:
            logger.error(f"获取代理信息时出错 (尝试 {retry+1}/{MAX_RETRIES}): {e}")
            time.sleep(1)
    return None

# 获取当前使用的节点
def get_current_node(group):
    for retry in range(MAX_RETRIES):
        try:
            url = f"{CLASH_API_URL}/proxies/{group}"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if "now" in data:
                    return data["now"]
            else:
                logger.error(f"获取当前节点失败: HTTP {response.status_code}")
                time.sleep(1)
        except Exception as e:
            logger.error(f"获取当前节点时出错 (尝试 {retry+1}/{MAX_RETRIES}): {e}")
            time.sleep(1)
    return "未知"

# 切换到指定节点
def switch_node(group, node):
    for retry in range(MAX_RETRIES):
        try:
            url = f"{CLASH_API_URL}/proxies/{group}"
            data = {"name": node}
            response = requests.put(url, json=data, timeout=5)
            if response.status_code == 204:
                logger.info(f"成功将 {group} 切换到 {node}")
                # 更新节点使用计数
                node_usage_counter[node] += 1
                return True
            else:
                logger.error(f"切换节点失败: HTTP {response.status_code}")
                time.sleep(1)
        except Exception as e:
            logger.error(f"切换节点时出错 (尝试 {retry+1}/{MAX_RETRIES}): {e}")
            time.sleep(1)
    return False

# 测试节点延迟
def test_node_latency(node):
    for retry in range(MAX_RETRIES):
        try:
            url = f"{CLASH_API_URL}/proxies/{node}/delay"
            params = {"timeout": 5000, "url": "https://www.gstatic.com/generate_204"}
            response = requests.get(url, params=params, timeout=6)
            if response.status_code == 200:
                data = response.json()
                if "delay" in data:
                    return data["delay"]  # 返回延迟（毫秒）
            else:
                logger.warning(f"测试节点 {node} 延迟失败: HTTP {response.status_code}")
                time.sleep(1)
        except Exception as e:
            logger.warning(f"测试节点 {node} 延迟时出错 (尝试 {retry+1}/{MAX_RETRIES}): {e}")
            time.sleep(1)
    return 9999  # 返回一个很大的值表示测试失败

# 等待API就绪
def wait_for_api_ready():
    logger.info("等待Clash API就绪...")
    for i in range(30):  # 最多等待30秒
        try:
            response = requests.get(f"{CLASH_API_URL}/proxies", timeout=2)
            if response.status_code == 200:
                logger.info("Clash API已就绪")
                return True
        except Exception:
            pass
        time.sleep(1)
    logger.error("Clash API未就绪，请检查sing-box服务是否正常运行")
    return False

# 选择下一个节点，确保平均切换并过滤高延迟节点
def select_next_node(current_node, all_nodes):
    # 过滤掉特殊节点和当前节点
    special_nodes = ["auto", "direct", "reject"]
    available_nodes = [node for node in all_nodes if node not in special_nodes and node != current_node]
    
    if not available_nodes:
        # 如果没有其他可用节点，使用所有非特殊节点
        available_nodes = [node for node in all_nodes if node not in special_nodes]
        if not available_nodes:
            return None
    
    # 测试节点延迟并过滤掉高延迟节点
    nodes_with_latency = []
    for node in available_nodes:
        latency = test_node_latency(node)
        if latency <= MAX_LATENCY:
            nodes_with_latency.append((node, latency))
        else:
            logger.info(f"节点 {node} 延迟 {latency}ms 超过阈值 {MAX_LATENCY}ms，已过滤")
    
    # 如果没有低延迟节点，则放宽限制
    if not nodes_with_latency:
        logger.warning(f"没有延迟低于 {MAX_LATENCY}ms 的节点，使用所有可用节点")
        for node in available_nodes:
            latency = test_node_latency(node)
            nodes_with_latency.append((node, latency))
    
    # 按延迟排序
    nodes_with_latency.sort(key=lambda x: x[1])
    
    # 从延迟最低的前5个节点中，按使用次数排序
    top_nodes = nodes_with_latency[:5] if len(nodes_with_latency) >= 5 else nodes_with_latency
    sorted_nodes = sorted(top_nodes, key=lambda x: node_usage_counter.get(x[0], 0))
    
    # 选择使用次数最少的节点
    selected_node = sorted_nodes[0][0]
    selected_latency = sorted_nodes[0][1]
    logger.info(f"选择节点 {selected_node}，延迟: {selected_latency}ms，使用次数: {node_usage_counter.get(selected_node, 0)}")
    
    return selected_node

# 主函数
def main():
    global current_ip, current_location
    
    logger.info("节点切换脚本启动")
    
    # 发送启动通知
    send_telegram_message(f"🚀 <b>Singbox代理服务已启动</b>\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n节点切换间隔: {SWITCH_INTERVAL}秒\n延迟阈值: {MAX_LATENCY}ms\n当前IP: {current_ip} ({current_location})")
    
    # 等待API就绪
    if not wait_for_api_ready():
        logger.error("无法连接到Clash API，退出程序")
        send_telegram_message("❌ <b>Singbox代理服务启动失败</b>\n\n无法连接到Clash API，请检查服务是否正常运行")
        return
    
    # 获取所有代理信息，检查配置是否正确
    proxies_info = get_all_proxies()
    if proxies_info and "proxies" in proxies_info:
        logger.info(f"成功连接到 Clash API")
        available_groups = []
        available_nodes = []
        
        for name, proxy in proxies_info["proxies"].items():
            if proxy.get("type") in ["Selector", "URLTest"]:
                available_groups.append(name)
            elif proxy.get("type") == "Shadowsocks":
                available_nodes.append(name)
        
        logger.info(f"可用代理组: {available_groups}")
        logger.info(f"可用节点数量: {len(available_nodes)}")
        
        # 验证配置的代理组是否存在
        for group in PROXY_GROUPS:
            if group not in available_groups:
                logger.warning(f"配置的代理组 '{group}' 不存在，请检查配置")
    else:
        logger.error("无法获取代理信息，请检查 Clash API 是否正常工作")
        send_telegram_message("❌ <b>Singbox代理服务异常</b>\n\n无法获取代理信息，请检查服务是否正常运行")
        return
    
    # 获取当前IP
    current_ip, current_location = get_current_ip()
    logger.info(f"初始出口IP: {current_ip} ({current_location})")
    
    # 记录上一个IP，用于检测变化
    last_ip = current_ip
    
    # 为每个代理组记录当前节点
    current_nodes = {}
    for group in PROXY_GROUPS:
        current_nodes[group] = get_current_node(group)
        logger.info(f"代理组 {group} 当前使用节点: {current_nodes[group]}")
    
    # 发送初始状态通知
    initial_message = f"📡 <b>Singbox代理服务状态</b>\n\n"
    initial_message += f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    initial_message += f"当前IP: {current_ip} ({current_location})\n"
    for group in PROXY_GROUPS:
        initial_message += f"代理组 {group} 使用节点: {current_nodes[group]}\n"
    initial_message += f"\n可用节点总数: {len(available_nodes)}"
    send_telegram_message(initial_message)
    
    # 主循环
    while True:
        try:
            for group in PROXY_GROUPS:
                # 获取当前节点
                current_node = get_current_node(group)
                current_nodes[group] = current_node
                
                # 获取可用节点列表
                all_nodes = []
                proxies_info = get_all_proxies()
                if proxies_info and "proxies" in proxies_info:
                    group_info = proxies_info["proxies"].get(group, {})
                    all_nodes = group_info.get("all", [])
                
                # 选择下一个节点
                next_node = select_next_node(current_node, all_nodes)
                
                if not next_node:
                    logger.warning(f"没有可用的节点可供切换")
                    continue
                
                logger.info(f"准备将 {group} 从 {current_node} 切换到 {next_node}")
                
                # 切换节点
                if switch_node(group, next_node):
                    # 等待切换生效
                    time.sleep(2)
                    
                    # 获取新IP
                    new_ip, new_location = get_current_ip()
                    logger.info(f"切换后出口IP: {new_ip} ({new_location})")
                    
                    # 检测IP是否变化
                    if new_ip != last_ip and "获取" not in new_ip:
                        logger.info(f"IP已变化: {last_ip} -> {new_ip} ({new_location})")
                        
                        # 发送IP变化通知
                        ip_change_message = f"🔄 <b>IP已切换</b>\n\n"
                        ip_change_message += f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                        ip_change_message += f"原IP: {current_ip} ({current_location})\n"
                        ip_change_message += f"新IP: {new_ip} ({new_location})\n"
                        ip_change_message += f"节点: {current_node} -> {next_node}\n"
                        ip_change_message += f"延迟: {test_node_latency(next_node)}ms"
                        send_telegram_message(ip_change_message)
                        
                        # 更新当前IP和位置信息
                        current_ip = new_ip
                        current_location = new_location
                        last_ip = new_ip
                    
                    # 更新当前节点
                    current_nodes[group] = next_node
                    
                    # 输出节点使用统计
                    if len(node_usage_counter) > 0:
                        total_switches = sum(node_usage_counter.values())
                        logger.info(f"节点切换统计: 总切换次数={total_switches}, 已使用节点数={len(node_usage_counter)}")
                        top_nodes = node_usage_counter.most_common(3)
                        logger.info(f"使用最多的节点: {top_nodes}")
            
            # 等待下一次切换
            logger.info(f"等待 {SWITCH_INTERVAL} 秒后进行下一次切换...")
            time.sleep(SWITCH_INTERVAL)
        except Exception as e:
            logger.error(f"运行时出错: {e}")
            send_telegram_message(f"⚠️ <b>Singbox代理服务异常</b>\n\n错误信息: {str(e)}\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            time.sleep(5)

if __name__ == "__main__":
    main()
