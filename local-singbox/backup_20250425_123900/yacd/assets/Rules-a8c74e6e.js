import{k as ne,h as W,aq as Re,ar as ie,as as Me,R as P,at as be,au as B,av as Oe,aw as ze,ax as V,r as A,a0 as q,ay as Te,L as se,a3 as Ce,j as O,b as g,a2 as we,B as xe,u as ae,d as Pe,g as Ee,C as Ne}from"./index-1a05af9b.js";import{_ as G,m as $}from"./memoize-one.esm-efa1e849.js";import{R as oe,T as ke}from"./TextFitler-3b17a569.js";import{f as Le}from"./index-84fa0cb3.js";import{F as Ae,p as We}from"./Fab-47e19297.js";import{u as De}from"./useRemainingViewPortHeight-dbe2192e.js";import"./rotate-cw-e799f805.js";import"./debounce-c1ba2006.js";var $e=function(r){ne(e,r);function e(n,i){var a;return a=r.call(this)||this,a.client=n,a.setOptions(i),a.bindMethods(),a.updateResult(),a}var t=e.prototype;return t.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},t.setOptions=function(i){this.options=this.client.defaultMutationOptions(i)},t.onUnsubscribe=function(){if(!this.listeners.length){var i;(i=this.currentMutation)==null||i.removeObserver(this)}},t.onMutationUpdate=function(i){this.updateResult();var a={listeners:!0};i.type==="success"?a.onSuccess=!0:i.type==="error"&&(a.onError=!0),this.notify(a)},t.getCurrentResult=function(){return this.currentResult},t.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},t.mutate=function(i,a){return this.mutateOptions=a,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,W({},this.options,{variables:typeof i<"u"?i:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},t.updateResult=function(){var i=this.currentMutation?this.currentMutation.state:Re(),a=W({},i,{isLoading:i.status==="loading",isSuccess:i.status==="success",isError:i.status==="error",isIdle:i.status==="idle",mutate:this.mutate,reset:this.reset});this.currentResult=a},t.notify=function(i){var a=this;ie.batch(function(){a.mutateOptions&&(i.onSuccess?(a.mutateOptions.onSuccess==null||a.mutateOptions.onSuccess(a.currentResult.data,a.currentResult.variables,a.currentResult.context),a.mutateOptions.onSettled==null||a.mutateOptions.onSettled(a.currentResult.data,null,a.currentResult.variables,a.currentResult.context)):i.onError&&(a.mutateOptions.onError==null||a.mutateOptions.onError(a.currentResult.error,a.currentResult.variables,a.currentResult.context),a.mutateOptions.onSettled==null||a.mutateOptions.onSettled(void 0,a.currentResult.error,a.currentResult.variables,a.currentResult.context))),i.listeners&&a.listeners.forEach(function(o){o(a.currentResult)})})},e}(Me);function le(r,e,t){var n=P.useRef(!1),i=P.useState(0),a=i[1],o=be(r,e,t),f=B(),u=P.useRef();u.current?u.current.setOptions(o):u.current=new $e(f,o);var v=u.current.getCurrentResult();P.useEffect(function(){n.current=!0;var b=u.current.subscribe(ie.batchCalls(function(){n.current&&a(function(_){return _+1})}));return function(){n.current=!1,b()}},[]);var y=P.useCallback(function(b,_){u.current.mutate(b,_).catch(Oe)},[]);if(v.error&&ze(void 0,u.current.options.useErrorBoundary,[v.error]))throw v.error;return W({},v,{mutate:y,mutateAsync:v.mutate})}var Fe=typeof performance=="object"&&typeof performance.now=="function",J=Fe?function(){return performance.now()}:function(){return Date.now()};function Z(r){cancelAnimationFrame(r.id)}function Ue(r,e){var t=J();function n(){J()-t>=e?r.call(null):i.id=requestAnimationFrame(n)}var i={id:requestAnimationFrame(n)};return i}var F=-1;function X(r){if(r===void 0&&(r=!1),F===-1||r){var e=document.createElement("div"),t=e.style;t.width="50px",t.height="50px",t.overflow="scroll",document.body.appendChild(e),F=e.offsetWidth-e.clientWidth,document.body.removeChild(e)}return F}var w=null;function Y(r){if(r===void 0&&(r=!1),w===null||r){var e=document.createElement("div"),t=e.style;t.width="50px",t.height="50px",t.overflow="scroll",t.direction="rtl";var n=document.createElement("div"),i=n.style;return i.width="100px",i.height="100px",e.appendChild(n),document.body.appendChild(e),e.scrollLeft>0?w="positive-descending":(e.scrollLeft=1,e.scrollLeft===0?w="negative":w="positive-ascending"),document.body.removeChild(e),w}return w}var Be=150,qe=function(e,t){return e};function He(r){var e,t=r.getItemOffset,n=r.getEstimatedTotalSize,i=r.getItemSize,a=r.getOffsetForIndexAndAlignment,o=r.getStartIndexForOffset,f=r.getStopIndexForStartIndex,u=r.initInstanceProps,v=r.shouldResetStyleCacheOnItemSizeChange,y=r.validateProps;return e=function(b){ne(_,b);function _(R){var s;return s=b.call(this,R)||this,s._instanceProps=u(s.props,V(s)),s._outerRef=void 0,s._resetIsScrollingTimeoutId=null,s.state={instance:V(s),isScrolling:!1,scrollDirection:"forward",scrollOffset:typeof s.props.initialScrollOffset=="number"?s.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},s._callOnItemsRendered=void 0,s._callOnItemsRendered=$(function(l,c,m,h){return s.props.onItemsRendered({overscanStartIndex:l,overscanStopIndex:c,visibleStartIndex:m,visibleStopIndex:h})}),s._callOnScroll=void 0,s._callOnScroll=$(function(l,c,m){return s.props.onScroll({scrollDirection:l,scrollOffset:c,scrollUpdateWasRequested:m})}),s._getItemStyle=void 0,s._getItemStyle=function(l){var c=s.props,m=c.direction,h=c.itemSize,I=c.layout,d=s._getItemStyleCache(v&&h,v&&I,v&&m),p;if(d.hasOwnProperty(l))p=d[l];else{var S=t(s.props,l,s._instanceProps),z=i(s.props,l,s._instanceProps),T=m==="horizontal"||I==="horizontal",k=m==="rtl",L=T?S:0;d[l]=p={position:"absolute",left:k?void 0:L,right:k?L:void 0,top:T?0:S,height:T?"100%":z,width:T?z:"100%"}}return p},s._getItemStyleCache=void 0,s._getItemStyleCache=$(function(l,c,m){return{}}),s._onScrollHorizontal=function(l){var c=l.currentTarget,m=c.clientWidth,h=c.scrollLeft,I=c.scrollWidth;s.setState(function(d){if(d.scrollOffset===h)return null;var p=s.props.direction,S=h;if(p==="rtl")switch(Y()){case"negative":S=-h;break;case"positive-descending":S=I-m-h;break}return S=Math.max(0,Math.min(S,I-m)),{isScrolling:!0,scrollDirection:d.scrollOffset<S?"forward":"backward",scrollOffset:S,scrollUpdateWasRequested:!1}},s._resetIsScrollingDebounced)},s._onScrollVertical=function(l){var c=l.currentTarget,m=c.clientHeight,h=c.scrollHeight,I=c.scrollTop;s.setState(function(d){if(d.scrollOffset===I)return null;var p=Math.max(0,Math.min(I,h-m));return{isScrolling:!0,scrollDirection:d.scrollOffset<p?"forward":"backward",scrollOffset:p,scrollUpdateWasRequested:!1}},s._resetIsScrollingDebounced)},s._outerRefSetter=function(l){var c=s.props.outerRef;s._outerRef=l,typeof c=="function"?c(l):c!=null&&typeof c=="object"&&c.hasOwnProperty("current")&&(c.current=l)},s._resetIsScrollingDebounced=function(){s._resetIsScrollingTimeoutId!==null&&Z(s._resetIsScrollingTimeoutId),s._resetIsScrollingTimeoutId=Ue(s._resetIsScrolling,Be)},s._resetIsScrolling=function(){s._resetIsScrollingTimeoutId=null,s.setState({isScrolling:!1},function(){s._getItemStyleCache(-1,null)})},s}_.getDerivedStateFromProps=function(s,l){return je(s,l),y(s),null};var M=_.prototype;return M.scrollTo=function(s){s=Math.max(0,s),this.setState(function(l){return l.scrollOffset===s?null:{scrollDirection:l.scrollOffset<s?"forward":"backward",scrollOffset:s,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},M.scrollToItem=function(s,l){l===void 0&&(l="auto");var c=this.props,m=c.itemCount,h=c.layout,I=this.state.scrollOffset;s=Math.max(0,Math.min(s,m-1));var d=0;if(this._outerRef){var p=this._outerRef;h==="vertical"?d=p.scrollWidth>p.clientWidth?X():0:d=p.scrollHeight>p.clientHeight?X():0}this.scrollTo(a(this.props,s,l,I,this._instanceProps,d))},M.componentDidMount=function(){var s=this.props,l=s.direction,c=s.initialScrollOffset,m=s.layout;if(typeof c=="number"&&this._outerRef!=null){var h=this._outerRef;l==="horizontal"||m==="horizontal"?h.scrollLeft=c:h.scrollTop=c}this._callPropsCallbacks()},M.componentDidUpdate=function(){var s=this.props,l=s.direction,c=s.layout,m=this.state,h=m.scrollOffset,I=m.scrollUpdateWasRequested;if(I&&this._outerRef!=null){var d=this._outerRef;if(l==="horizontal"||c==="horizontal")if(l==="rtl")switch(Y()){case"negative":d.scrollLeft=-h;break;case"positive-ascending":d.scrollLeft=h;break;default:var p=d.clientWidth,S=d.scrollWidth;d.scrollLeft=S-p-h;break}else d.scrollLeft=h;else d.scrollTop=h}this._callPropsCallbacks()},M.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&Z(this._resetIsScrollingTimeoutId)},M.render=function(){var s=this.props,l=s.children,c=s.className,m=s.direction,h=s.height,I=s.innerRef,d=s.innerElementType,p=s.innerTagName,S=s.itemCount,z=s.itemData,T=s.itemKey,k=T===void 0?qe:T,L=s.layout,he=s.outerElementType,ve=s.outerTagName,pe=s.style,ge=s.useIsScrolling,Ie=s.width,H=this.state.isScrolling,D=m==="horizontal"||L==="horizontal",Se=D?this._onScrollHorizontal:this._onScrollVertical,j=this._getRangeToRender(),ye=j[0],_e=j[1],Q=[];if(S>0)for(var N=ye;N<=_e;N++)Q.push(A.createElement(l,{data:z,key:k(N,z),index:N,isScrolling:ge?H:void 0,style:this._getItemStyle(N)}));var K=n(this.props,this._instanceProps);return A.createElement(he||ve||"div",{className:c,onScroll:Se,ref:this._outerRefSetter,style:W({position:"relative",height:h,width:Ie,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:m},pe)},A.createElement(d||p||"div",{children:Q,ref:I,style:{height:D?"100%":K,pointerEvents:H?"none":void 0,width:D?K:"100%"}}))},M._callPropsCallbacks=function(){if(typeof this.props.onItemsRendered=="function"){var s=this.props.itemCount;if(s>0){var l=this._getRangeToRender(),c=l[0],m=l[1],h=l[2],I=l[3];this._callOnItemsRendered(c,m,h,I)}}if(typeof this.props.onScroll=="function"){var d=this.state,p=d.scrollDirection,S=d.scrollOffset,z=d.scrollUpdateWasRequested;this._callOnScroll(p,S,z)}},M._getRangeToRender=function(){var s=this.props,l=s.itemCount,c=s.overscanCount,m=this.state,h=m.isScrolling,I=m.scrollDirection,d=m.scrollOffset;if(l===0)return[0,0,0,0];var p=o(this.props,d,this._instanceProps),S=f(this.props,p,d,this._instanceProps),z=!h||I==="backward"?Math.max(1,c):1,T=!h||I==="forward"?Math.max(1,c):1;return[Math.max(0,p-z),Math.max(0,Math.min(l-1,S+T)),p,S]},_}(A.PureComponent),e.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},e}var je=function(e,t){e.children,e.direction,e.height,e.layout,e.innerTagName,e.outerTagName,e.width,t.instance},Qe=50,E=function(e,t,n){var i=e,a=i.itemSize,o=n.itemMetadataMap,f=n.lastMeasuredIndex;if(t>f){var u=0;if(f>=0){var v=o[f];u=v.offset+v.size}for(var y=f+1;y<=t;y++){var b=a(y);o[y]={offset:u,size:b},u+=b}n.lastMeasuredIndex=t}return o[t]},Ke=function(e,t,n){var i=t.itemMetadataMap,a=t.lastMeasuredIndex,o=a>0?i[a].offset:0;return o>=n?ce(e,t,a,0,n):Ve(e,t,Math.max(0,a),n)},ce=function(e,t,n,i,a){for(;i<=n;){var o=i+Math.floor((n-i)/2),f=E(e,o,t).offset;if(f===a)return o;f<a?i=o+1:f>a&&(n=o-1)}return i>0?i-1:0},Ve=function(e,t,n,i){for(var a=e.itemCount,o=1;n<a&&E(e,n,t).offset<i;)n+=o,o*=2;return ce(e,t,Math.min(n,a-1),Math.floor(n/2),i)},ee=function(e,t){var n=e.itemCount,i=t.itemMetadataMap,a=t.estimatedItemSize,o=t.lastMeasuredIndex,f=0;if(o>=n&&(o=n-1),o>=0){var u=i[o];f=u.offset+u.size}var v=n-o-1,y=v*a;return f+y},Ge=He({getItemOffset:function(e,t,n){return E(e,t,n).offset},getItemSize:function(e,t,n){return n.itemMetadataMap[t].size},getEstimatedTotalSize:ee,getOffsetForIndexAndAlignment:function(e,t,n,i,a,o){var f=e.direction,u=e.height,v=e.layout,y=e.width,b=f==="horizontal"||v==="horizontal",_=b?y:u,M=E(e,t,a),R=ee(e,a),s=Math.max(0,Math.min(R-_,M.offset)),l=Math.max(0,M.offset-_+M.size+o);switch(n==="smart"&&(i>=l-_&&i<=s+_?n="auto":n="center"),n){case"start":return s;case"end":return l;case"center":return Math.round(l+(s-l)/2);case"auto":default:return i>=l&&i<=s?i:i<l?l:s}},getStartIndexForOffset:function(e,t,n){return Ke(e,n,t)},getStopIndexForStartIndex:function(e,t,n,i){for(var a=e.direction,o=e.height,f=e.itemCount,u=e.layout,v=e.width,y=a==="horizontal"||u==="horizontal",b=y?v:o,_=E(e,t,i),M=n+b,R=_.offset+_.size,s=t;s<f-1&&R<M;)s++,R+=E(e,s,i).size;return s},initInstanceProps:function(e,t){var n=e,i=n.estimatedItemSize,a={itemMetadataMap:{},estimatedItemSize:i||Qe,lastMeasuredIndex:-1};return t.resetAfterIndex=function(o,f){f===void 0&&(f=!0),a.lastMeasuredIndex=Math.min(a.lastMeasuredIndex,o-1),t._getItemStyleCache(-1),f&&t.forceUpdate()},a},shouldResetStyleCacheOnItemSizeChange:!1,validateProps:function(e){e.itemSize}});function te(r,e){for(var t in r)if(!(t in e))return!0;for(var n in e)if(r[n]!==e[n])return!0;return!1}var Je=["style"],Ze=["style"];function Xe(r,e){var t=r.style,n=G(r,Je),i=e.style,a=G(e,Ze);return!te(t,i)&&!te(n,a)}function Ye(r){const e=r.providers,t=Object.keys(e),n={};for(let i=0;i<t.length;i++){const a=t[i];n[a]={...e[a],idx:i}}return{byName:n,names:t}}async function et(r,e){const{url:t,init:n}=q(e);let i={providers:{}};try{const a=await fetch(t+r,n);a.ok&&(i=await a.json())}catch(a){console.log("failed to GET /providers/rules",a)}return Ye(i)}async function ue({name:r,apiConfig:e}){const{url:t,init:n}=q(e);try{return(await fetch(t+`/providers/rules/${r}`,{method:"PUT",...n})).ok}catch(i){return console.log("failed to PUT /providers/rules/:name",i),!1}}async function tt({names:r,apiConfig:e}){for(let t=0;t<r.length;t++)await ue({name:r[t],apiConfig:e})}var rt=function(r,e,t,n,i,a,o,f){if(!r){var u;if(e===void 0)u=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var v=[t,n,i,a,o,f],y=0;u=new Error(e.replace(/%s/g,function(){return v[y++]})),u.name="Invariant Violation"}throw u.framesToPop=1,u}},nt=rt;function it(r){return nt(r.rules&&r.rules.length>=0,"there is no valid rules list in the rules API response"),r.rules.map((e,t)=>({...e,id:t}))}async function st(r,e){let t={rules:[]};try{const{url:n,init:i}=q(e),a=await fetch(n+r,i);a.ok&&(t=await a.json())}catch(n){console.log("failed to fetch rules",n)}return it(t)}const de=Te({key:"ruleFilterText",default:""});function at(r,e){const t=B(),{mutate:n,isLoading:i}=le(ue,{onSuccess:()=>{t.invalidateQueries("/providers/rules")}});return[o=>{o.preventDefault(),n({name:r,apiConfig:e})},i]}function ot(r){const e=B(),{data:t}=fe(r),{mutate:n,isLoading:i}=le(tt,{onSuccess:()=>{e.invalidateQueries("/providers/rules")}});return[o=>{o.preventDefault(),n({names:t.names,apiConfig:r})},i]}function fe(r){return se(["/providers/rules",r],()=>et("/providers/rules",r))}function lt(r){const{data:e,isFetching:t}=se(["/rules",r],()=>st("/rules",r)),{data:n}=fe(r),[i]=Ce(de);if(i==="")return{rules:e,provider:n,isFetching:t};{const a=i.toLowerCase();return{rules:e.filter(o=>o.payload.toLowerCase().indexOf(a)>=0),isFetching:t,provider:{byName:n.byName,names:n.names.filter(o=>o.toLowerCase().indexOf(a)>=0)}}}}const ct="_RuleProviderItem_ly9yn_1",ut="_left_ly9yn_7",dt="_middle_ly9yn_14",ft="_gray_ly9yn_20",mt="_refreshButtonWrapper_ly9yn_24",x={RuleProviderItem:ct,left:ut,middle:dt,gray:ft,refreshButtonWrapper:mt};function ht({idx:r,name:e,vehicleType:t,behavior:n,updatedAt:i,ruleCount:a,apiConfig:o}){const[f,u]=at(e,o),v=Le(new Date(i),new Date);return O("div",{className:x.RuleProviderItem,children:[g("span",{className:x.left,children:r}),O("div",{className:x.middle,children:[g(we,{name:e,type:`${t} / ${n}`}),g("div",{className:x.gray,children:a<2?`${a} rule`:`${a} rules`}),O("small",{className:x.gray,children:["Updated ",v," ago"]})]}),g("span",{className:x.refreshButtonWrapper,children:g(xe,{onClick:f,disabled:u,children:g(oe,{isRotating:u})})})]})}function vt({apiConfig:r}){const[e,t]=ot(r),{t:n}=ae();return g(Ae,{icon:g(oe,{isRotating:t}),text:n("update_all_rule_provider"),style:We,onClick:e})}const pt="_rule_1e5p9_4",gt="_left_1e5p9_15",It="_a_1e5p9_22",St="_b_1e5p9_29",yt="_type_1e5p9_41",_t="_size_1e5p9_46",Rt="_payloadAndSize_1e5p9_50",C={rule:pt,left:gt,a:It,b:St,type:yt,size:_t,payloadAndSize:Rt},U={_default:"#59caf9",DIRECT:"#f5bc41",REJECT:"#cb3166"};function Mt({proxy:r}){let e=U._default;return U[r]&&(e=U[r]),{color:e}}function bt({type:r,payload:e,proxy:t,id:n,size:i}){const a=Mt({proxy:t});return O("div",{className:C.rule,children:[g("div",{className:C.left,children:n}),O("div",{style:{marginLeft:10},children:[O("div",{className:C.payloadAndSize,children:[g("div",{className:C.payload,children:e}),(r==="GeoSite"||r==="GeoIP")&&O("div",{style:{margin:"0 1em"},className:C.size,children:[" ","size: ",i]})]}),O("div",{className:C.a,children:[g("div",{className:C.type,children:r}),g("div",{style:a,children:t})]})]})]})}const Ot="_header_10x16_4",zt="_RuleProviderItemWrapper_10x16_11",me={header:Ot,RuleProviderItemWrapper:zt},{memo:Tt}=P,re=30;function Ct(r,{rules:e,provider:t}){const n=t.names.length;return r<n?t.names[r]:e[r-n].id}function wt({provider:r}){return function(t){const n=r.names.length;return t<n?90:60}}const xt=Tt(({index:r,style:e,data:t})=>{const{rules:n,provider:i,apiConfig:a}=t,o=i.names.length;if(r<o){const u=i.names[r],v=i.byName[u];return g("div",{style:e,className:me.RuleProviderItemWrapper,children:g(ht,{apiConfig:a,...v})})}const f=n[r-o];return g("div",{style:e,children:g(bt,{...f})})},Xe),Pt=r=>({apiConfig:Ee(r)}),Ut=Pe(Pt)(Et);function Et({apiConfig:r}){const[e,t]=De(),{rules:n,provider:i}=lt(r),a=wt({provider:i}),{t:o}=ae();return O("div",{children:[O("div",{className:me.header,children:[g(Ne,{title:o("Rules")}),g(ke,{textAtom:de,placeholder:o("Search")})]}),g("div",{ref:e,style:{paddingBottom:re},children:g(Ge,{height:t-re,width:"100%",itemCount:n.length+i.names.length,itemSize:a,itemData:{rules:n,provider:i,apiConfig:r},itemKey:Ct,children:xt})}),i&&i.names&&i.names.length>0?g(vt,{apiConfig:r}):null]})}export{Ut as default};
