var un=Object.defineProperty;var gn=(i,t,e)=>t in i?un(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var S=(i,t,e)=>(gn(i,typeof t!="symbol"?t+"":t,e),e);/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 <PERSON><PERSON>
 * Released under the MIT License
 */function te(i){return i+.5|0}const ot=(i,t,e)=>Math.max(Math.min(i,e),t);function Wt(i){return ot(te(i*2.55),0,255)}function ht(i){return ot(te(i*255),0,255)}function st(i){return ot(te(i/2.55)/100,0,1)}function ui(i){return ot(te(i*100),0,100)}const q={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Ye=[..."0123456789ABCDEF"],pn=i=>Ye[i&15],mn=i=>Ye[(i&240)>>4]+Ye[i&15],oe=i=>(i&240)>>4===(i&15),bn=i=>oe(i.r)&&oe(i.g)&&oe(i.b)&&oe(i.a);function _n(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&q[i[1]]*17,g:255&q[i[2]]*17,b:255&q[i[3]]*17,a:t===5?q[i[4]]*17:255}:(t===7||t===9)&&(e={r:q[i[1]]<<4|q[i[2]],g:q[i[3]]<<4|q[i[4]],b:q[i[5]]<<4|q[i[6]],a:t===9?q[i[7]]<<4|q[i[8]]:255})),e}const xn=(i,t)=>i<255?t(i):"";function yn(i){var t=bn(i)?pn:mn;return i?"#"+t(i.r)+t(i.g)+t(i.b)+xn(i.a,t):void 0}const vn=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Ss(i,t,e){const s=t*Math.min(e,1-e),n=(o,r=(o+i/30)%12)=>e-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function kn(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function wn(i,t,e){const s=Ss(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function Mn(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function ti(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),r=Math.min(e,s,n),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=Mn(e,s,n,h,o),l=l*60+.5),[l|0,c||0,a]}function ei(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(ht)}function ii(i,t,e){return ei(Ss,i,t,e)}function Sn(i,t,e){return ei(wn,i,t,e)}function Pn(i,t,e){return ei(kn,i,t,e)}function Ps(i){return(i%360+360)%360}function Dn(i){const t=vn.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?Wt(+t[5]):ht(+t[5]));const n=Ps(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=Sn(n,o,r):t[1]==="hsv"?s=Pn(n,o,r):s=ii(n,o,r),{r:s[0],g:s[1],b:s[2],a:e}}function On(i,t){var e=ti(i);e[0]=Ps(e[0]+t),e=ii(e),i.r=e[0],i.g=e[1],i.b=e[2]}function Ln(i){if(!i)return;const t=ti(i),e=t[0],s=ui(t[1]),n=ui(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${st(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const gi={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},pi={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Cn(){const i={},t=Object.keys(pi),e=Object.keys(gi);let s,n,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],n=0;n<e.length;n++)o=e[n],a=a.replace(o,gi[o]);o=parseInt(pi[r],16),i[a]=[o>>16&255,o>>8&255,o&255]}return i}let re;function Tn(i){re||(re=Cn(),re.transparent=[0,0,0,0]);const t=re[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const In=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function An(i){const t=In.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const r=+t[7];e=t[8]?Wt(r):ot(r*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?Wt(s):ot(s,0,255)),n=255&(t[4]?Wt(n):ot(n,0,255)),o=255&(t[6]?Wt(o):ot(o,0,255)),{r:s,g:n,b:o,a:e}}}function Fn(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${st(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const Ee=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Pt=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function zn(i,t,e){const s=Pt(st(i.r)),n=Pt(st(i.g)),o=Pt(st(i.b));return{r:ht(Ee(s+e*(Pt(st(t.r))-s))),g:ht(Ee(n+e*(Pt(st(t.g))-n))),b:ht(Ee(o+e*(Pt(st(t.b))-o))),a:i.a+e*(t.a-i.a)}}function ae(i,t,e){if(i){let s=ti(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=ii(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function Ds(i,t){return i&&Object.assign(t||{},i)}function mi(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=ht(i[3]))):(t=Ds(i,{r:0,g:0,b:0,a:1}),t.a=ht(t.a)),t}function En(i){return i.charAt(0)==="r"?An(i):Dn(i)}class Kt{constructor(t){if(t instanceof Kt)return t;const e=typeof t;let s;e==="object"?s=mi(t):e==="string"&&(s=_n(t)||Tn(t)||En(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=Ds(this._rgb);return t&&(t.a=st(t.a)),t}set rgb(t){this._rgb=mi(t)}rgbString(){return this._valid?Fn(this._rgb):void 0}hexString(){return this._valid?yn(this._rgb):void 0}hslString(){return this._valid?Ln(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=s.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=zn(this._rgb,t._rgb,e)),this}clone(){return new Kt(this.rgb)}alpha(t){return this._rgb.a=ht(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=te(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ae(this._rgb,2,t),this}darken(t){return ae(this._rgb,2,-t),this}saturate(t){return ae(this._rgb,1,t),this}desaturate(t){return ae(this._rgb,1,-t),this}rotate(t){return On(this._rgb,t),this}}/*!
 * Chart.js v4.2.0
 * https://www.chartjs.org
 * (c) 2023 Chart.js Contributors
 * Released under the MIT License
 */const Rn=(()=>{let i=0;return()=>i++})();function A(i){return i===null||typeof i>"u"}function F(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function O(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function z(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function U(i,t){return z(i)?i:t}function D(i,t){return typeof i>"u"?t:i}const Bn=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function I(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function N(i,t,e,s){let n,o,r;if(F(i))if(o=i.length,s)for(n=o-1;n>=0;n--)t.call(e,i[n],n);else for(n=0;n<o;n++)t.call(e,i[n],n);else if(O(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function bi(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function ke(i){if(F(i))return i.map(ke);if(O(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=ke(i[e[n]]);return t}return i}function Os(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function Hn(i,t,e,s){if(!Os(i))return;const n=t[i],o=e[i];O(n)&&O(o)?qt(n,o,s):t[i]=ke(o)}function qt(i,t,e){const s=F(t)?t:[t],n=s.length;if(!O(i))return i;e=e||{};const o=e.merger||Hn;let r;for(let a=0;a<n;++a){if(r=s[a],!O(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function jt(i,t){return qt(i,t,{merger:Nn})}function Nn(i,t,e){if(!Os(i))return;const s=t[i],n=e[i];O(s)&&O(n)?jt(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=ke(n))}const _i={"":i=>i,x:i=>i.x,y:i=>i.y};function Wn(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function Vn(i){const t=Wn(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function we(i,t){return(_i[t]||(_i[t]=Vn(t)))(i)}function si(i){return i.charAt(0).toUpperCase()+i.slice(1)}const Z=i=>typeof i<"u",ft=i=>typeof i=="function",xi=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function jn(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const H=Math.PI,X=2*H,$n=X+H,Me=Number.POSITIVE_INFINITY,Un=H/180,j=H/2,dt=H/4,yi=H*2/3,rt=Math.log10,Ot=Math.sign;function $t(i,t,e){return Math.abs(i-t)<e}function vi(i){const t=Math.round(i);i=$t(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(rt(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function Yn(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function Gt(i){return!isNaN(parseFloat(i))&&isFinite(i)}function Xn(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Ls(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function at(i){return i*(H/180)}function ni(i){return i*(180/H)}function ki(i){if(!z(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function Kn(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*H&&(o+=X),{angle:o,distance:n}}function wi(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function qn(i,t){return(i-t+$n)%X-H}function Y(i){return(i%X+X)%X}function Cs(i,t,e,s){const n=Y(i),o=Y(t),r=Y(e),a=Y(o-n),l=Y(r-n),c=Y(n-o),h=Y(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function tt(i,t,e){return Math.max(t,Math.min(e,i))}function Gn(i){return tt(i,-32768,32767)}function Dt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function oi(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const _t=(i,t,e,s)=>oi(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),Zn=(i,t,e)=>oi(i,e,s=>i[s][t]>=e);function Qn(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Ts=["push","pop","shift","splice","unshift"];function Jn(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Ts.forEach(e=>{const s="_onData"+si(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function Mi(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Ts.forEach(o=>{delete i[o]}),delete i._chartjs)}function to(i){const t=new Set;let e,s;for(e=0,s=i.length;e<s;++e)t.add(i[e]);return t.size===s?i:Array.from(t)}const Is=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function As(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Is.call(window,()=>{s=!1,i.apply(t,e)}))}}function eo(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const Fs=i=>i==="start"?"left":i==="end"?"right":"center",$=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,io=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t;function so(i,t,e){const s=t.length;let n=0,o=s;if(i._sorted){const{iScale:r,_parsed:a}=i,l=r.axis,{min:c,max:h,minDefined:f,maxDefined:d}=r.getUserBounds();f&&(n=tt(Math.min(_t(a,r.axis,c).lo,e?s:_t(t,l,r.getPixelForValue(c)).lo),0,s-1)),d?o=tt(Math.max(_t(a,r.axis,h,!0).hi+1,e?0:_t(t,l,r.getPixelForValue(h),!0).hi+1),n,s)-n:o=s-n}return{start:n,count:o}}function no(i){const{xScale:t,yScale:e,_scaleRanges:s}=i,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!s)return i._scaleRanges=n,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==e.min||s.ymax!==e.max;return Object.assign(s,n),o}const le=i=>i===0||i===1,Si=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*X/e)),Pi=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*X/e)+1,Ut={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*j)+1,easeOutSine:i=>Math.sin(i*j),easeInOutSine:i=>-.5*(Math.cos(H*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>le(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>le(i)?i:Si(i,.075,.3),easeOutElastic:i=>le(i)?i:Pi(i,.075,.3),easeInOutElastic(i){return le(i)?i:i<.5?.5*Si(i*2,.1125,.45):.5+.5*Pi(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-Ut.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?Ut.easeInBounce(i*2)*.5:Ut.easeOutBounce(i*2-1)*.5+.5};function zs(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Di(i){return zs(i)?i:new Kt(i)}function Re(i){return zs(i)?i:new Kt(i).saturate(.5).darken(.1).hexString()}const oo=["x","y","borderWidth","radius","tension"],ro=["color","borderColor","backgroundColor"];function ao(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:ro},numbers:{type:"number",properties:oo}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function lo(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Oi=new Map;function co(i,t){t=t||{};const e=i+JSON.stringify(t);let s=Oi.get(e);return s||(s=new Intl.NumberFormat(i,t),Oi.set(e,s)),s}function ri(i,t,e){return co(t,e).format(i)}const Es={values(i){return F(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=ho(i,e)}const r=rt(Math.abs(o)),a=Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),ri(i,s,l)},logarithmic(i,t,e){if(i===0)return"0";const s=e[t].significand||i/Math.pow(10,Math.floor(rt(i)));return[1,2,3,5,10,15].includes(s)||t>.8*e.length?Es.numeric.call(this,i,t,e):""}};function ho(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var Te={formatters:Es};function fo(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Te.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const yt=Object.create(null),Xe=Object.create(null);function Yt(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Be(i,t,e){return typeof t=="string"?qt(Yt(i,t),e):qt(Yt(i,""),t)}class uo{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>Re(n.backgroundColor),this.hoverBorderColor=(s,n)=>Re(n.borderColor),this.hoverColor=(s,n)=>Re(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Be(this,t,e)}get(t){return Yt(this,t)}describe(t,e){return Be(Xe,t,e)}override(t,e){return Be(yt,t,e)}route(t,e,s,n){const o=Yt(this,t),r=Yt(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return O(l)?Object.assign({},c,l):D(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var R=new uo({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[ao,lo,fo]);function go(i){return!i||A(i.size)||A(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function Se(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function po(i,t,e,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(n=s.data={},o=s.garbageCollect=[],s.font=t),i.save(),i.font=t;let r=0;const a=e.length;let l,c,h,f,d;for(l=0;l<a;l++)if(f=e[l],f!=null&&F(f)!==!0)r=Se(i,n,o,r,f);else if(F(f))for(c=0,h=f.length;c<h;c++)d=f[c],d!=null&&!F(d)&&(r=Se(i,n,o,r,d));i.restore();const u=o.length/2;if(u>e.length){for(l=0;l<u;l++)delete n[o[l]];o.splice(0,u)}return r}function ut(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function Li(i,t){t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore()}function mo(i,t,e,s){Rs(i,t,e,s,null)}function Rs(i,t,e,s,n){let o,r,a,l,c,h,f,d;const u=t.pointStyle,m=t.rotation,g=t.radius;let p=(m||0)*Un;if(u&&typeof u=="object"&&(o=u.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(p),i.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),i.restore();return}if(!(isNaN(g)||g<=0)){switch(i.beginPath(),u){default:n?i.ellipse(e,s,n/2,g,0,0,X):i.arc(e,s,g,0,X),i.closePath();break;case"triangle":h=n?n/2:g,i.moveTo(e+Math.sin(p)*h,s-Math.cos(p)*g),p+=yi,i.lineTo(e+Math.sin(p)*h,s-Math.cos(p)*g),p+=yi,i.lineTo(e+Math.sin(p)*h,s-Math.cos(p)*g),i.closePath();break;case"rectRounded":c=g*.516,l=g-c,r=Math.cos(p+dt)*l,f=Math.cos(p+dt)*(n?n/2-c:l),a=Math.sin(p+dt)*l,d=Math.sin(p+dt)*(n?n/2-c:l),i.arc(e-f,s-a,c,p-H,p-j),i.arc(e+d,s-r,c,p-j,p),i.arc(e+f,s+a,c,p,p+j),i.arc(e-d,s+r,c,p+j,p+H),i.closePath();break;case"rect":if(!m){l=Math.SQRT1_2*g,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}p+=dt;case"rectRot":f=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,d=Math.sin(p)*(n?n/2:g),i.moveTo(e-f,s-a),i.lineTo(e+d,s-r),i.lineTo(e+f,s+a),i.lineTo(e-d,s+r),i.closePath();break;case"crossRot":p+=dt;case"cross":f=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,d=Math.sin(p)*(n?n/2:g),i.moveTo(e-f,s-a),i.lineTo(e+f,s+a),i.moveTo(e+d,s-r),i.lineTo(e-d,s+r);break;case"star":f=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,d=Math.sin(p)*(n?n/2:g),i.moveTo(e-f,s-a),i.lineTo(e+f,s+a),i.moveTo(e+d,s-r),i.lineTo(e-d,s+r),p+=dt,f=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,d=Math.sin(p)*(n?n/2:g),i.moveTo(e-f,s-a),i.lineTo(e+f,s+a),i.moveTo(e+d,s-r),i.lineTo(e-d,s+r);break;case"line":r=n?n/2:Math.cos(p)*g,a=Math.sin(p)*g,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(p)*(n?n/2:g),s+Math.sin(p)*g);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function Zt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function Ie(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function Ae(i){i.restore()}function bo(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function _o(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function Lt(i,t,e,s,n,o={}){const r=F(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,xo(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&vo(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),A(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),yo(i,e,s,c,o),s+=n.lineHeight;i.restore()}function xo(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),A(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function yo(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function vo(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Bs(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,-j,H,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,H,j,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,j,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-j,!0),i.lineTo(e+r.topLeft,s)}const ko=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,wo=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Mo(i,t){const e=(""+i).match(ko);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const So=i=>+i||0;function Hs(i,t){const e={},s=O(t),n=s?Object.keys(t):t,o=O(i)?s?r=>D(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=So(o(r));return e}function Po(i){return Hs(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Ns(i){return Hs(i,["topLeft","topRight","bottomLeft","bottomRight"])}function G(i){const t=Po(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function et(i,t){i=i||{},t=t||R.font;let e=D(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=D(i.style,t.style);s&&!(""+s).match(wo)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:D(i.family,t.family),lineHeight:Mo(D(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:D(i.weight,t.weight),string:""};return n.string=go(n),n}function ce(i,t,e,s){let n=!0,o,r,a;for(o=0,r=i.length;o<r;++o)if(a=i[o],a!==void 0&&(t!==void 0&&typeof a=="function"&&(a=a(t),n=!1),e!==void 0&&F(a)&&(a=a[e%a.length],n=!1),a!==void 0))return s&&!n&&(s.cacheable=!1),a}function Do(i,t,e){const{min:s,max:n}=i,o=Bn(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function kt(i,t){return Object.assign(Object.create(i),t)}function ai(i,t=[""],e=i,s,n=()=>i[0]){Z(s)||(s=$s("_fallback",i));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:e,_fallback:s,_getTarget:n,override:r=>ai([r,...i],t,e,s)};return new Proxy(o,{deleteProperty(r,a){return delete r[a],delete r._keys,delete i[0][a],!0},get(r,a){return Vs(r,a,()=>zo(a,t,i,r))},getOwnPropertyDescriptor(r,a){return Reflect.getOwnPropertyDescriptor(r._scopes[0],a)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(r,a){return Ti(r).includes(a)},ownKeys(r){return Ti(r)},set(r,a,l){const c=r._storage||(r._storage=n());return r[a]=c[a]=l,delete r._keys,!0}})}function Ct(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:Ws(i,s),setContext:o=>Ct(i,o,e,s),override:o=>Ct(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return Vs(o,r,()=>Lo(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function Ws(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:ft(e)?e:()=>e,isIndexable:ft(s)?s:()=>s}}const Oo=(i,t)=>i?i+si(t):t,li=(i,t)=>O(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Vs(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t))return i[t];const s=e();return i[t]=s,s}function Lo(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return ft(a)&&r.isScriptable(t)&&(a=Co(t,a,i,e)),F(a)&&a.length&&(a=To(t,a,i,r.isIndexable)),li(t,a)&&(a=Ct(a,n,o&&o[t],r)),a}function Co(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);return a.add(i),t=t(o,r||s),a.delete(i),li(i,t)&&(t=ci(n._scopes,n,i,t)),t}function To(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(Z(o.index)&&s(i))t=t[o.index%t.length];else if(O(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const f=ci(c,n,i,h);t.push(Ct(f,o,r&&r[i],a))}}return t}function js(i,t,e){return ft(i)?i(t,e):i}const Io=(i,t)=>i===!0?t:typeof i=="string"?we(t,i):void 0;function Ao(i,t,e,s,n){for(const o of t){const r=Io(e,o);if(r){i.add(r);const a=js(r._fallback,e,n);if(Z(a)&&a!==e&&a!==s)return a}else if(r===!1&&Z(s)&&e!==s)return null}return!1}function ci(i,t,e,s){const n=t._rootScopes,o=js(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=Ci(a,r,e,o||e,s);return l===null||Z(o)&&o!==e&&(l=Ci(a,r,o,l,s),l===null)?!1:ai(Array.from(a),[""],n,o,()=>Fo(t,e,s))}function Ci(i,t,e,s,n){for(;e;)e=Ao(i,t,e,s,n);return e}function Fo(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return F(n)&&O(e)?e:n||{}}function zo(i,t,e,s){let n;for(const o of t)if(n=$s(Oo(o,i),e),Z(n))return li(i,n)?ci(e,s,i,n):n}function $s(i,t){for(const e of t){if(!e)continue;const s=e[i];if(Z(s))return s}}function Ti(i){let t=i._keys;return t||(t=i._keys=Eo(i._scopes)),t}function Eo(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}const Ro=Number.EPSILON||1e-14,Tt=(i,t)=>t<i.length&&!i[t].skip&&i[t],Us=i=>i==="x"?"y":"x";function Bo(i,t,e,s){const n=i.skip?t:i,o=t,r=e.skip?t:e,a=wi(o,n),l=wi(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const f=s*c,d=s*h;return{previous:{x:o.x-f*(r.x-n.x),y:o.y-f*(r.y-n.y)},next:{x:o.x+d*(r.x-n.x),y:o.y+d*(r.y-n.y)}}}function Ho(i,t,e){const s=i.length;let n,o,r,a,l,c=Tt(i,0);for(let h=0;h<s-1;++h)if(l=c,c=Tt(i,h+1),!(!l||!c)){if($t(t[h],0,Ro)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[h]=n*r*t[h],e[h+1]=o*r*t[h])}}function No(i,t,e="x"){const s=Us(e),n=i.length;let o,r,a,l=Tt(i,0);for(let c=0;c<n;++c){if(r=a,a=l,l=Tt(i,c+1),!a)continue;const h=a[e],f=a[s];r&&(o=(h-r[e])/3,a[`cp1${e}`]=h-o,a[`cp1${s}`]=f-o*t[c]),l&&(o=(l[e]-h)/3,a[`cp2${e}`]=h+o,a[`cp2${s}`]=f+o*t[c])}}function Wo(i,t="x"){const e=Us(t),s=i.length,n=Array(s).fill(0),o=Array(s);let r,a,l,c=Tt(i,0);for(r=0;r<s;++r)if(a=l,l=c,c=Tt(i,r+1),!!l){if(c){const h=c[t]-l[t];n[r]=h!==0?(c[e]-l[e])/h:0}o[r]=a?c?Ot(n[r-1])!==Ot(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}Ho(i,n,o),No(i,o,t)}function he(i,t,e){return Math.max(Math.min(i,e),t)}function Vo(i,t){let e,s,n,o,r,a=Zt(i[0],t);for(e=0,s=i.length;e<s;++e)r=o,o=a,a=e<s-1&&Zt(i[e+1],t),o&&(n=i[e],r&&(n.cp1x=he(n.cp1x,t.left,t.right),n.cp1y=he(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=he(n.cp2x,t.left,t.right),n.cp2y=he(n.cp2y,t.top,t.bottom)))}function jo(i,t,e,s,n){let o,r,a,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")Wo(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,r=i.length;o<r;++o)a=i[o],l=Bo(c,a,i[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&Vo(i,e)}function Ys(){return typeof window<"u"&&typeof document<"u"}function hi(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Pe(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const Fe=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function $o(i,t){return Fe(i).getPropertyValue(t)}const Uo=["top","right","bottom","left"];function xt(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=Uo[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Yo=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function Xo(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(Yo(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function mt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=Fe(e),o=n.boxSizing==="border-box",r=xt(n,"padding"),a=xt(n,"border","width"),{x:l,y:c,box:h}=Xo(i,e),f=r.left+(h&&a.left),d=r.top+(h&&a.top);let{width:u,height:m}=t;return o&&(u-=r.width+a.width,m-=r.height+a.height),{x:Math.round((l-f)/u*e.width/s),y:Math.round((c-d)/m*e.height/s)}}function Ko(i,t,e){let s,n;if(t===void 0||e===void 0){const o=hi(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=Fe(o),l=xt(a,"border","width"),c=xt(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=Pe(a.maxWidth,o,"clientWidth"),n=Pe(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||Me,maxHeight:n||Me}}const fe=i=>Math.round(i*10)/10;function qo(i,t,e,s){const n=Fe(i),o=xt(n,"margin"),r=Pe(n.maxWidth,i,"clientWidth")||Me,a=Pe(n.maxHeight,i,"clientHeight")||Me,l=Ko(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const d=xt(n,"border","width"),u=xt(n,"padding");c-=u.width+d.width,h-=u.height+d.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=fe(Math.min(c,r,l.maxWidth)),h=fe(Math.min(h,a,l.maxHeight)),c&&!h&&(h=fe(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=fe(Math.floor(h*s))),{width:c,height:h}}function Ii(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Go=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch{}return i}();function Ai(i,t){const e=$o(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function bt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function Zo(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function Qo(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},r=bt(i,n,e),a=bt(n,o,e),l=bt(o,t,e),c=bt(r,a,e),h=bt(a,l,e);return bt(c,h,e)}const Jo=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},tr=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function He(i,t,e){return i?Jo(t,e):tr()}function er(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function ir(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function Xs(i){return i==="angle"?{between:Cs,compare:qn,normalize:Y}:{between:Dt,compare:(t,e)=>t-e,normalize:t=>t}}function Fi({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function sr(i,t,e){const{property:s,start:n,end:o}=e,{between:r,normalize:a}=Xs(s),l=t.length;let{start:c,end:h,loop:f}=i,d,u;if(f){for(c+=l,h+=l,d=0,u=l;d<u&&r(a(t[c%l][s]),n,o);++d)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:f,style:i.style}}function Ks(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,r=t.length,{compare:a,between:l,normalize:c}=Xs(s),{start:h,end:f,loop:d,style:u}=sr(i,t,e),m=[];let g=!1,p=null,b,x,w;const L=()=>l(n,w,b)&&a(n,w)!==0,_=()=>a(o,b)===0||l(o,w,b),y=()=>g||L(),v=()=>!g||_();for(let k=h,M=h;k<=f;++k)x=t[k%r],!x.skip&&(b=c(x[s]),b!==w&&(g=l(b,n,o),p===null&&y()&&(p=a(b,n)===0?k:M),p!==null&&v()&&(m.push(Fi({start:p,end:k,loop:d,count:r,style:u})),p=null),M=k,w=b));return p!==null&&m.push(Fi({start:p,end:f,loop:d,count:r,style:u})),m}function qs(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=Ks(s[n],i.points,t);o.length&&e.push(...o)}return e}function nr(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function or(i,t,e,s){const n=i.length,o=[];let r=t,a=i[t],l;for(l=t+1;l<=e;++l){const c=i[l%n];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%n,end:r%n,loop:s}),o}function rr(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:r,end:a}=nr(e,n,o,s);if(s===!0)return zi(i,[{start:r,end:a,loop:o}],e,t);const l=a<r?a+n:a,c=!!i._fullLoop&&r===0&&a===n-1;return zi(i,or(e,r,l,c),e,t)}function zi(i,t,e,s){return!s||!s.setContext||!e?t:ar(i,t,e,s)}function ar(i,t,e,s){const n=i._chart.getContext(),o=Ei(i.options),{_datasetIndex:r,options:{spanGaps:a}}=i,l=e.length,c=[];let h=o,f=t[0].start,d=f;function u(m,g,p,b){const x=a?-1:1;if(m!==g){for(m+=l;e[m%l].skip;)m-=x;for(;e[g%l].skip;)g+=x;m%l!==g%l&&(c.push({start:m%l,end:g%l,loop:p,style:b}),h=b,f=g%l)}}for(const m of t){f=a?f:m.start;let g=e[f%l],p;for(d=f+1;d<=m.end;d++){const b=e[d%l];p=Ei(s.setContext(kt(n,{type:"segment",p0:g,p1:b,p0DataIndex:(d-1)%l,p1DataIndex:d%l,datasetIndex:r}))),lr(p,h)&&u(f,d-1,m.loop,h),g=b,h=p}f<d-1&&u(f,d-1,m.loop,h)}return c}function Ei(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function lr(i,t){return t&&JSON.stringify(i)!==JSON.stringify(t)}/*!
 * Chart.js v4.2.0
 * https://www.chartjs.org
 * (c) 2023 Chart.js Contributors
 * Released under the MIT License
 */class cr{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Is.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var it=new cr;const Ri="transparent",hr={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=Di(i||Ri),n=s.valid&&Di(t||Ri);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class fr{constructor(t,e,s,n){const o=e[s];n=ce([t.to,n,o,t.from]);const r=ce([t.from,o,n]);this._active=!0,this._fn=t.fn||hr[t.type||typeof r],this._easing=Ut[t.easing]||Ut.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=ce([t.to,e,n,t.from]),this._from=ce([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class dr{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!O(t))return;const e=Object.keys(R.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!O(o))return;const r={};for(const a of e)r[a]=o[a];(F(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=gr(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&ur(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let f=o[c];const d=s.get(c);if(f)if(d&&f.active()){f.update(d,h,a);continue}else f.cancel();if(!d||!d.duration){t[c]=h;continue}o[c]=f=new fr(d,t,c,h),n.push(f)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return it.add(this._chart,s),!0}}function ur(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function gr(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Bi(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function pr(i,t,e){if(e===!1)return!1;const s=Bi(i,e),n=Bi(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function mr(i){let t,e,s,n;return O(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function Gs(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Hi(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t!==null){for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(s.all)continue;break}c=i.values[l],z(c)&&(o||t===0||Ot(t)===Ot(c))&&(t+=c)}return t}}function br(i){const t=Object.keys(i),e=new Array(t.length);let s,n,o;for(s=0,n=t.length;s<n;++s)o=t[s],e[s]={x:o,y:i[o]};return e}function Ni(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function _r(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function xr(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function yr(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function Wi(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function Vi(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=_r(o,r,s),f=t.length;let d;for(let u=0;u<f;++u){const m=t[u],{[l]:g,[c]:p}=m,b=m._stacks||(m._stacks={});d=b[c]=yr(n,h,g),d[a]=p,d._top=Wi(d,r,!0,s.type),d._bottom=Wi(d,r,!1,s.type);const x=d._visualValues||(d._visualValues={});x[a]=p}}function Ne(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function vr(i,t){return kt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function kr(i,t,e){return kt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Et(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const We=i=>i==="reset"||i==="none",ji=(i,t)=>t?i:Object.assign({},i),wr=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:Gs(e,!0),values:null};class Xt{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ni(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Et(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(f,d,u,m)=>f==="x"?d:f==="r"?m:u,o=e.xAxisID=D(s.xAxisID,Ne(t,"x")),r=e.yAxisID=D(s.yAxisID,Ne(t,"y")),a=e.rAxisID=D(s.rAxisID,Ne(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Mi(this._data,this),t._stacked&&Et(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(O(e))this._data=br(e);else if(s!==e){if(s){Mi(s,this);const n=this._cachedMeta;Et(n),n._parsed=[]}e&&Object.isExtensible(e)&&Jn(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=Ni(e.vScale,e),e.stack!==s.stack&&(n=!0,Et(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&Vi(this,e._parsed)}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,f,d;if(this._parsing===!1)s._parsed=n,s._sorted=!0,d=n;else{F(n[t])?d=this.parseArrayData(s,n,t,e):O(n[t])?d=this.parseObjectData(s,n,t,e):d=this.parsePrimitiveData(s,n,t,e);const u=()=>f[a]===null||c&&f[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=f=d[h],l&&(u()&&(l=!1),c=f);s._sorted=l}r&&Vi(this,d)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,f=new Array(n);let d,u,m;for(d=0,u=n;d<u;++d)m=d+s,f[d]={[a]:h||o.parse(c[m],m),[l]:r.parse(e[m],m)};return f}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,f;for(l=0,c=n;l<c;++l)h=l+s,f=e[h],a[l]={x:o.parse(f[0],h),y:r.parse(f[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,f,d,u;for(h=0,f=n;h<f;++h)d=h+s,u=e[d],c[h]={x:o.parse(we(u,a),d),y:r.parse(we(u,l),d)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:Gs(n,!0),values:e._stacks[t.axis]._visualValues};return Hi(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=Hi(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=wr(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:f}=xr(a);let d,u;function m(){u=n[d];const g=u[a.axis];return!z(u[t.axis])||h>g||f<g}for(d=0;d<r&&!(!m()&&(this.updateRangeFromParsed(c,t,u,l),o));++d);if(o){for(d=r-1;d>=0;--d)if(!m()){this.updateRangeFromParsed(c,t,u,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],z(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=mr(D(this.options.clip,pr(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const f=n[h];f.hidden||(f.active&&c?r.push(f):f.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=kr(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=vr(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&Z(s);if(a)return ji(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),f=n?[`${t}Hover`,"hover",t,""]:[t,""],d=c.getOptionScopes(this.getDataset(),h),u=Object.keys(R.elements[t]),m=()=>this.getContext(s,n,e),g=c.resolveNamedOptions(d,u,m,f);return g.$shared&&(g.$shared=l,o[r]=Object.freeze(ji(g,l))),g}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,f=h.datasetAnimationScopeKeys(this._type,e),d=h.getOptionScopes(this.getDataset(),f);l=h.createResolver(d,this.getContext(t,s,e))}const c=new dr(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||We(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){We(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!We(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&Et(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}S(Xt,"defaults",{}),S(Xt,"datasetElementType",null),S(Xt,"dataElementType",null);class _e extends Xt{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:s,data:n=[],_dataset:o}=e,r=this.chart._animationsDisabled;let{start:a,count:l}=so(e,n,r);this._drawStart=a,this._drawCount=l,no(e)&&(a=0,l=n.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=n;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!r,options:c},t),this.updateElements(n,a,l,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:f}=this._getSharedOptions(e,n),d=r.axis,u=a.axis,{spanGaps:m,segment:g}=this.options,p=Gt(m)?m:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",x=e+s,w=t.length;let L=e>0&&this.getParsed(e-1);for(let _=0;_<w;++_){const y=t[_],v=b?y:{};if(_<e||_>=x){v.skip=!0;continue}const k=this.getParsed(_),M=A(k[u]),C=v[d]=r.getPixelForValue(k[d],_),P=v[u]=o||M?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,k,l):k[u],_);v.skip=isNaN(C)||isNaN(P)||M,v.stop=_>0&&Math.abs(k[d]-L[d])>p,g&&(v.parsed=k,v.raw=c.data[_]),f&&(v.options=h||this.resolveDataElementOptions(_,y.active?"active":n)),b||this.updateElement(y,_,v,n),L=k}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,s=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return s;const o=n[0].size(this.resolveDataElementOptions(0)),r=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}S(_e,"id","line"),S(_e,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),S(_e,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});function gt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class fi{static override(t){Object.assign(fi.prototype,t)}constructor(t){this.options=t||{}}init(){}formats(){return gt()}parse(){return gt()}format(){return gt()}add(){return gt()}diff(){return gt()}startOf(){return gt()}endOf(){return gt()}}var Mr={_date:fi};function Sr(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const l=a._reversePixels?Zn:_t;if(s){if(n._sharedOptions){const c=o[0],h=typeof c.getRange=="function"&&c.getRange(t);if(h){const f=l(o,t,e-h),d=l(o,t,e+h);return{lo:f.lo,hi:d.hi}}}}else return l(o,t,e)}return{lo:0,hi:o.length-1}}function ee(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:f,hi:d}=Sr(o[a],t,r,n);for(let u=f;u<=d;++u){const m=h[u];m.skip||s(m,c,u)}}}function Pr(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Ve(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||ee(i,e,t,function(a,l,c){!n&&!Zt(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function Dr(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:f}=Kn(r,{x:t.x,y:t.y});Cs(f,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return ee(i,e,t,o),n}function Or(i,t,e,s,n,o){let r=[];const a=Pr(e);let l=Number.POSITIVE_INFINITY;function c(h,f,d){const u=h.inRange(t.x,t.y,n);if(s&&!u)return;const m=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(m))&&!u)return;const p=a(t,m);p<l?(r=[{element:h,datasetIndex:f,index:d}],l=p):p===l&&r.push({element:h,datasetIndex:f,index:d})}return ee(i,e,t,c),r}function je(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?Dr(i,t,e,n):Or(i,t,e,s,n,o)}function $i(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return ee(i,e,t,(l,c,h)=>{l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var Lr={evaluateInteractionItems:ee,modes:{index(i,t,e,s){const n=mt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?Ve(i,n,o,s,r):je(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,f=c.data[h];f&&!f.skip&&l.push({element:f,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=mt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?Ve(i,n,o,s,r):je(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=mt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Ve(i,n,o,s,r)},nearest(i,t,e,s){const n=mt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return je(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=mt(t,i);return $i(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=mt(t,i);return $i(i,n,"y",e.intersect,s)}}};const Zs=["left","top","right","bottom"];function Rt(i,t){return i.filter(e=>e.pos===t)}function Ui(i,t){return i.filter(e=>Zs.indexOf(e.pos)===-1&&e.box.axis===t)}function Bt(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function Cr(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function Tr(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!Zs.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function Ir(i,t){const e=Tr(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function Ar(i){const t=Cr(i),e=Bt(t.filter(c=>c.box.fullSize),!0),s=Bt(Rt(t,"left"),!0),n=Bt(Rt(t,"right")),o=Bt(Rt(t,"top"),!0),r=Bt(Rt(t,"bottom")),a=Ui(t,"x"),l=Ui(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Rt(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function Yi(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function Qs(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function Fr(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!O(n)){e.size&&(i[n]-=e.size);const f=s[e.stack]||{size:0,count:1};f.size=Math.max(f.size,e.horizontal?o.height:o.width),e.size=f.size/f.count,i[n]+=e.size}o.getPadding&&Qs(r,o.getPadding());const a=Math.max(0,t.outerWidth-Yi(r,i,"left","right")),l=Math.max(0,t.outerHeight-Yi(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function zr(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function Er(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function Vt(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,Er(a.horizontal,t));const{same:f,other:d}=Fr(t,e,a,s);c|=f&&n.length,h=h||d,l.fullSize||n.push(a)}return c&&Vt(n,t,e,s)||h}function de(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function Xi(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const f=t.w*h,d=c.size||l.height;Z(c.start)&&(r=c.start),l.fullSize?de(l,n.left,r,e.outerWidth-n.right-n.left,d):de(l,t.left+c.placed,r,f,d),c.start=r,c.placed+=f,r=l.bottom}else{const f=t.h*h,d=c.size||l.width;Z(c.start)&&(o=c.start),l.fullSize?de(l,o,n.top,d,e.outerHeight-n.bottom-n.top):de(l,o,t.top+c.placed,d,f),c.start=o,c.placed+=f,o=l.right}}t.x=o,t.y=r}var lt={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=G(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=Ar(i.boxes),l=a.vertical,c=a.horizontal;N(i.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const h=l.reduce((g,p)=>p.box.options&&p.box.options.display===!1?g:g+1,0)||1,f=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),d=Object.assign({},n);Qs(d,G(s));const u=Object.assign({maxPadding:d,w:o,h:r,x:n.left,y:n.top},n),m=Ir(l.concat(c),f);Vt(a.fullSize,u,f,m),Vt(l,u,f,m),Vt(c,u,f,m)&&Vt(l,u,f,m),zr(u),Xi(a.leftAndTop,u,f,m),u.x+=u.w,u.y+=u.h,Xi(a.rightAndBottom,u,f,m),i.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},N(a.chartArea,g=>{const p=g.box;Object.assign(p,i.chartArea),p.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class Js{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Rr extends Js{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const xe="$chartjs",Br={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Ki=i=>i===null||i==="";function Hr(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[xe]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Ki(n)){const o=Ai(i,"width");o!==void 0&&(i.width=o)}if(Ki(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=Ai(i,"height");o!==void 0&&(i.height=o)}return i}const tn=Go?{passive:!0}:!1;function Nr(i,t,e){i.addEventListener(t,e,tn)}function Wr(i,t,e){i.canvas.removeEventListener(t,e,tn)}function Vr(i,t){const e=Br[i.type]||i.type,{x:s,y:n}=mt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function De(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function jr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||De(a.addedNodes,s),r=r&&!De(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function $r(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||De(a.removedNodes,s),r=r&&!De(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const Qt=new Map;let qi=0;function en(){const i=window.devicePixelRatio;i!==qi&&(qi=i,Qt.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function Ur(i,t){Qt.size||window.addEventListener("resize",en),Qt.set(i,t)}function Yr(i){Qt.delete(i),Qt.size||window.removeEventListener("resize",en)}function Xr(i,t,e){const s=i.canvas,n=s&&hi(s);if(!n)return;const o=As((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),Ur(i,o),r}function $e(i,t,e){e&&e.disconnect(),t==="resize"&&Yr(i)}function Kr(i,t,e){const s=i.canvas,n=As(o=>{i.ctx!==null&&e(Vr(o,i))},i);return Nr(s,t,n),n}class qr extends Js{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Hr(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[xe])return!1;const s=e[xe].initial;["height","width"].forEach(o=>{const r=s[o];A(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[xe],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:jr,detach:$r,resize:Xr}[e]||Kr;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:$e,detach:$e,resize:$e}[e]||Wr)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return qo(t,e,s,n)}isAttached(t){const e=hi(t);return!!(e&&e.isConnected)}}function Gr(i){return!Ys()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Rr:qr}class vt{constructor(){S(this,"active",!1)}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Gt(this.x)&&Gt(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}S(vt,"defaults",{}),S(vt,"defaultRoutes");function Zr(i,t){const e=i.options.ticks,s=Qr(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?ta(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return ea(t,c,o,r/n),c;const h=Jr(o,t,n);if(r>0){let f,d;const u=r>1?Math.round((l-a)/(r-1)):null;for(ue(t,c,h,A(u)?0:a-u,a),f=0,d=r-1;f<d;f++)ue(t,c,h,o[f],o[f+1]);return ue(t,c,h,l,A(u)?t.length:l+u),c}return ue(t,c,h),c}function Qr(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function Jr(i,t,e){const s=ia(i),n=t.length/e;if(!s)return Math.max(n,1);const o=Yn(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function ta(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function ea(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function ue(i,t,e,s,n){const o=D(s,0),r=Math.min(D(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function ia(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const sa=i=>i==="left"?"right":i==="right"?"left":i,Gi=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e;function Zi(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function na(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function oa(i,t){N(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Ht(i){return i.drawTicks?i.tickLength:0}function Qi(i,t){if(!i.display)return 0;const e=et(i.font,t),s=G(i.padding);return(F(i.text)?i.text.length:1)*e.lineHeight+s.height}function ra(i,t){return kt(i,{scale:t,type:"scale"})}function aa(i,t,e){return kt(i,{tick:e,index:t,type:"tick"})}function la(i,t,e){let s=Fs(i);return(e&&t!=="right"||!e&&t==="right")&&(s=sa(s)),s}function ca(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let f=0,d,u,m;const g=r-n,p=a-o;if(i.isHorizontal()){if(u=$(s,o,a),O(e)){const b=Object.keys(e)[0],x=e[b];m=h[b].getPixelForValue(x)+g-t}else e==="center"?m=(c.bottom+c.top)/2+g-t:m=Gi(i,e,t);d=a-o}else{if(O(e)){const b=Object.keys(e)[0],x=e[b];u=h[b].getPixelForValue(x)-p+t}else e==="center"?u=(c.left+c.right)/2-p+t:u=Gi(i,e,t);m=$(s,r,n),f=e==="left"?-j:j}return{titleX:u,titleY:m,maxWidth:d,rotation:f}}class wt extends vt{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=U(t,Number.POSITIVE_INFINITY),e=U(e,Number.NEGATIVE_INFINITY),s=U(s,Number.POSITIVE_INFINITY),n=U(n,Number.NEGATIVE_INFINITY),{min:U(t,s),max:U(e,n),minDefined:z(t),maxDefined:z(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:U(e,U(s,e)),max:U(s,U(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){I(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Do(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Zi(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=Zr(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){I(this.options.afterUpdate,[this])}beforeSetDimensions(){I(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){I(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),I(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){I(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=I(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){I(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){I(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=this.ticks.length,n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),f=h.widest.width,d=h.highest.height,u=tt(this.chart.width-f,0,this.maxWidth);a=t.offset?this.maxWidth/s:u/(s-1),f+6>a&&(a=u/(s-(t.offset?.5:1)),l=this.maxHeight-Ht(t.grid)-e.padding-Qi(t.title,this.chart.options.font),c=Math.sqrt(f*f+d*d),r=ni(Math.min(Math.asin(tt((h.highest.height+6)/a,-1,1)),Math.asin(tt(l/c,-1,1))-Math.asin(tt(d/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){I(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){I(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Qi(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=Ht(o)+l):(t.height=this.maxHeight,t.width=Ht(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:f,highest:d}=this._getLabelSizes(),u=s.padding*2,m=at(this.labelRotation),g=Math.cos(m),p=Math.sin(m);if(a){const b=s.mirror?0:p*f.width+g*d.height;t.height=Math.min(this.maxHeight,t.height+b+u)}else{const b=s.mirror?0:g*f.width+p*d.height;t.width=Math.min(this.maxWidth,t.width+b+u)}this._calculatePadding(c,h,p,g)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1);let d=0,u=0;l?c?(d=n*t.width,u=s*e.height):(d=s*t.height,u=n*e.width):o==="start"?u=e.width:o==="end"?d=t.width:o!=="inner"&&(d=t.width/2,u=e.width/2),this.paddingLeft=Math.max((d-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((u-f+r)*this.width/(this.width-f),0)}else{let h=e.height/2,f=t.height/2;o==="start"?(h=0,f=t.height):o==="end"&&(h=e.height,f=0),this.paddingTop=h+r,this.paddingBottom=f+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){I(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)A(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=Zi(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length)}return t}_computeLabelSizes(t,e){const{ctx:s,_longestTextCache:n}=this,o=[],r=[];let a=0,l=0,c,h,f,d,u,m,g,p,b,x,w;for(c=0;c<e;++c){if(d=t[c].label,u=this._resolveTickFontOptions(c),s.font=m=u.string,g=n[m]=n[m]||{data:{},gc:[]},p=u.lineHeight,b=x=0,!A(d)&&!F(d))b=Se(s,g.data,g.gc,b,d),x=p;else if(F(d))for(h=0,f=d.length;h<f;++h)w=d[h],!A(w)&&!F(w)&&(b=Se(s,g.data,g.gc,b,w),x+=p);o.push(b),r.push(x),a=Math.max(b,a),l=Math.max(x,l)}oa(n,e);const L=o.indexOf(a),_=r.indexOf(l),y=v=>({width:o[v]||0,height:r[v]||0});return{first:y(0),last:y(e-1),widest:y(L),highest:y(_),widths:o,heights:r}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return Gn(this._alignToPixels?ut(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=aa(this.getContext(),t,s))}return this.$context||(this.$context=ra(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=at(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),f=this.ticks.length+(l?1:0),d=Ht(o),u=[],m=a.setContext(this.getContext()),g=m.display?m.width:0,p=g/2,b=function(B){return ut(s,B,g)};let x,w,L,_,y,v,k,M,C,P,T,W;if(r==="top")x=b(this.bottom),v=this.bottom-d,M=x-p,P=b(t.top)+p,W=t.bottom;else if(r==="bottom")x=b(this.top),P=t.top,W=b(t.bottom)-p,v=x+p,M=this.top+d;else if(r==="left")x=b(this.right),y=this.right-d,k=x-p,C=b(t.left)+p,T=t.right;else if(r==="right")x=b(this.left),C=t.left,T=b(t.right)-p,y=x+p,k=this.left+d;else if(e==="x"){if(r==="center")x=b((t.top+t.bottom)/2+.5);else if(O(r)){const B=Object.keys(r)[0],K=r[B];x=b(this.chart.scales[B].getPixelForValue(K))}P=t.top,W=t.bottom,v=x+p,M=v+d}else if(e==="y"){if(r==="center")x=b((t.left+t.right)/2);else if(O(r)){const B=Object.keys(r)[0],K=r[B];x=b(this.chart.scales[B].getPixelForValue(K))}y=x-p,k=y-d,C=t.left,T=t.right}const Q=D(n.ticks.maxTicksLimit,f),E=Math.max(1,Math.ceil(f/Q));for(w=0;w<f;w+=E){const B=this.getContext(w),K=o.setContext(B),ie=a.setContext(B),se=K.lineWidth,Mt=K.color,ne=ie.dash||[],St=ie.dashOffset,It=K.tickWidth,At=K.tickColor,Ft=K.tickBorderDash||[],zt=K.tickBorderDashOffset;L=na(this,w,l),L!==void 0&&(_=ut(s,L,se),c?y=k=C=T=_:v=M=P=W=_,u.push({tx1:y,ty1:v,tx2:k,ty2:M,x1:C,y1:P,x2:T,y2:W,width:se,color:Mt,borderDash:ne,borderDashOffset:St,tickWidth:It,tickColor:At,tickBorderDash:Ft,tickBorderDashOffset:zt}))}return this._ticksLength=f,this._borderValue=x,u}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:f}=o,d=Ht(s.grid),u=d+h,m=f?-h:u,g=-at(this.labelRotation),p=[];let b,x,w,L,_,y,v,k,M,C,P,T,W="middle";if(n==="top")y=this.bottom-m,v=this._getXAxisLabelAlignment();else if(n==="bottom")y=this.top+m,v=this._getXAxisLabelAlignment();else if(n==="left"){const E=this._getYAxisLabelAlignment(d);v=E.textAlign,_=E.x}else if(n==="right"){const E=this._getYAxisLabelAlignment(d);v=E.textAlign,_=E.x}else if(e==="x"){if(n==="center")y=(t.top+t.bottom)/2+u;else if(O(n)){const E=Object.keys(n)[0],B=n[E];y=this.chart.scales[E].getPixelForValue(B)+u}v=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")_=(t.left+t.right)/2-u;else if(O(n)){const E=Object.keys(n)[0],B=n[E];_=this.chart.scales[E].getPixelForValue(B)}v=this._getYAxisLabelAlignment(d).textAlign}e==="y"&&(l==="start"?W="top":l==="end"&&(W="bottom"));const Q=this._getLabelSizes();for(b=0,x=a.length;b<x;++b){w=a[b],L=w.label;const E=o.setContext(this.getContext(b));k=this.getPixelForTick(b)+o.labelOffset,M=this._resolveTickFontOptions(b),C=M.lineHeight,P=F(L)?L.length:1;const B=P/2,K=E.color,ie=E.textStrokeColor,se=E.textStrokeWidth;let Mt=v;r?(_=k,v==="inner"&&(b===x-1?Mt=this.options.reverse?"left":"right":b===0?Mt=this.options.reverse?"right":"left":Mt="center"),n==="top"?c==="near"||g!==0?T=-P*C+C/2:c==="center"?T=-Q.highest.height/2-B*C+C:T=-Q.highest.height+C/2:c==="near"||g!==0?T=C/2:c==="center"?T=Q.highest.height/2-B*C:T=Q.highest.height-P*C,f&&(T*=-1),g!==0&&!E.showLabelBackdrop&&(_+=C/2*Math.sin(g))):(y=k,T=(1-P)*C/2);let ne;if(E.showLabelBackdrop){const St=G(E.backdropPadding),It=Q.heights[b],At=Q.widths[b];let Ft=T-St.top,zt=0-St.left;switch(W){case"middle":Ft-=It/2;break;case"bottom":Ft-=It;break}switch(v){case"center":zt-=At/2;break;case"right":zt-=At;break}ne={left:zt,top:Ft,width:At+St.width,height:It+St.height,color:E.backdropColor}}p.push({label:L,font:M,textOffset:T,options:{rotation:g,color:K,strokeColor:ie,strokeWidth:se,textAlign:Mt,textBaseline:W,translation:[_,y],backdrop:ne}})}return p}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-at(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,f,d;this.isHorizontal()?(c=ut(t,this.left,r)-r/2,h=ut(t,this.right,a)+a/2,f=d=l):(f=ut(t,this.top,r)-r/2,d=ut(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,f),e.lineTo(h,d),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&Ie(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Lt(s,c,0,h,l,a)}n&&Ae(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=et(s.font),r=G(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||O(e)?(l+=r.bottom,F(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:f,rotation:d}=ca(this,l,e,a);Lt(t,s.text,0,0,o,{color:s.color,maxWidth:f,rotation:d,textAlign:la(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=D(t.grid&&t.grid.z,-1),n=D(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==wt.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return et(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class ge{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;da(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,ha(t,r,s),this.override&&R.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in R[n]&&(delete R[n][s],this.override&&delete yt[s])}}function ha(i,t,e){const s=qt(Object.create(null),[e?R.get(e):{},R.get(t),i.defaults]);R.set(t,s),i.defaultRoutes&&fa(t,i.defaultRoutes),i.descriptors&&R.describe(t,i.descriptors)}function fa(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");R.route(o,n,l,a)})}function da(i){return"id"in i&&"defaults"in i}class ua{constructor(){this.controllers=new ge(Xt,"datasets",!0),this.elements=new ge(vt,"elements"),this.plugins=new ge(Object,"plugins"),this.scales=new ge(wt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):N(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=si(t);I(s["before"+n],[],s),e[t](s),I(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var J=new ua;class ga{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(I(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){A(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=D(s.options&&s.options.plugins,{}),o=pa(s);return n===!1&&!e?[]:ba(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function pa(i){const t={},e=[],s=Object.keys(J.plugins.items);for(let o=0;o<s.length;o++)e.push(J.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function ma(i,t){return!t&&i===!1?null:i===!0?{}:i}function ba(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=ma(s[l],n);c!==null&&o.push({plugin:a,options:_a(i.config,{plugin:a,local:e[l]},c,r)})}return o}function _a(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Ke(i,t){const e=R.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function xa(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function ya(i,t){return i===t?"_index_":"_value_"}function va(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function Oe(i,t){if(i==="x"||i==="y"||i==="r"||(i=t.axis||va(t.position)||i.length>1&&Oe(i[0].toLowerCase(),t),i))return i;throw new Error(`Cannot determine type of '${name}' axis. Please provide 'axis' or 'position' option.`)}function ka(i,t){const e=yt[i.type]||{scales:{}},s=t.scales||{},n=Ke(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!O(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=Oe(r,a),c=ya(l,n),h=e.scales||{};o[r]=jt(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||Ke(a,t),h=(yt[a]||{}).scales||{};Object.keys(h).forEach(f=>{const d=xa(f,l),u=r[d+"AxisID"]||d;o[u]=o[u]||Object.create(null),jt(o[u],[{axis:d},s[u],h[f]])})}),Object.keys(o).forEach(r=>{const a=o[r];jt(a,[R.scales[a.type],R.scale])}),o}function sn(i){const t=i.options||(i.options={});t.plugins=D(t.plugins,{}),t.scales=ka(i,t)}function nn(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function wa(i){return i=i||{},i.data=nn(i.data),sn(i),i}const Ji=new Map,on=new Set;function pe(i,t){let e=Ji.get(i);return e||(e=t(),Ji.set(i,e),on.add(e)),e}const Nt=(i,t,e)=>{const s=we(t,e);s!==void 0&&i.add(s)};class Ma{constructor(t){this._config=wa(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=nn(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),sn(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return pe(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return pe(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return pe(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return pe(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(f=>Nt(l,t,f))),h.forEach(f=>Nt(l,n,f)),h.forEach(f=>Nt(l,yt[o]||{},f)),h.forEach(f=>Nt(l,R,f)),h.forEach(f=>Nt(l,Xe,f))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),on.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,yt[e]||{},R.datasets[e]||{},{type:e},R,Xe]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=ts(this._resolverCache,t,n);let l=r;if(Pa(r,e)){o.$shared=!1,s=ft(s)?s():s;const c=this.createResolver(t,s,a);l=Ct(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=ts(this._resolverCache,t,s);return O(e)?Ct(o,e,void 0,n):o}}function ts(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:ai(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const Sa=i=>O(i)&&Object.getOwnPropertyNames(i).reduce((t,e)=>t||ft(i[e]),!1);function Pa(i,t){const{isScriptable:e,isIndexable:s}=Ws(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(ft(a)||Sa(a))||r&&F(a))return!0}return!1}var Da="4.2.0";const Oa=["top","bottom","left","right","chartArea"];function es(i,t){return i==="top"||i==="bottom"||Oa.indexOf(i)===-1&&t==="x"}function is(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function ss(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),I(e&&e.onComplete,[i],t)}function La(i){const t=i.chart,e=t.options.animation;I(e&&e.onProgress,[i],t)}function rn(i){return Ys()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const ye={},ns=i=>{const t=rn(i);return Object.values(ye).filter(e=>e.canvas===t).pop()};function Ca(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function Ta(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}function Ia(i){const{xScale:t,yScale:e}=i;if(t&&e)return{left:t.left,right:t.right,top:e.top,bottom:e.bottom}}class nt{static register(...t){J.add(...t),os()}static unregister(...t){J.remove(...t),os()}constructor(t,e){const s=this.config=new Ma(e),n=rn(t),o=ns(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Gr(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=Rn(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new ga,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=eo(f=>this.update(f),r.resizeDelay||0),this._dataChanges=[],ye[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}it.listen(this,"complete",ss),it.listen(this,"progress",La),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return A(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return J}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Ii(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Li(this.canvas,this.ctx),this}stop(){return it.stop(this),this}resize(t,e){it.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Ii(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),I(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};N(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=Oe(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),N(o,r=>{const a=r.options,l=a.id,c=Oe(l,a),h=D(a.type,r.dtype);(a.position===void 0||es(a.position,c)!==es(r.dposition))&&(a.position=r.dposition),n[l]=!0;let f=null;if(l in s&&s[l].type===h)f=s[l];else{const d=J.getScale(h);f=new d({id:l,type:h,ctx:this.ctx,chart:this}),s[f.id]=f}f.init(a,t)}),N(n,(r,a)=>{r||delete s[a]}),N(s,r=>{lt.configure(this,r,r.options),lt.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(is("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||Ke(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=J.getController(a),{datasetElementType:c,dataElementType:h}=R.datasets[a];Object.assign(l,{dataElementType:J.getElement(h),datasetElementType:c&&J.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){N(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:f}=this.getDatasetMeta(c),d=!n&&o.indexOf(f)===-1;f.buildOrUpdateElements(d),r=Math.max(+f.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||N(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(is("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){N(this.scales,t=>{lt.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!xi(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;Ca(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!xi(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;lt.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],N(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,ft(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(it.has(this)?this.attached&&!it.running(this)&&it.start(this):(this.draw(),ss({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resize(s,n),this._resizeBeforeDraw=null}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s=t._clip,n=!s.disabled,o=Ia(t)||this.chartArea,r={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(n&&Ie(e,{left:s.left===!1?0:o.left-s.left,right:s.right===!1?this.width:o.right+s.right,top:s.top===!1?0:o.top-s.top,bottom:s.bottom===!1?this.height:o.bottom+s.bottom}),t.controller.draw(),n&&Ae(e),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(t){return Zt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=Lr.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=kt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);Z(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),it.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Li(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ye[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};N(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){N(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},N(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!bi(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(f=>h.datasetIndex===f.datasetIndex&&h.index===f.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=jn(t),c=Ta(t,this._lastEvent,s,l);s&&(this._lastEvent=null,I(o.onHover,[t,a,this],this),l&&I(o.onClick,[t,a,this],this));const h=!bi(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}}S(nt,"defaults",R),S(nt,"instances",ye),S(nt,"overrides",yt),S(nt,"registry",J),S(nt,"version",Da),S(nt,"getChart",ns);function os(){return N(nt.instances,i=>i._plugins.invalidate())}function an(i,t,e=t){i.lineCap=D(e.borderCapStyle,t.borderCapStyle),i.setLineDash(D(e.borderDash,t.borderDash)),i.lineDashOffset=D(e.borderDashOffset,t.borderDashOffset),i.lineJoin=D(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=D(e.borderWidth,t.borderWidth),i.strokeStyle=D(e.borderColor,t.borderColor)}function Aa(i,t,e){i.lineTo(e.x,e.y)}function Fa(i){return i.stepped?bo:i.tension||i.cubicInterpolationMode==="monotone"?_o:Aa}function ln(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:r,end:a}=t,l=Math.max(n,r),c=Math.min(o,a),h=n<r&&o<r||n>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function za(i,t,e,s){const{points:n,options:o}=t,{count:r,start:a,loop:l,ilen:c}=ln(n,e,s),h=Fa(o);let{move:f=!0,reverse:d}=s||{},u,m,g;for(u=0;u<=c;++u)m=n[(a+(d?c-u:u))%r],!m.skip&&(f?(i.moveTo(m.x,m.y),f=!1):h(i,g,m,d,o.stepped),g=m);return l&&(m=n[(a+(d?c:0))%r],h(i,g,m,d,o.stepped)),!!l}function Ea(i,t,e,s){const n=t.points,{count:o,start:r,ilen:a}=ln(n,e,s),{move:l=!0,reverse:c}=s||{};let h=0,f=0,d,u,m,g,p,b;const x=L=>(r+(c?a-L:L))%o,w=()=>{g!==p&&(i.lineTo(h,p),i.lineTo(h,g),i.lineTo(h,b))};for(l&&(u=n[x(0)],i.moveTo(u.x,u.y)),d=0;d<=a;++d){if(u=n[x(d)],u.skip)continue;const L=u.x,_=u.y,y=L|0;y===m?(_<g?g=_:_>p&&(p=_),h=(f*h+L)/++f):(w(),i.lineTo(L,_),m=y,f=0,g=p=_),b=_}w()}function qe(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Ea:za}function Ra(i){return i.stepped?Zo:i.tension||i.cubicInterpolationMode==="monotone"?Qo:bt}function Ba(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),an(i,t.options),i.stroke(n)}function Ha(i,t,e,s){const{segments:n,options:o}=t,r=qe(t);for(const a of n)an(i,o,a.style),i.beginPath(),r(i,t,a,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const Na=typeof Path2D=="function";function Wa(i,t,e,s){Na&&!t.options.segment?Ba(i,t,e,s):Ha(i,t,e,s)}class ct extends vt{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;jo(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=rr(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,r=qs(this,{property:e,start:n,end:n});if(!r.length)return;const a=[],l=Ra(s);let c,h;for(c=0,h=r.length;c<h;++c){const{start:f,end:d}=r[c],u=o[f],m=o[d];if(u===m){a.push(u);continue}const g=Math.abs((n-u[e])/(m[e]-u[e])),p=l(u,m,g,s.stepped);p[e]=t[e],a.push(p)}return a.length===1?a[0]:a}pathSegment(t,e,s){return qe(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=qe(this);let r=this._loop;e=e||0,s=s||this.points.length-e;for(const a of n)r&=o(t,this,a,{start:e,end:e+s-1});return!!r}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Wa(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}S(ct,"id","line"),S(ct,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),S(ct,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),S(ct,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function rs(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class ve extends vt{constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,s){const n=this.options,{x:o,y:r}=this.getProps(["x","y"],s);return Math.pow(t-o,2)+Math.pow(e-r,2)<Math.pow(n.hitRadius+n.radius,2)}inXRange(t,e){return rs(this,t,"x",e)}inYRange(t,e){return rs(this,t,"y",e)}getCenterPoint(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}size(t){t=t||this.options||{};let e=t.radius||0;e=Math.max(e,e&&t.hoverRadius||0);const s=e&&t.borderWidth||0;return(e+s)*2}draw(t,e){const s=this.options;this.skip||s.radius<.1||!Zt(this,e,this.size(s)/2)||(t.strokeStyle=s.borderColor,t.lineWidth=s.borderWidth,t.fillStyle=s.backgroundColor,mo(t,s,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}S(ve,"id","point"),S(ve,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),S(ve,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Va(i,t,e){const s=i.segments,n=i.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=di(l,c,n);const h=Ge(e,n[l],n[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:n[l],end:n[c]});continue}const f=qs(t,h);for(const d of f){const u=Ge(e,o[d.start],o[d.end],d.loop),m=Ks(a,n,u);for(const g of m)r.push({source:g,target:d,start:{[e]:as(h,u,"start",Math.max)},end:{[e]:as(h,u,"end",Math.min)}})}}return r}function Ge(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=Y(n),o=Y(o)),{property:i,start:n,end:o}}function ja(i,t){const{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=di(r,a,n);const l=n[r],c=n[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function di(i,t,e){for(;t>i;t--){const s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function as(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function cn(i,t){let e=[],s=!1;return F(i)?(s=!0,e=i):e=ja(i,t),e.length?new ct({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function ls(i){return i&&i.fill!==!1}function $a(i,t,e){let n=i[t].fill;const o=[t];let r;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!z(n))return n;if(r=i[n],!r)return!1;if(r.visible)return n;o.push(n),n=r.fill}return!1}function Ua(i,t,e){const s=qa(i);if(O(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return z(n)&&Math.floor(n)===n?Ya(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function Ya(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function Xa(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:O(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function Ka(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:O(i)?s=i.value:s=t.getBaseValue(),s}function qa(i){const t=i.options,e=t.fill;let s=D(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function Ga(i){const{scale:t,index:e,line:s}=i,n=[],o=s.segments,r=s.points,a=Za(t,e);a.push(cn({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)Qa(n,r[h],a)}return new ct({points:n,options:{}})}function Za(i,t){const e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function Qa(i,t,e){const s=[];for(let n=0;n<e.length;n++){const o=e[n],{first:r,last:a,point:l}=Ja(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(i.push(l),!a)break}}i.push(...s)}function Ja(i,t,e){const s=i.interpolate(t,e);if(!s)return{};const n=s[e],o=i.segments,r=i.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],f=r[h.start][e],d=r[h.end][e];if(Dt(n,f,d)){a=n===f,l=n===d;break}}return{first:a,last:l,point:s}}class hn{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){const{x:n,y:o,radius:r}=this;return e=e||{start:0,end:X},t.arc(n,o,r,e.end,e.start,!0),!s.bounds}interpolate(t){const{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function tl(i){const{chart:t,fill:e,line:s}=i;if(z(e))return el(t,e);if(e==="stack")return Ga(i);if(e==="shape")return!0;const n=il(i);return n instanceof hn?n:cn(n,s)}function el(i,t){const e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function il(i){return(i.scale||{}).getPointPositionForValue?nl(i):sl(i)}function sl(i){const{scale:t={},fill:e}=i,s=Xa(e,t);if(z(s)){const n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function nl(i){const{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,r=Ka(e,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new hn({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<n;++l)a.push(t.getPointPositionForValue(l,r));return a}function Ue(i,t,e){const s=tl(t),{line:n,scale:o,axis:r}=t,a=n.options,l=a.fill,c=a.backgroundColor,{above:h=c,below:f=c}=l||{};s&&n.points.length&&(Ie(i,e),ol(i,{line:n,target:s,above:h,below:f,area:e,scale:o,axis:r}),Ae(i))}function ol(i,t){const{line:e,target:s,above:n,below:o,area:r,scale:a}=t,l=e._loop?"angle":t.axis;i.save(),l==="x"&&o!==n&&(cs(i,s,r.top),hs(i,{line:e,target:s,color:n,scale:a,property:l}),i.restore(),i.save(),cs(i,s,r.bottom)),hs(i,{line:e,target:s,color:o,scale:a,property:l}),i.restore()}function cs(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],f=n[di(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(f.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function hs(i,t){const{line:e,target:s,property:n,color:o,scale:r}=t,a=Va(e,s,n);for(const{source:l,target:c,start:h,end:f}of a){const{style:{backgroundColor:d=o}={}}=l,u=s!==!0;i.save(),i.fillStyle=d,rl(i,r,u&&Ge(n,h,f)),i.beginPath();const m=!!e.pathSegment(i,l);let g;if(u){m?i.closePath():fs(i,s,f,n);const p=!!s.pathSegment(i,c,{move:m,reverse:!0});g=m&&p,g||fs(i,s,h,n)}i.closePath(),i.fill(g?"evenodd":"nonzero"),i.restore()}}function rl(i,t,e){const{top:s,bottom:n}=t.chart.chartArea,{property:o,start:r,end:a}=e||{};o==="x"&&(i.beginPath(),i.rect(r,s,a-r,n-s),i.clip())}function fs(i,t,e,s){const n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var al={id:"filler",afterDatasetsUpdate(i,t,e){const s=(i.data.datasets||[]).length,n=[];let o,r,a,l;for(r=0;r<s;++r)o=i.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof ct&&(l={visible:i.isDatasetVisible(r),index:r,fill:Ua(a,r,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,n.push(l);for(r=0;r<s;++r)l=n[r],!(!l||l.fill===!1)&&(l.fill=$a(n,r,e.propagate))},beforeDraw(i,t,e){const s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let r=n.length-1;r>=0;--r){const a=n[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&Ue(i.ctx,a,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;ls(o)&&Ue(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){const s=t.meta.$filler;!ls(s)||e.drawTime!=="beforeDatasetDraw"||Ue(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const ds=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},ll=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class us extends vt{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=I(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=et(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=ds(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let f=t;o.textAlign="left",o.textBaseline="middle";let d=-1,u=-h;return this.legendItems.forEach((m,g)=>{const p=s+e/2+o.measureText(m.text).width;(g===0||c[c.length-1]+p+2*a>r)&&(f+=h,c[c.length-(g>0?0:1)]=0,u+=h,d++),l[g]={left:0,top:u,row:d,width:p,height:n},c[c.length-1]+=p+a}),f}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let f=a,d=0,u=0,m=0,g=0;return this.legendItems.forEach((p,b)=>{const{itemWidth:x,itemHeight:w}=cl(s,e,o,p,n);b>0&&u+w+2*a>h&&(f+=d+a,c.push({width:d,height:u}),m+=d+a,g++,d=u=0),l[b]={left:m,top:u,col:g,width:x,height:w},d=Math.max(d,x),u+=w+a}),f+=d,c.push({width:d,height:u}),f}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=He(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=$(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=$(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=$(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=$(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Ie(t,this),this._draw(),Ae(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=R.color,l=He(t.rtl,this.left,this.width),c=et(r.font),{padding:h}=r,f=c.size,d=f/2;let u;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:m,boxHeight:g,itemHeight:p}=ds(r,f),b=function(y,v,k){if(isNaN(m)||m<=0||isNaN(g)||g<0)return;n.save();const M=D(k.lineWidth,1);if(n.fillStyle=D(k.fillStyle,a),n.lineCap=D(k.lineCap,"butt"),n.lineDashOffset=D(k.lineDashOffset,0),n.lineJoin=D(k.lineJoin,"miter"),n.lineWidth=M,n.strokeStyle=D(k.strokeStyle,a),n.setLineDash(D(k.lineDash,[])),r.usePointStyle){const C={radius:g*Math.SQRT2/2,pointStyle:k.pointStyle,rotation:k.rotation,borderWidth:M},P=l.xPlus(y,m/2),T=v+d;Rs(n,C,P,T,r.pointStyleWidth&&m)}else{const C=v+Math.max((f-g)/2,0),P=l.leftForLtr(y,m),T=Ns(k.borderRadius);n.beginPath(),Object.values(T).some(W=>W!==0)?Bs(n,{x:P,y:C,w:m,h:g,radius:T}):n.rect(P,C,m,g),n.fill(),M!==0&&n.stroke()}n.restore()},x=function(y,v,k){Lt(n,k.text,y,v+p/2,c,{strikethrough:k.hidden,textAlign:l.textAlign(k.textAlign)})},w=this.isHorizontal(),L=this._computeTitleHeight();w?u={x:$(o,this.left+h,this.right-s[0]),y:this.top+h+L,line:0}:u={x:this.left+h,y:$(o,this.top+L+h,this.bottom-e[0].height),line:0},er(this.ctx,t.textDirection);const _=p+h;this.legendItems.forEach((y,v)=>{n.strokeStyle=y.fontColor,n.fillStyle=y.fontColor;const k=n.measureText(y.text).width,M=l.textAlign(y.textAlign||(y.textAlign=r.textAlign)),C=m+d+k;let P=u.x,T=u.y;l.setWidth(this.width),w?v>0&&P+C+h>this.right&&(T=u.y+=_,u.line++,P=u.x=$(o,this.left+h,this.right-s[u.line])):v>0&&T+_>this.bottom&&(P=u.x=P+e[u.line].width+h,u.line++,T=u.y=$(o,this.top+L+h,this.bottom-e[u.line].height));const W=l.x(P);if(b(W,T,y),P=io(M,P+m+d,w?P+C:this.right,t.rtl),x(l.x(P),T,y),w)u.x+=C+h;else if(typeof y.text!="string"){const Q=c.lineHeight;u.y+=fn(y,Q)}else u.y+=_}),ir(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=et(e.font),n=G(e.padding);if(!e.display)return;const o=He(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,f=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),h=this.top+c,f=$(t.align,f,this.right-d);else{const m=this.columnSizes.reduce((g,p)=>Math.max(g,p.height),0);h=c+$(t.align,this.top,this.bottom-m-t.labels.padding-this._computeTitleHeight())}const u=$(a,f,f+d);r.textAlign=o.textAlign(Fs(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,Lt(r,e.text,u,h,s)}_computeTitleHeight(){const t=this.options.title,e=et(t.font),s=G(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(Dt(t,this.left,this.right)&&Dt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],Dt(t,n.left,n.left+n.width)&&Dt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!dl(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=ll(n,s);n&&!o&&I(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&I(e.onHover,[t,s,this],this)}else s&&I(e.onClick,[t,s,this],this)}}function cl(i,t,e,s,n){const o=hl(s,i,t,e),r=fl(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function hl(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function fl(i,t,e){let s=i;return typeof t.text!="string"&&(s=fn(t,e)),s}function fn(i,t){const e=i.text?i.text.length+.5:0;return t*e}function dl(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var ul={id:"legend",_element:us,start(i,t,e){const s=i.legend=new us({ctx:i.ctx,options:e,chart:i});lt.configure(i,s,e),lt.addBox(i,s)},stop(i){lt.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;lt.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=G(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};const gl=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function pl(i,t,e,s){const n=i.indexOf(t);if(n===-1)return gl(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const ml=(i,t)=>i===null?null:tt(Math.round(i),0,t);function gs(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Ze extends wt{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(A(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:pl(s,t,D(e,t),this._addedLabels),ml(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return gs.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}S(Ze,"id","category"),S(Ze,"defaults",{ticks:{callback:gs}});function bl(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:f,includeBounds:d}=i,u=o||1,m=h-1,{min:g,max:p}=t,b=!A(r),x=!A(a),w=!A(c),L=(p-g)/(f+1);let _=vi((p-g)/m/u)*u,y,v,k,M;if(_<1e-14&&!b&&!x)return[{value:g},{value:p}];M=Math.ceil(p/_)-Math.floor(g/_),M>m&&(_=vi(M*_/m/u)*u),A(l)||(y=Math.pow(10,l),_=Math.ceil(_*y)/y),n==="ticks"?(v=Math.floor(g/_)*_,k=Math.ceil(p/_)*_):(v=g,k=p),b&&x&&o&&Xn((a-r)/o,_/1e3)?(M=Math.round(Math.min((a-r)/_,h)),_=(a-r)/M,v=r,k=a):w?(v=b?r:v,k=x?a:k,M=c-1,_=(k-v)/M):(M=(k-v)/_,$t(M,Math.round(M),_/1e3)?M=Math.round(M):M=Math.ceil(M));const C=Math.max(ki(_),ki(v));y=Math.pow(10,A(l)?C:l),v=Math.round(v*y)/y,k=Math.round(k*y)/y;let P=0;for(b&&(d&&v!==r?(e.push({value:r}),v<r&&P++,$t(Math.round((v+P*_)*y)/y,r,ps(r,L,i))&&P++):v<r&&P++);P<M;++P)e.push({value:Math.round((v+P*_)*y)/y});return x&&d&&k!==a?e.length&&$t(e[e.length-1].value,a,ps(a,L,i))?e[e.length-1].value=a:e.push({value:a}):(!x||k===a)&&e.push({value:k}),e}function ps(i,t,{horizontal:e,minRotation:s}){const n=at(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class Le extends wt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return A(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=Ot(n),c=Ot(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=bl(n,o);return t.bounds==="ticks"&&Ls(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return ri(t,this.chart.options.locale,this.options.ticks.format)}}class Qe extends Le{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=z(t)?t:0,this.max=z(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=at(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}S(Qe,"id","linear"),S(Qe,"defaults",{ticks:{callback:Te.formatters.numeric}});const Jt=i=>Math.floor(rt(i)),pt=(i,t)=>Math.pow(10,Jt(i)+t);function ms(i){return i/Math.pow(10,Jt(i))===1}function bs(i,t,e){const s=Math.pow(10,e),n=Math.floor(i/s);return Math.ceil(t/s)-n}function _l(i,t){const e=t-i;let s=Jt(e);for(;bs(i,t,s)>10;)s++;for(;bs(i,t,s)<10;)s--;return Math.min(s,Jt(i))}function xl(i,{min:t,max:e}){t=U(i.min,t);const s=[],n=Jt(t);let o=_l(t,e),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((t-l)*r)/r,h=Math.floor((t-l)/a/10)*a*10;let f=Math.floor((c-h)/Math.pow(10,o)),d=U(i.min,Math.round((l+h+f*Math.pow(10,o))*r)/r);for(;d<e;)s.push({value:d,major:ms(d),significand:f}),f>=10?f=f<15?15:20:f++,f>=20&&(o++,f=2,r=o>=0?1:r),d=Math.round((l+h+f*Math.pow(10,o))*r)/r;const u=U(i.max,d);return s.push({value:u,major:ms(u),significand:f}),s}class _s extends wt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const s=Le.prototype.parse.apply(this,[t,e]);if(s===0){this._zero=!0;return}return z(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=z(t)?Math.max(0,t):null,this.max=z(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!z(this._userMin)&&(this.min=t===pt(this.min,0)?pt(this.min,-1):pt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let s=this.min,n=this.max;const o=a=>s=t?s:a,r=a=>n=e?n:a;s===n&&(s<=0?(o(1),r(10)):(o(pt(s,-1)),r(pt(n,1)))),s<=0&&o(pt(n,-1)),n<=0&&r(pt(s,1)),this.min=s,this.max=n}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},s=xl(e,this);return t.bounds==="ticks"&&Ls(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":ri(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=rt(t),this._valueRange=rt(this.max)-rt(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(rt(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}S(_s,"id","logarithmic"),S(_s,"defaults",{ticks:{callback:Te.formatters.logarithmic,major:{enabled:!0}}});function Je(i){const t=i.ticks;if(t.display&&i.display){const e=G(t.backdropPadding);return D(t.font&&t.font.size,R.font.size)+e.height}return 0}function yl(i,t,e){return e=F(e)?e:[e],{w:po(i,t.string,e),h:e.length*t.lineHeight}}function xs(i,t,e,s,n){return i===s||i===n?{start:t-e/2,end:t+e/2}:i<s||i>n?{start:t-e,end:t}:{start:t,end:t+e}}function vl(i){const t={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},e=Object.assign({},t),s=[],n=[],o=i._pointLabels.length,r=i.options.pointLabels,a=r.centerPointLabels?H/o:0;for(let l=0;l<o;l++){const c=r.setContext(i.getPointLabelContext(l));n[l]=c.padding;const h=i.getPointPosition(l,i.drawingArea+n[l],a),f=et(c.font),d=yl(i.ctx,f,i._pointLabels[l]);s[l]=d;const u=Y(i.getIndexAngle(l)+a),m=Math.round(ni(u)),g=xs(m,h.x,d.w,0,180),p=xs(m,h.y,d.h,90,270);kl(e,t,u,g,p)}i.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),i._pointLabelItems=wl(i,s,n)}function kl(i,t,e,s,n){const o=Math.abs(Math.sin(e)),r=Math.abs(Math.cos(e));let a=0,l=0;s.start<t.l?(a=(t.l-s.start)/o,i.l=Math.min(i.l,t.l-a)):s.end>t.r&&(a=(s.end-t.r)/o,i.r=Math.max(i.r,t.r+a)),n.start<t.t?(l=(t.t-n.start)/r,i.t=Math.min(i.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/r,i.b=Math.max(i.b,t.b+l))}function wl(i,t,e){const s=[],n=i._pointLabels.length,o=i.options,r=Je(o)/2,a=i.drawingArea,l=o.pointLabels.centerPointLabels?H/n:0;for(let c=0;c<n;c++){const h=i.getPointPosition(c,a+r+e[c],l),f=Math.round(ni(Y(h.angle+j))),d=t[c],u=Pl(h.y,d.h,f),m=Ml(f),g=Sl(h.x,d.w,m);s.push({x:h.x,y:u,textAlign:m,left:g,top:u,right:g+d.w,bottom:u+d.h})}return s}function Ml(i){return i===0||i===180?"center":i<180?"left":"right"}function Sl(i,t,e){return e==="right"?i-=t:e==="center"&&(i-=t/2),i}function Pl(i,t,e){return e===90||e===270?i-=t/2:(e>270||e<90)&&(i-=t),i}function Dl(i,t){const{ctx:e,options:{pointLabels:s}}=i;for(let n=t-1;n>=0;n--){const o=s.setContext(i.getPointLabelContext(n)),r=et(o.font),{x:a,y:l,textAlign:c,left:h,top:f,right:d,bottom:u}=i._pointLabelItems[n],{backdropColor:m}=o;if(!A(m)){const g=Ns(o.borderRadius),p=G(o.backdropPadding);e.fillStyle=m;const b=h-p.left,x=f-p.top,w=d-h+p.width,L=u-f+p.height;Object.values(g).some(_=>_!==0)?(e.beginPath(),Bs(e,{x:b,y:x,w,h:L,radius:g}),e.fill()):e.fillRect(b,x,w,L)}Lt(e,i._pointLabels[n],a,l+r.lineHeight/2,r,{color:o.color,textAlign:c,textBaseline:"middle"})}}function dn(i,t,e,s){const{ctx:n}=i;if(e)n.arc(i.xCenter,i.yCenter,t,0,X);else{let o=i.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let r=1;r<s;r++)o=i.getPointPosition(r,t),n.lineTo(o.x,o.y)}}function Ol(i,t,e,s,n){const o=i.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!s||!a||!l||e<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(n.dash),o.lineDashOffset=n.dashOffset,o.beginPath(),dn(i,e,r,s),o.closePath(),o.stroke(),o.restore())}function Ll(i,t,e){return kt(i,{label:e,index:t,type:"pointLabel"})}class me extends Le{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=G(Je(this.options)/2),e=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=z(t)&&!isNaN(t)?t:0,this.max=z(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Je(this.options))}generateTickLabels(t){Le.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,s)=>{const n=I(this.options.pointLabels.callback,[e,s],this);return n||n===0?n:""}).filter((e,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?vl(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,n))}getIndexAngle(t){const e=X/(this._pointLabels.length||1),s=this.options.startAngle||0;return Y(t*e+at(s))}getDistanceFromCenterForValue(t){if(A(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(A(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const s=e[t];return Ll(this.getContext(),t,s)}}getPointPosition(t,e,s=0){const n=this.getIndexAngle(t)-j+s;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:s,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),dn(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:s,grid:n,border:o}=e,r=this._pointLabels.length;let a,l,c;if(e.pointLabels.display&&Dl(this,r),n.display&&this.ticks.forEach((h,f)=>{if(f!==0){l=this.getDistanceFromCenterForValue(h.value);const d=this.getContext(f),u=n.setContext(d),m=o.setContext(d);Ol(this,u,l,r,m)}}),s.display){for(t.save(),a=r-1;a>=0;a--){const h=s.setContext(this.getPointLabelContext(a)),{color:f,lineWidth:d}=h;!d||!f||(t.lineWidth=d,t.strokeStyle=f,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.ticks.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,s=e.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&!e.reverse)return;const c=s.setContext(this.getContext(l)),h=et(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const f=G(c.backdropPadding);t.fillRect(-r/2-f.left,-o-h.size/2-f.top,r+f.width,h.size+f.height)}Lt(t,a.label,0,-o,h,{color:c.color})}),t.restore()}drawTitle(){}}S(me,"id","radialLinear"),S(me,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Te.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),S(me,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),S(me,"descriptors",{angleLines:{_fallback:"grid"}});const ze={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},V=Object.keys(ze);function Cl(i,t){return i-t}function ys(i,t){if(A(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),z(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(Gt(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function vs(i,t,e,s){const n=V.length;for(let o=V.indexOf(i);o<n-1;++o){const r=ze[V[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return V[o]}return V[n-1]}function Tl(i,t,e,s,n){for(let o=V.length-1;o>=V.indexOf(e);o--){const r=V[o];if(ze[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return V[e?V.indexOf(e):0]}function Il(i){for(let t=V.indexOf(i)+1,e=V.length;t<e;++t)if(ze[V[t]].common)return V[t]}function ks(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=oi(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Al(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function ws(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:Al(i,s,n,e)}class Ce extends wt{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new Mr._date(t.adapters.date);n.init(e),jt(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:ys(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=z(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=z(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=Qn(n,o,r);return this._unit=e.unit||(s.autoSkip?vs(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Tl(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:Il(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),ws(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=tt(e,0,r),s=tt(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||vs(o.minUnit,e,s,this._getLabelCapacity(e)),a=D(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=Gt(l)||l===!0,h={};let f=e,d,u;if(c&&(f=+t.startOf(f,"isoWeek",l)),f=+t.startOf(f,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const m=n.ticks.source==="data"&&this.getDataTimestamps();for(d=f,u=0;d<s;d=+t.add(d,a,r),u++)ks(h,d,m);return(d===s||n.bounds==="ticks"||u===1)&&ks(h,d,m),Object.keys(h).sort((g,p)=>g-p).map(g=>+g)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return I(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],f=c&&a[c],d=s[e],u=c&&f&&d&&d.major;return this._adapter.format(t,n||(u?f:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=at(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,ws(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(ys(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return to(t.sort(Cl))}}S(Ce,"id","time"),S(Ce,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function be(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=_t(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=_t(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class Ms extends Ce{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=be(e,this.min),this._tableRange=be(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(be(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return be(this._table,s*this._tableRange+this._minPos,!0)}}S(Ms,"id","timeseries"),S(Ms,"defaults",Ce.defaults);nt.register(ct,ve,_e,Ze,Qe,al,ul);export{nt as Chart};
