const e={All:"All",Overview:"Overview",Proxies:"Proxies",Rules:"Rules",Conns:"Conns",Config:"Config",Logs:"Logs",Upload:"Upload",Download:"Download","Upload Total":"Upload Total","Download Total":"Download Total","Active Connections":"Active Connections","Memory Usage":"Memory Usage","Pause Refresh":"Pause Refresh","Resume Refresh":"Resume Refresh",close_all_connections:"Close All Connections",close_filter_connections:"Close all connections after filtering",Search:"Search",Up:"Up",Down:"Down","Test Latency":"Test Latency",settings:"settings",sort_in_grp:"Sorting in group",hide_unavail_proxies:"Hide unavailable proxies",auto_close_conns:"Automatically close old connections",order_natural:"Original order in config file",order_latency_asc:"By latency from small to big",order_latency_desc:"By latency from big to small",order_name_asc:"By name alphabetically (A-Z)",order_name_desc:"By name alphabetically (Z-A)",Connections:"Connections",current_backend:"Current Backend",Active:"Active",switch_backend:"Switch backend",Closed:"Closed",switch_theme:"Switch theme",theme:"theme",about:"about",no_logs:"No logs yet, hang tight...",chart_style:"Chart Style",latency_test_url:"Latency Test URL",lang:"Language",update_all_rule_provider:"Update all rule providers",update_all_proxy_provider:"Update all proxy providers",reload_config_file:"Reload config file",restart_core:"Restart core",upgrade_core:"Upgrade core",update_geo_databases_file:"Update GEO Databases ",flush_fake_ip_pool:"Flush fake-ip data",enable_tun_device:"Enable TUN Device",allow_lan:"Allow LAN",tls_sniffing:"Sniffer",c_host:"Host",c_sni:"Sniff Host",c_process:"Process",c_dl:"DL",c_ul:"UL",c_dl_speed:"DL Speed",c_ul_speed:"UP Speed",c_chains:"Chains",c_rule:"Rule",c_time:"Time",c_source:"Source",c_destination_ip:"Destination IP",c_type:"Type",c_ctrl:"Close",close_all_confirm:"Are you sure you want to close all connections?",close_all_confirm_yes:"I'm sure",close_all_confirm_no:"No",manage_column:"Custom columns",reset_column:"Reset columns",device_name:"Device Tag",delete:"Delete",add_tag:"Add tag",client_tag:"Client tags",sourceip_tip:"Prefix with / for regular expressions, otherwise it's a complete match",disconnect:"Close Connection",internel:"Internal Connection"};export{e as data};
