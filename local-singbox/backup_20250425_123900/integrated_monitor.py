#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
整合监控脚本 - 集成了API监控和IP切换功能
作者: Cascade AI
创建日期: 2025-04-25
"""

import os
import sys
import time
import json
import logging
import requests
import subprocess
import re
import signal
import threading
import schedule
import socket
import urllib.parse
from datetime import datetime

# 配置日志
LOG_DIR = "/home/<USER>/api/local-singbox/logs"
LOG_FILE = os.path.join(LOG_DIR, "integrated_monitor.log")
os.makedirs(LOG_DIR, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# API配置
API_HOST = "127.0.0.1"
API_PORT = 80  # 官方容器使用80端口
API_URL = f"http://{API_HOST}:{API_PORT}"
TEST_ENDPOINT = f"{API_URL}/api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo"

# 代理配置
PROXY_HOST = "127.0.0.1"
PROXY_PORT = 1080
PROXIES = {
    "http": f"socks5://{PROXY_HOST}:{PROXY_PORT}",
    "https": f"socks5://{PROXY_HOST}:{PROXY_PORT}"
}

# TikHub API配置
TIKHUB_API_URL = "https://beta.tikhub.io/api/v1/douyin/web/fetch_douyin_web_guest_cookie"
TIKHUB_API_KEY = "kqNoW3xz9Ccnpwk8jzO4wHEP/hQ0osX2vZx44CW4sWB9rXoTWORd2z2UMg=="
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36"

# Docker容器名称
DOCKER_CONTAINER_NAME = "dlr-douyin-api"
COOKIE_CONFIG_PATH = "/app/crawlers/douyin/web/config.yaml"  # 容器内Cookie配置文件路径
COOKIE_UPDATE_COOLDOWN = 60  # Cookie更新冷却时间（60秒）

# Telegram配置
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "235196660"
TELEGRAM_NOTIFICATION_COOLDOWN = 300  # 5分钟冷却时间，避免发送过多消息

# Node配置
NODE_CONFIG_DIR = "/home/<USER>/api/local-singbox/config"
NODE_CONFIG_FILE = os.path.join(NODE_CONFIG_DIR, "config.json")
SWITCH_INTERVAL = 120  # 切换节点的间隔（秒）

# 状态变量
current_ip = "未知"
last_cookie_update = None
api_status = "未知"
cookie_status = "未知"
docker_log_monitor_running = True
retry_count = 0
MAX_RETRY_ATTEMPTS = 3
last_telegram_notification_time = 0
ip_switch_running = True
current_node_index = 0

# 全局锁，防止并发操作
lock = threading.Lock()

def send_telegram_message(message):
    """发送Telegram消息"""
    global last_telegram_notification_time
    current_time = time.time()
    
    # 检查是否在冷却期内
    if current_time - last_telegram_notification_time < TELEGRAM_NOTIFICATION_COOLDOWN:
        logger.info(f"Telegram通知在冷却期内，跳过发送 (剩余 {int(TELEGRAM_NOTIFICATION_COOLDOWN - (current_time - last_telegram_notification_time))} 秒)")
        return False
    
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    data = {
        "chat_id": TELEGRAM_CHAT_ID,
        "text": message,
        "parse_mode": "HTML"
    }
    try:
        response = requests.post(url, data=data, timeout=10)
        if response.status_code == 200:
            logger.info("Telegram消息发送成功")
            last_telegram_notification_time = current_time
            return True
        else:
            logger.error(f"Telegram消息发送失败: {response.text}")
            return False
    except Exception as e:
        logger.error(f"发送Telegram消息时出错: {e}")
        return False

def get_current_ip():
    """获取当前出口IP"""
    global current_ip
    try:
        response = requests.get("https://api.ipify.org", proxies=PROXIES, timeout=10)
        if response.status_code == 200:
            new_ip = response.text.strip()
            if new_ip != current_ip and current_ip != "未知":
                logger.info(f"IP已变化: {current_ip} -> {new_ip}")
                send_telegram_message(f"🔄 <b>代理IP已变化</b>\n\n旧IP: {current_ip}\n新IP: {new_ip}")
            current_ip = new_ip
            logger.info(f"当前出口IP: {current_ip}")
            return current_ip
        else:
            logger.error(f"获取IP失败: HTTP {response.status_code}")
            return "未知"
    except Exception as e:
        logger.error(f"获取IP时出错: {e}")
        return "未知"

def check_api_status():
    """检查API状态"""
    global api_status
    try:
        response = requests.get(TEST_ENDPOINT, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200:
                logger.info("API状态正常")
                api_status = "正常"
                return True
            else:
                logger.warning(f"API返回错误: {data.get('status_msg', '未知错误')}")
                api_status = f"异常 - {data.get('status_msg', '未知错误')}"
                return False
        elif response.status_code == 400:
            logger.error("API返回400错误，可能是Cookie失效")
            api_status = "异常 - Cookie可能失效"
            return False
        else:
            logger.error(f"API状态异常: HTTP {response.status_code}")
            api_status = f"异常 - HTTP {response.status_code}"
            return False
    except requests.exceptions.ConnectionError:
        logger.error("API连接失败，服务可能未运行")
        api_status = "异常 - 连接失败"
        return False
    except Exception as e:
        logger.error(f"检查API状态时出错: {e}")
        api_status = f"异常 - {str(e)}"
        return False

def fetch_new_cookie():
    """从TikHub获取新Cookie"""
    global cookie_status
    try:
        # 编码user_agent参数
        encoded_user_agent = urllib.parse.quote(USER_AGENT)
        # 构建完整URL
        url = f"{TIKHUB_API_URL}?user_agent={encoded_user_agent}"
        
        # 设置正确的请求头
        headers = {
            'accept': 'application/json',
            'Authorization': f'Bearer {TIKHUB_API_KEY}'
        }
        
        # 发送请求
        logger.info(f"正在请求TikHub API获取新Cookie: {url}")
        response = requests.get(url, headers=headers, timeout=15)
        
        logger.info(f"TikHub API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"TikHub API响应内容: {json.dumps(data)[:200]}...")
            
            # 新的响应格式处理
            if data.get("code") == 200 and data.get("data") and data.get("data").get("Cookie"):
                cookie = data["data"].get("Cookie")
                logger.info("成功获取新Cookie")
                cookie_status = "已更新"
                return cookie
            else:
                logger.error(f"TikHub API响应格式不符合预期: {json.dumps(data)}")
                cookie_status = "获取失败 - 响应格式错误"
        else:
            logger.error(f"获取新Cookie失败: HTTP {response.status_code}")
            cookie_status = f"获取失败 - HTTP {response.status_code}"
        
        return None
    except Exception as e:
        logger.error(f"获取新Cookie时出错: {e}")
        cookie_status = f"获取失败 - {str(e)}"
        return None

def update_api_config(cookie):
    """更新API配置文件中的Cookie"""
    try:
        # 创建临时文件
        temp_file = "/tmp/douyin_cookie.txt"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(cookie)
        
        # 复制到容器
        copy_cmd = f"docker cp {temp_file} {DOCKER_CONTAINER_NAME}:/tmp/douyin_cookie.txt"
        result = os.system(copy_cmd)
        
        if result != 0:
            logger.error(f"复制Cookie到容器失败: {result}")
            return False
        
        # 在容器内更新配置文件 - 使用适合官方容器的更新命令
        update_cmd = f"""docker exec {DOCKER_CONTAINER_NAME} bash -c "
            # 备份原始配置文件
            cp {COOKIE_CONFIG_PATH} {COOKIE_CONFIG_PATH}.bak
            
            # 使用sed替换Cookie行，保持YAML格式
            sed -i '/Cookie:/c\\      Cookie: '{cookie} {COOKIE_CONFIG_PATH}
        " """
        
        result = os.system(update_cmd)
        
        if result != 0:
            logger.error(f"在容器内更新Cookie失败: {result}")
            return False
        
        logger.info("Cookie更新成功")
        return True
    except Exception as e:
        logger.error(f"更新Cookie时出错: {e}")
        return False

def restart_api_service():
    """重启API服务"""
    try:
        logger.info(f"正在重启容器: {DOCKER_CONTAINER_NAME}")
        
        # 执行重启命令
        restart_command = f"docker restart {DOCKER_CONTAINER_NAME}"
        result = os.system(restart_command)
        
        if result == 0:
            logger.info(f"容器 {DOCKER_CONTAINER_NAME} 重启成功")
            return True
        else:
            logger.error(f"容器 {DOCKER_CONTAINER_NAME} 重启失败，返回码: {result}")
            return False
    except Exception as e:
        logger.error(f"重启容器时出错: {e}")
        return False

def update_cookie_and_restart():
    """更新Cookie并重启容器"""
    global last_cookie_update, retry_count
    
    current_time = time.time()
    
    # 检查是否可以更新Cookie
    if not last_cookie_update or (current_time - last_cookie_update.timestamp() > COOKIE_UPDATE_COOLDOWN):
        # 发送API异常通知
        api_error_message = f"""
<b>⚠️ 检测到API返回错误</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
出口IP: {current_ip}
"""
        send_telegram_message(api_error_message)
        
        with lock:
            retry_count += 1
        
        if retry_count > MAX_RETRY_ATTEMPTS:
            logger.warning(f"已达到最大重试次数 ({MAX_RETRY_ATTEMPTS})，尝试重启容器")
            
            # 发送重启通知
            restart_message = f"""
<b>🔄 容器重启通知</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
原因: 已达到最大重试次数 ({MAX_RETRY_ATTEMPTS})
当前出口IP: {current_ip}
"""
            send_telegram_message(restart_message)
            
            restart_api_service()
            with lock:
                last_cookie_update = datetime.now()
                retry_count = 0
            return
        
        logger.warning(f"尝试更新Cookie (尝试 {retry_count}/{MAX_RETRY_ATTEMPTS})...")
        
        # 发送Cookie更新通知
        cookie_update_message = f"""
<b>🔄 正在更新Cookie</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
尝试次数: {retry_count}/{MAX_RETRY_ATTEMPTS}
当前出口IP: {current_ip}
"""
        send_telegram_message(cookie_update_message)
        
        new_cookie = fetch_new_cookie()
        
        if new_cookie:
            if update_api_config(new_cookie):
                logger.info("Cookie已更新，记录更新时间")
                
                # 发送Cookie更新成功通知
                cookie_success_message = f"""
<b>✅ Cookie更新成功</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
当前出口IP: {current_ip}
"""
                send_telegram_message(cookie_success_message)
                
                with lock:
                    last_cookie_update = datetime.now()
                    # Cookie已成功更新，重置重试计数
                    retry_count = 0
                
                # 重要：更新Cookie后重启容器
                restart_message = f"""
<b>🔄 Cookie已更新，正在重启容器</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
当前出口IP: {current_ip}
"""
                send_telegram_message(restart_message)
                restart_api_service()
    else:
        logger.info(f"Cookie更新冷却中，还需等待 {int(COOKIE_UPDATE_COOLDOWN - (current_time - last_cookie_update.timestamp()))} 秒")

def monitor_docker_logs():
    """监控Docker日志，检测API错误"""
    global docker_log_monitor_running
    
    logger.info(f"开始监控 {DOCKER_CONTAINER_NAME} 容器的日志...")
    
    # 发送启动通知
    startup_message = f"""
<b>🚀 Docker日志监控服务已启动</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
监控容器: {DOCKER_CONTAINER_NAME}
出口IP: {current_ip}
功能: 实时监控API错误并自动更新Cookie
"""
    send_telegram_message(startup_message)
    
    # 使用subprocess持续读取Docker日志
    process = subprocess.Popen(
        ["docker", "logs", "-f", DOCKER_CONTAINER_NAME],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        bufsize=1
    )
    
    # 定义需要检测的错误模式
    error_patterns = [
        r"ERROR.*无效响应类型.*",
        r"程序出现异常.*",
        r"HTTP/1\.1\" 400 Bad Request",
        r"第 \d+ 次响应内容为空",
        r"Cookie可能失效",
        r"请检查错误信息"
    ]
    
    while docker_log_monitor_running:
        try:
            line = process.stdout.readline()
            if not line:
                break
                
            # 检查是否包含任何错误模式
            for pattern in error_patterns:
                if re.search(pattern, line):
                    logger.warning(f"检测到错误日志: {line.strip()}")
                    # 记录错误并准备更新Cookie
                    update_cookie_and_restart()
                    # 一旦检测到错误并更新Cookie，等待一段时间再继续监控
                    time.sleep(60)
                    break
                    
        except Exception as e:
            logger.error(f"监控Docker日志时出错: {e}")
            time.sleep(10)
    
    logger.info("Docker日志监控已停止")

def load_node_config():
    """加载节点配置"""
    try:
        with open(NODE_CONFIG_FILE, 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        logger.error(f"加载节点配置失败: {e}")
        return None

def save_node_config(config):
    """保存节点配置"""
    try:
        with open(NODE_CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存节点配置失败: {e}")
        return False

def get_available_nodes():
    """获取可用节点列表"""
    config = load_node_config()
    if not config:
        return []
        
    try:
        outbounds = config.get('outbounds', [])
        for outbound in outbounds:
            if outbound.get('tag') == 'proxy':
                return outbound.get('outbounds', [])
    except Exception as e:
        logger.error(f"获取可用节点失败: {e}")
    
    return []

def switch_to_node(node_index):
    """切换到指定节点"""
    nodes = get_available_nodes()
    if not nodes or node_index >= len(nodes):
        logger.error(f"无效的节点索引: {node_index}, 可用节点数: {len(nodes)}")
        return False
        
    config = load_node_config()
    if not config:
        return False
        
    try:
        # 更新当前选择的节点
        outbounds = config.get('outbounds', [])
        for outbound in outbounds:
            if outbound.get('tag') == 'proxy':
                outbound['selected'] = nodes[node_index]
                logger.info(f"已切换到节点: {nodes[node_index]}")
                break
                
        # 保存配置
        if save_node_config(config):
            # 发送SIGHUP信号给singbox进程，使其重新加载配置
            singbox_pid_file = "/home/<USER>/api/local-singbox/singbox.pid"
            if os.path.exists(singbox_pid_file):
                with open(singbox_pid_file, 'r') as f:
                    pid = int(f.read().strip())
                os.kill(pid, signal.SIGHUP)
                logger.info(f"已发送重新加载信号给singbox进程 (PID: {pid})")
                return True
            else:
                logger.error("无法找到singbox进程PID文件")
        
        return False
    except Exception as e:
        logger.error(f"切换节点失败: {e}")
        return False

def rotate_ip():
    """定期轮换IP地址"""
    global current_node_index, ip_switch_running
    
    logger.info("IP轮换脚本启动")
    startup_message = f"""
<b>🔄 IP轮换服务已启动</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
当前出口IP: {get_current_ip()}
轮换间隔: {SWITCH_INTERVAL} 秒
"""
    send_telegram_message(startup_message)
    
    while ip_switch_running:
        try:
            nodes = get_available_nodes()
            if nodes:
                # 切换到下一个节点
                current_node_index = (current_node_index + 1) % len(nodes)
                logger.info(f"切换到下一个节点 (索引: {current_node_index}, 总节点数: {len(nodes)})")
                
                if switch_to_node(current_node_index):
                    # 等待配置生效
                    time.sleep(5)
                    # 获取新IP
                    new_ip = get_current_ip()
                    logger.info(f"当前出口IP: {new_ip}")
                
            else:
                logger.warning("没有可用的节点")
                
            # 等待下一次切换
            time.sleep(SWITCH_INTERVAL)
            
        except Exception as e:
            logger.error(f"IP轮换过程中出错: {e}")
            time.sleep(30)  # 出错后等待30秒再尝试

def generate_status_report():
    """生成状态报告"""
    # 获取API容器状态
    try:
        container_info = subprocess.check_output(f"docker inspect {DOCKER_CONTAINER_NAME}", shell=True)
        container_info = json.loads(container_info)
        container_status = container_info[0]['State']['Status'] if container_info else "未知"
    except:
        container_status = "未知"
    
    # 获取系统信息
    try:
        uptime = subprocess.check_output("uptime", shell=True).decode('utf-8').strip()
        api_memory = subprocess.check_output(f"docker stats {DOCKER_CONTAINER_NAME} --no-stream --format '{{{{.MemUsage}}}}'", shell=True).decode('utf-8').strip()
    except:
        uptime = "无法获取"
        api_memory = "无法获取"
    
    # 获取Cookie更新时间
    if last_cookie_update:
        last_update = last_cookie_update.strftime('%Y-%m-%d %H:%M:%S')
    else:
        last_update = "未更新"
    
    # 生成状态报告
    status_message = f"""
<b>📊 系统状态报告</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

<b>代理状态:</b>
- 当前IP: {current_ip}
- 节点索引: {current_node_index}
- 可用节点数: {len(get_available_nodes())}

<b>API服务状态:</b>
- 容器状态: {container_status}
- API状态: {api_status}

<b>Cookie状态:</b>
- 状态: {cookie_status}
- 最后更新: {last_update}

<b>系统信息:</b>
- API内存使用: {api_memory}
- 系统负载: {uptime.split("load average:")[1].strip() if "load average:" in uptime else "未知"}
"""
    
    logger.info("生成状态报告")
    send_telegram_message(status_message)

def main():
    """主函数"""
    global docker_log_monitor_running, ip_switch_running
    
    logger.info("启动集成监控服务...")
    send_telegram_message(f"🚀 <b>集成监控服务已启动</b>\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 初始化状态
    get_current_ip()
    check_api_status()
    
    # 启动Docker日志监控线程
    docker_log_thread = threading.Thread(target=monitor_docker_logs)
    docker_log_thread.daemon = True
    docker_log_thread.start()
    logger.info("Docker日志监控线程已启动")
    
    # 启动IP切换线程
    ip_switch_thread = threading.Thread(target=rotate_ip)
    ip_switch_thread.daemon = True
    ip_switch_thread.start()
    logger.info("IP切换线程已启动")
    
    # 设置定时任务
    schedule.every(1).minutes.do(get_current_ip)
    # 移除定期检查API状态的任务，因为我们现在通过Docker日志实时监控
    # schedule.every(5).minutes.do(check_and_update_cookie)
    schedule.every(30).minutes.do(generate_status_report)
    
    # 生成初始状态报告
    generate_status_report()
    
    # 设置信号处理
    def signal_handler(sig, frame):
        logger.info("接收到终止信号，准备退出...")
        global docker_log_monitor_running, ip_switch_running
        docker_log_monitor_running = False
        ip_switch_running = False
        
        logger.info("等待线程结束...")
        if docker_log_thread.is_alive():
            docker_log_thread.join(timeout=5)
        if ip_switch_thread.is_alive():
            ip_switch_thread.join(timeout=5)
            
        logger.info("监控服务已停止")
        send_telegram_message(f"🛑 <b>集成监控服务已停止</b>\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行定时任务
    while True:
        try:
            schedule.run_pending()
            time.sleep(1)
        except KeyboardInterrupt:
            signal_handler(signal.SIGINT, None)
        except Exception as e:
            error_message = f"""
<b>❌ 监控程序异常</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
错误: {str(e)}
"""
            send_telegram_message(error_message)
            logger.critical(f"监控程序异常: {str(e)}")
            time.sleep(30)  # 异常后等待30秒再继续

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("监控程序被手动终止")
    except Exception as e:
        error_message = f"""
<b>❌ 监控程序异常终止</b>
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
错误: {str(e)}
"""
        send_telegram_message(error_message)
        logger.critical(f"监控程序异常终止: {str(e)}")
