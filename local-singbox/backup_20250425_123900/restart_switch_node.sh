#!/bin/bash
# 重启节点切换脚本

# 设置路径
SINGBOX_DIR="/home/<USER>/api/local-singbox"
LOG_DIR="$SINGBOX_DIR/logs"
SWITCH_LOG="$LOG_DIR/switch_node.log"

# 记录日志
echo "$(date '+%Y-%m-%d %H:%M:%S') - 重启节点切换脚本..." >> "$SWITCH_LOG"

# 停止现有进程
pkill -f "python3 $SINGBOX_DIR/scripts/switch_node.py"
sleep 2

# 启动新进程
nohup python3 "$SINGBOX_DIR/scripts/switch_node.py" > "$SWITCH_LOG" 2>&1 &

# 检查是否成功启动
sleep 2
if pgrep -f "python3 $SINGBOX_DIR/scripts/switch_node.py" > /dev/null; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 节点切换脚本重启成功" >> "$SWITCH_LOG"
    echo "节点切换脚本重启成功"
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 节点切换脚本重启失败" >> "$SWITCH_LOG"
    echo "节点切换脚本重启失败"
fi
