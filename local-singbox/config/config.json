{"log": {"level": "info", "timestamp": true}, "experimental": {"clash_api": {"external_controller": "0.0.0.0:9090", "external_ui": "yacd", "secret": "", "default_mode": "rule"}}, "inbounds": [{"type": "mixed", "tag": "mixed-in-0", "listen": "::", "listen_port": 1080, "sniff": true, "sniff_override_destination": true, "domain_strategy": "prefer_ipv4"}, {"type": "mixed", "tag": "mixed-in-1", "listen": "::", "listen_port": 1081, "sniff": true, "sniff_override_destination": true, "domain_strategy": "prefer_ipv4"}, {"type": "mixed", "tag": "mixed-in-2", "listen": "::", "listen_port": 1082, "sniff": true, "sniff_override_destination": true, "domain_strategy": "prefer_ipv4"}, {"type": "mixed", "tag": "mixed-in-3", "listen": "::", "listen_port": 1083, "sniff": true, "sniff_override_destination": true, "domain_strategy": "prefer_ipv4"}], "outbounds": [{"type": "selector", "tag": "proxy0", "outbounds": ["direct", "server1", "server2", "server3", "server4", "server5", "server6", "server7", "server8", "server9", "server10", "server11", "server12", "server13", "server14", "server15", "server16", "server17", "server18", "server19", "server20", "server21", "server22", "server23", "server24", "server25", "server26", "server27", "server28", "server29", "server30", "server31", "server32", "server33", "server34", "server35", "server36", "server37", "server38", "server39", "server40", "server41", "server42", "server43", "server44", "server45", "server46", "server47", "server48", "server49", "server50", "server51", "server52", "server53", "server54", "server55", "server56", "server57"], "default": "server1"}, {"type": "selector", "tag": "proxy1", "outbounds": ["direct", "server1", "server2", "server3", "server4", "server5", "server6", "server7", "server8", "server9", "server10", "server11", "server12", "server13", "server14", "server15", "server16", "server17", "server18", "server19", "server20", "server21", "server22", "server23", "server24", "server25", "server26", "server27", "server28", "server29", "server30", "server31", "server32", "server33", "server34", "server35", "server36", "server37", "server38", "server39", "server40", "server41", "server42", "server43", "server44", "server45", "server46", "server47", "server48", "server49", "server50", "server51", "server52", "server53", "server54", "server55", "server56", "server57"], "default": "server1"}, {"type": "selector", "tag": "proxy2", "outbounds": ["direct", "server1", "server2", "server3", "server4", "server5", "server6", "server7", "server8", "server9", "server10", "server11", "server12", "server13", "server14", "server15", "server16", "server17", "server18", "server19", "server20", "server21", "server22", "server23", "server24", "server25", "server26", "server27", "server28", "server29", "server30", "server31", "server32", "server33", "server34", "server35", "server36", "server37", "server38", "server39", "server40", "server41", "server42", "server43", "server44", "server45", "server46", "server47", "server48", "server49", "server50", "server51", "server52", "server53", "server54", "server55", "server56", "server57"], "default": "server1"}, {"type": "selector", "tag": "proxy3", "outbounds": ["direct", "server1", "server2", "server3", "server4", "server5", "server6", "server7", "server8", "server9", "server10", "server11", "server12", "server13", "server14", "server15", "server16", "server17", "server18", "server19", "server20", "server21", "server22", "server23", "server24", "server25", "server26", "server27", "server28", "server29", "server30", "server31", "server32", "server33", "server34", "server35", "server36", "server37", "server38", "server39", "server40", "server41", "server42", "server43", "server44", "server45", "server46", "server47", "server48", "server49", "server50", "server51", "server52", "server53", "server54", "server55", "server56", "server57"], "default": "server1"}, {"type": "selector", "tag": "proxy", "outbounds": ["direct", "server1", "server2", "server3", "server4", "server5", "server6", "server7", "server8", "server9", "server10", "server11", "server12", "server13", "server14", "server15", "server16", "server17", "server18", "server19", "server20", "server21", "server22", "server23", "server24", "server25", "server26", "server27", "server28", "server29", "server30", "server31", "server32", "server33", "server34", "server35", "server36", "server37", "server38", "server39", "server40", "server41", "server42", "server43", "server44", "server45", "server46", "server47", "server48", "server49", "server50", "server51", "server52", "server53", "server54", "server55", "server56", "server57"], "default": "server1"}, {"type": "urltest", "tag": "auto", "outbounds": ["server1", "server2", "server3", "server4", "server5", "server6", "server7", "server8", "server9", "server10", "server11", "server12", "server13", "server14", "server15", "server16", "server17", "server18", "server19", "server20", "server21", "server22", "server23", "server24", "server25", "server26", "server27", "server28", "server29", "server30", "server31", "server32", "server33", "server34", "server35", "server36", "server37", "server38", "server39", "server40", "server41", "server42", "server43", "server44", "server45", "server46", "server47", "server48", "server49", "server50", "server51", "server52", "server53", "server54", "server55", "server56", "server57"], "url": "https://www.gstatic.com/generate_204", "interval": "1m", "tolerance": 50}, {"type": "direct", "tag": "direct"}, {"type": "shadowsocks", "tag": "server1", "server": "hknode.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "b8c3f4d97013af43ccfc", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server2", "server": "hk-hkt.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "d44454ef4745164c21bb", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server3", "server": "tw.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "1126ea1f8ae8fe2828e3", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server4", "server": "japan.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "d29713bdd6979b87359b", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server5", "server": "jp-home.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "dcc03ebf9363c612fd6c", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server6", "server": "sgp.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "fd82aaf23fbfdd80b137", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server7", "server": "malassia-home.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "7338d988d71591d76b3f", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server8", "server": "thailand-node.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "51f8c7454c93d0d83c29", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server9", "server": "kr-node.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "c8efd112555b8e8513f5", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server10", "server": "vietnam.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "b16cb18d8992f3b83b7d", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server11", "server": "philippines.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "6113536135af69e572c6", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server12", "server": "indonesia.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "777bc05be0dd624959fb", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server13", "server": "india.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "b5d9f64162f55ecd80e6", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server14", "server": "ceylon.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "fe51727afe2fca582ebf", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server15", "server": "turkey.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "6d03b3321bf353ae565c", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server16", "server": "Kazakhstan.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "3e79196cf9e970671245", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server17", "server": "pakistan.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "3938bfae997974860251", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server18", "server": "israel.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "ec39dd066cc142fbb81c", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server19", "server": "Uzbekistan.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "639eb0842553912e4086", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server20", "server": "nepal.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "d3a40b9d9b91c1e412c5", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server21", "server": "uae.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "3fb1d360ec0c4c7a5054", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server22", "server": "sa.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "ade70eeee6bba98e86b8", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server23", "server": "sydney-australia.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "97c3cf442df2d7099275", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server24", "server": "new-zeeland.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "9d29e58723af34cad8da", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server25", "server": "spain.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "97998add7bf4fea885ef", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server26", "server": "Italy.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "4580f3fc18890cf73774", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server27", "server": "united-kingdom.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "c8960ca4a0add435fc34", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server28", "server": "bulgaria.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "b74518fb49bd645e6ce7", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server29", "server": "canada-vancouver.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "f4cd0a554e9d0558c581", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server30", "server": "germany.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "c1ee5895686790a383fb", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server31", "server": "austria.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "c1ee5895686790a383fb", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server32", "server": "french.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "2bcb59022f92119844f5", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server33", "server": "belarus.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "11575f448fb4021fc611", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server34", "server": "ireland.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "a9067a4c876a68327028", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server35", "server": "romania.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "0a0fb8dbfb8cc2f25f02", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server36", "server": "iceland.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "13e9563510f42a83d42f", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server37", "server": "Latvia.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "cfc6f3f75f5ef7b2665c", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server38", "server": "denmark.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "02b55be99c1f1d69d389", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server39", "server": "norway.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "d2a3d8cd42423efce7fb", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server40", "server": "finland.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "d8e075a9d0ab49514ee6", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server41", "server": "sweden.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "c8a1d0a69902859d209f", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server42", "server": "netherland.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "6c97b151673c03993eac", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server43", "server": "russia.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "1954df8a5f3876000ea6", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server44", "server": "usa-node.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "22046a35aa74890b48f5", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server45", "server": "usa-home.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "8afdf96fd05fcb799174", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server46", "server": "mexico.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "de4653bc8f982b05fcdd", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server47", "server": "peru.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "776a7ea40205ea82c86e", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server48", "server": "ecuador.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "722a54414ae6ee706ac9", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server49", "server": "colombia.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "2b7df6aadc759756b0cd", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server50", "server": "argentina.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "1a402babc02465f45bea", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server51", "server": "Brazil.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "7452c550383bf60ac9d3", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server52", "server": "costa-rica.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "a643f6f3733c9962851d", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server53", "server": "chile.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "1d8e4861b251c8c4ce78", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server54", "server": "africa.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "b4c9e286a46bd6ecca13", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server55", "server": "nigeria.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "8141b20c775ce63fab96", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server56", "server": "egypt.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "bd62dac6636078647014", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}, {"type": "shadowsocks", "tag": "server57", "server": "kenya.tag-global-ddns.top", "server_port": 9527, "method": "aes-128-gcm", "password": "13853411bc6994e52a1a", "plugin": "obfs-local", "plugin_opts": "obfs=http;obfs-host=0b95dbd343b8979d386c.microsoft.com"}], "route": {"rules": [{"inbound": ["mixed-in-3"], "outbound": "proxy3"}, {"inbound": ["mixed-in-2"], "outbound": "proxy2"}, {"inbound": ["mixed-in-1"], "outbound": "proxy1"}, {"inbound": ["mixed-in-0"], "outbound": "proxy0"}]}}