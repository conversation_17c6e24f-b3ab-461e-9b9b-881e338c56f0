# 集成服务配置文件 - 多实例版本
# 作者: Cascade AI
# 创建日期: 2025-05-29

# 代理服务配置
proxy:
  host: 127.0.0.1
  # 多端口配置 - 每个API实例使用独立的代理端口
  ports:
    - 1080  # API实例0
    - 1081  # API实例1
    - 1082  # API实例2
    - 1083  # API实例3
  # 旧的单端口配置（保留兼容性）
  port: 1080
  switch_interval: 120  # 切换间隔（秒）
  node_config_file: /home/<USER>/local-singbox/config/config.json

# API服务配置 - 单服务多端口模式
api_instances:
  - name: "API实例0"
    systemd_service: "Douyin_TikTok_API_Multiport"
    host: 127.0.0.1
    port: 8080
    proxy_port: 1080  # 使用独立的代理端口
    proxy_group: "proxy0"  # 对应的Clash API代理组
    config_path: /home/<USER>/API/Douyin_TikTok_Download_API/crawlers/douyin/web/config.yaml
    test_endpoint: /api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo
    
  - name: "API实例1"
    systemd_service: "Douyin_TikTok_API_Multiport"
    host: 127.0.0.1
    port: 8081
    proxy_port: 1081  # 使用独立的代理端口
    proxy_group: "proxy1"  # 对应的Clash API代理组
    config_path: /home/<USER>/API/Douyin_TikTok_Download_API/crawlers/douyin/web/config_instance1.yaml
    test_endpoint: /api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo
    
  - name: "API实例2"
    systemd_service: "Douyin_TikTok_API_Multiport"
    host: 127.0.0.1
    port: 8082
    proxy_port: 1082  # 使用独立的代理端口
    proxy_group: "proxy2"  # 对应的Clash API代理组
    config_path: /home/<USER>/API/Douyin_TikTok_Download_API/crawlers/douyin/web/config_instance2.yaml
    test_endpoint: /api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo
    
  - name: "API实例3"
    systemd_service: "Douyin_TikTok_API_Multiport"
    host: 127.0.0.1
    port: 8083
    proxy_port: 1083  # 使用独立的代理端口
    proxy_group: "proxy3"  # 对应的Clash API代理组
    config_path: /home/<USER>/API/Douyin_TikTok_Download_API/crawlers/douyin/web/config_instance3.yaml
    test_endpoint: /api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo

# Cookie配置
cookie:
  pool_size: 20
  pool_file: /home/<USER>/API/local-singbox/config/cookie_pool_multi.json
  tikhub_api_url: https://api.tikhub.io/api/v1/douyin/web/fetch_douyin_web_guest_cookie
  tikhub_api_key: kqNoW3xz9Ccnpwk8jzO4wHEP/hQ0osX2vZx44CW4sWB9rXoTWORd2z2UMg==
  user_agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
  update_cooldown: 3600  # Cookie更新冷却时间（秒）
  max_retry_attempts: 5

# 监控配置
monitoring:
  error_threshold: 3       # 错误阈值
  error_window: 300       # 错误计数时间窗口（秒）
  check_interval: 30      # 检查间隔（秒）
  status_report_interval: 43200  # 状态报告间隔（秒）
  # 进程白名单 - 这些进程不会被清理
  process_whitelist:
    # 白名单路径 - 工作目录在这些路径下的进程不会被清理
    paths:
      - /home/<USER>/downloader  # 下载器目录
    # 白名单进程名 - 包含这些名称的进程不会被清理
    processes:
      - surveillance.py  # 监控程序
  error_patterns:
    immediate_action:
      - "Resolved Douyin with cookies"
      - "搜索接口响应失败:"
      - "msToken=undefined"
      - "list is empty"
      - "odin_tt"
      - "UserProfileException"
      - "请求失败"
      - "post count is 0"
      - "_signature"
      - "失败结果: 用户"
    standard:
      - "ERROR"
      - "Exception"
      - "Failed"
      - "错误"
      - "失败"

# 通知设置
notifications:
  telegram:
    bot_token: **********************************************
    chat_id: 235196660
    cooldown: 300  # 通知冷却时间（秒）

# Supabase配置
supabase:
  # url和key可以在这里配置，也可以通过环境变量SUPABASE_URL和SUPABASE_KEY设置
  url: https://wjanjmsywbydjbfrdkaz.supabase.co
  key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE
  does_table_id: 1  # does表的记录ID

# 订阅设置
subscription:
  urls:
    - https://global.tagonline.asia/api/v1/client/subscribe?token=9fe0baab7e2b181e70e93b39aecdd3cb
    - https://global.tagonline.asia/api/v1/client/subscribe?token=b29cb7fa58ab93e36a2f06be4ac3e3cc
  update_interval: 24  # 更新间隔（小时）
  update_hour: 3       # 每日更新时间点
  last_used_url_file: /home/<USER>/API/local-singbox/config/last_subscription_url.txt

# Clash API设置
clash_api:
  url: http://127.0.0.1:9090
  # 多代理组配置 - 每个API实例对应一个代理组
  proxy_groups:
    - proxy0
    - proxy1
    - proxy2
    - proxy3
  max_retries: 3
  max_latency: 1000  # 最大可接受延迟（毫秒）