# 集成服务配置文件
# 作者: Cascade AI
# 创建日期: 2025-04-25

# 代理服务配置
proxy:
  host: 127.0.0.1
  port: 1080
  switch_interval: 120  # 切换间隔（秒）
  node_config_file: /home/<USER>/local-singbox/config/config.json

# API服务配置
api:
  container_name: dlr-douyin-api
  host: 127.0.0.1
  port: 80
  config_path: /app/crawlers/douyin/web/config.yaml
  test_endpoint: /api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo

# Cookie管理
cookie:
  tikhub_api_url: https://beta.tikhub.io/api/v1/douyin/web/fetch_douyin_web_guest_cookie
  tikhub_api_key: kqNoW3xz9Ccnpwk8jzO4wHEP/hQ0osX2vZx44CW4sWB9rXoTWORd2z2UMg==
  user_agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36
  update_cooldown: 60  # Cookie更新冷却时间（秒）
  max_retry_attempts: 3

# 监控设置
monitoring:
  docker_log_patterns:
    - 无效响应类型
    - 程序出现异常
    - HTTP/1\.1\" 400 Bad Request
    - 第 \d+ 次响应内容为空
    - Cookie可能失效
    - 请检查错误信息
  ip_check_interval: 60  # 检查IP间隔（秒）
  status_report_interval: 1800  # 状态报告间隔（秒）

# 通知设置
notifications:
  telegram:
    bot_token: **********************************************
    chat_id: 235196660
    cooldown: 300  # 通知冷却时间（秒）

# 日志设置
logging:
  dir: /home/<USER>/local-singbox/logs
  main_log: integrated_service.log
  level: INFO
