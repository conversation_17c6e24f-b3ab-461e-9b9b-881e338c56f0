#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json
import logging
import os
import sys
import argparse
from datetime import datetime
from tabulate import tabulate

# 设置日志
LOG_DIR = "/home/<USER>/api/local-singbox/logs"
LOG_FILE = os.path.join(LOG_DIR, "proxy_test.log")
os.makedirs(LOG_DIR, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ],
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)

# 代理配置
PROXY_HOST = "127.0.0.1"
PROXY_PORT = 1080
PROXIES = {
    "http": f"socks5://{PROXY_HOST}:{PROXY_PORT}",
    "https": f"socks5://{PROXY_HOST}:{PROXY_PORT}"
}

# IP信息API
IP_INFO_API = "http://ip-api.com/json/{}"

# 测试网站列表
TEST_SITES = [
    {"name": "Google", "url": "https://www.google.com", "keyword": "Google"},
    {"name": "YouTube", "url": "https://www.youtube.com", "keyword": "YouTube"},
    {"name": "Twitter", "url": "https://twitter.com", "keyword": "Twitter"},
    {"name": "Facebook", "url": "https://www.facebook.com", "keyword": "Facebook"},
    {"name": "抖音", "url": "https://www.douyin.com", "keyword": "抖音"},
    {"name": "百度", "url": "https://www.baidu.com", "keyword": "百度"}
]

# 历史IP记录
ip_history = []

def get_ip_info(use_proxy=True):
    """获取当前IP信息"""
    proxies = PROXIES if use_proxy else None
    
    # 尝试多个IP信息服务
    services = [
        {"name": "ipify", "url": "https://api.ipify.org?format=json", "ip_key": "ip"},
        {"name": "ipinfo", "url": "https://ipinfo.io/json", "ip_key": "ip"},
        {"name": "ifconfig.me", "url": "https://ifconfig.me/all.json", "ip_key": "ip_addr"}
    ]
    
    for service in services:
        try:
            response = requests.get(service["url"], proxies=proxies, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if service["ip_key"] in data:
                    ip = data[service["ip_key"]]
                    return ip
        except Exception as e:
            logger.warning(f"服务 {service['name']} 请求失败: {e}")
    
    return "未知"

def get_ip_location(ip):
    """获取IP地理位置信息"""
    try:
        response = requests.get(IP_INFO_API.format(ip), timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                result = {
                    "country": data.get("country", "未知"),
                    "city": data.get("city", "未知"),
                    "isp": data.get("isp", "未知"),
                    "lat": data.get("lat", 0),
                    "lon": data.get("lon", 0),
                    "timezone": data.get("timezone", "未知"),
                    "org": data.get("org", "未知")
                }
                return result
        return {"country": "未知", "city": "未知", "isp": "未知"}
    except Exception as e:
        logger.error(f"获取IP地理位置信息失败: {e}")
        return {"country": "未知", "city": "未知", "isp": "未知"}

def test_website(site, proxies=None):
    """测试网站可访问性"""
    start_time = time.time()
    try:
        response = requests.get(site["url"], proxies=proxies, timeout=10, allow_redirects=True)
        elapsed = time.time() - start_time
        
        if response.status_code == 200 and site["keyword"] in response.text:
            return {"status": "成功", "time": f"{elapsed:.2f}秒", "code": response.status_code}
        else:
            return {"status": "失败", "time": f"{elapsed:.2f}秒", "code": response.status_code}
    except Exception as e:
        elapsed = time.time() - start_time
        return {"status": "错误", "time": f"{elapsed:.2f}秒", "error": str(e)}

def run_tests(interval, count=None, verbose=False):
    """运行测试"""
    test_count = 0
    
    # 获取本地IP（不使用代理）
    local_ip = get_ip_info(use_proxy=False)
    local_location = get_ip_location(local_ip)
    logger.info(f"本地IP: {local_ip} ({local_location['country']} {local_location['city']} - {local_location['isp']})")
    
    print("\n" + "="*80)
    print(f"代理测试开始 - 间隔: {interval}秒" + (" - 无限循环" if count is None else f" - 次数: {count}"))
    print(f"本地IP: {local_ip} ({local_location['country']} {local_location['city']} - {local_location['isp']})")
    print("="*80 + "\n")
    
    try:
        while count is None or test_count < count:
            test_count += 1
            
            # 获取代理IP
            proxy_ip = get_ip_info(use_proxy=True)
            proxy_location = get_ip_location(proxy_ip)
            
            # 记录IP历史
            if not ip_history or ip_history[-1]["ip"] != proxy_ip:
                ip_history.append({
                    "ip": proxy_ip,
                    "location": proxy_location,
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
            
            logger.info(f"测试 #{test_count} - 代理IP: {proxy_ip} ({proxy_location['country']} {proxy_location['city']} - {proxy_location['isp']})")
            
            # 打印当前IP信息
            print("\n" + "-"*80)
            print(f"测试 #{test_count} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"代理IP: {proxy_ip} ({proxy_location['country']} {proxy_location['city']} - {proxy_location['isp']})")
            
            # 如果是详细模式，测试网站可访问性
            if verbose:
                results = []
                for site in TEST_SITES:
                    print(f"测试网站: {site['name']}...", end="", flush=True)
                    result = test_website(site, PROXIES)
                    print(f" {result['status']} ({result['time']})")
                    results.append([site['name'], site['url'], result['status'], result['time']])
                
                # 打印测试结果表格
                print("\n网站可访问性测试结果:")
                print(tabulate(results, headers=["网站", "URL", "状态", "响应时间"], tablefmt="grid"))
            
            print("-"*80)
            
            # 如果不是最后一次测试，等待指定时间
            if count is None or test_count < count:
                if verbose:
                    for i in range(interval, 0, -1):
                        print(f"\r下一次测试将在 {i} 秒后进行...", end="", flush=True)
                        time.sleep(1)
                    print("\r" + " " * 40 + "\r", end="")
                else:
                    time.sleep(interval)
    
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    
    finally:
        # 打印IP历史记录
        if ip_history:
            print("\n\nIP变化历史:")
            ip_table = []
            for i, record in enumerate(ip_history):
                ip_table.append([
                    i+1,
                    record["time"],
                    record["ip"],
                    f"{record['location']['country']} {record['location']['city']}",
                    record["location"]["isp"]
                ])
            print(tabulate(ip_table, headers=["#", "时间", "IP", "位置", "ISP"], tablefmt="grid"))
        
        print("\n测试完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="代理服务测试工具")
    parser.add_argument("-i", "--interval", type=int, default=60, help="测试间隔（秒）")
    parser.add_argument("-c", "--count", type=int, help="测试次数（不指定则无限循环）")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细模式，测试网站可访问性")
    
    args = parser.parse_args()
    
    try:
        # 检查代理是否可用
        test_response = requests.get("https://www.google.com", proxies=PROXIES, timeout=5)
        if test_response.status_code != 200:
            logger.error(f"代理测试失败: HTTP {test_response.status_code}")
            print(f"错误: 代理测试失败，HTTP状态码 {test_response.status_code}")
            return
    except Exception as e:
        logger.error(f"代理测试失败: {e}")
        print(f"错误: 代理不可用，请确保代理服务正在运行。\n详细错误: {e}")
        return
    
    # 运行测试
    run_tests(args.interval, args.count, args.verbose)

if __name__ == "__main__":
    main()
