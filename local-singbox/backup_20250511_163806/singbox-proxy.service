[Unit]
Description=Singbox 代理服务 - 自动切换IP和更新订阅
After=network.target
Wants=network-online.target

[Service]
Type=forking
User=ubuntu
WorkingDirectory=/home/<USER>/api/local-singbox
ExecStart=/home/<USER>/api/local-singbox/scripts/manage_service.sh start
ExecStop=/home/<USER>/api/local-singbox/scripts/manage_service.sh stop
ExecReload=/home/<USER>/api/local-singbox/scripts/manage_service.sh restart
PIDFile=/home/<USER>/api/local-singbox/singbox.pid
Restart=on-failure
RestartSec=30
LimitNOFILE=1048576

[Install]
WantedBy=multi-user.target
