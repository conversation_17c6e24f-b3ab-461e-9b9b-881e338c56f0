#!/bin/bash
# Docker透明代理配置脚本
# 将Docker容器流量透明地转发到singbox代理
# 强制模式：当代理不可用时，容器将无法访问网络

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# Singbox代理端口
PROXY_PORT=1080
# Docker网络通常使用的IP范围
DOCKER_SUBNET="**********/16"
# Docker网桥接口
DOCKER_INTERFACE="docker0"

# 检查root权限
if [ "$EUID" -ne 0 ]; then
  echo -e "${RED}错误: 请使用sudo或root权限运行此脚本${NC}"
  exit 1
fi

# 检查iptables是否安装
if ! command -v iptables &> /dev/null; then
  echo -e "${RED}iptables未安装. 请先安装iptables${NC}"
  exit 1
fi

# 检查singbox是否运行
function check_singbox() {
  if ! ss -lntp | grep -q ":$PROXY_PORT"; then
    echo -e "${RED}警告: 在端口 $PROXY_PORT 上没有检测到singbox服务${NC}"
    echo -e "${RED}Singbox必须运行才能使Docker容器访问网络${NC}"
    read -p "是否继续? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      echo -e "${RED}操作取消${NC}"
      exit 1
    fi
  else
    echo -e "${GREEN}检测到singbox服务在端口 $PROXY_PORT${NC}"
  fi
}

# 启用转发
function enable_forwarding() {
  echo -e "${BLUE}启用IP转发...${NC}"
  sysctl -w net.ipv4.ip_forward=1
  echo 1 > /proc/sys/net/ipv4/ip_forward
  
  # 确保重启后设置仍然生效
  if ! grep -q "net.ipv4.ip_forward=1" /etc/sysctl.conf; then
    echo "net.ipv4.ip_forward=1" >> /etc/sysctl.conf
    sysctl -p
  fi
}

# 保存当前iptables规则
function save_current_rules() {
  echo -e "${BLUE}备份当前iptables规则...${NC}"
  mkdir -p /home/<USER>/api/local-singbox/backup
  iptables-save > /home/<USER>/api/local-singbox/backup/iptables-backup-$(date +%Y%m%d-%H%M%S).rules
  echo -e "${GREEN}已备份iptables规则${NC}"
}

# 设置透明代理规则，不允许回退
function setup_transparent_proxy() {
  echo -e "${BLUE}设置严格透明代理规则...${NC}"
  
  # 创建新链
  iptables -t nat -N DOCKER_PROXY 2>/dev/null || true
  iptables -t filter -N DOCKER_FILTER 2>/dev/null || true
  
  # 清理现有链中的规则
  iptables -t nat -F DOCKER_PROXY
  iptables -t filter -F DOCKER_FILTER
  
  # 添加透明代理规则 - 仅允许内部网络直连，其余全部重定向到代理
  iptables -t nat -A DOCKER_PROXY -d 0.0.0.0/8 -j RETURN
  iptables -t nat -A DOCKER_PROXY -d 10.0.0.0/8 -j RETURN
  iptables -t nat -A DOCKER_PROXY -d *********/8 -j RETURN
  iptables -t nat -A DOCKER_PROXY -d ***********/16 -j RETURN
  iptables -t nat -A DOCKER_PROXY -d **********/12 -j RETURN
  iptables -t nat -A DOCKER_PROXY -d ***********/16 -j RETURN
  iptables -t nat -A DOCKER_PROXY -d *********/4 -j RETURN
  iptables -t nat -A DOCKER_PROXY -d 240.0.0.0/4 -j RETURN
  
  # 重定向所有TCP流量到singbox代理
  iptables -t nat -A DOCKER_PROXY -p tcp -j REDIRECT --to-port $PROXY_PORT
  
  # 将来自Docker容器的流量路由到DOCKER_PROXY链
  # 判断是否已经有相同规则
  if ! iptables -t nat -C PREROUTING -i $DOCKER_INTERFACE -p tcp -j DOCKER_PROXY 2>/dev/null; then
    iptables -t nat -A PREROUTING -i $DOCKER_INTERFACE -p tcp -j DOCKER_PROXY
    echo -e "${GREEN}已添加规则: 将来自$DOCKER_INTERFACE的TCP流量重定向到端口$PROXY_PORT${NC}"
  else
    echo -e "${YELLOW}规则已存在: 将来自$DOCKER_INTERFACE的TCP流量重定向到端口$PROXY_PORT${NC}"
  fi
  
  # 添加OUTPUT规则，确保从Docker子网发出的流量也通过代理
  if ! iptables -t nat -C OUTPUT -s $DOCKER_SUBNET -p tcp -j DOCKER_PROXY 2>/dev/null; then
    iptables -t nat -A OUTPUT -s $DOCKER_SUBNET -p tcp -j DOCKER_PROXY
    echo -e "${GREEN}已添加规则: 将来自$DOCKER_SUBNET的出站TCP流量重定向到端口$PROXY_PORT${NC}"
  else
    echo -e "${YELLOW}规则已存在: 将来自$DOCKER_SUBNET的出站TCP流量重定向到端口$PROXY_PORT${NC}"
  fi
  
  # ===== 关键添加：确保singbox关闭时阻止所有流量 =====
  
  # 添加过滤规则，确保singbox关闭时没有回退选项
  echo -e "${BLUE}添加强制规则: 当singbox未运行时阻止Docker流量...${NC}"
  
  # 清除可能已存在的规则
  iptables -t filter -F DOCKER_FILTER
  
  # 允许访问内部网络
  iptables -t filter -A DOCKER_FILTER -d 0.0.0.0/8 -j ACCEPT
  iptables -t filter -A DOCKER_FILTER -d 10.0.0.0/8 -j ACCEPT
  iptables -t filter -A DOCKER_FILTER -d *********/8 -j ACCEPT
  iptables -t filter -A DOCKER_FILTER -d ***********/16 -j ACCEPT
  iptables -t filter -A DOCKER_FILTER -d **********/12 -j ACCEPT
  iptables -t filter -A DOCKER_FILTER -d ***********/16 -j ACCEPT
  iptables -t filter -A DOCKER_FILTER -d *********/4 -j ACCEPT
  iptables -t filter -A DOCKER_FILTER -d 240.0.0.0/4 -j ACCEPT
  
  # 允许访问代理服务器
  iptables -t filter -A DOCKER_FILTER -p tcp -d 127.0.0.1 --dport $PROXY_PORT -j ACCEPT
  iptables -t filter -A DOCKER_FILTER -p tcp -d localhost --dport $PROXY_PORT -j ACCEPT
  
  # 创建辅助脚本，实现严格检查singbox是否运行
  cat > /usr/local/bin/check_singbox_running.sh << 'EOF'
#!/bin/bash
# 检查singbox代理是否在运行
if ss -lntp | grep -q ":1080"; then
  exit 0
else
  exit 1
fi
EOF
  chmod +x /usr/local/bin/check_singbox_running.sh
  
  # 替代方案：使用自定义的owner match模块实现严格模式
  # 添加基于脚本的检查规则，如果singbox不在运行，拒绝所有对外连接
  iptables -t filter -A DOCKER_FILTER -m owner ! --uid-owner 0 -j ACCEPT
  iptables -t filter -A DOCKER_FILTER -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
  
  # 添加规则：当singbox不在运行时，阻止所有外部连接
  # 使用owner模块代替socket模块
  iptables -t filter -A DOCKER_FILTER -m comment --comment "非内网流量需要singbox代理" -j REJECT
  
  # 创建cron任务，动态更新防火墙规则
  cat > /etc/cron.d/check_singbox << 'EOF'
* * * * * root if /usr/local/bin/check_singbox_running.sh; then iptables -t filter -I DOCKER_FILTER 10 -j ACCEPT; else iptables -t filter -D DOCKER_FILTER 10; fi >/dev/null 2>&1
EOF
  chmod 644 /etc/cron.d/check_singbox
  
  # 在每10秒检查一次singbox状态并更新规则
  (crontab -l 2>/dev/null || echo ""; echo "* * * * * sleep 10; if /usr/local/bin/check_singbox_running.sh; then iptables -t filter -I DOCKER_FILTER 10 -j ACCEPT >/dev/null 2>&1; else iptables -t filter -D DOCKER_FILTER 10 >/dev/null 2>&1; fi") | crontab -
  (crontab -l 2>/dev/null || echo ""; echo "* * * * * sleep 20; if /usr/local/bin/check_singbox_running.sh; then iptables -t filter -I DOCKER_FILTER 10 -j ACCEPT >/dev/null 2>&1; else iptables -t filter -D DOCKER_FILTER 10 >/dev/null 2>&1; fi") | crontab -
  (crontab -l 2>/dev/null || echo ""; echo "* * * * * sleep 30; if /usr/local/bin/check_singbox_running.sh; then iptables -t filter -I DOCKER_FILTER 10 -j ACCEPT >/dev/null 2>&1; else iptables -t filter -D DOCKER_FILTER 10 >/dev/null 2>&1; fi") | crontab -
  (crontab -l 2>/dev/null || echo ""; echo "* * * * * sleep 40; if /usr/local/bin/check_singbox_running.sh; then iptables -t filter -I DOCKER_FILTER 10 -j ACCEPT >/dev/null 2>&1; else iptables -t filter -D DOCKER_FILTER 10 >/dev/null 2>&1; fi") | crontab -
  (crontab -l 2>/dev/null || echo ""; echo "* * * * * sleep 50; if /usr/local/bin/check_singbox_running.sh; then iptables -t filter -I DOCKER_FILTER 10 -j ACCEPT >/dev/null 2>&1; else iptables -t filter -D DOCKER_FILTER 10 >/dev/null 2>&1; fi") | crontab -
  
  # 立即执行一次，初始化规则
  if /usr/local/bin/check_singbox_running.sh; then
    iptables -t filter -I DOCKER_FILTER 10 -j ACCEPT
    echo -e "${GREEN}singbox正在运行，允许网络连接${NC}"
  else
    echo -e "${RED}singbox未运行，阻止所有网络连接${NC}"
  fi
  
  # 应用DOCKER_FILTER链到Docker网络的出站流量
  if ! iptables -t filter -C FORWARD -i $DOCKER_INTERFACE -j DOCKER_FILTER 2>/dev/null; then
    iptables -t filter -A FORWARD -i $DOCKER_INTERFACE -j DOCKER_FILTER
    echo -e "${GREEN}已添加过滤规则: Docker流量必须通过singbox或被阻止${NC}"
  else
    echo -e "${YELLOW}过滤规则已存在: Docker流量必须通过singbox或被阻止${NC}"
  fi
  
  # 同样应用到OUTPUT链，确保容器内的流量也受限制
  if ! iptables -t filter -C OUTPUT -s $DOCKER_SUBNET -j DOCKER_FILTER 2>/dev/null; then
    iptables -t filter -A OUTPUT -s $DOCKER_SUBNET -j DOCKER_FILTER
    echo -e "${GREEN}已添加过滤规则: 来自Docker子网的流量必须通过singbox或被阻止${NC}"
  else
    echo -e "${YELLOW}过滤规则已存在: 来自Docker子网的流量必须通过singbox或被阻止${NC}"
  fi
}

# 移除透明代理规则
function remove_transparent_proxy() {
  echo -e "${BLUE}移除透明代理规则...${NC}"
  
  # 移除PREROUTING规则
  if iptables -t nat -C PREROUTING -i $DOCKER_INTERFACE -p tcp -j DOCKER_PROXY 2>/dev/null; then
    iptables -t nat -D PREROUTING -i $DOCKER_INTERFACE -p tcp -j DOCKER_PROXY
    echo -e "${GREEN}已移除规则: 将来自$DOCKER_INTERFACE的TCP流量重定向到DOCKER_PROXY${NC}"
  fi
  
  # 移除OUTPUT规则
  if iptables -t nat -C OUTPUT -s $DOCKER_SUBNET -p tcp -j DOCKER_PROXY 2>/dev/null; then
    iptables -t nat -D OUTPUT -s $DOCKER_SUBNET -p tcp -j DOCKER_PROXY
    echo -e "${GREEN}已移除规则: 将来自$DOCKER_SUBNET的出站TCP流量重定向到DOCKER_PROXY${NC}"
  fi
  
  # 移除过滤规则
  if iptables -t filter -C FORWARD -i $DOCKER_INTERFACE -j DOCKER_FILTER 2>/dev/null; then
    iptables -t filter -D FORWARD -i $DOCKER_INTERFACE -j DOCKER_FILTER
    echo -e "${GREEN}已移除过滤规则: Docker流量必须通过singbox或被阻止${NC}"
  fi
  
  if iptables -t filter -C OUTPUT -s $DOCKER_SUBNET -j DOCKER_FILTER 2>/dev/null; then
    iptables -t filter -D OUTPUT -s $DOCKER_SUBNET -j DOCKER_FILTER
    echo -e "${GREEN}已移除过滤规则: 来自Docker子网的流量必须通过singbox或被阻止${NC}"
  fi
  
  # 清空并删除自定义链
  iptables -t nat -F DOCKER_PROXY 2>/dev/null || true
  iptables -t nat -X DOCKER_PROXY 2>/dev/null || true
  iptables -t filter -F DOCKER_FILTER 2>/dev/null || true
  iptables -t filter -X DOCKER_FILTER 2>/dev/null || true
  
  # 删除辅助脚本和cron任务
  rm -f /usr/local/bin/check_singbox_running.sh
  rm -f /etc/cron.d/check_singbox
  
  # 移除相关的crontab条目
  crontab -l 2>/dev/null | grep -v "check_singbox_running.sh" | crontab -
  
  echo -e "${GREEN}已移除所有透明代理规则${NC}"
}

# 显示当前透明代理规则
function show_transparent_proxy_rules() {
  echo -e "${BLUE}当前透明代理规则:${NC}"
  echo -e "${YELLOW}NAT表中的DOCKER_PROXY链:${NC}"
  iptables -t nat -L DOCKER_PROXY -n --line-numbers 2>/dev/null || echo "DOCKER_PROXY链不存在"
  
  echo -e "\n${YELLOW}NAT表中的PREROUTING链:${NC}"
  iptables -t nat -L PREROUTING -n --line-numbers | grep DOCKER_PROXY || echo "PREROUTING中没有DOCKER_PROXY规则"
  
  echo -e "\n${YELLOW}NAT表中的OUTPUT链:${NC}"
  iptables -t nat -L OUTPUT -n --line-numbers | grep DOCKER_PROXY || echo "OUTPUT中没有DOCKER_PROXY规则"
  
  echo -e "\n${YELLOW}过滤表中的DOCKER_FILTER链:${NC}"
  iptables -t filter -L DOCKER_FILTER -n --line-numbers 2>/dev/null || echo "DOCKER_FILTER链不存在"
  
  echo -e "\n${YELLOW}过滤表中的FORWARD链:${NC}"
  iptables -t filter -L FORWARD -n --line-numbers | grep DOCKER_FILTER || echo "FORWARD中没有DOCKER_FILTER规则"
  
  echo -e "\n${YELLOW}过滤表中的OUTPUT链:${NC}"
  iptables -t filter -L OUTPUT -n --line-numbers | grep DOCKER_FILTER || echo "OUTPUT中没有DOCKER_FILTER规则"
  
  echo -e "\n${YELLOW}自动检查脚本状态:${NC}"
  if [ -f /usr/local/bin/check_singbox_running.sh ]; then
    echo -e "${GREEN}自动检查脚本已安装${NC}"
    if /usr/local/bin/check_singbox_running.sh; then
      echo -e "${GREEN}singbox服务正在运行，Docker可以访问网络${NC}"
    else
      echo -e "${RED}singbox服务未运行，Docker网络连接应被阻止${NC}"
    fi
  else
    echo -e "${RED}自动检查脚本未安装${NC}"
  fi
  
  echo -e "\n${YELLOW}cron任务状态:${NC}"
  if [ -f /etc/cron.d/check_singbox ]; then
    echo -e "${GREEN}cron任务已配置${NC}"
    cat /etc/cron.d/check_singbox
  else
    crontab -l | grep check_singbox_running.sh || echo -e "${RED}cron任务未配置${NC}"
  fi
}

# 检查Docker和singbox状态
function check_status() {
  echo -e "${BLUE}检查Docker状态:${NC}"
  if systemctl is-active --quiet docker; then
    echo -e "${GREEN}Docker服务正在运行${NC}"
  else
    echo -e "${RED}Docker服务未运行${NC}"
  fi
  
  echo -e "\n${BLUE}检查singbox状态:${NC}"
  if ss -lntp | grep -q ":$PROXY_PORT"; then
    echo -e "${GREEN}singbox服务正在运行在端口 $PROXY_PORT${NC}"
    echo -e "${GREEN}Docker容器可以访问网络${NC}"
  else
    echo -e "${RED}在端口 $PROXY_PORT 上未检测到singbox服务${NC}"
    echo -e "${RED}Docker容器无法访问网络（如果启用了透明代理）${NC}"
  fi
  
  echo -e "\n${BLUE}检查IP转发状态:${NC}"
  if [ "$(cat /proc/sys/net/ipv4/ip_forward)" -eq "1" ]; then
    echo -e "${GREEN}IP转发已启用${NC}"
  else
    echo -e "${RED}IP转发未启用${NC}"
  fi
  
  echo -e "\n${BLUE}Docker网络信息:${NC}"
  ip addr show $DOCKER_INTERFACE || echo -e "${RED}找不到Docker接口 $DOCKER_INTERFACE${NC}"
  
  echo -e "\n${BLUE}透明代理规则状态:${NC}"
  if iptables -t nat -L DOCKER_PROXY -n &>/dev/null && iptables -t filter -L DOCKER_FILTER -n &>/dev/null; then
    echo -e "${GREEN}透明代理规则已配置（严格模式：当singbox关闭时Docker将断网）${NC}"
    show_transparent_proxy_rules
  elif iptables -t nat -L DOCKER_PROXY -n &>/dev/null; then
    echo -e "${YELLOW}仅配置了基本透明代理规则（无严格模式）${NC}"
    show_transparent_proxy_rules
  else
    echo -e "${RED}透明代理规则未配置${NC}"
  fi
}

# 测试透明代理
function test_proxy() {
  echo -e "${BLUE}测试透明代理...${NC}"
  
  # 首先检查设置是否已启用
  if ! iptables -t nat -L DOCKER_PROXY -n &>/dev/null; then
    echo -e "${RED}透明代理规则未配置. 请先使用 'setup' 选项${NC}"
    exit 1
  fi
  
  # 检查singbox是否运行
  echo -e "${YELLOW}检查singbox状态...${NC}"
  if ss -lntp | grep -q ":$PROXY_PORT"; then
    echo -e "${GREEN}singbox服务正在运行，测试网络连接...${NC}"
    
    # 创建一个临时测试容器
    echo -e "创建测试容器..."
    docker run --rm -it --name proxy_test alpine sh -c "apk add --no-cache curl && curl -s http://ipinfo.io/json && echo"
    
    echo -e "\n${YELLOW}测试完成.${NC}"
    echo -e "${BLUE}如果看到的IP是singbox代理IP，则说明透明代理配置成功${NC}"
    
    # 测试严格模式
    echo -e "\n${YELLOW}现在测试严格模式 - 请临时停止singbox服务:${NC}"
    echo -e "${RED}注意: 这将导致测试容器无法访问网络，这是预期的行为!${NC}"
    echo -e "请在新的终端窗口中运行: sudo systemctl stop singbox 或 /home/<USER>/api/local-singbox/scripts/singbox_service.sh stop"
    echo -e "然后按任意键继续测试..."
    read -n 1 -s
    
    echo -e "\n${YELLOW}测试singbox停止后的网络连接...${NC}"
    docker run --rm -it --name proxy_test alpine sh -c "apk add --no-cache curl && echo '尝试连接...' && curl -m 5 -s http://ipinfo.io/json || echo '无法连接 - 这是正确的行为，说明严格模式有效'"
    
    echo -e "\n${RED}重要: 请记得重新启动singbox服务:${NC}"
    echo -e "sudo systemctl start singbox 或 /home/<USER>/api/local-singbox/scripts/singbox_service.sh start"
  else
    echo -e "${RED}singbox服务未运行. 如果已配置严格模式，Docker容器将无法访问网络${NC}"
    echo -e "${RED}这是预期的行为 - 确认singbox必须运行，Docker容器才能访问网络${NC}"
    echo -e "\n${YELLOW}尝试从容器访问网络（应该失败）...${NC}"
    
    docker run --rm -it --name proxy_test alpine sh -c "apk add --no-cache curl && echo '尝试连接...' && curl -m 5 -s http://ipinfo.io/json || echo '无法连接 - 这是正确的行为，说明严格模式有效'"
    
    echo -e "\n${YELLOW}测试完成. 如果无法连接，说明严格模式配置正确${NC}"
    echo -e "${RED}请启动singbox服务以恢复Docker容器的网络连接:${NC}"
    echo -e "sudo systemctl start singbox 或 /home/<USER>/api/local-singbox/scripts/singbox_service.sh start"
  fi
}

# 使用说明
function show_usage() {
  echo -e "${BLUE}Docker透明代理配置工具 - 强制模式${NC}"
  echo -e "使用方法: $0 [选项]"
  echo -e "选项:"
  echo -e "  ${GREEN}setup${NC}    设置严格透明代理规则，强制Docker容器只能通过singbox访问网络"
  echo -e "  ${GREEN}remove${NC}   移除透明代理规则"
  echo -e "  ${GREEN}status${NC}   显示当前透明代理状态"
  echo -e "  ${GREEN}rules${NC}    显示当前已配置的iptables规则"
  echo -e "  ${GREEN}test${NC}     测试透明代理和严格模式是否工作"
  echo -e "  ${GREEN}help${NC}     显示此帮助信息"
  echo -e "\n${RED}注意:${NC} 在严格模式下，如果singbox服务停止，Docker容器将完全无法访问网络"
}

# 主逻辑
case "$1" in
  setup)
    check_singbox
    save_current_rules
    enable_forwarding
    setup_transparent_proxy
    echo -e "${GREEN}严格透明代理设置完成.${NC}"
    echo -e "${RED}重要提示:${NC} 所有Docker容器流量将强制通过singbox代理."
    echo -e "${RED}如果singbox服务停止，Docker容器将无法访问网络.${NC}"
    ;;
  remove)
    save_current_rules
    remove_transparent_proxy
    echo -e "${GREEN}透明代理规则已移除${NC}"
    ;;
  status)
    check_status
    ;;
  rules)
    show_transparent_proxy_rules
    ;;
  test)
    test_proxy
    ;;
  help|*)
    show_usage
    ;;
esac

exit 0
