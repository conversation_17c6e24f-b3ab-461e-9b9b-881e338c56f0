#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import json
import logging
from logging.handlers import RotatingFileHandler
import requests
import subprocess
import threading
import schedule
import signal
import re
import urllib.parse
import socket
from datetime import datetime
from collections import Counter

# 配置日志
LOG_DIR = "/home/<USER>/api/local-singbox/logs"
LOG_FILE = os.path.join(LOG_DIR, "integrated_monitor.log")
os.makedirs(LOG_DIR, exist_ok=True)

# 设置带轮转的日志处理器
file_handler = RotatingFileHandler(
    LOG_FILE,
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,          # 保留5个备份
    encoding='utf-8'
)
console_handler = logging.StreamHandler()

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        file_handler,
        console_handler
    ]
)
logger = logging.getLogger(__name__)

# 记录启动信息
logger.info("启动集成监控服务（带日志轮转功能）...")

#====================================================================
# 共享配置
#====================================================================

# Telegram 配置
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "235196660"

# 代理配置
PROXY_HOST = "127.0.0.1"
PROXY_PORT = 1080
PROXIES = {
    "http": f"socks5://{PROXY_HOST}:{PROXY_PORT}",
    "https": f"socks5://{PROXY_HOST}:{PROXY_PORT}"
}

# IP信息API
IP_INFO_API = "http://ip-api.com/json/{}"

# 状态信息
current_ip = "未知"
current_location = "未知"

#====================================================================
# 节点切换相关配置
#====================================================================

# sing-box Clash API 配置
CLASH_API_URL = "http://127.0.0.1:9090"  # sing-box 的 Clash API 地址
PROXY_GROUPS = ["proxy"]  # 要切换的代理组名称
SWITCH_INTERVAL = 600  # 切换间隔（秒）
MAX_RETRIES = 3  # 最大重试次数
MAX_LATENCY = 300  # 最大延迟（毫秒），超过此值的节点将被过滤

# 节点使用计数
node_usage_counter = Counter()
node_switch_running = True

#====================================================================
# 节点切换相关功能
#====================================================================

def get_all_proxies():
    """获取所有代理信息"""
    for retry in range(MAX_RETRIES):
        try:
            url = f"{CLASH_API_URL}/proxies"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取代理信息失败: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"获取代理信息失败 (尝试 {retry+1}/{MAX_RETRIES}): {e}")
        
        time.sleep(2)
    return None

def get_current_node(group):
    """获取当前使用的节点"""
    for retry in range(MAX_RETRIES):
        try:
            url = f"{CLASH_API_URL}/proxies/{group}"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get("now", "未知")
        except Exception as e:
            logger.error(f"获取当前节点失败 (尝试 {retry+1}/{MAX_RETRIES}): {e}")
        
        time.sleep(2)
    return "未知"

def switch_node(group, node):
    """切换到指定节点"""
    for retry in range(MAX_RETRIES):
        try:
            url = f"{CLASH_API_URL}/proxies/{group}"
            data = {"name": node}
            response = requests.put(url, json=data, timeout=5)
            if response.status_code == 204:
                logger.info(f"成功切换 {group} 到节点 {node}")
                return True
            else:
                logger.error(f"切换节点失败: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"切换节点失败 (尝试 {retry+1}/{MAX_RETRIES}): {e}")
        
        time.sleep(2)
    return False

def test_node_latency(node):
    """测试节点延迟"""
    for retry in range(MAX_RETRIES):
        try:
            url = f"{CLASH_API_URL}/proxies/{node}/delay"
            params = {"timeout": 5000, "url": "http://www.gstatic.com/generate_204"}
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                return data.get("delay", -1)
            else:
                logger.warning(f"测试节点 {node} 延迟失败: HTTP {response.status_code}")
        except Exception as e:
            logger.warning(f"测试节点 {node} 延迟失败: {e}")
        
        time.sleep(1)
    return -1

def wait_for_api_ready():
    """等待API就绪"""
    for i in range(10):
        try:
            response = requests.get(f"{CLASH_API_URL}/configs", timeout=5)
            if response.status_code == 200:
                logger.info("Clash API 就绪")
                return True
        except Exception:
            pass
        
        logger.info(f"等待Clash API就绪... ({i+1}/10)")
        time.sleep(2)
    
    logger.error("Clash API 未就绪")
    return False

def select_next_node(current_node, all_nodes):
    """选择下一个节点，确保平均切换并过滤高延迟节点"""
    if not all_nodes:
        logger.error("没有可用节点")
        return None
    
    # 过滤掉当前节点
    available_nodes = [node for node in all_nodes if node != current_node]
    if not available_nodes:
        logger.warning("只有一个节点可用")
        return current_node
    
    # 获取所有节点的延迟
    latencies = {}
    for node in available_nodes[:10]:  # 最多测试10个节点以节省时间
        latency = test_node_latency(node)
        if latency > 0 and latency < MAX_LATENCY:
            latencies[node] = latency
    
    # 如果没有可用节点，尝试所有节点
    if not latencies:
        logger.warning("所有测试的节点延迟都过高，随机选择一个节点")
        import random
        return random.choice(available_nodes)
    
    # 按使用次数排序，优先使用使用次数最少的节点
    sorted_nodes = sorted(latencies.keys(), key=lambda x: (node_usage_counter[x], latencies[x]))
    
    # 选择使用最少且延迟最低的节点
    next_node = sorted_nodes[0]
    node_usage_counter[next_node] += 1
    
    logger.info(f"选择节点 {next_node}，延迟: {latencies[next_node]}ms，已使用: {node_usage_counter[next_node]}次")
    return next_node

def node_switch_thread():
    """节点切换线程"""
    global node_switch_running, current_ip, current_location
    
    logger.info("启动节点切换服务...")
    
    # 等待API就绪
    if not wait_for_api_ready():
        send_telegram_message("❌ <b>Singbox代理服务启动失败</b>\n\n无法连接到Clash API，请检查服务是否正常运行")
        return
    
    # 获取当前IP
    current_ip, current_location = get_current_ip()
    
    # 获取初始节点信息
    current_nodes = {}
    available_nodes = []
    
    for group in PROXY_GROUPS:
        current_nodes[group] = get_current_node(group)
        
        # 获取所有节点
        proxies_info = get_all_proxies()
        if proxies_info and "proxies" in proxies_info:
            group_info = proxies_info["proxies"].get(group, {})
            available_nodes = group_info.get("all", [])
        
        logger.info(f"代理组 {group} 当前使用节点: {current_nodes[group]}")
    
    # 发送初始状态通知
    initial_message = f"""📡 <b>Singbox代理服务状态</b>

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
当前IP: {current_ip} ({current_location})"""

    for group in PROXY_GROUPS:
        initial_message += f"\n代理组 {group} 使用节点: {current_nodes[group]}"
    
    initial_message += f"\n\n可用节点总数: {len(available_nodes)}"
    send_telegram_message(initial_message)
    
    # 记录上一次IP，用于检测变化
    last_ip = current_ip
    
    # 主循环
    while node_switch_running:
        try:
            for group in PROXY_GROUPS:
                # 获取当前节点
                current_node = get_current_node(group)
                current_nodes[group] = current_node
                
                # 获取可用节点列表
                all_nodes = []
                proxies_info = get_all_proxies()
                if proxies_info and "proxies" in proxies_info:
                    group_info = proxies_info["proxies"].get(group, {})
                    all_nodes = group_info.get("all", [])
                
                if not all_nodes:
                    logger.warning("未找到可用节点，跳过本次切换")
                    continue
                
                # 选择下一个节点
                next_node = select_next_node(current_node, all_nodes)
                
                if not next_node:
                    logger.warning(f"没有可用的节点可供切换")
                    continue
                
                logger.info(f"准备将 {group} 从 {current_node} 切换到 {next_node}")
                
                # 切换节点
                if switch_node(group, next_node):
                    # 等待切换生效
                    time.sleep(2)
                    
                    # 获取新IP
                    new_ip, new_location = get_current_ip()
                    logger.info(f"切换后出口IP: {new_ip} ({new_location})")
                    
                    # 检测IP是否变化
                    if new_ip != last_ip and new_ip != "未知" and last_ip != "未知":
                        logger.info(f"IP已变化: {last_ip} -> {new_ip} ({new_location})")
                        
                        # 发送IP变化通知
                        ip_change_message = f"""🔄 <b>IP已切换</b>

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
原IP: {last_ip} ({current_location})
新IP: {new_ip} ({new_location})
节点: {current_node} -> {next_node}
延迟: {test_node_latency(next_node)}ms"""
                        send_telegram_message(ip_change_message)
                        
                        # 更新当前IP和位置信息
                        current_ip = new_ip
                        current_location = new_location
                        last_ip = new_ip
                    
                    # 更新当前节点
                    current_nodes[group] = next_node
                    
                    # 输出节点使用统计
                    if len(node_usage_counter) > 0:
                        total_switches = sum(node_usage_counter.values())
                        logger.info(f"节点切换统计: 总切换次数={total_switches}, 已使用节点数={len(node_usage_counter)}")
                        top_nodes = node_usage_counter.most_common(3)
                        logger.info(f"使用最多的节点: {top_nodes}")
            
            # 等待下一次切换
            logger.info(f"等待 {SWITCH_INTERVAL} 秒后进行下一次切换...")
            for i in range(SWITCH_INTERVAL):
                if not node_switch_running:
                    break
                time.sleep(1)
                
        except Exception as e:
            logger.error(f"节点切换时出错: {e}")
            send_telegram_message(f"⚠️ <b>Singbox代理服务异常</b>\n\n错误信息: {str(e)}\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            time.sleep(5)
    
    logger.info("节点切换服务已停止")

#====================================================================
# API监控相关配置
#====================================================================

# API配置
API_HOST = "127.0.0.1"
API_PORT = 80
API_URL = f"http://{API_HOST}:{API_PORT}"
TEST_ENDPOINT = f"{API_URL}/api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo"

# TikHub API配置
TIKHUB_API_URL = "https://beta.tikhub.io/api/v1/douyin/web/fetch_douyin_web_guest_cookie"
TIKHUB_API_KEY = "kqNoW3xz9Ccnpwk8jzO4wHEP/hQ0osX2vZx44CW4sWB9rXoTWORd2z2UMg=="
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36"

# API服务配置
SYSTEMD_SERVICE_NAME = "Douyin_TikTok_Download_API"
COOKIE_CONFIG_PATH = "/home/<USER>/Douyin_TikTok_Download_API/crawlers/douyin/web/config.yaml"  # 本地API配置文件路径
COOKIE_UPDATE_COOLDOWN = 60  # Cookie更新冷却时间（60秒）

# API状态信息
last_cookie_update = None
api_status = "未知"
cookie_status = "未知"
retry_count = 0
MAX_RETRY_ATTEMPTS = 5  # 将最大重试次数从3增加到5
ERROR_THRESHOLD = 3  # 错误阈值
ERROR_WINDOW = 60  # 错误计数窗口期（秒）
error_count = {}  # 恢复被误删的error_count字典定义
error_timestamps = {}  # 记录每种错误的时间戳
docker_log_monitor_running = True

#====================================================================
# API监控相关功能
#====================================================================

def check_api_status():
    """检查API状态"""
    global api_status, cookie_status
    try:
        # 首先检查systemd服务是否运行
        check_service = subprocess.run(
            ["systemctl", "is-active", SYSTEMD_SERVICE_NAME],
            capture_output=True, text=True
        )
        
        if check_service.returncode != 0:
            logger.error(f"服务 {SYSTEMD_SERVICE_NAME} 未运行")
            api_status = "未运行"
            return False
            
        # 检查服务是否响应
        response = requests.get(TEST_ENDPOINT, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status_code") == 0:
                logger.info("API状态正常")
                api_status = "正常"
                return True
            else:
                # 当API返回非零状态码时，记录警告但仍视为可用状态
                error_msg = data.get('status_msg', '未知错误')
                logger.warning(f"API返回非零状态码: {error_msg}")
                
                # 只有在明确错误的情况下才标记为异常，否则视为正常
                if error_msg == "未知错误" or "内容为空" in error_msg:
                    logger.info("API响应内容为空，但服务可正常运行")
                    api_status = "正常 - 响应空"
                    return True
                else:
                    api_status = f"异常 - {error_msg}"
                    return False
        else:
            logger.warning(f"API返回非200状态码: {response.status_code}")
            api_status = f"异常 - HTTP {response.status_code}"
            return False
    except requests.exceptions.Timeout:
        logger.error("API请求超时")
        api_status = "异常 - 请求超时"
        return False
    except requests.exceptions.ConnectionError:
        # 连接错误通常意味着服务未运行
        logger.error("API连接错误")
        api_status = "未运行"
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"API请求异常: {e}")
        api_status = f"异常 - {str(e)}"
        return False
    except Exception as e:
        logger.error(f"检查API状态时出错: {e}")
        api_status = f"异常 - {str(e)}"
        return False

def fetch_new_cookie():
    """从TikHub获取新Cookie"""
    global cookie_status, retry_count

    if retry_count >= MAX_RETRY_ATTEMPTS:
        logger.error(f"达到最大重试次数 ({MAX_RETRY_ATTEMPTS})，停止尝试")
        cookie_status = "获取失败 - 达到最大重试次数"
        return None

    retry_count += 1
    logger.info(f"尝试获取新Cookie (尝试 {retry_count}/{MAX_RETRY_ATTEMPTS})...")
    
    try:
        # 构建请求URL
        encoded_user_agent = urllib.parse.quote(USER_AGENT)
        request_url = f"{TIKHUB_API_URL}?user_agent={encoded_user_agent}"
        
        logger.info(f"正在请求TikHub API获取新Cookie: {request_url}")
        
        # 发送请求
        headers = {
            "User-Agent": USER_AGENT,
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {TIKHUB_API_KEY}"  # 使用正确的Bearer授权格式
        }
        
        logger.info(f"TikHub API请求头: {headers}")
        
        # 尝试直接请求，不使用代理
        logger.info("尝试直接请求TikHub API (不使用代理)...")
        response = requests.get(request_url, headers=headers, timeout=30)
        
        logger.info(f"TikHub API响应状态码: {response.status_code}")
        logger.info(f"TikHub API响应头: {response.headers}")
        logger.info(f"TikHub API响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200 and "data" in data:
                # 尝试获取Cookie，注意大小写
                cookie = data["data"].get("Cookie", "") or data["data"].get("cookie", "")
                if cookie:
                    logger.info("成功获取新Cookie")
                    cookie_status = "获取成功"
                    retry_count = 0  # 重置重试计数
                    return cookie
                else:
                    logger.error(f"API返回的数据中没有Cookie字段，返回数据: {data['data']}")
                    cookie_status = "获取失败 - 返回数据中无Cookie"
            else:
                logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                cookie_status = f"获取失败 - {data.get('message', '未知错误')}"
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            cookie_status = f"获取失败 - HTTP {response.status_code}"
        
        return None
    except Exception as e:
        logger.error(f"获取Cookie时出错: {e}")
        cookie_status = f"获取失败 - {str(e)}"
        return None

def update_api_config(cookie):
    """更新API配置文件中的Cookie"""
    global cookie_status
    
    if not cookie:
        logger.error("Cookie为空，无法更新配置")
        cookie_status = "更新失败 - Cookie为空"
        return False
    
    try:
        # 检查API服务是否在运行
        check_cmd = f"systemctl is-active {SYSTEMD_SERVICE_NAME}"
        status = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
        
        if status.returncode != 0:
            logger.error(f"服务 {SYSTEMD_SERVICE_NAME} 未运行")
            cookie_status = "更新失败 - 服务未运行"
            return False
        
        # 直接读取本地配置文件
        if not os.path.exists(COOKIE_CONFIG_PATH):
            logger.error(f"配置文件不存在: {COOKIE_CONFIG_PATH}")
            cookie_status = "更新失败 - 配置文件不存在"
            return False
            
        with open(COOKIE_CONFIG_PATH, 'r') as f:
            current_config = f.read()
        
        # 解析YAML
        import yaml
        config = yaml.safe_load(current_config)
        
        # 更新Cookie - 适配嵌套结构
        try:
            if "TokenManager" in config and "douyin" in config["TokenManager"] and "headers" in config["TokenManager"]["douyin"]:
                config["TokenManager"]["douyin"]["headers"]["Cookie"] = cookie
                logger.info("已找到正确的Cookie配置路径：TokenManager.douyin.headers.Cookie")
            else:
                logger.error(f"配置文件结构不符合预期，无法更新Cookie。当前配置结构: {config}")
                cookie_status = "更新失败 - 配置文件结构不符合预期"
                return False
        except Exception as e:
            logger.error(f"更新Cookie时出错: {e}")
            cookie_status = f"更新失败 - {str(e)}"
            return False
        
        # 转换回YAML
        new_config = yaml.dump(config, default_flow_style=False)
        
        # 直接写入配置文件
        with open(COOKIE_CONFIG_PATH, "w") as f:
            f.write(new_config)
        
        logger.info("Cookie更新成功")
        cookie_status = "更新成功"
        return True
    except Exception as e:
        logger.error(f"更新配置文件时出错: {e}")
        cookie_status = f"更新失败 - {str(e)}"
        return False

def restart_api_service():
    """重启API服务"""
    try:
        logger.info(f"正在重启服务: {SYSTEMD_SERVICE_NAME}")
        
        # 使用systemctl重启服务
        restart_cmd = f"sudo systemctl restart {SYSTEMD_SERVICE_NAME}"
        result = subprocess.run(restart_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"服务 {SYSTEMD_SERVICE_NAME} 重启指令发送成功")
            
            # 等待服务启动
            logger.info("等待API服务完全初始化 (15秒)...")
            time.sleep(15)
            
            # 检查服务是否正在运行
            check_cmd = f"systemctl is-active {SYSTEMD_SERVICE_NAME}"
            status = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
            
            if status.returncode == 0:
                logger.info(f"服务 {SYSTEMD_SERVICE_NAME} 成功运行")
                return True
            else:
                logger.error(f"重启后服务 {SYSTEMD_SERVICE_NAME} 未运行")
                return False
        else:
            logger.error(f"重启服务失败: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"重启API服务时出错: {e}")
        return False

def update_cookie_and_restart():
    """更新Cookie并重启容器"""
    global last_cookie_update, cookie_status
    
    # 检查冷却时间
    if last_cookie_update and (datetime.now() - last_cookie_update).total_seconds() < COOKIE_UPDATE_COOLDOWN:
        logger.info(f"Cookie更新冷却中，跳过本次更新 (上次更新: {last_cookie_update.strftime('%Y-%m-%d %H:%M:%S')})")
        return False
    
    # 构建API错误消息
    api_error_message = f"""⚠️ <b>抖音API服务异常</b>

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
状态: {api_status}
当前IP: {current_ip} ({current_location})

正在尝试更新Cookie并重启服务...
"""
    send_telegram_message(api_error_message)
    
    # 获取新Cookie
    new_cookie = fetch_new_cookie()
    if not new_cookie:
        send_telegram_message(f"❌ <b>抖音API服务更新失败</b>\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n原因: 无法从TikHub获取新Cookie")
        return False
    
    # 更新配置文件
    if not update_api_config(new_cookie):
        send_telegram_message(f"❌ <b>抖音API服务更新失败</b>\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n原因: 无法更新配置文件")
        return False
    
    # 重启容器
    restart_message = f"""🔄 <b>正在重启抖音API服务</b>

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
原因: Cookie已更新
"""
    send_telegram_message(restart_message)
    
    if not restart_api_service():
        send_telegram_message(f"❌ <b>抖音API服务更新失败</b>\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n原因: 无法重启API服务")
        return False
    
    # 等待服务启动
    logger.info("等待API服务完全初始化 (30秒)...")
    time.sleep(30)
    
    # 检查API状态
    if check_api_status():
        cookie_success_message = f"""✅ <b>抖音API服务已恢复正常</b>

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
当前IP: {current_ip}
Cookie: 已更新
"""
        send_telegram_message(cookie_success_message)
        last_cookie_update = datetime.now()
        return True
    else:
        send_telegram_message(f"⚠️ <b>抖音API服务仍然异常</b>\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n原因: 更新Cookie后API仍无法正常工作")
        return False

def monitor_service_logs():
    """监控systemd服务日志，检测API错误"""
    global error_count
    
    try:
        # 检查服务是否在运行
        check_cmd = f"systemctl is-active {SYSTEMD_SERVICE_NAME}"
        status = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
        
        if status.returncode != 0:
            logger.error(f"服务 {SYSTEMD_SERVICE_NAME} 未运行，无法监控日志")
            return False
        
        # 获取服务日志
        logs_cmd = f"journalctl -u {SYSTEMD_SERVICE_NAME} -n 50 --no-pager"
        logs = subprocess.check_output(logs_cmd, shell=True).decode()
        
        # 过滤日志中的错误
        error_patterns = [
            "Error: ", 
            "ERROR ", 
            "Unauthorized", 
            "400 Bad Request",
            "401 Unauthorized",
            "403 Forbidden",
            "404 Not Found",
            "429 Too Many Requests",
            "500 Internal Server Error",
            "502 Bad Gateway",
            "503 Service Unavailable",
            "504 Gateway Timeout",
            "Connection refused",
            "timeout",
            "代理服务器连接失败",
            "没有权限",
            "AttributeError",
            "KeyError",
            "ModuleNotFoundError"
        ]
        
        now = datetime.now()
        error_lines = []
        for line in logs.split('\n'):
            for pattern in error_patterns:
                if pattern in line:
                    error_lines.append(line)
                    error_msg = pattern
                    # 记录错误发生时间
                    if pattern not in error_count:
                        error_count[pattern] = []
                    error_count[pattern].append(now)
                    
                    # 发送错误通知
                    logger.warning(f"检测到错误: '{error_msg}'")
                    break
        
        # 清理超过窗口期的错误记录
        for pattern in list(error_count.keys()):
            error_count[pattern] = [t for t in error_count[pattern] if (now - t).total_seconds() <= ERROR_WINDOW]
            if not error_count[pattern]:
                del error_count[pattern]
        
        # 触发Cookie更新
        for pattern, timestamps in error_count.items():
            if len(timestamps) >= ERROR_THRESHOLD:
                logger.warning(f"错误 '{pattern}' 在{ERROR_WINDOW}秒内出现{len(timestamps)}次，开始更新Cookie...")
                send_telegram_message(f"🔄 <b>开始更新Cookie</b>\n\n原因: 错误 '{pattern}' 在{ERROR_WINDOW}秒内出现{len(timestamps)}次")
                
                # 触发更新
                if update_cookie_and_restart():
                    logger.info("已更新Cookie并重启API服务")
                    # 清空错误计数
                    error_count = {}
                break
        
        return len(error_lines) > 0
    except Exception as e:
        logger.error(f"监控服务日志时出错: {e}")
        return False

def api_monitor_thread():
    """API监控线程"""
    global api_monitor_running, api_status
    
    logger.info("开始API监控线程")
    
    # 初始化检查API状态
    try:
        # 检查服务是否运行
        check_cmd = f"systemctl is-active {SYSTEMD_SERVICE_NAME}"
        status = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
        
        if status.returncode == 0:
            logger.info(f"服务 {SYSTEMD_SERVICE_NAME} 正在运行")
            api_status = "运行中"
        else:
            logger.error(f"服务 {SYSTEMD_SERVICE_NAME} 未运行")
            api_status = "未运行"
    except Exception as e:
        logger.error(f"检查API状态出错: {e}")
        api_status = "检查失败"
    
    # 主监控循环
    while api_monitor_running:
        try:
            # 检查API状态
            check_api_status()
            
            # 监控服务日志以检测问题
            monitor_service_logs()
            
            # 每60秒执行一次检查
            time.sleep(60)
        except Exception as e:
            logger.error(f"API监控线程出错: {e}")
            time.sleep(60)  # 出错时也等待一分钟再继续
    
    logger.info("API监控线程已停止")

def generate_status_report():
    """生成状态报告"""
    global current_ip, current_location, api_status, cookie_status
    
    try:
        # 获取当前IP和地理位置
        if current_ip == "未知":
            current_ip, current_location = get_current_ip()
        
        # 不再主动检查API状态
        # check_api_status()
        
        # 检查Docker容器状态
        docker_running = False
        api_running = False
        api_memory = "未知"
        
        try:
            # 检查Docker容器是否运行
            check_container = subprocess.run(
                ["docker", "ps", "-q", "-f", f"name={DOCKER_CONTAINER_NAME}"],
                capture_output=True, text=True
            )
            docker_running = bool(check_container.stdout.strip())
            
            # 检查容器内进程状态
            if docker_running:
                check_process = subprocess.run(
                    ["docker", "exec", DOCKER_CONTAINER_NAME, "pgrep", "-f", "python"],
                    capture_output=True, text=True
                )
                api_running = bool(check_process.stdout.strip())
                
                # 获取内存使用情况
                mem_info = subprocess.run(
                    ["docker", "stats", "--no-stream", "--format", "{{.MemPerc}}", DOCKER_CONTAINER_NAME],
                    capture_output=True, text=True
                )
                if mem_info.stdout:
                    api_memory = mem_info.stdout.strip()
        except Exception as e:
            logger.error(f"检查容器状态时出错: {e}")
        
        # 获取系统信息
        uptime = subprocess.run(["uptime"], capture_output=True, text=True).stdout.strip()
        
        # 获取Cookie信息
        try:
            # 检查容器内的Cookie配置文件是否存在
            if docker_running:
                check_cookie = subprocess.run(
                    ["docker", "exec", DOCKER_CONTAINER_NAME, "cat", COOKIE_CONFIG_PATH],
                    capture_output=True, text=True
                )
                
                # 如果容器内有Cookie配置文件，更新cookie_status
                if check_cookie.returncode == 0 and "ttwid" in check_cookie.stdout:
                    if cookie_status == "未知" or "获取失败" in cookie_status:
                        cookie_status = "有效"
        except Exception as e:
            logger.error(f"检查Cookie状态时出错: {e}")
        
        # 获取最后一次Cookie更新时间
        last_update = "从未"
        try:
            if docker_running:
                # 获取Cookie文件的修改时间
                check_time = subprocess.run(
                    ["docker", "exec", DOCKER_CONTAINER_NAME, "stat", "-c", "%y", COOKIE_CONFIG_PATH],
                    capture_output=True, text=True
                )
                if check_time.returncode == 0:
                    last_update = check_time.stdout.strip()
        except Exception as e:
            logger.error(f"获取Cookie更新时间时出错: {e}")
        
        # 构建状态消息
        status_message = f"""📊 <b>状态报告</b>

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

<b>网络状态:</b>
- 出口IP: {current_ip} ({current_location})
- 当前节点: {get_current_node("proxy")}

<b>服务状态:</b>
- API服务: {"✅ 运行中" if api_running else "❌ 未运行"}
- 代理服务: {"✅ 运行中" if check_singbox_running() else "❌ 未运行"}
- API响应: {api_status}

<b>Cookie状态:</b>
- 状态: {cookie_status}
- 最后更新: {last_update}

<b>系统信息:</b>
- API内存使用: {api_memory}
- 系统负载: {uptime.split("load average:")[1].strip() if "load average:" in uptime else "未知"}
"""
        
        logger.info("生成状态报告")
        send_telegram_message(status_message)

    except Exception as e:
        logger.error(f"生成状态报告时出错: {e}")

    # 生成初始状态报告
    generate_status_report()
    
    # 运行定时任务
    while docker_log_monitor_running:
        try:
            schedule.run_pending()
            time.sleep(1)
        except Exception as e:
            logger.error(f"运行API监控定时任务时出错: {e}")
            time.sleep(60)  # 出错后等待一分钟再继续
    
    logger.info("API监控服务已停止")

#====================================================================
# 共享函数
#====================================================================

def send_telegram_message(message):
    """发送Telegram消息"""
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    data = {
        "chat_id": TELEGRAM_CHAT_ID,
        "text": message,
        "parse_mode": "HTML"
    }
    try:
        response = requests.post(url, data=data, timeout=10)
        if response.status_code == 200:
            logger.info("Telegram消息发送成功")
        else:
            logger.error(f"Telegram消息发送失败: {response.text}")
    except Exception as e:
        logger.error(f"发送Telegram消息时出错: {e}")

def get_ip_location(ip):
    """获取IP地理位置信息"""
    try:
        response = requests.get(IP_INFO_API.format(ip), timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                country = data.get("country", "未知国家")
                city = data.get("city", "未知城市")
                isp = data.get("isp", "未知ISP")
                return f"{country} {city} ({isp})"
        return "未知"
    except Exception as e:
        logger.error(f"获取IP地理位置信息失败: {e}")
        return "未知"

def get_current_ip():
    """获取当前出口IP"""
    global current_ip, current_location
    
    for retry in range(MAX_RETRIES):
        try:
            # 使用代理获取当前IP
            session = requests.Session()
            session.proxies = PROXIES
            
            # 尝试多个IP信息服务
            services = [
                "https://api.ipify.org?format=json",
                "https://ipinfo.io/json",
                "https://ifconfig.me/all.json"
            ]
            
            for service_url in services:
                try:
                    response = session.get(service_url, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if "ip" in data:
                            new_ip = data.get("ip", "未知")
                            new_location = get_ip_location(new_ip)
                            
                            # 检查IP是否变化
                            if new_ip != current_ip and current_ip != "未知":
                                logger.info(f"IP已变化: {current_ip} -> {new_ip}")
                                send_telegram_message(f"🔄 <b>代理IP已变化</b>\n\n旧IP: {current_ip} ({current_location})\n新IP: {new_ip} ({new_location})")
                            
                            current_ip = new_ip
                            current_location = new_location
                            logger.info(f"当前出口IP: {current_ip} ({current_location})")
                            return current_ip, current_location
                        else:
                            continue
                except Exception as e:
                    logger.warning(f"服务 {service_url} 请求失败: {e}")
                    continue
            
            logger.warning(f"所有IP服务都失败，尝试 {retry+1}/{MAX_RETRIES}")
            time.sleep(2)
        except Exception as e:
            logger.error(f"获取当前IP失败 (尝试 {retry+1}/{MAX_RETRIES}): {e}")
            time.sleep(2)
    
    return "未知", "未知"

def check_singbox_running():
    """检查sing-box服务是否运行"""
    try:
        # 使用pgrep检查sing-box进程
        result = subprocess.run(
            ["pgrep", "-f", "sing-box"],
            capture_output=True, text=True
        )
        return bool(result.stdout.strip())
    except Exception as e:
        logger.error(f"检查sing-box状态时出错: {e}")
        return False

#====================================================================
# 主函数
#====================================================================

if __name__ == "__main__":
    # 启动节点切换线程
    node_switch_thread = threading.Thread(target=node_switch_thread)
    node_switch_thread.daemon = True
    node_switch_thread.start()
    logger.info("节点切换线程已启动")
    
    # 启动API监控线程
    api_monitor_thread = threading.Thread(target=api_monitor_thread)
    api_monitor_thread.daemon = True
    api_monitor_thread.start()
    logger.info("API监控线程已启动")
    
    # 等待线程结束
    while True:
        time.sleep(1)
