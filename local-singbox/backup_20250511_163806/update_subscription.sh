#!/bin/bash

# 订阅链接
SUBSCRIPTION_URL="https://global.tagonline.asia/api/v1/client/subscribe?token=9fe0baab7e2b181e70e93b39aecdd3cb"

# Telegram 配置
TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_CHAT_ID="235196660"

# 临时文件
TEMP_FILE="/tmp/subscription.txt"
CONFIG_FILE="/home/<USER>/api/local-singbox/config/config.json"
TEMP_CONFIG="/tmp/config.json"
LOG_FILE="/home/<USER>/api/local-singbox/logs/update_subscription.log"

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
    echo "$1"
}

# 发送Telegram消息
send_telegram_message() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -d "chat_id=${TELEGRAM_CHAT_ID}" \
        -d "text=${message}" \
        -d "parse_mode=HTML" > /dev/null
    
    if [ $? -eq 0 ]; then
        log "Telegram消息发送成功"
    else
        log "Telegram消息发送失败"
    fi
}

log "开始更新订阅..."
send_telegram_message "🔄 <b>开始更新订阅</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
订阅URL: ${SUBSCRIPTION_URL}"

# 下载订阅内容
log "正在从 $SUBSCRIPTION_URL 获取订阅..."
curl -s "$SUBSCRIPTION_URL" | base64 -d > "$TEMP_FILE"

if [ ! -s "$TEMP_FILE" ]; then
    log "错误: 获取订阅失败或内容为空"
    send_telegram_message "❌ <b>订阅更新失败</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
原因: 获取订阅失败或内容为空"
    exit 1
fi

log "成功获取订阅内容"

# 提取所有服务器信息
servers=$(grep -oE 'ss://[^[:space:]]+' "$TEMP_FILE")
server_count=$(echo "$servers" | wc -l)

if [ -z "$servers" ]; then
    log "错误: 未找到有效的 Shadowsocks 服务器信息"
    send_telegram_message "❌ <b>订阅更新失败</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
原因: 未找到有效的 Shadowsocks 服务器信息"
    exit 1
fi

log "找到 $server_count 个 Shadowsocks 服务器"

# 创建新的配置文件
cat > "$TEMP_CONFIG" << 'EOL'
{
  "log": {
    "level": "info",
    "timestamp": true
  },
  "experimental": {
    "clash_api": {
      "external_controller": "0.0.0.0:9090",
      "external_ui": "yacd",
      "secret": "",
      "default_mode": "rule"
    }
  },
  "inbounds": [
    {
      "type": "mixed",
      "tag": "mixed-in",
      "listen": "::",
      "listen_port": 1080,
      "sniff": true,
      "sniff_override_destination": true,
      "domain_strategy": "prefer_ipv4"
    }
  ],
  "outbounds": [
    {
      "type": "selector",
      "tag": "proxy",
      "outbounds": [],
      "default": "server1"
    },
    {
      "type": "urltest",
      "tag": "auto",
      "outbounds": [],
      "url": "https://www.gstatic.com/generate_204",
      "interval": "1m",
      "tolerance": 50
    },
EOL

# 处理每个服务器
index=1
outbounds=""

while IFS= read -r line; do
    if [[ $line =~ ss://([^@]+)@([^:]+):([0-9]+) ]]; then
        userinfo="${BASH_REMATCH[1]}"
        server="${BASH_REMATCH[2]}"
        port="${BASH_REMATCH[3]}"
        
        # 解码用户信息
        decoded=$(echo "$userinfo" | base64 -d)
        if [[ $decoded =~ ([^:]+):(.+) ]]; then
            method="${BASH_REMATCH[1]}"
            password="${BASH_REMATCH[2]}"
            
            # 添加到outbounds数组
            if [ -n "$outbounds" ]; then
                outbounds="$outbounds, "
            fi
            outbounds="$outbounds\"server$index\""
            
            # 写入服务器配置
            cat >> "$TEMP_CONFIG" << EOL
    {
      "type": "shadowsocks",
      "tag": "server$index",
      "server": "$server",
      "server_port": $port,
      "method": "$method",
      "password": "$password",
      "plugin": "obfs-local",
      "plugin_opts": "obfs=http;obfs-host=d5004ce2e61aa5459306.microsoft.com"
    },
EOL
            log "成功解析服务器 server$index: $server:$port"
            ((index++))
        fi
    fi
done <<< "$servers"

# 更新selector和auto outbounds列表
sed -i "s/\"outbounds\": \[\]/\"outbounds\": \[$outbounds\]/" "$TEMP_CONFIG"

# 添加direct出站
cat >> "$TEMP_CONFIG" << 'EOL'
    {
      "type": "direct",
      "tag": "direct"
    }
  ]
}
EOL

# 检查新配置文件是否有效
if [ -s "$TEMP_CONFIG" ]; then
    # 备份当前配置
    backup_path="${CONFIG_FILE}.bak.$(date '+%Y%m%d%H%M%S')"
    cp "$CONFIG_FILE" "$backup_path"
    log "已备份当前配置到 $backup_path"
    
    # 更新配置
    mv "$TEMP_CONFIG" "$CONFIG_FILE"
    log "配置文件已更新，共添加 $((index-1)) 个节点"
    
    # 重启 sing-box
    log "正在重启 sing-box 服务..."
    pid=$(pgrep -f '/home/<USER>/api/local-singbox/bin/sing-box')
    if [ -n "$pid" ]; then
        kill "$pid"
        sleep 2
    fi
    
    nohup /home/<USER>/api/local-singbox/bin/sing-box run -c "$CONFIG_FILE" > /home/<USER>/api/local-singbox/logs/singbox.log 2>&1 &
    
    if [ $? -eq 0 ]; then
        log "sing-box 服务已重启"
        log "订阅更新完成"
        
        # 发送成功通知
        send_telegram_message "✅ <b>订阅更新成功</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
节点数量: $((index-1))
服务状态: 已重启"
    else
        log "错误: 重启 sing-box 服务失败"
        send_telegram_message "⚠️ <b>订阅更新部分成功</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
节点数量: $((index-1))
服务状态: 重启失败"
        exit 1
    fi
else
    log "错误: 更新失败，配置文件为空"
    send_telegram_message "❌ <b>订阅更新失败</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
原因: 配置文件生成失败"
    exit 1
fi

# 清理临时文件
rm -f "$TEMP_FILE"
