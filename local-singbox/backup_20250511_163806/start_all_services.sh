#!/bin/bash
# 启动所有服务的脚本

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置路径
SINGBOX_DIR="/home/<USER>/api/local-singbox"
DOUYIN_API_DIR="/home/<USER>/douyin-api"
LOG_DIR="$SINGBOX_DIR/logs"
mkdir -p "$LOG_DIR"

# Telegram 配置
TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_CHAT_ID="235196660"

# 记录日志
log() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_DIR/start_services.log"
}

# 发送Telegram消息
send_telegram_message() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -d "chat_id=${TELEGRAM_CHAT_ID}" \
        -d "text=${message}" \
        -d "parse_mode=HTML" > /dev/null
    
    if [ $? -eq 0 ]; then
        log "${GREEN}Telegram消息发送成功${NC}"
    else
        log "${RED}Telegram消息发送失败${NC}"
    fi
}

# 检查并安装依赖
check_dependencies() {
    log "${YELLOW}检查依赖...${NC}"
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        log "${RED}未安装Python3，正在安装...${NC}"
        sudo apt-get update && sudo apt-get install -y python3 python3-pip
    fi
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log "${RED}未安装pip3，正在安装...${NC}"
        sudo apt-get install -y python3-pip
    fi
    
    # 检查Python依赖
    log "${YELLOW}安装Python依赖...${NC}"
    pip3 install requests schedule
    
    log "${GREEN}依赖检查完成${NC}"
}

# 启动sing-box服务
start_singbox() {
    log "${YELLOW}启动Singbox代理服务...${NC}"
    
    # 检查服务是否已在运行
    if pgrep -f "/home/<USER>/api/local-singbox/bin/sing-box" > /dev/null; then
        log "${YELLOW}Singbox代理服务已在运行，重启服务...${NC}"
        "$SINGBOX_DIR/scripts/manage_service.sh" restart
    else
        log "${YELLOW}启动Singbox代理服务...${NC}"
        "$SINGBOX_DIR/scripts/manage_service.sh" start
    fi
    
    # 检查服务是否成功启动
    sleep 2
    if pgrep -f "/home/<USER>/api/local-singbox/bin/sing-box" > /dev/null; then
        log "${GREEN}Singbox代理服务启动成功${NC}"
        return 0
    else
        log "${RED}Singbox代理服务启动失败${NC}"
        return 1
    fi
}

# 启动监控脚本
start_monitors() {
    log "${YELLOW}启动监控脚本...${NC}"
    
    # 检查Singbox监控脚本是否已在运行
    if pgrep -f "python3 /home/<USER>/api/local-singbox/scripts/integrated_monitor.py" > /dev/null; then
        log "${YELLOW}集成监控脚本已在运行，重启脚本...${NC}"
        pkill -f "python3 /home/<USER>/api/local-singbox/scripts/integrated_monitor.py"
        sleep 2
    fi
    
    # 启动集成监控脚本
    nohup python3 "$SINGBOX_DIR/scripts/integrated_monitor.py" > "$LOG_DIR/integrated_monitor.log" 2>&1 &
    
    # 检查脚本是否成功启动
    sleep 2
    if pgrep -f "python3 /home/<USER>/api/local-singbox/scripts/integrated_monitor.py" > /dev/null; then
        log "${GREEN}集成监控脚本启动成功${NC}"
        return 0
    else
        log "${RED}集成监控脚本启动失败${NC}"
        return 1
    fi
}

# 设置订阅更新定时任务
setup_subscription_cron() {
    log "${YELLOW}设置订阅更新定时任务...${NC}"
    
    # 检查定时任务是否已存在
    if crontab -l 2>/dev/null | grep -q "update_subscription.sh"; then
        log "${YELLOW}订阅更新定时任务已存在，更新...${NC}"
        (crontab -l 2>/dev/null | grep -v "update_subscription.sh" ; echo "0 4 * * * bash /home/<USER>/api/local-singbox/scripts/update_subscription.sh") | crontab -
    else
        log "${YELLOW}添加订阅更新定时任务...${NC}"
        (crontab -l 2>/dev/null ; echo "0 4 * * * bash /home/<USER>/api/local-singbox/scripts/update_subscription.sh") | crontab -
    fi
    
    log "${GREEN}订阅更新定时任务已设置${NC}"
    return 0
}

# 主函数
main() {
    log "===== 开始启动所有服务 ====="
    
    # 发送启动通知
    send_telegram_message "🚀 <b>开始启动所有服务</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
服务列表:
- Singbox代理服务
- 集成监控服务（包含节点切换和API监控）
- 订阅更新定时任务"
    
    # 检查依赖
    check_dependencies
    
    # 启动服务
    start_singbox
    singbox_status=$?
    
    start_monitors
    monitor_status=$?
    
    setup_subscription_cron
    cron_status=$?
    
    # 检查启动结果
    success=true
    services_status=""
    
    if [ $singbox_status -eq 0 ]; then
        services_status="${services_status}- Singbox代理服务: ✅ 成功\n"
    else
        services_status="${services_status}- Singbox代理服务: ❌ 失败\n"
        success=false
    fi
    
    if [ $monitor_status -eq 0 ]; then
        services_status="${services_status}- 集成监控服务: ✅ 成功\n"
    else
        services_status="${services_status}- 集成监控服务: ❌ 失败\n"
        success=false
    fi
    
    if [ $cron_status -eq 0 ]; then
        services_status="${services_status}- 定时任务: ✅ 成功"
    else
        services_status="${services_status}- 定时任务: ❌ 失败"
        success=false
    fi
    
    # 发送结果通知
    if $success; then
        log "${GREEN}所有服务启动成功${NC}"
        send_telegram_message "✅ <b>所有服务启动成功</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
服务状态:
$services_status"
    else
        log "${RED}部分服务启动失败${NC}"
        send_telegram_message "⚠️ <b>部分服务启动失败</b>

时间: $(date '+%Y-%m-%d %H:%M:%S')
服务状态:
$services_status

请检查日志并手动处理失败的服务。"
    fi
    
    log "===== 服务启动完成 ====="
}

# 执行主函数
main
