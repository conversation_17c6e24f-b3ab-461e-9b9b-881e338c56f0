[Unit]
Description=Integrated Singbox Proxy Service with Monitoring
After=network.target

[Service]
Type=forking
User=ubuntu
WorkingDirectory=/home/<USER>/API/local-singbox
ExecStart=/home/<USER>/API/local-singbox/scripts/singbox_service.sh daemon
ExecStop=/home/<USER>/API/local-singbox/scripts/singbox_service.sh stop
Restart=on-failure
RestartSec=10
KillMode=process
PIDFile=/home/<USER>/API/local-singbox/singbox_service.pid

[Install]
WantedBy=multi-user.target
