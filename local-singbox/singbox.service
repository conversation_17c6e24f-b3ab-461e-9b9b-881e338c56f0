[Unit]
Description=Singbox Proxy Service
After=network.target
Before=integrated-monitor-master.service

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/API/local-singbox
ExecStart=/home/<USER>/API/local-singbox/bin/sing-box run -c /home/<USER>/API/local-singbox/config/config.json
Restart=on-failure
RestartSec=10

# 资源限制
LimitNOFILE=65535

# 日志
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target