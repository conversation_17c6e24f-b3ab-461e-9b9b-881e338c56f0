function h(e,r){if(e==null)return{};var t={},u=Object.keys(e),n,a;for(a=0;a<u.length;a++)n=u[a],!(r.indexOf(n)>=0)&&(t[n]=e[n]);return t}var o=Number.isNaN||function(r){return typeof r=="number"&&r!==r};function l(e,r){return!!(e===r||o(e)&&o(r))}function g(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(!l(e[t],r[t]))return!1;return!0}function v(e,r){r===void 0&&(r=g);var t,u=[],n,a=!1;function s(){for(var f=[],i=0;i<arguments.length;i++)f[i]=arguments[i];return a&&t===this&&r(f,u)||(n=e.apply(this,f),a=!0,t=this,u=f),n}return s}export{h as _,v as m};
