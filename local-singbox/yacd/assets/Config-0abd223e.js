import{r as E,R as h,p as v,c as re,b as n,j as c,s as le,t as V,v as G,w as oe,x as H,d as J,y as se,g as Q,z as ie,u as ce,A as de,D as x,E as ue,F as me,G as he,H as pe,J as ve,K as fe,L as ge,C as be,S as N,N as ye,B as y,O as we,P as ke,Q as _e}from"./index-1a05af9b.js";import{r as Ce}from"./logs-ca50193b.js";import{S as k}from"./Select-04258549.js";import{I as S,S as Oe}from"./Input-e46653b4.js";import{R as P}from"./rotate-cw-e799f805.js";function I(){return I=Object.assign||function(e){for(var o=1;o<arguments.length;o++){var l=arguments[o];for(var a in l)Object.prototype.hasOwnProperty.call(l,a)&&(e[a]=l[a])}return e},I.apply(this,arguments)}function xe(e,o){if(e==null)return{};var l=Ne(e,o),a,t;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(t=0;t<r.length;t++)a=r[t],!(o.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(l[a]=e[a])}return l}function Ne(e,o){if(e==null)return{};var l={},a=Object.keys(e),t,r;for(r=0;r<a.length;r++)t=a[r],!(o.indexOf(t)>=0)&&(l[t]=e[t]);return l}var T=E.forwardRef(function(e,o){var l=e.color,a=l===void 0?"currentColor":l,t=e.size,r=t===void 0?24:t,p=xe(e,["color","size"]);return h.createElement("svg",I({ref:o,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:a,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},p),h.createElement("polyline",{points:"8 17 12 21 16 17"}),h.createElement("line",{x1:"12",y1:"12",x2:"12",y2:"21"}),h.createElement("path",{d:"M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29"}))});T.propTypes={color:v.string,size:v.oneOfType([v.string,v.number])};T.displayName="DownloadCloud";const Se=T;function L(){return L=Object.assign||function(e){for(var o=1;o<arguments.length;o++){var l=arguments[o];for(var a in l)Object.prototype.hasOwnProperty.call(l,a)&&(e[a]=l[a])}return e},L.apply(this,arguments)}function Pe(e,o){if(e==null)return{};var l=je(e,o),a,t;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(t=0;t<r.length;t++)a=r[t],!(o.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(l[a]=e[a])}return l}function je(e,o){if(e==null)return{};var l={},a=Object.keys(e),t,r;for(r=0;r<a.length;r++)t=a[r],!(o.indexOf(t)>=0)&&(l[t]=e[t]);return l}var $=E.forwardRef(function(e,o){var l=e.color,a=l===void 0?"currentColor":l,t=e.size,r=t===void 0?24:t,p=Pe(e,["color","size"]);return h.createElement("svg",L({ref:o,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:a,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},p),h.createElement("path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}),h.createElement("polyline",{points:"16 17 21 12 16 7"}),h.createElement("line",{x1:"21",y1:"12",x2:"9",y2:"12"}))});$.propTypes={color:v.string,size:v.oneOfType([v.string,v.number])};$.displayName="LogOut";const Ie=$;function z(){return z=Object.assign||function(e){for(var o=1;o<arguments.length;o++){var l=arguments[o];for(var a in l)Object.prototype.hasOwnProperty.call(l,a)&&(e[a]=l[a])}return e},z.apply(this,arguments)}function Le(e,o){if(e==null)return{};var l=ze(e,o),a,t;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(t=0;t<r.length;t++)a=r[t],!(o.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(l[a]=e[a])}return l}function ze(e,o){if(e==null)return{};var l={},a=Object.keys(e),t,r;for(r=0;r<a.length;r++)t=a[r],!(o.indexOf(t)>=0)&&(l[t]=e[t]);return l}var R=E.forwardRef(function(e,o){var l=e.color,a=l===void 0?"currentColor":l,t=e.size,r=t===void 0?24:t,p=Le(e,["color","size"]);return h.createElement("svg",z({ref:o,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:a,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},p),h.createElement("polyline",{points:"3 6 5 6 21 6"}),h.createElement("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}),h.createElement("line",{x1:"10",y1:"11",x2:"10",y2:"17"}),h.createElement("line",{x1:"14",y1:"11",x2:"14",y2:"17"}))});R.propTypes={color:v.string,size:v.oneOfType([v.string,v.number])};R.displayName="Trash2";const Ee=R,Te="_root_1vck5_4",$e="_section_1vck5_5",Re="_wrapSwitch_1vck5_30",Me="_sep_1vck5_36",De="_label_1vck5_49",i={root:Te,section:$e,wrapSwitch:Re,sep:Me,label:De},Fe="_fieldset_1hnn2_1",We="_input_1hnn2_10",Be="_cnt_1hnn2_10",j={fieldset:Fe,input:We,cnt:Be};function Ue({OptionComponent:e,optionPropsList:o,selectedIndex:l,onChange:a}){const t=re("visually-hidden",j.input),r=p=>{a(p.target.value)};return n("fieldset",{className:j.fieldset,children:o.map((p,d)=>c("label",{children:[n("input",{type:"radio",checked:l===d,name:"selection",value:d,"aria-labelledby":"traffic chart type "+d,onChange:r,className:t}),n("div",{className:j.cnt,children:n(e,{...p})})]},d))})}const{useMemo:Ae}=H,Ve={plugins:{legend:{display:!1}},scales:{x:{display:!1,type:"category"},y:{display:!1,type:"linear"}}},K=[23e3,35e3,46e3,33e3,9e4,68e3,23e3,45e3],Ge=[184e3,183e3,196e3,182e3,19e4,186e3,182e3,189e3],He=K;function Je({id:e}){const o=le.read(),l=Ae(()=>({labels:He,datasets:[{...V,...G[e].up,data:K},{...V,...G[e].down,data:Ge}]}),[e]),a="chart-"+e;return oe(o.Chart,a,l,null,Ve),n("div",{style:{width:80,padding:5},children:n("canvas",{id:a})})}const{useEffect:q,useState:Qe,useCallback:f,useRef:Ke}=H,qe=[{id:0},{id:1},{id:2},{id:3}],Xe=[["debug","Debug"],["info","Info"],["warning","Warning"],["error","Error"],["silent","Silent"]],Ye=[{key:"port",label:"Http Port"},{key:"socks-port",label:"Socks5 Port"},{key:"mixed-port",label:"Mixed Port"},{key:"redir-port",label:"Redir Port"},{key:"mitm-port",label:"MITM Port"}],Ze=[["zh-cn","简体中文"],["zh-tw","繁體中文"],["en","English"],["vi","Vietnamese"]],et=[["direct","Direct"],["rule","Rule"],["script","Script"],["global","Global"]],tt=[["gvisor","gVisor"],["mixed","Mixed"],["system","System"]],nt=e=>({configs:se(e),apiConfig:Q(e)}),at=e=>({selectedChartStyleIndex:ke(e),latencyTestUrl:_e(e),apiConfig:Q(e)}),rt=J(at)(st),ht=J(nt)(lt);function lt({dispatch:e,configs:o,apiConfig:l}){return q(()=>{e(ie(l))},[e,l]),n(rt,{configs:o})}function ot(e){return e&&e.meta&&!e.premium?"Clash.Meta ":e&&e.meta&&e.premium?"sing-box ":"Clash Premium"}function st({dispatch:e,configs:o,selectedChartStyleIndex:l,latencyTestUrl:a,apiConfig:t}){var W,B,U,A;const{t:r,i18n:p}=ce(),[d,_]=Qe(o),M=Ke(o);q(()=>{M.current!==o&&_(o),M.current=o},[o]);const X=f(()=>{e(de("apiConfig"))},[e]),C=f((s,u)=>{_({...d,[s]:u})},[d]),D=f((s,u)=>{const g={...d.tun,[s]:u};_({...d,tun:{...g}})},[d]),b=f(({name:s,value:u})=>{switch(s){case"mode":case"log-level":case"allow-lan":case"sniffing":C(s,u),e(x(t,{[s]:u})),s==="log-level"&&Ce({...t,logLevel:u});break;case"mitm-port":case"redir-port":case"socks-port":case"mixed-port":case"port":if(u!==""){const g=parseInt(u,10);if(g<0||g>65535)return}C(s,u);break;case"enable":case"stack":D(s,u),e(x(t,{tun:{[s]:u}}));break;default:return}},[t,e,C,D]),{selectChartStyleIndex:Y,updateAppConfig:F}=ue(),w=f(s=>{const{name:u,value:g}=s.target;switch(u){case"port":case"socks-port":case"mixed-port":case"redir-port":case"mitm-port":{const O=parseInt(g,10);if(O<0||O>65535)return;e(x(t,{[u]:O}));break}case"latencyTestUrl":{F(u,g);break}case"device name":case"interface name":break;default:throw new Error(`unknown input name ${u}`)}},[t,e,F]),Z=f(()=>{e(me(t))},[t,e]),ee=f(()=>{e(he(t))},[t,e]),te=f(()=>{e(pe(t))},[t,e]),ne=f(()=>{e(ve(t))},[t,e]),ae=f(()=>{e(fe(t))},[t,e]),{data:m}=ge(["/version",t],()=>we("/version",t));return c("div",{children:[n(be,{title:r("Config")}),c("div",{className:i.root,children:[m.meta&&m.premium||Ye.map(s=>d[s.key]!==void 0?c("div",{children:[n("div",{className:i.label,children:s.label}),n(S,{name:s.key,value:d[s.key],onChange:({target:{name:u,value:g}})=>b({name:u,value:g}),onBlur:w})]},s.key):null),c("div",{children:[n("div",{className:i.label,children:"Mode"}),n(k,{options:d["mode-list"]?d["mode-list"].map(s=>[s,s]):et,selected:d["mode-list"]?d.mode:d.mode.toLowerCase(),onChange:s=>b({name:"mode",value:s.target.value})})]}),c("div",{children:[n("div",{className:i.label,children:"Log Level"}),n(k,{options:Xe,selected:d["log-level"].toLowerCase(),onChange:s=>b({name:"log-level",value:s.target.value})})]}),m.meta&&m.premium||c("div",{children:[n("div",{className:i.label,children:r("allow_lan")}),n("div",{className:i.wrapSwitch,children:n(N,{name:"allow-lan",checked:d["allow-lan"],onChange:s=>b({name:"allow-lan",value:s})})})]}),m.meta&&!m.premium&&c("div",{children:[n("div",{className:i.label,children:r("tls_sniffing")}),n("div",{className:i.wrapSwitch,children:n(N,{name:"sniffing",checked:d.sniffing,onChange:s=>b({name:"sniffing",value:s})})})]})]}),n("div",{className:i.sep,children:n("div",{})}),m.meta&&c(ye,{children:[m.premium||c("div",{children:[c("div",{className:i.section,children:[c("div",{children:[n("div",{className:i.label,children:r("enable_tun_device")}),n("div",{className:i.wrapSwitch,children:n(N,{checked:(W=d.tun)==null?void 0:W.enable,onChange:s=>b({name:"enable",value:s})})})]}),c("div",{children:[n("div",{className:i.label,children:"TUN IP Stack"}),n(k,{options:tt,selected:(U=(B=d.tun)==null?void 0:B.stack)==null?void 0:U.toLowerCase(),onChange:s=>b({name:"stack",value:s.target.value})})]}),c("div",{children:[n("div",{className:i.label,children:"Device Name"}),n(S,{name:"device name",value:(A=d.tun)==null?void 0:A.device,onChange:w})]}),c("div",{children:[n("div",{className:i.label,children:"Interface Name"}),n(S,{name:"interface name",value:d["interface-name"]||"",onChange:w})]})]}),n("div",{className:i.sep,children:n("div",{})})]}),c("div",{className:i.section,children:[c("div",{children:[n("div",{className:i.label,children:"Reload"}),n(y,{start:n(P,{size:16}),label:r("reload_config_file"),onClick:Z})]}),m.meta&&!m.premium&&c("div",{children:[n("div",{className:i.label,children:"GEO Databases"}),n(y,{start:n(Se,{size:16}),label:r("update_geo_databases_file"),onClick:ne})]}),c("div",{children:[n("div",{className:i.label,children:"FakeIP"}),n(y,{start:n(Ee,{size:16}),label:r("flush_fake_ip_pool"),onClick:ae})]}),m.meta&&!m.premium&&c("div",{children:[n("div",{className:i.label,children:"Restart"}),n(y,{start:n(P,{size:16}),label:r("restart_core"),onClick:ee})]}),m.meta&&!m.premium&&c("div",{children:[n("div",{className:i.label,children:"⚠️ Upgrade ⚠️"}),n(y,{start:n(P,{size:16}),label:r("upgrade_core"),onClick:te})]})]}),n("div",{className:i.sep,children:n("div",{})})]}),c("div",{className:i.section,children:[c("div",{children:[n("div",{className:i.label,children:r("latency_test_url")}),n(Oe,{name:"latencyTestUrl",type:"text",value:a,onBlur:w})]}),c("div",{children:[n("div",{className:i.label,children:r("lang")}),n("div",{children:n(k,{options:Ze,selected:p.language,onChange:s=>p.changeLanguage(s.target.value)})})]}),c("div",{children:[n("div",{className:i.label,children:r("chart_style")}),n(Ue,{OptionComponent:Je,optionPropsList:qe,selectedIndex:l,onChange:Y})]}),c("div",{children:[c("div",{className:i.label,children:[r("current_backend"),n("p",{children:ot(m)+(t==null?void 0:t.baseURL)})]}),n("div",{className:i.label,children:"Action"}),n(y,{start:n(Ie,{size:16}),label:r("switch_backend"),onClick:X})]})]})]})}export{ht as default};
