var BS=Object.defineProperty;var zS=(e,t,n)=>t in e?BS(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Bh=(e,t,n)=>(zS(e,typeof t!="symbol"?t+"":t,n),n);function sg(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerpolicy&&(i.referrerPolicy=o.referrerpolicy),o.crossorigin==="use-credentials"?i.credentials="include":o.crossorigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var ls=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Kf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function VS(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){if(this instanceof r){var o=[null];o.push.apply(o,arguments);var i=Function.bind.apply(t,o);return new i}return t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}),n}var Li={},WS={get exports(){return Li},set exports(e){Li=e}},El={},L={},HS={get exports(){return L},set exports(e){L=e}},fe={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sa=Symbol.for("react.element"),qS=Symbol.for("react.portal"),KS=Symbol.for("react.fragment"),QS=Symbol.for("react.strict_mode"),GS=Symbol.for("react.profiler"),XS=Symbol.for("react.provider"),YS=Symbol.for("react.context"),JS=Symbol.for("react.forward_ref"),ZS=Symbol.for("react.suspense"),e_=Symbol.for("react.memo"),t_=Symbol.for("react.lazy"),zh=Symbol.iterator;function n_(e){return e===null||typeof e!="object"?null:(e=zh&&e[zh]||e["@@iterator"],typeof e=="function"?e:null)}var lg={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ug=Object.assign,cg={};function Lo(e,t,n){this.props=e,this.context=t,this.refs=cg,this.updater=n||lg}Lo.prototype.isReactComponent={};Lo.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Lo.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function fg(){}fg.prototype=Lo.prototype;function Qf(e,t,n){this.props=e,this.context=t,this.refs=cg,this.updater=n||lg}var Gf=Qf.prototype=new fg;Gf.constructor=Qf;ug(Gf,Lo.prototype);Gf.isPureReactComponent=!0;var Vh=Array.isArray,dg=Object.prototype.hasOwnProperty,Xf={current:null},hg={key:!0,ref:!0,__self:!0,__source:!0};function pg(e,t,n){var r,o={},i=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(i=""+t.key),t)dg.call(t,r)&&!hg.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var l=Array(s),u=0;u<s;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:sa,type:e,key:i,ref:a,props:o,_owner:Xf.current}}function r_(e,t){return{$$typeof:sa,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Yf(e){return typeof e=="object"&&e!==null&&e.$$typeof===sa}function o_(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Wh=/\/+/g;function pu(e,t){return typeof e=="object"&&e!==null&&e.key!=null?o_(""+e.key):t.toString(36)}function us(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case sa:case qS:a=!0}}if(a)return a=e,o=o(a),e=r===""?"."+pu(a,0):r,Vh(o)?(n="",e!=null&&(n=e.replace(Wh,"$&/")+"/"),us(o,t,n,"",function(u){return u})):o!=null&&(Yf(o)&&(o=r_(o,n+(!o.key||a&&a.key===o.key?"":(""+o.key).replace(Wh,"$&/")+"/")+e)),t.push(o)),1;if(a=0,r=r===""?".":r+":",Vh(e))for(var s=0;s<e.length;s++){i=e[s];var l=r+pu(i,s);a+=us(i,t,n,l,o)}else if(l=n_(e),typeof l=="function")for(e=l.call(e),s=0;!(i=e.next()).done;)i=i.value,l=r+pu(i,s++),a+=us(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function Pa(e,t,n){if(e==null)return e;var r=[],o=0;return us(e,r,"","",function(i){return t.call(n,i,o++)}),r}function i_(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var tt={current:null},cs={transition:null},a_={ReactCurrentDispatcher:tt,ReactCurrentBatchConfig:cs,ReactCurrentOwner:Xf};fe.Children={map:Pa,forEach:function(e,t,n){Pa(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Pa(e,function(){t++}),t},toArray:function(e){return Pa(e,function(t){return t})||[]},only:function(e){if(!Yf(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};fe.Component=Lo;fe.Fragment=KS;fe.Profiler=GS;fe.PureComponent=Qf;fe.StrictMode=QS;fe.Suspense=ZS;fe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=a_;fe.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ug({},e.props),o=e.key,i=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,a=Xf.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(l in t)dg.call(t,l)&&!hg.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&s!==void 0?s[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){s=Array(l);for(var u=0;u<l;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:sa,type:e.type,key:o,ref:i,props:r,_owner:a}};fe.createContext=function(e){return e={$$typeof:YS,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:XS,_context:e},e.Consumer=e};fe.createElement=pg;fe.createFactory=function(e){var t=pg.bind(null,e);return t.type=e,t};fe.createRef=function(){return{current:null}};fe.forwardRef=function(e){return{$$typeof:JS,render:e}};fe.isValidElement=Yf;fe.lazy=function(e){return{$$typeof:t_,_payload:{_status:-1,_result:e},_init:i_}};fe.memo=function(e,t){return{$$typeof:e_,type:e,compare:t===void 0?null:t}};fe.startTransition=function(e){var t=cs.transition;cs.transition={};try{e()}finally{cs.transition=t}};fe.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};fe.useCallback=function(e,t){return tt.current.useCallback(e,t)};fe.useContext=function(e){return tt.current.useContext(e)};fe.useDebugValue=function(){};fe.useDeferredValue=function(e){return tt.current.useDeferredValue(e)};fe.useEffect=function(e,t){return tt.current.useEffect(e,t)};fe.useId=function(){return tt.current.useId()};fe.useImperativeHandle=function(e,t,n){return tt.current.useImperativeHandle(e,t,n)};fe.useInsertionEffect=function(e,t){return tt.current.useInsertionEffect(e,t)};fe.useLayoutEffect=function(e,t){return tt.current.useLayoutEffect(e,t)};fe.useMemo=function(e,t){return tt.current.useMemo(e,t)};fe.useReducer=function(e,t,n){return tt.current.useReducer(e,t,n)};fe.useRef=function(e){return tt.current.useRef(e)};fe.useState=function(e){return tt.current.useState(e)};fe.useSyncExternalStore=function(e,t,n){return tt.current.useSyncExternalStore(e,t,n)};fe.useTransition=function(){return tt.current.useTransition()};fe.version="18.2.0";(function(e){e.exports=fe})(HS);const V=Kf(L),Tt=sg({__proto__:null,default:V},[L]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s_=L,l_=Symbol.for("react.element"),u_=Symbol.for("react.fragment"),c_=Object.prototype.hasOwnProperty,f_=s_.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d_={key:!0,ref:!0,__self:!0,__source:!0};function vg(e,t,n){var r,o={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)c_.call(t,r)&&!d_.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:l_,type:e,key:i,ref:a,props:o,_owner:f_.current}}El.Fragment=u_;El.jsx=vg;El.jsxs=vg;(function(e){e.exports=El})(WS);const Cr=Li.Fragment,R=Li.jsx,le=Li.jsxs;const h_="modulepreload",p_=function(e,t){return new URL(e,t).href},Hh={},Ot=function(t,n,r){if(!n||n.length===0)return t();const o=document.getElementsByTagName("link");return Promise.all(n.map(i=>{if(i=p_(i,r),i in Hh)return;Hh[i]=!0;const a=i.endsWith(".css"),s=a?'[rel="stylesheet"]':"";if(!!r)for(let c=o.length-1;c>=0;c--){const f=o[c];if(f.href===i&&(!a||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${s}`))return;const u=document.createElement("link");if(u.rel=a?"stylesheet":h_,a||(u.as="script",u.crossOrigin=""),u.href=i,document.head.appendChild(u),a)return new Promise((c,f)=>{u.addEventListener("load",c),u.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t())};function Ht(e){return Ht=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ht(e)}function At(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function v_(e,t){if(Ht(e)!=="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(Ht(r)!=="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function mg(e){var t=v_(e,"string");return Ht(t)==="symbol"?t:String(t)}function qh(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,mg(r.key),r)}}function Mt(e,t,n){return t&&qh(e.prototype,t),n&&qh(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function zn(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xs(e,t){return xs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},xs(e,t)}function Cl(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xs(e,t)}function la(e,t){if(t&&(Ht(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return zn(e)}function un(e){return un=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},un(e)}function cn(e,t,n){return t=mg(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function gg(e){if(Array.isArray(e))return e}function m_(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Kh(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function yg(e,t){if(e){if(typeof e=="string")return Kh(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Kh(e,t)}}function wg(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function g_(e){return gg(e)||m_(e)||yg(e)||wg()}function Qh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Gh(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Qh(Object(n),!0).forEach(function(r){cn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Qh(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var y_={type:"logger",log:function(t){this.output("log",t)},warn:function(t){this.output("warn",t)},error:function(t){this.output("error",t)},output:function(t,n){console&&console[t]&&console[t].apply(console,n)}},w_=function(){function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};At(this,e),this.init(t,n)}return Mt(e,[{key:"init",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=r.prefix||"i18next:",this.logger=n||y_,this.options=r,this.debug=r.debug}},{key:"setDebug",value:function(n){this.debug=n}},{key:"log",value:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return this.forward(r,"log","",!0)}},{key:"warn",value:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return this.forward(r,"warn","",!0)}},{key:"error",value:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return this.forward(r,"error","")}},{key:"deprecate",value:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return this.forward(r,"warn","WARNING DEPRECATED: ",!0)}},{key:"forward",value:function(n,r,o,i){return i&&!this.debug?null:(typeof n[0]=="string"&&(n[0]="".concat(o).concat(this.prefix," ").concat(n[0])),this.logger[r](n))}},{key:"create",value:function(n){return new e(this.logger,Gh(Gh({},{prefix:"".concat(this.prefix,":").concat(n,":")}),this.options))}},{key:"clone",value:function(n){return n=n||this.options,n.prefix=n.prefix||this.prefix,new e(this.logger,n)}}]),e}(),rn=new w_,Zn=function(){function e(){At(this,e),this.observers={}}return Mt(e,[{key:"on",value:function(n,r){var o=this;return n.split(" ").forEach(function(i){o.observers[i]=o.observers[i]||[],o.observers[i].push(r)}),this}},{key:"off",value:function(n,r){if(this.observers[n]){if(!r){delete this.observers[n];return}this.observers[n]=this.observers[n].filter(function(o){return o!==r})}}},{key:"emit",value:function(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];if(this.observers[n]){var a=[].concat(this.observers[n]);a.forEach(function(l){l.apply(void 0,o)})}if(this.observers["*"]){var s=[].concat(this.observers["*"]);s.forEach(function(l){l.apply(l,[n].concat(o))})}}}]),e}();function Wo(){var e,t,n=new Promise(function(r,o){e=r,t=o});return n.resolve=e,n.reject=t,n}function Xh(e){return e==null?"":""+e}function S_(e,t,n){e.forEach(function(r){t[r]&&(n[r]=t[r])})}function Jf(e,t,n){function r(s){return s&&s.indexOf("###")>-1?s.replace(/###/g,"."):s}function o(){return!e||typeof e=="string"}for(var i=typeof t!="string"?[].concat(t):t.split(".");i.length>1;){if(o())return{};var a=r(i.shift());!e[a]&&n&&(e[a]=new n),Object.prototype.hasOwnProperty.call(e,a)?e=e[a]:e={}}return o()?{}:{obj:e,k:r(i.shift())}}function Yh(e,t,n){var r=Jf(e,t,Object),o=r.obj,i=r.k;o[i]=n}function __(e,t,n,r){var o=Jf(e,t,Object),i=o.obj,a=o.k;i[a]=i[a]||[],r&&(i[a]=i[a].concat(n)),r||i[a].push(n)}function ks(e,t){var n=Jf(e,t),r=n.obj,o=n.k;if(r)return r[o]}function Jh(e,t,n){var r=ks(e,n);return r!==void 0?r:ks(t,n)}function Sg(e,t,n){for(var r in t)r!=="__proto__"&&r!=="constructor"&&(r in e?typeof e[r]=="string"||e[r]instanceof String||typeof t[r]=="string"||t[r]instanceof String?n&&(e[r]=t[r]):Sg(e[r],t[r],n):e[r]=t[r]);return e}function Vr(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}var b_={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};function E_(e){return typeof e=="string"?e.replace(/[&<>"'\/]/g,function(t){return b_[t]}):e}var Rl=typeof window<"u"&&window.navigator&&typeof window.navigator.userAgentData>"u"&&window.navigator.userAgent&&window.navigator.userAgent.indexOf("MSIE")>-1,C_=[" ",",","?","!",";"];function R_(e,t,n){t=t||"",n=n||"";var r=C_.filter(function(s){return t.indexOf(s)<0&&n.indexOf(s)<0});if(r.length===0)return!0;var o=new RegExp("(".concat(r.map(function(s){return s==="?"?"\\?":s}).join("|"),")")),i=!o.test(e);if(!i){var a=e.indexOf(n);a>0&&!o.test(e.substring(0,a))&&(i=!0)}return i}function Zh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Ta(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Zh(Object(n),!0).forEach(function(r){cn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Zh(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function O_(e){var t=x_();return function(){var r=un(e),o;if(t){var i=un(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return la(this,o)}}function x_(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function _g(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(e){if(e[t])return e[t];for(var r=t.split(n),o=e,i=0;i<r.length;++i){if(!o||typeof o[r[i]]=="string"&&i+1<r.length)return;if(o[r[i]]===void 0){for(var a=2,s=r.slice(i,i+a).join(n),l=o[s];l===void 0&&r.length>i+a;)a++,s=r.slice(i,i+a).join(n),l=o[s];if(l===void 0)return;if(l===null)return null;if(t.endsWith(s)){if(typeof l=="string")return l;if(s&&typeof l[s]=="string")return l[s]}var u=r.slice(i+a).join(n);return u?_g(l,u,n):void 0}o=o[r[i]]}return o}}var k_=function(e){Cl(n,e);var t=O_(n);function n(r){var o,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};return At(this,n),o=t.call(this),Rl&&Zn.call(zn(o)),o.data=r||{},o.options=i,o.options.keySeparator===void 0&&(o.options.keySeparator="."),o.options.ignoreJSONStructure===void 0&&(o.options.ignoreJSONStructure=!0),o}return Mt(n,[{key:"addNamespaces",value:function(o){this.options.ns.indexOf(o)<0&&this.options.ns.push(o)}},{key:"removeNamespaces",value:function(o){var i=this.options.ns.indexOf(o);i>-1&&this.options.ns.splice(i,1)}},{key:"getResource",value:function(o,i,a){var s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},l=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,u=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure,c=[o,i];a&&typeof a!="string"&&(c=c.concat(a)),a&&typeof a=="string"&&(c=c.concat(l?a.split(l):a)),o.indexOf(".")>-1&&(c=o.split("."));var f=ks(this.data,c);return f||!u||typeof a!="string"?f:_g(this.data&&this.data[o]&&this.data[o][i],a,l)}},{key:"addResource",value:function(o,i,a,s){var l=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1},u=this.options.keySeparator;u===void 0&&(u=".");var c=[o,i];a&&(c=c.concat(u?a.split(u):a)),o.indexOf(".")>-1&&(c=o.split("."),s=i,i=c[1]),this.addNamespaces(i),Yh(this.data,c,s),l.silent||this.emit("added",o,i,a,s)}},{key:"addResources",value:function(o,i,a){var s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(var l in a)(typeof a[l]=="string"||Object.prototype.toString.apply(a[l])==="[object Array]")&&this.addResource(o,i,l,a[l],{silent:!0});s.silent||this.emit("added",o,i,a)}},{key:"addResourceBundle",value:function(o,i,a,s,l){var u=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1},c=[o,i];o.indexOf(".")>-1&&(c=o.split("."),s=a,a=i,i=c[1]),this.addNamespaces(i);var f=ks(this.data,c)||{};s?Sg(f,a,l):f=Ta(Ta({},f),a),Yh(this.data,c,f),u.silent||this.emit("added",o,i,a)}},{key:"removeResourceBundle",value:function(o,i){this.hasResourceBundle(o,i)&&delete this.data[o][i],this.removeNamespaces(i),this.emit("removed",o,i)}},{key:"hasResourceBundle",value:function(o,i){return this.getResource(o,i)!==void 0}},{key:"getResourceBundle",value:function(o,i){return i||(i=this.options.defaultNS),this.options.compatibilityAPI==="v1"?Ta(Ta({},{}),this.getResource(o,i)):this.getResource(o,i)}},{key:"getDataByLanguage",value:function(o){return this.data[o]}},{key:"hasLanguageSomeTranslations",value:function(o){var i=this.getDataByLanguage(o),a=i&&Object.keys(i)||[];return!!a.find(function(s){return i[s]&&Object.keys(i[s]).length>0})}},{key:"toJSON",value:function(){return this.data}}]),n}(Zn),bg={processors:{},addPostProcessor:function(t){this.processors[t.name]=t},handle:function(t,n,r,o,i){var a=this;return t.forEach(function(s){a.processors[s]&&(n=a.processors[s].process(n,r,o,i))}),n}};function ep(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Je(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?ep(Object(n),!0).forEach(function(r){cn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ep(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function P_(e){var t=T_();return function(){var r=un(e),o;if(t){var i=un(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return la(this,o)}}function T_(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}var tp={},np=function(e){Cl(n,e);var t=P_(n);function n(r){var o,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return At(this,n),o=t.call(this),Rl&&Zn.call(zn(o)),S_(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],r,zn(o)),o.options=i,o.options.keySeparator===void 0&&(o.options.keySeparator="."),o.logger=rn.create("translator"),o}return Mt(n,[{key:"changeLanguage",value:function(o){o&&(this.language=o)}},{key:"exists",value:function(o){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(o==null)return!1;var a=this.resolve(o,i);return a&&a.res!==void 0}},{key:"extractFromKey",value:function(o,i){var a=i.nsSeparator!==void 0?i.nsSeparator:this.options.nsSeparator;a===void 0&&(a=":");var s=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,l=i.ns||this.options.defaultNS||[],u=a&&o.indexOf(a)>-1,c=!this.options.userDefinedKeySeparator&&!i.keySeparator&&!this.options.userDefinedNsSeparator&&!i.nsSeparator&&!R_(o,a,s);if(u&&!c){var f=o.match(this.interpolator.nestingRegexp);if(f&&f.length>0)return{key:o,namespaces:l};var d=o.split(a);(a!==s||a===s&&this.options.ns.indexOf(d[0])>-1)&&(l=d.shift()),o=d.join(s)}return typeof l=="string"&&(l=[l]),{key:o,namespaces:l}}},{key:"translate",value:function(o,i,a){var s=this;if(Ht(i)!=="object"&&this.options.overloadTranslationOptionHandler&&(i=this.options.overloadTranslationOptionHandler(arguments)),i||(i={}),o==null)return"";Array.isArray(o)||(o=[String(o)]);var l=i.returnDetails!==void 0?i.returnDetails:this.options.returnDetails,u=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,c=this.extractFromKey(o[o.length-1],i),f=c.key,d=c.namespaces,p=d[d.length-1],v=i.lng||this.language,y=i.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(v&&v.toLowerCase()==="cimode"){if(y){var _=i.nsSeparator||this.options.nsSeparator;return l?(m.res="".concat(p).concat(_).concat(f),m):"".concat(p).concat(_).concat(f)}return l?(m.res=f,m):f}var m=this.resolve(o,i),h=m&&m.res,g=m&&m.usedKey||f,S=m&&m.exactUsedKey||f,k=Object.prototype.toString.apply(h),T=["[object Number]","[object Function]","[object RegExp]"],N=i.joinArrays!==void 0?i.joinArrays:this.options.joinArrays,M=!this.i18nFormat||this.i18nFormat.handleAsObject,G=typeof h!="string"&&typeof h!="boolean"&&typeof h!="number";if(M&&h&&G&&T.indexOf(k)<0&&!(typeof N=="string"&&k==="[object Array]")){if(!i.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");var $=this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,h,Je(Je({},i),{},{ns:d})):"key '".concat(f," (").concat(this.language,")' returned an object instead of string.");return l?(m.res=$,m):$}if(u){var X=k==="[object Array]",ce=X?[]:{},re=X?S:g;for(var w in h)if(Object.prototype.hasOwnProperty.call(h,w)){var P="".concat(re).concat(u).concat(w);ce[w]=this.translate(P,Je(Je({},i),{joinArrays:!1,ns:d})),ce[w]===P&&(ce[w]=h[w])}h=ce}}else if(M&&typeof N=="string"&&k==="[object Array]")h=h.join(N),h&&(h=this.extendTranslation(h,o,i,a));else{var I=!1,C=!1,O=i.count!==void 0&&typeof i.count!="string",A=n.hasDefaultValue(i),D=O?this.pluralResolver.getSuffix(v,i.count,i):"",z=i["defaultValue".concat(D)]||i.defaultValue;!this.isValidLookup(h)&&A&&(I=!0,h=z),this.isValidLookup(h)||(C=!0,h=f);var b=i.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey,U=b&&C?void 0:h,B=A&&z!==h&&this.options.updateMissing;if(C||I||B){if(this.logger.log(B?"updateKey":"missingKey",v,p,f,B?z:h),u){var J=this.resolve(f,Je(Je({},i),{},{keySeparator:!1}));J&&J.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}var W=[],Z=this.languageUtils.getFallbackCodes(this.options.fallbackLng,i.lng||this.language);if(this.options.saveMissingTo==="fallback"&&Z&&Z[0])for(var ae=0;ae<Z.length;ae++)W.push(Z[ae]);else this.options.saveMissingTo==="all"?W=this.languageUtils.toResolveHierarchy(i.lng||this.language):W.push(i.lng||this.language);var Y=function(ye,pe,we){var rt=A&&we!==h?we:U;s.options.missingKeyHandler?s.options.missingKeyHandler(ye,p,pe,rt,B,i):s.backendConnector&&s.backendConnector.saveMissing&&s.backendConnector.saveMissing(ye,p,pe,rt,B,i),s.emit("missingKey",ye,p,pe,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&O?W.forEach(function(me){s.pluralResolver.getSuffixes(me,i).forEach(function(ye){Y([me],f+ye,i["defaultValue".concat(ye)]||z)})}):Y(W,f,z))}h=this.extendTranslation(h,o,i,m,a),C&&h===f&&this.options.appendNamespaceToMissingKey&&(h="".concat(p,":").concat(f)),(C||I)&&this.options.parseMissingKeyHandler&&(this.options.compatibilityAPI!=="v1"?h=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?"".concat(p,":").concat(f):f,I?h:void 0):h=this.options.parseMissingKeyHandler(h))}return l?(m.res=h,m):h}},{key:"extendTranslation",value:function(o,i,a,s,l){var u=this;if(this.i18nFormat&&this.i18nFormat.parse)o=this.i18nFormat.parse(o,Je(Je({},this.options.interpolation.defaultVariables),a),s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!a.skipInterpolation){a.interpolation&&this.interpolator.init(Je(Je({},a),{interpolation:Je(Je({},this.options.interpolation),a.interpolation)}));var c=typeof o=="string"&&(a&&a.interpolation&&a.interpolation.skipOnVariables!==void 0?a.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables),f;if(c){var d=o.match(this.interpolator.nestingRegexp);f=d&&d.length}var p=a.replace&&typeof a.replace!="string"?a.replace:a;if(this.options.interpolation.defaultVariables&&(p=Je(Je({},this.options.interpolation.defaultVariables),p)),o=this.interpolator.interpolate(o,p,a.lng||this.language,a),c){var v=o.match(this.interpolator.nestingRegexp),y=v&&v.length;f<y&&(a.nest=!1)}a.nest!==!1&&(o=this.interpolator.nest(o,function(){for(var h=arguments.length,g=new Array(h),S=0;S<h;S++)g[S]=arguments[S];return l&&l[0]===g[0]&&!a.context?(u.logger.warn("It seems you are nesting recursively key: ".concat(g[0]," in key: ").concat(i[0])),null):u.translate.apply(u,g.concat([i]))},a)),a.interpolation&&this.interpolator.reset()}var _=a.postProcess||this.options.postProcess,m=typeof _=="string"?[_]:_;return o!=null&&m&&m.length&&a.applyPostProcessor!==!1&&(o=bg.handle(m,o,i,this.options&&this.options.postProcessPassResolved?Je({i18nResolved:s},a):a,this)),o}},{key:"resolve",value:function(o){var i=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s,l,u,c,f;return typeof o=="string"&&(o=[o]),o.forEach(function(d){if(!i.isValidLookup(s)){var p=i.extractFromKey(d,a),v=p.key;l=v;var y=p.namespaces;i.options.fallbackNS&&(y=y.concat(i.options.fallbackNS));var _=a.count!==void 0&&typeof a.count!="string",m=_&&!a.ordinal&&a.count===0&&i.pluralResolver.shouldUseIntlApi(),h=a.context!==void 0&&(typeof a.context=="string"||typeof a.context=="number")&&a.context!=="",g=a.lngs?a.lngs:i.languageUtils.toResolveHierarchy(a.lng||i.language,a.fallbackLng);y.forEach(function(S){i.isValidLookup(s)||(f=S,!tp["".concat(g[0],"-").concat(S)]&&i.utils&&i.utils.hasLoadedNamespace&&!i.utils.hasLoadedNamespace(f)&&(tp["".concat(g[0],"-").concat(S)]=!0,i.logger.warn('key "'.concat(l,'" for languages "').concat(g.join(", "),`" won't get resolved as namespace "`).concat(f,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),g.forEach(function(k){if(!i.isValidLookup(s)){c=k;var T=[v];if(i.i18nFormat&&i.i18nFormat.addLookupKeys)i.i18nFormat.addLookupKeys(T,v,k,S,a);else{var N;_&&(N=i.pluralResolver.getSuffix(k,a.count,a));var M="".concat(i.options.pluralSeparator,"zero");if(_&&(T.push(v+N),m&&T.push(v+M)),h){var G="".concat(v).concat(i.options.contextSeparator).concat(a.context);T.push(G),_&&(T.push(G+N),m&&T.push(G+M))}}for(var $;$=T.pop();)i.isValidLookup(s)||(u=$,s=i.getResource(k,S,$,a))}}))})}}),{res:s,usedKey:l,exactUsedKey:u,usedLng:c,usedNS:f}}},{key:"isValidLookup",value:function(o){return o!==void 0&&!(!this.options.returnNull&&o===null)&&!(!this.options.returnEmptyString&&o==="")}},{key:"getResource",value:function(o,i,a){var s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(o,i,a,s):this.resourceStore.getResource(o,i,a,s)}}],[{key:"hasDefaultValue",value:function(o){var i="defaultValue";for(var a in o)if(Object.prototype.hasOwnProperty.call(o,a)&&i===a.substring(0,i.length)&&o[a]!==void 0)return!0;return!1}}]),n}(Zn);function vu(e){return e.charAt(0).toUpperCase()+e.slice(1)}var rp=function(){function e(t){At(this,e),this.options=t,this.supportedLngs=this.options.supportedLngs||!1,this.logger=rn.create("languageUtils")}return Mt(e,[{key:"getScriptPartFromCode",value:function(n){if(!n||n.indexOf("-")<0)return null;var r=n.split("-");return r.length===2||(r.pop(),r[r.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(r.join("-"))}},{key:"getLanguagePartFromCode",value:function(n){if(!n||n.indexOf("-")<0)return n;var r=n.split("-");return this.formatLanguageCode(r[0])}},{key:"formatLanguageCode",value:function(n){if(typeof n=="string"&&n.indexOf("-")>-1){var r=["hans","hant","latn","cyrl","cans","mong","arab"],o=n.split("-");return this.options.lowerCaseLng?o=o.map(function(i){return i.toLowerCase()}):o.length===2?(o[0]=o[0].toLowerCase(),o[1]=o[1].toUpperCase(),r.indexOf(o[1].toLowerCase())>-1&&(o[1]=vu(o[1].toLowerCase()))):o.length===3&&(o[0]=o[0].toLowerCase(),o[1].length===2&&(o[1]=o[1].toUpperCase()),o[0]!=="sgn"&&o[2].length===2&&(o[2]=o[2].toUpperCase()),r.indexOf(o[1].toLowerCase())>-1&&(o[1]=vu(o[1].toLowerCase())),r.indexOf(o[2].toLowerCase())>-1&&(o[2]=vu(o[2].toLowerCase()))),o.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?n.toLowerCase():n}},{key:"isSupportedCode",value:function(n){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(n=this.getLanguagePartFromCode(n)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(n)>-1}},{key:"getBestMatchFromCodes",value:function(n){var r=this;if(!n)return null;var o;return n.forEach(function(i){if(!o){var a=r.formatLanguageCode(i);(!r.options.supportedLngs||r.isSupportedCode(a))&&(o=a)}}),!o&&this.options.supportedLngs&&n.forEach(function(i){if(!o){var a=r.getLanguagePartFromCode(i);if(r.isSupportedCode(a))return o=a;o=r.options.supportedLngs.find(function(s){if(s.indexOf(a)===0)return s})}}),o||(o=this.getFallbackCodes(this.options.fallbackLng)[0]),o}},{key:"getFallbackCodes",value:function(n,r){if(!n)return[];if(typeof n=="function"&&(n=n(r)),typeof n=="string"&&(n=[n]),Object.prototype.toString.apply(n)==="[object Array]")return n;if(!r)return n.default||[];var o=n[r];return o||(o=n[this.getScriptPartFromCode(r)]),o||(o=n[this.formatLanguageCode(r)]),o||(o=n[this.getLanguagePartFromCode(r)]),o||(o=n.default),o||[]}},{key:"toResolveHierarchy",value:function(n,r){var o=this,i=this.getFallbackCodes(r||this.options.fallbackLng||[],n),a=[],s=function(u){u&&(o.isSupportedCode(u)?a.push(u):o.logger.warn("rejecting language code not found in supportedLngs: ".concat(u)))};return typeof n=="string"&&n.indexOf("-")>-1?(this.options.load!=="languageOnly"&&s(this.formatLanguageCode(n)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&s(this.getScriptPartFromCode(n)),this.options.load!=="currentOnly"&&s(this.getLanguagePartFromCode(n))):typeof n=="string"&&s(this.formatLanguageCode(n)),i.forEach(function(l){a.indexOf(l)<0&&s(o.formatLanguageCode(l))}),a}}]),e}(),L_=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],N_={1:function(t){return Number(t>1)},2:function(t){return Number(t!=1)},3:function(t){return 0},4:function(t){return Number(t%10==1&&t%100!=11?0:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?1:2)},5:function(t){return Number(t==0?0:t==1?1:t==2?2:t%100>=3&&t%100<=10?3:t%100>=11?4:5)},6:function(t){return Number(t==1?0:t>=2&&t<=4?1:2)},7:function(t){return Number(t==1?0:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?1:2)},8:function(t){return Number(t==1?0:t==2?1:t!=8&&t!=11?2:3)},9:function(t){return Number(t>=2)},10:function(t){return Number(t==1?0:t==2?1:t<7?2:t<11?3:4)},11:function(t){return Number(t==1||t==11?0:t==2||t==12?1:t>2&&t<20?2:3)},12:function(t){return Number(t%10!=1||t%100==11)},13:function(t){return Number(t!==0)},14:function(t){return Number(t==1?0:t==2?1:t==3?2:3)},15:function(t){return Number(t%10==1&&t%100!=11?0:t%10>=2&&(t%100<10||t%100>=20)?1:2)},16:function(t){return Number(t%10==1&&t%100!=11?0:t!==0?1:2)},17:function(t){return Number(t==1||t%10==1&&t%100!=11?0:1)},18:function(t){return Number(t==0?0:t==1?1:2)},19:function(t){return Number(t==1?0:t==0||t%100>1&&t%100<11?1:t%100>10&&t%100<20?2:3)},20:function(t){return Number(t==1?0:t==0||t%100>0&&t%100<20?1:2)},21:function(t){return Number(t%100==1?1:t%100==2?2:t%100==3||t%100==4?3:0)},22:function(t){return Number(t==1?0:t==2?1:(t<0||t>10)&&t%10==0?2:3)}},A_=["v1","v2","v3"],op={zero:0,one:1,two:2,few:3,many:4,other:5};function M_(){var e={};return L_.forEach(function(t){t.lngs.forEach(function(n){e[n]={numbers:t.nr,plurals:N_[t.fc]}})}),e}var I_=function(){function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};At(this,e),this.languageUtils=t,this.options=n,this.logger=rn.create("pluralResolver"),(!this.options.compatibilityJSON||this.options.compatibilityJSON==="v4")&&(typeof Intl>"u"||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=M_()}return Mt(e,[{key:"addRule",value:function(n,r){this.rules[n]=r}},{key:"getRule",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.shouldUseIntlApi())try{return new Intl.PluralRules(n,{type:r.ordinal?"ordinal":"cardinal"})}catch{return}return this.rules[n]||this.rules[this.languageUtils.getLanguagePartFromCode(n)]}},{key:"needsPlural",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=this.getRule(n,r);return this.shouldUseIntlApi()?o&&o.resolvedOptions().pluralCategories.length>1:o&&o.numbers.length>1}},{key:"getPluralFormsOfKey",value:function(n,r){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(n,o).map(function(i){return"".concat(r).concat(i)})}},{key:"getSuffixes",value:function(n){var r=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=this.getRule(n,o);return i?this.shouldUseIntlApi()?i.resolvedOptions().pluralCategories.sort(function(a,s){return op[a]-op[s]}).map(function(a){return"".concat(r.options.prepend).concat(a)}):i.numbers.map(function(a){return r.getSuffix(n,a,o)}):[]}},{key:"getSuffix",value:function(n,r){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=this.getRule(n,o);return i?this.shouldUseIntlApi()?"".concat(this.options.prepend).concat(i.select(r)):this.getSuffixRetroCompatible(i,r):(this.logger.warn("no plural rule found for: ".concat(n)),"")}},{key:"getSuffixRetroCompatible",value:function(n,r){var o=this,i=n.noAbs?n.plurals(r):n.plurals(Math.abs(r)),a=n.numbers[i];this.options.simplifyPluralSuffix&&n.numbers.length===2&&n.numbers[0]===1&&(a===2?a="plural":a===1&&(a=""));var s=function(){return o.options.prepend&&a.toString()?o.options.prepend+a.toString():a.toString()};return this.options.compatibilityJSON==="v1"?a===1?"":typeof a=="number"?"_plural_".concat(a.toString()):s():this.options.compatibilityJSON==="v2"||this.options.simplifyPluralSuffix&&n.numbers.length===2&&n.numbers[0]===1?s():this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString()}},{key:"shouldUseIntlApi",value:function(){return!A_.includes(this.options.compatibilityJSON)}}]),e}();function ip(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Dt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?ip(Object(n),!0).forEach(function(r){cn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ip(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var D_=function(){function e(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};At(this,e),this.logger=rn.create("interpolator"),this.options=t,this.format=t.interpolation&&t.interpolation.format||function(n){return n},this.init(t)}return Mt(e,[{key:"init",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};n.interpolation||(n.interpolation={escapeValue:!0});var r=n.interpolation;this.escape=r.escape!==void 0?r.escape:E_,this.escapeValue=r.escapeValue!==void 0?r.escapeValue:!0,this.useRawValueToEscape=r.useRawValueToEscape!==void 0?r.useRawValueToEscape:!1,this.prefix=r.prefix?Vr(r.prefix):r.prefixEscaped||"{{",this.suffix=r.suffix?Vr(r.suffix):r.suffixEscaped||"}}",this.formatSeparator=r.formatSeparator?r.formatSeparator:r.formatSeparator||",",this.unescapePrefix=r.unescapeSuffix?"":r.unescapePrefix||"-",this.unescapeSuffix=this.unescapePrefix?"":r.unescapeSuffix||"",this.nestingPrefix=r.nestingPrefix?Vr(r.nestingPrefix):r.nestingPrefixEscaped||Vr("$t("),this.nestingSuffix=r.nestingSuffix?Vr(r.nestingSuffix):r.nestingSuffixEscaped||Vr(")"),this.nestingOptionsSeparator=r.nestingOptionsSeparator?r.nestingOptionsSeparator:r.nestingOptionsSeparator||",",this.maxReplaces=r.maxReplaces?r.maxReplaces:1e3,this.alwaysFormat=r.alwaysFormat!==void 0?r.alwaysFormat:!1,this.resetRegExp()}},{key:"reset",value:function(){this.options&&this.init(this.options)}},{key:"resetRegExp",value:function(){var n="".concat(this.prefix,"(.+?)").concat(this.suffix);this.regexp=new RegExp(n,"g");var r="".concat(this.prefix).concat(this.unescapePrefix,"(.+?)").concat(this.unescapeSuffix).concat(this.suffix);this.regexpUnescape=new RegExp(r,"g");var o="".concat(this.nestingPrefix,"(.+?)").concat(this.nestingSuffix);this.nestingRegexp=new RegExp(o,"g")}},{key:"interpolate",value:function(n,r,o,i){var a=this,s,l,u,c=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{};function f(_){return _.replace(/\$/g,"$$$$")}var d=function(m){if(m.indexOf(a.formatSeparator)<0){var h=Jh(r,c,m);return a.alwaysFormat?a.format(h,void 0,o,Dt(Dt(Dt({},i),r),{},{interpolationkey:m})):h}var g=m.split(a.formatSeparator),S=g.shift().trim(),k=g.join(a.formatSeparator).trim();return a.format(Jh(r,c,S),k,o,Dt(Dt(Dt({},i),r),{},{interpolationkey:S}))};this.resetRegExp();var p=i&&i.missingInterpolationHandler||this.options.missingInterpolationHandler,v=i&&i.interpolation&&i.interpolation.skipOnVariables!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables,y=[{regex:this.regexpUnescape,safeValue:function(m){return f(m)}},{regex:this.regexp,safeValue:function(m){return a.escapeValue?f(a.escape(m)):f(m)}}];return y.forEach(function(_){for(u=0;s=_.regex.exec(n);){var m=s[1].trim();if(l=d(m),l===void 0)if(typeof p=="function"){var h=p(n,s,i);l=typeof h=="string"?h:""}else if(i&&i.hasOwnProperty(m))l="";else if(v){l=s[0];continue}else a.logger.warn("missed to pass in variable ".concat(m," for interpolating ").concat(n)),l="";else typeof l!="string"&&!a.useRawValueToEscape&&(l=Xh(l));var g=_.safeValue(l);if(n=n.replace(s[0],g),v?(_.regex.lastIndex+=l.length,_.regex.lastIndex-=s[0].length):_.regex.lastIndex=0,u++,u>=a.maxReplaces)break}}),n}},{key:"nest",value:function(n,r){var o=this,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},a,s,l;function u(p,v){var y=this.nestingOptionsSeparator;if(p.indexOf(y)<0)return p;var _=p.split(new RegExp("".concat(y,"[ ]*{"))),m="{".concat(_[1]);p=_[0],m=this.interpolate(m,l);var h=m.match(/'/g),g=m.match(/"/g);(h&&h.length%2===0&&!g||g.length%2!==0)&&(m=m.replace(/'/g,'"'));try{l=JSON.parse(m),v&&(l=Dt(Dt({},v),l))}catch(S){return this.logger.warn("failed parsing options string in nesting for key ".concat(p),S),"".concat(p).concat(y).concat(m)}return delete l.defaultValue,p}for(;a=this.nestingRegexp.exec(n);){var c=[];l=Dt({},i),l=l.replace&&typeof l.replace!="string"?l.replace:l,l.applyPostProcessor=!1,delete l.defaultValue;var f=!1;if(a[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(a[1])){var d=a[1].split(this.formatSeparator).map(function(p){return p.trim()});a[1]=d.shift(),c=d,f=!0}if(s=r(u.call(this,a[1].trim(),l),l),s&&a[0]===n&&typeof s!="string")return s;typeof s!="string"&&(s=Xh(s)),s||(this.logger.warn("missed to resolve ".concat(a[1]," for nesting ").concat(n)),s=""),f&&(s=c.reduce(function(p,v){return o.format(p,v,i.lng,Dt(Dt({},i),{},{interpolationkey:a[1].trim()}))},s.trim())),n=n.replace(a[0],s),this.regexp.lastIndex=0}return n}}]),e}();function ap(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function xn(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?ap(Object(n),!0).forEach(function(r){cn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ap(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function $_(e){var t=e.toLowerCase().trim(),n={};if(e.indexOf("(")>-1){var r=e.split("(");t=r[0].toLowerCase().trim();var o=r[1].substring(0,r[1].length-1);if(t==="currency"&&o.indexOf(":")<0)n.currency||(n.currency=o.trim());else if(t==="relativetime"&&o.indexOf(":")<0)n.range||(n.range=o.trim());else{var i=o.split(";");i.forEach(function(a){if(a){var s=a.split(":"),l=g_(s),u=l[0],c=l.slice(1),f=c.join(":").trim().replace(/^'+|'+$/g,"");n[u.trim()]||(n[u.trim()]=f),f==="false"&&(n[u.trim()]=!1),f==="true"&&(n[u.trim()]=!0),isNaN(f)||(n[u.trim()]=parseInt(f,10))}})}}return{formatName:t,formatOptions:n}}function Wr(e){var t={};return function(r,o,i){var a=o+JSON.stringify(i),s=t[a];return s||(s=e(o,i),t[a]=s),s(r)}}var U_=function(){function e(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};At(this,e),this.logger=rn.create("formatter"),this.options=t,this.formats={number:Wr(function(n,r){var o=new Intl.NumberFormat(n,r);return function(i){return o.format(i)}}),currency:Wr(function(n,r){var o=new Intl.NumberFormat(n,xn(xn({},r),{},{style:"currency"}));return function(i){return o.format(i)}}),datetime:Wr(function(n,r){var o=new Intl.DateTimeFormat(n,xn({},r));return function(i){return o.format(i)}}),relativetime:Wr(function(n,r){var o=new Intl.RelativeTimeFormat(n,xn({},r));return function(i){return o.format(i,r.range||"day")}}),list:Wr(function(n,r){var o=new Intl.ListFormat(n,xn({},r));return function(i){return o.format(i)}})},this.init(t)}return Mt(e,[{key:"init",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}},o=r.interpolation;this.formatSeparator=o.formatSeparator?o.formatSeparator:o.formatSeparator||","}},{key:"add",value:function(n,r){this.formats[n.toLowerCase().trim()]=r}},{key:"addCached",value:function(n,r){this.formats[n.toLowerCase().trim()]=Wr(r)}},{key:"format",value:function(n,r,o,i){var a=this,s=r.split(this.formatSeparator),l=s.reduce(function(u,c){var f=$_(c),d=f.formatName,p=f.formatOptions;if(a.formats[d]){var v=u;try{var y=i&&i.formatParams&&i.formatParams[i.interpolationkey]||{},_=y.locale||y.lng||i.locale||i.lng||o;v=a.formats[d](u,_,xn(xn(xn({},p),i),y))}catch(m){a.logger.warn(m)}return v}else a.logger.warn("there was no format function for ".concat(d));return u},n);return l}}]),e}();function sp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function lp(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?sp(Object(n),!0).forEach(function(r){cn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):sp(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function F_(e){var t=j_();return function(){var r=un(e),o;if(t){var i=un(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return la(this,o)}}function j_(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function B_(e,t){e.pending[t]!==void 0&&(delete e.pending[t],e.pendingCount--)}var z_=function(e){Cl(n,e);var t=F_(n);function n(r,o,i){var a,s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return At(this,n),a=t.call(this),Rl&&Zn.call(zn(a)),a.backend=r,a.store=o,a.services=i,a.languageUtils=i.languageUtils,a.options=s,a.logger=rn.create("backendConnector"),a.waitingReads=[],a.maxParallelReads=s.maxParallelReads||10,a.readingCalls=0,a.maxRetries=s.maxRetries>=0?s.maxRetries:5,a.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,a.state={},a.queue=[],a.backend&&a.backend.init&&a.backend.init(i,s.backend,s),a}return Mt(n,[{key:"queueLoad",value:function(o,i,a,s){var l=this,u={},c={},f={},d={};return o.forEach(function(p){var v=!0;i.forEach(function(y){var _="".concat(p,"|").concat(y);!a.reload&&l.store.hasResourceBundle(p,y)?l.state[_]=2:l.state[_]<0||(l.state[_]===1?c[_]===void 0&&(c[_]=!0):(l.state[_]=1,v=!1,c[_]===void 0&&(c[_]=!0),u[_]===void 0&&(u[_]=!0),d[y]===void 0&&(d[y]=!0)))}),v||(f[p]=!0)}),(Object.keys(u).length||Object.keys(c).length)&&this.queue.push({pending:c,pendingCount:Object.keys(c).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(u),pending:Object.keys(c),toLoadLanguages:Object.keys(f),toLoadNamespaces:Object.keys(d)}}},{key:"loaded",value:function(o,i,a){var s=o.split("|"),l=s[0],u=s[1];i&&this.emit("failedLoading",l,u,i),a&&this.store.addResourceBundle(l,u,a),this.state[o]=i?-1:2;var c={};this.queue.forEach(function(f){__(f.loaded,[l],u),B_(f,o),i&&f.errors.push(i),f.pendingCount===0&&!f.done&&(Object.keys(f.loaded).forEach(function(d){c[d]||(c[d]={});var p=f.loaded[d];p.length&&p.forEach(function(v){c[d][v]===void 0&&(c[d][v]=!0)})}),f.done=!0,f.errors.length?f.callback(f.errors):f.callback())}),this.emit("loaded",c),this.queue=this.queue.filter(function(f){return!f.done})}},{key:"read",value:function(o,i,a){var s=this,l=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,u=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,c=arguments.length>5?arguments[5]:void 0;if(!o.length)return c(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:o,ns:i,fcName:a,tried:l,wait:u,callback:c});return}this.readingCalls++;var f=function(y,_){if(s.readingCalls--,s.waitingReads.length>0){var m=s.waitingReads.shift();s.read(m.lng,m.ns,m.fcName,m.tried,m.wait,m.callback)}if(y&&_&&l<s.maxRetries){setTimeout(function(){s.read.call(s,o,i,a,l+1,u*2,c)},u);return}c(y,_)},d=this.backend[a].bind(this.backend);if(d.length===2){try{var p=d(o,i);p&&typeof p.then=="function"?p.then(function(v){return f(null,v)}).catch(f):f(null,p)}catch(v){f(v)}return}return d(o,i,f)}},{key:"prepareLoading",value:function(o,i){var a=this,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},l=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),l&&l();typeof o=="string"&&(o=this.languageUtils.toResolveHierarchy(o)),typeof i=="string"&&(i=[i]);var u=this.queueLoad(o,i,s,l);if(!u.toLoad.length)return u.pending.length||l(),null;u.toLoad.forEach(function(c){a.loadOne(c)})}},{key:"load",value:function(o,i,a){this.prepareLoading(o,i,{},a)}},{key:"reload",value:function(o,i,a){this.prepareLoading(o,i,{reload:!0},a)}},{key:"loadOne",value:function(o){var i=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",s=o.split("|"),l=s[0],u=s[1];this.read(l,u,"read",void 0,void 0,function(c,f){c&&i.logger.warn("".concat(a,"loading namespace ").concat(u," for language ").concat(l," failed"),c),!c&&f&&i.logger.log("".concat(a,"loaded namespace ").concat(u," for language ").concat(l),f),i.loaded(o,c,f)})}},{key:"saveMissing",value:function(o,i,a,s,l){var u=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},c=arguments.length>6&&arguments[6]!==void 0?arguments[6]:function(){};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(i)){this.logger.warn('did not save key "'.concat(a,'" as the namespace "').concat(i,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(a==null||a==="")){if(this.backend&&this.backend.create){var f=lp(lp({},u),{},{isUpdate:l}),d=this.backend.create.bind(this.backend);if(d.length<6)try{var p;d.length===5?p=d(o,i,a,s,f):p=d(o,i,a,s),p&&typeof p.then=="function"?p.then(function(v){return c(null,v)}).catch(c):c(null,p)}catch(v){c(v)}else d(o,i,a,s,c,f)}!o||!o[0]||this.store.addResource(o[0],i,a,s)}}}]),n}(Zn);function up(){return{debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!0,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:function(t){var n={};if(Ht(t[1])==="object"&&(n=t[1]),typeof t[1]=="string"&&(n.defaultValue=t[1]),typeof t[2]=="string"&&(n.tDescription=t[2]),Ht(t[2])==="object"||Ht(t[3])==="object"){var r=t[3]||t[2];Object.keys(r).forEach(function(o){n[o]=r[o]})}return n},interpolation:{escapeValue:!0,format:function(t,n,r,o){return t},prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}}function cp(e){return typeof e.ns=="string"&&(e.ns=[e.ns]),typeof e.fallbackLng=="string"&&(e.fallbackLng=[e.fallbackLng]),typeof e.fallbackNS=="string"&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&e.supportedLngs.indexOf("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e}function fp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Zt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?fp(Object(n),!0).forEach(function(r){cn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fp(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function V_(e){var t=W_();return function(){var r=un(e),o;if(t){var i=un(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return la(this,o)}}function W_(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function La(){}function H_(e){var t=Object.getOwnPropertyNames(Object.getPrototypeOf(e));t.forEach(function(n){typeof e[n]=="function"&&(e[n]=e[n].bind(e))})}var Ps=function(e){Cl(n,e);var t=V_(n);function n(){var r,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=arguments.length>1?arguments[1]:void 0;if(At(this,n),r=t.call(this),Rl&&Zn.call(zn(r)),r.options=cp(o),r.services={},r.logger=rn,r.modules={external:[]},H_(zn(r)),i&&!r.isInitialized&&!o.isClone){if(!r.options.initImmediate)return r.init(o,i),la(r,zn(r));setTimeout(function(){r.init(o,i)},0)}return r}return Mt(n,[{key:"init",value:function(){var o=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},a=arguments.length>1?arguments[1]:void 0;typeof i=="function"&&(a=i,i={}),!i.defaultNS&&i.defaultNS!==!1&&i.ns&&(typeof i.ns=="string"?i.defaultNS=i.ns:i.ns.indexOf("translation")<0&&(i.defaultNS=i.ns[0]));var s=up();this.options=Zt(Zt(Zt({},s),this.options),cp(i)),this.options.compatibilityAPI!=="v1"&&(this.options.interpolation=Zt(Zt({},s.interpolation),this.options.interpolation)),i.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=i.keySeparator),i.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=i.nsSeparator);function l(m){return m?typeof m=="function"?new m:m:null}if(!this.options.isClone){this.modules.logger?rn.init(l(this.modules.logger),this.options):rn.init(null,this.options);var u;this.modules.formatter?u=this.modules.formatter:typeof Intl<"u"&&(u=U_);var c=new rp(this.options);this.store=new k_(this.options.resources,this.options);var f=this.services;f.logger=rn,f.resourceStore=this.store,f.languageUtils=c,f.pluralResolver=new I_(c,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),u&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(f.formatter=l(u),f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new D_(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new z_(l(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",function(m){for(var h=arguments.length,g=new Array(h>1?h-1:0),S=1;S<h;S++)g[S-1]=arguments[S];o.emit.apply(o,[m].concat(g))}),this.modules.languageDetector&&(f.languageDetector=l(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=l(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new np(this.services,this.options),this.translator.on("*",function(m){for(var h=arguments.length,g=new Array(h>1?h-1:0),S=1;S<h;S++)g[S-1]=arguments[S];o.emit.apply(o,[m].concat(g))}),this.modules.external.forEach(function(m){m.init&&m.init(o)})}if(this.format=this.options.interpolation.format,a||(a=La),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){var d=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);d.length>0&&d[0]!=="dev"&&(this.options.lng=d[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined");var p=["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"];p.forEach(function(m){o[m]=function(){var h;return(h=o.store)[m].apply(h,arguments)}});var v=["addResource","addResources","addResourceBundle","removeResourceBundle"];v.forEach(function(m){o[m]=function(){var h;return(h=o.store)[m].apply(h,arguments),o}});var y=Wo(),_=function(){var h=function(S,k){o.isInitialized&&!o.initializedStoreOnce&&o.logger.warn("init: i18next is already initialized. You should call init just once!"),o.isInitialized=!0,o.options.isClone||o.logger.log("initialized",o.options),o.emit("initialized",o.options),y.resolve(k),a(S,k)};if(o.languages&&o.options.compatibilityAPI!=="v1"&&!o.isInitialized)return h(null,o.t.bind(o));o.changeLanguage(o.options.lng,h)};return this.options.resources||!this.options.initImmediate?_():setTimeout(_,0),y}},{key:"loadResources",value:function(o){var i=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:La,s=a,l=typeof o=="string"?o:this.language;if(typeof o=="function"&&(s=o),!this.options.resources||this.options.partialBundledLanguages){if(l&&l.toLowerCase()==="cimode")return s();var u=[],c=function(p){if(p){var v=i.services.languageUtils.toResolveHierarchy(p);v.forEach(function(y){u.indexOf(y)<0&&u.push(y)})}};if(l)c(l);else{var f=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);f.forEach(function(d){return c(d)})}this.options.preload&&this.options.preload.forEach(function(d){return c(d)}),this.services.backendConnector.load(u,this.options.ns,function(d){!d&&!i.resolvedLanguage&&i.language&&i.setResolvedLanguage(i.language),s(d)})}else s(null)}},{key:"reloadResources",value:function(o,i,a){var s=Wo();return o||(o=this.languages),i||(i=this.options.ns),a||(a=La),this.services.backendConnector.reload(o,i,function(l){s.resolve(),a(l)}),s}},{key:"use",value:function(o){if(!o)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!o.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return o.type==="backend"&&(this.modules.backend=o),(o.type==="logger"||o.log&&o.warn&&o.error)&&(this.modules.logger=o),o.type==="languageDetector"&&(this.modules.languageDetector=o),o.type==="i18nFormat"&&(this.modules.i18nFormat=o),o.type==="postProcessor"&&bg.addPostProcessor(o),o.type==="formatter"&&(this.modules.formatter=o),o.type==="3rdParty"&&this.modules.external.push(o),this}},{key:"setResolvedLanguage",value:function(o){if(!(!o||!this.languages)&&!(["cimode","dev"].indexOf(o)>-1))for(var i=0;i<this.languages.length;i++){var a=this.languages[i];if(!(["cimode","dev"].indexOf(a)>-1)&&this.store.hasLanguageSomeTranslations(a)){this.resolvedLanguage=a;break}}}},{key:"changeLanguage",value:function(o,i){var a=this;this.isLanguageChangingTo=o;var s=Wo();this.emit("languageChanging",o);var l=function(d){a.language=d,a.languages=a.services.languageUtils.toResolveHierarchy(d),a.resolvedLanguage=void 0,a.setResolvedLanguage(d)},u=function(d,p){p?(l(p),a.translator.changeLanguage(p),a.isLanguageChangingTo=void 0,a.emit("languageChanged",p),a.logger.log("languageChanged",p)):a.isLanguageChangingTo=void 0,s.resolve(function(){return a.t.apply(a,arguments)}),i&&i(d,function(){return a.t.apply(a,arguments)})},c=function(d){!o&&!d&&a.services.languageDetector&&(d=[]);var p=typeof d=="string"?d:a.services.languageUtils.getBestMatchFromCodes(d);p&&(a.language||l(p),a.translator.language||a.translator.changeLanguage(p),a.services.languageDetector&&a.services.languageDetector.cacheUserLanguage&&a.services.languageDetector.cacheUserLanguage(p)),a.loadResources(p,function(v){u(v,p)})};return!o&&this.services.languageDetector&&!this.services.languageDetector.async?c(this.services.languageDetector.detect()):!o&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(c):this.services.languageDetector.detect(c):c(o),s}},{key:"getFixedT",value:function(o,i,a){var s=this,l=function u(c,f){var d;if(Ht(f)!=="object"){for(var p=arguments.length,v=new Array(p>2?p-2:0),y=2;y<p;y++)v[y-2]=arguments[y];d=s.options.overloadTranslationOptionHandler([c,f].concat(v))}else d=Zt({},f);d.lng=d.lng||u.lng,d.lngs=d.lngs||u.lngs,d.ns=d.ns||u.ns,d.keyPrefix=d.keyPrefix||a||u.keyPrefix;var _=s.options.keySeparator||".",m;return d.keyPrefix&&Array.isArray(c)?m=c.map(function(h){return"".concat(d.keyPrefix).concat(_).concat(h)}):m=d.keyPrefix?"".concat(d.keyPrefix).concat(_).concat(c):c,s.t(m,d)};return typeof o=="string"?l.lng=o:l.lngs=o,l.ns=i,l.keyPrefix=a,l}},{key:"t",value:function(){var o;return this.translator&&(o=this.translator).translate.apply(o,arguments)}},{key:"exists",value:function(){var o;return this.translator&&(o=this.translator).exists.apply(o,arguments)}},{key:"setDefaultNamespace",value:function(o){this.options.defaultNS=o}},{key:"hasLoadedNamespace",value:function(o){var i=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;var s=this.resolvedLanguage||this.languages[0],l=this.options?this.options.fallbackLng:!1,u=this.languages[this.languages.length-1];if(s.toLowerCase()==="cimode")return!0;var c=function(p,v){var y=i.services.backendConnector.state["".concat(p,"|").concat(v)];return y===-1||y===2};if(a.precheck){var f=a.precheck(this,c);if(f!==void 0)return f}return!!(this.hasResourceBundle(s,o)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||c(s,o)&&(!l||c(u,o)))}},{key:"loadNamespaces",value:function(o,i){var a=this,s=Wo();return this.options.ns?(typeof o=="string"&&(o=[o]),o.forEach(function(l){a.options.ns.indexOf(l)<0&&a.options.ns.push(l)}),this.loadResources(function(l){s.resolve(),i&&i(l)}),s):(i&&i(),Promise.resolve())}},{key:"loadLanguages",value:function(o,i){var a=Wo();typeof o=="string"&&(o=[o]);var s=this.options.preload||[],l=o.filter(function(u){return s.indexOf(u)<0});return l.length?(this.options.preload=s.concat(l),this.loadResources(function(u){a.resolve(),i&&i(u)}),a):(i&&i(),Promise.resolve())}},{key:"dir",value:function(o){if(o||(o=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!o)return"rtl";var i=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],a=this.services&&this.services.languageUtils||new rp(up());return i.indexOf(a.getLanguagePartFromCode(o))>-1||o.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}},{key:"cloneInstance",value:function(){var o=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:La,s=Zt(Zt(Zt({},this.options),i),{isClone:!0}),l=new n(s);(i.debug!==void 0||i.prefix!==void 0)&&(l.logger=l.logger.clone(i));var u=["store","services","language"];return u.forEach(function(c){l[c]=o[c]}),l.services=Zt({},this.services),l.services.utils={hasLoadedNamespace:l.hasLoadedNamespace.bind(l)},l.translator=new np(l.services,l.options),l.translator.on("*",function(c){for(var f=arguments.length,d=new Array(f>1?f-1:0),p=1;p<f;p++)d[p-1]=arguments[p];l.emit.apply(l,[c].concat(d))}),l.init(s,a),l.translator.options=l.options,l.translator.backendConnector.services.utils={hasLoadedNamespace:l.hasLoadedNamespace.bind(l)},l}},{key:"toJSON",value:function(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}]),n}(Zn);cn(Ps,"createInstance",function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new Ps(e,t)});var Ye=Ps.createInstance();Ye.createInstance=Ps.createInstance;Ye.createInstance;Ye.dir;Ye.init;Ye.loadResources;Ye.reloadResources;Ye.use;Ye.changeLanguage;Ye.getFixedT;Ye.t;Ye.exists;Ye.setDefaultNamespace;Ye.hasLoadedNamespace;Ye.loadNamespaces;Ye.loadLanguages;var Eg=[],q_=Eg.forEach,K_=Eg.slice;function Q_(e){return q_.call(K_.call(arguments,1),function(t){if(t)for(var n in t)e[n]===void 0&&(e[n]=t[n])}),e}var dp=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,G_=function(t,n,r){var o=r||{};o.path=o.path||"/";var i=encodeURIComponent(n),a="".concat(t,"=").concat(i);if(o.maxAge>0){var s=o.maxAge-0;if(Number.isNaN(s))throw new Error("maxAge should be a Number");a+="; Max-Age=".concat(Math.floor(s))}if(o.domain){if(!dp.test(o.domain))throw new TypeError("option domain is invalid");a+="; Domain=".concat(o.domain)}if(o.path){if(!dp.test(o.path))throw new TypeError("option path is invalid");a+="; Path=".concat(o.path)}if(o.expires){if(typeof o.expires.toUTCString!="function")throw new TypeError("option expires is invalid");a+="; Expires=".concat(o.expires.toUTCString())}if(o.httpOnly&&(a+="; HttpOnly"),o.secure&&(a+="; Secure"),o.sameSite){var l=typeof o.sameSite=="string"?o.sameSite.toLowerCase():o.sameSite;switch(l){case!0:a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"strict":a+="; SameSite=Strict";break;case"none":a+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return a},hp={create:function(t,n,r,o){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};r&&(i.expires=new Date,i.expires.setTime(i.expires.getTime()+r*60*1e3)),o&&(i.domain=o),document.cookie=G_(t,encodeURIComponent(n),i)},read:function(t){for(var n="".concat(t,"="),r=document.cookie.split(";"),o=0;o<r.length;o++){for(var i=r[o];i.charAt(0)===" ";)i=i.substring(1,i.length);if(i.indexOf(n)===0)return i.substring(n.length,i.length)}return null},remove:function(t){this.create(t,"",-1)}},X_={name:"cookie",lookup:function(t){var n;if(t.lookupCookie&&typeof document<"u"){var r=hp.read(t.lookupCookie);r&&(n=r)}return n},cacheUserLanguage:function(t,n){n.lookupCookie&&typeof document<"u"&&hp.create(n.lookupCookie,t,n.cookieMinutes,n.cookieDomain,n.cookieOptions)}},Y_={name:"querystring",lookup:function(t){var n;if(typeof window<"u"){var r=window.location.search;!window.location.search&&window.location.hash&&window.location.hash.indexOf("?")>-1&&(r=window.location.hash.substring(window.location.hash.indexOf("?")));for(var o=r.substring(1),i=o.split("&"),a=0;a<i.length;a++){var s=i[a].indexOf("=");if(s>0){var l=i[a].substring(0,s);l===t.lookupQuerystring&&(n=i[a].substring(s+1))}}}return n}},Ho=null,pp=function(){if(Ho!==null)return Ho;try{Ho=window!=="undefined"&&window.localStorage!==null;var t="i18next.translate.boo";window.localStorage.setItem(t,"foo"),window.localStorage.removeItem(t)}catch{Ho=!1}return Ho},J_={name:"localStorage",lookup:function(t){var n;if(t.lookupLocalStorage&&pp()){var r=window.localStorage.getItem(t.lookupLocalStorage);r&&(n=r)}return n},cacheUserLanguage:function(t,n){n.lookupLocalStorage&&pp()&&window.localStorage.setItem(n.lookupLocalStorage,t)}},qo=null,vp=function(){if(qo!==null)return qo;try{qo=window!=="undefined"&&window.sessionStorage!==null;var t="i18next.translate.boo";window.sessionStorage.setItem(t,"foo"),window.sessionStorage.removeItem(t)}catch{qo=!1}return qo},Z_={name:"sessionStorage",lookup:function(t){var n;if(t.lookupSessionStorage&&vp()){var r=window.sessionStorage.getItem(t.lookupSessionStorage);r&&(n=r)}return n},cacheUserLanguage:function(t,n){n.lookupSessionStorage&&vp()&&window.sessionStorage.setItem(n.lookupSessionStorage,t)}},eb={name:"navigator",lookup:function(t){var n=[];if(typeof navigator<"u"){if(navigator.languages)for(var r=0;r<navigator.languages.length;r++)n.push(navigator.languages[r]);navigator.userLanguage&&n.push(navigator.userLanguage),navigator.language&&n.push(navigator.language)}return n.length>0?n:void 0}},tb={name:"htmlTag",lookup:function(t){var n,r=t.htmlTag||(typeof document<"u"?document.documentElement:null);return r&&typeof r.getAttribute=="function"&&(n=r.getAttribute("lang")),n}},nb={name:"path",lookup:function(t){var n;if(typeof window<"u"){var r=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(r instanceof Array)if(typeof t.lookupFromPathIndex=="number"){if(typeof r[t.lookupFromPathIndex]!="string")return;n=r[t.lookupFromPathIndex].replace("/","")}else n=r[0].replace("/","")}return n}},rb={name:"subdomain",lookup:function(t){var n=typeof t.lookupFromSubdomainIndex=="number"?t.lookupFromSubdomainIndex+1:1,r=typeof window<"u"&&window.location&&window.location.hostname&&window.location.hostname.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(r)return r[n]}};function ob(){return{order:["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"],lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"]}}var Cg=function(){function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};At(this,e),this.type="languageDetector",this.detectors={},this.init(t,n)}return Mt(e,[{key:"init",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=n,this.options=Q_(r,this.options||{},ob()),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=o,this.addDetector(X_),this.addDetector(Y_),this.addDetector(J_),this.addDetector(Z_),this.addDetector(eb),this.addDetector(tb),this.addDetector(nb),this.addDetector(rb)}},{key:"addDetector",value:function(n){this.detectors[n.name]=n}},{key:"detect",value:function(n){var r=this;n||(n=this.options.order);var o=[];return n.forEach(function(i){if(r.detectors[i]){var a=r.detectors[i].lookup(r.options);a&&typeof a=="string"&&(a=[a]),a&&(o=o.concat(a))}}),this.services.languageUtils.getBestMatchFromCodes?o:o.length>0?o[0]:null}},{key:"cacheUserLanguage",value:function(n,r){var o=this;r||(r=this.options.caches),r&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(n)>-1||r.forEach(function(i){o.detectors[i]&&o.detectors[i].cacheUserLanguage(n,o.options)}))}}]),e}();Cg.type="languageDetector";function uc(e){return uc=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},uc(e)}var Rg=[],ib=Rg.forEach,ab=Rg.slice;function cc(e){return ib.call(ab.call(arguments,1),function(t){if(t)for(var n in t)e[n]===void 0&&(e[n]=t[n])}),e}function Og(){return typeof XMLHttpRequest=="function"||(typeof XMLHttpRequest>"u"?"undefined":uc(XMLHttpRequest))==="object"}function sb(e){return!!e&&typeof e.then=="function"}function lb(e){return sb(e)?e:Promise.resolve(e)}function ub(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Ni={},cb={get exports(){return Ni},set exports(e){Ni=e}},hi={},fb={get exports(){return hi},set exports(e){hi=e}},mp;function db(){return mp||(mp=1,function(e,t){var n=typeof self<"u"?self:ls,r=function(){function i(){this.fetch=!1,this.DOMException=n.DOMException}return i.prototype=n,new i}();(function(i){(function(a){var s={searchParams:"URLSearchParams"in i,iterable:"Symbol"in i&&"iterator"in Symbol,blob:"FileReader"in i&&"Blob"in i&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in i,arrayBuffer:"ArrayBuffer"in i};function l(w){return w&&DataView.prototype.isPrototypeOf(w)}if(s.arrayBuffer)var u=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],c=ArrayBuffer.isView||function(w){return w&&u.indexOf(Object.prototype.toString.call(w))>-1};function f(w){if(typeof w!="string"&&(w=String(w)),/[^a-z0-9\-#$%&'*+.^_`|~]/i.test(w))throw new TypeError("Invalid character in header field name");return w.toLowerCase()}function d(w){return typeof w!="string"&&(w=String(w)),w}function p(w){var P={next:function(){var I=w.shift();return{done:I===void 0,value:I}}};return s.iterable&&(P[Symbol.iterator]=function(){return P}),P}function v(w){this.map={},w instanceof v?w.forEach(function(P,I){this.append(I,P)},this):Array.isArray(w)?w.forEach(function(P){this.append(P[0],P[1])},this):w&&Object.getOwnPropertyNames(w).forEach(function(P){this.append(P,w[P])},this)}v.prototype.append=function(w,P){w=f(w),P=d(P);var I=this.map[w];this.map[w]=I?I+", "+P:P},v.prototype.delete=function(w){delete this.map[f(w)]},v.prototype.get=function(w){return w=f(w),this.has(w)?this.map[w]:null},v.prototype.has=function(w){return this.map.hasOwnProperty(f(w))},v.prototype.set=function(w,P){this.map[f(w)]=d(P)},v.prototype.forEach=function(w,P){for(var I in this.map)this.map.hasOwnProperty(I)&&w.call(P,this.map[I],I,this)},v.prototype.keys=function(){var w=[];return this.forEach(function(P,I){w.push(I)}),p(w)},v.prototype.values=function(){var w=[];return this.forEach(function(P){w.push(P)}),p(w)},v.prototype.entries=function(){var w=[];return this.forEach(function(P,I){w.push([I,P])}),p(w)},s.iterable&&(v.prototype[Symbol.iterator]=v.prototype.entries);function y(w){if(w.bodyUsed)return Promise.reject(new TypeError("Already read"));w.bodyUsed=!0}function _(w){return new Promise(function(P,I){w.onload=function(){P(w.result)},w.onerror=function(){I(w.error)}})}function m(w){var P=new FileReader,I=_(P);return P.readAsArrayBuffer(w),I}function h(w){var P=new FileReader,I=_(P);return P.readAsText(w),I}function g(w){for(var P=new Uint8Array(w),I=new Array(P.length),C=0;C<P.length;C++)I[C]=String.fromCharCode(P[C]);return I.join("")}function S(w){if(w.slice)return w.slice(0);var P=new Uint8Array(w.byteLength);return P.set(new Uint8Array(w)),P.buffer}function k(){return this.bodyUsed=!1,this._initBody=function(w){this._bodyInit=w,w?typeof w=="string"?this._bodyText=w:s.blob&&Blob.prototype.isPrototypeOf(w)?this._bodyBlob=w:s.formData&&FormData.prototype.isPrototypeOf(w)?this._bodyFormData=w:s.searchParams&&URLSearchParams.prototype.isPrototypeOf(w)?this._bodyText=w.toString():s.arrayBuffer&&s.blob&&l(w)?(this._bodyArrayBuffer=S(w.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):s.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(w)||c(w))?this._bodyArrayBuffer=S(w):this._bodyText=w=Object.prototype.toString.call(w):this._bodyText="",this.headers.get("content-type")||(typeof w=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):s.searchParams&&URLSearchParams.prototype.isPrototypeOf(w)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},s.blob&&(this.blob=function(){var w=y(this);if(w)return w;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?y(this)||Promise.resolve(this._bodyArrayBuffer):this.blob().then(m)}),this.text=function(){var w=y(this);if(w)return w;if(this._bodyBlob)return h(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(g(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},s.formData&&(this.formData=function(){return this.text().then(G)}),this.json=function(){return this.text().then(JSON.parse)},this}var T=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function N(w){var P=w.toUpperCase();return T.indexOf(P)>-1?P:w}function M(w,P){P=P||{};var I=P.body;if(w instanceof M){if(w.bodyUsed)throw new TypeError("Already read");this.url=w.url,this.credentials=w.credentials,P.headers||(this.headers=new v(w.headers)),this.method=w.method,this.mode=w.mode,this.signal=w.signal,!I&&w._bodyInit!=null&&(I=w._bodyInit,w.bodyUsed=!0)}else this.url=String(w);if(this.credentials=P.credentials||this.credentials||"same-origin",(P.headers||!this.headers)&&(this.headers=new v(P.headers)),this.method=N(P.method||this.method||"GET"),this.mode=P.mode||this.mode||null,this.signal=P.signal||this.signal,this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&I)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody(I)}M.prototype.clone=function(){return new M(this,{body:this._bodyInit})};function G(w){var P=new FormData;return w.trim().split("&").forEach(function(I){if(I){var C=I.split("="),O=C.shift().replace(/\+/g," "),A=C.join("=").replace(/\+/g," ");P.append(decodeURIComponent(O),decodeURIComponent(A))}}),P}function $(w){var P=new v,I=w.replace(/\r?\n[\t ]+/g," ");return I.split(/\r?\n/).forEach(function(C){var O=C.split(":"),A=O.shift().trim();if(A){var D=O.join(":").trim();P.append(A,D)}}),P}k.call(M.prototype);function X(w,P){P||(P={}),this.type="default",this.status=P.status===void 0?200:P.status,this.ok=this.status>=200&&this.status<300,this.statusText="statusText"in P?P.statusText:"OK",this.headers=new v(P.headers),this.url=P.url||"",this._initBody(w)}k.call(X.prototype),X.prototype.clone=function(){return new X(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new v(this.headers),url:this.url})},X.error=function(){var w=new X(null,{status:0,statusText:""});return w.type="error",w};var ce=[301,302,303,307,308];X.redirect=function(w,P){if(ce.indexOf(P)===-1)throw new RangeError("Invalid status code");return new X(null,{status:P,headers:{location:w}})},a.DOMException=i.DOMException;try{new a.DOMException}catch{a.DOMException=function(P,I){this.message=P,this.name=I;var C=Error(P);this.stack=C.stack},a.DOMException.prototype=Object.create(Error.prototype),a.DOMException.prototype.constructor=a.DOMException}function re(w,P){return new Promise(function(I,C){var O=new M(w,P);if(O.signal&&O.signal.aborted)return C(new a.DOMException("Aborted","AbortError"));var A=new XMLHttpRequest;function D(){A.abort()}A.onload=function(){var z={status:A.status,statusText:A.statusText,headers:$(A.getAllResponseHeaders()||"")};z.url="responseURL"in A?A.responseURL:z.headers.get("X-Request-URL");var b="response"in A?A.response:A.responseText;I(new X(b,z))},A.onerror=function(){C(new TypeError("Network request failed"))},A.ontimeout=function(){C(new TypeError("Network request failed"))},A.onabort=function(){C(new a.DOMException("Aborted","AbortError"))},A.open(O.method,O.url,!0),O.credentials==="include"?A.withCredentials=!0:O.credentials==="omit"&&(A.withCredentials=!1),"responseType"in A&&s.blob&&(A.responseType="blob"),O.headers.forEach(function(z,b){A.setRequestHeader(b,z)}),O.signal&&(O.signal.addEventListener("abort",D),A.onreadystatechange=function(){A.readyState===4&&O.signal.removeEventListener("abort",D)}),A.send(typeof O._bodyInit>"u"?null:O._bodyInit)})}return re.polyfill=!0,i.fetch||(i.fetch=re,i.Headers=v,i.Request=M,i.Response=X),a.Headers=v,a.Request=M,a.Response=X,a.fetch=re,Object.defineProperty(a,"__esModule",{value:!0}),a})({})})(r),r.fetch.ponyfill=!0,delete r.fetch.polyfill;var o=r;t=o.fetch,t.default=o.fetch,t.fetch=o.fetch,t.Headers=o.Headers,t.Request=o.Request,t.Response=o.Response,e.exports=t}(fb,hi)),hi}(function(e,t){var n;if(typeof fetch=="function"&&(typeof ls<"u"&&ls.fetch?n=ls.fetch:typeof window<"u"&&window.fetch?n=window.fetch:n=fetch),typeof ub<"u"&&(typeof window>"u"||typeof window.document>"u")){var r=n||db();r.default&&(r=r.default),t.default=r,e.exports=t.default}})(cb,Ni);const xg=Ni,gp=sg({__proto__:null,default:xg},[Ni]);function Ts(e){return Ts=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ts(e)}var wn;typeof fetch=="function"&&(typeof global<"u"&&global.fetch?wn=global.fetch:typeof window<"u"&&window.fetch?wn=window.fetch:wn=fetch);var Ai;Og()&&(typeof global<"u"&&global.XMLHttpRequest?Ai=global.XMLHttpRequest:typeof window<"u"&&window.XMLHttpRequest&&(Ai=window.XMLHttpRequest));var Ls;typeof ActiveXObject=="function"&&(typeof global<"u"&&global.ActiveXObject?Ls=global.ActiveXObject:typeof window<"u"&&window.ActiveXObject&&(Ls=window.ActiveXObject));!wn&&gp&&!Ai&&!Ls&&(wn=xg||gp);typeof wn!="function"&&(wn=void 0);var fc=function(t,n){if(n&&Ts(n)==="object"){var r="";for(var o in n)r+="&"+encodeURIComponent(o)+"="+encodeURIComponent(n[o]);if(!r)return t;t=t+(t.indexOf("?")!==-1?"&":"?")+r.slice(1)}return t},yp=function(t,n,r){wn(t,n).then(function(o){if(!o.ok)return r(o.statusText||"Error",{status:o.status});o.text().then(function(i){r(null,{status:o.status,data:i})}).catch(r)}).catch(r)},wp=!1,hb=function(t,n,r,o){t.queryStringParams&&(n=fc(n,t.queryStringParams));var i=cc({},typeof t.customHeaders=="function"?t.customHeaders():t.customHeaders);r&&(i["Content-Type"]="application/json");var a=typeof t.requestOptions=="function"?t.requestOptions(r):t.requestOptions,s=cc({method:r?"POST":"GET",body:r?t.stringify(r):void 0,headers:i},wp?{}:a);try{yp(n,s,o)}catch(l){if(!a||Object.keys(a).length===0||!l.message||l.message.indexOf("not implemented")<0)return o(l);try{Object.keys(a).forEach(function(u){delete s[u]}),yp(n,s,o),wp=!0}catch(u){o(u)}}},pb=function(t,n,r,o){r&&Ts(r)==="object"&&(r=fc("",r).slice(1)),t.queryStringParams&&(n=fc(n,t.queryStringParams));try{var i;Ai?i=new Ai:i=new Ls("MSXML2.XMLHTTP.3.0"),i.open(r?"POST":"GET",n,1),t.crossDomain||i.setRequestHeader("X-Requested-With","XMLHttpRequest"),i.withCredentials=!!t.withCredentials,r&&i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.overrideMimeType&&i.overrideMimeType("application/json");var a=t.customHeaders;if(a=typeof a=="function"?a():a,a)for(var s in a)i.setRequestHeader(s,a[s]);i.onreadystatechange=function(){i.readyState>3&&o(i.status>=400?i.statusText:null,{status:i.status,data:i.responseText})},i.send(r)}catch(l){console&&console.log(l)}},vb=function(t,n,r,o){if(typeof r=="function"&&(o=r,r=void 0),o=o||function(){},wn&&n.indexOf("file:")!==0)return hb(t,n,r,o);if(Og()||typeof ActiveXObject=="function")return pb(t,n,r,o);o(new Error("No fetch and no xhr implementation found!"))};function Mi(e){return Mi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mi(e)}function mb(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Sp(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,kg(r.key),r)}}function gb(e,t,n){return t&&Sp(e.prototype,t),n&&Sp(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function yb(e,t,n){return t=kg(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kg(e){var t=wb(e,"string");return Mi(t)==="symbol"?t:String(t)}function wb(e,t){if(Mi(e)!=="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(Mi(r)!=="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Sb=function(){return{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",allowMultiLoading:!1,parse:function(n){return JSON.parse(n)},stringify:JSON.stringify,parsePayload:function(n,r,o){return yb({},r,o||"")},request:vb,reloadInterval:typeof window<"u"?!1:60*60*1e3,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}},Pg=function(){function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};mb(this,e),this.services=t,this.options=n,this.allOptions=r,this.type="backend",this.init(t,n,r)}return gb(e,[{key:"init",value:function(n){var r=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=n,this.options=cc(o,this.options||{},Sb()),this.allOptions=i,this.services&&this.options.reloadInterval&&setInterval(function(){return r.reload()},this.options.reloadInterval)}},{key:"readMulti",value:function(n,r,o){this._readAny(n,n,r,r,o)}},{key:"read",value:function(n,r,o){this._readAny([n],n,[r],r,o)}},{key:"_readAny",value:function(n,r,o,i,a){var s=this,l=this.options.loadPath;typeof this.options.loadPath=="function"&&(l=this.options.loadPath(n,o)),l=lb(l),l.then(function(u){if(!u)return a(null,{});var c=s.services.interpolator.interpolate(u,{lng:n.join("+"),ns:o.join("+")});s.loadUrl(c,a,r,i)})}},{key:"loadUrl",value:function(n,r,o,i){var a=this;this.options.request(this.options,n,void 0,function(s,l){if(l&&(l.status>=500&&l.status<600||!l.status))return r("failed loading "+n+"; status code: "+l.status,!0);if(l&&l.status>=400&&l.status<500)return r("failed loading "+n+"; status code: "+l.status,!1);if(!l&&s&&s.message&&s.message.indexOf("Failed to fetch")>-1)return r("failed loading "+n+": "+s.message,!0);if(s)return r(s,!1);var u,c;try{typeof l.data=="string"?u=a.options.parse(l.data,o,i):u=l.data}catch{c="failed parsing "+n+" to json"}if(c)return r(c,!1);r(null,u)})}},{key:"create",value:function(n,r,o,i,a){var s=this;if(this.options.addPath){typeof n=="string"&&(n=[n]);var l=this.options.parsePayload(r,o,i),u=0,c=[],f=[];n.forEach(function(d){var p=s.options.addPath;typeof s.options.addPath=="function"&&(p=s.options.addPath(d,r));var v=s.services.interpolator.interpolate(p,{lng:d,ns:r});s.options.request(s.options,v,l,function(y,_){u+=1,c.push(y),f.push(_),u===n.length&&typeof a=="function"&&a(c,f)})})}}},{key:"reload",value:function(){var n=this,r=this.services,o=r.backendConnector,i=r.languageUtils,a=r.logger,s=o.language;if(!(s&&s.toLowerCase()==="cimode")){var l=[],u=function(f){var d=i.toResolveHierarchy(f);d.forEach(function(p){l.indexOf(p)<0&&l.push(p)})};u(s),this.allOptions.preload&&this.allOptions.preload.forEach(function(c){return u(c)}),l.forEach(function(c){n.allOptions.ns.forEach(function(f){o.read(c,f,"read",null,null,function(d,p){d&&a.warn("loading namespace ".concat(f," for language ").concat(c," failed"),d),!d&&p&&a.log("loaded namespace ".concat(f," for language ").concat(c),p),o.loaded("".concat(c,"|").concat(f),d,p)})})})}}}]),e}();Pg.type="backend";function _b(){if(console&&console.warn){for(var e,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];typeof n[0]=="string"&&(n[0]="react-i18next:: ".concat(n[0])),(e=console).warn.apply(e,n)}}var _p={};function dc(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];typeof t[0]=="string"&&_p[t[0]]||(typeof t[0]=="string"&&(_p[t[0]]=new Date),_b.apply(void 0,t))}function bp(e,t,n){e.loadNamespaces(t,function(){if(e.isInitialized)n();else{var r=function o(){setTimeout(function(){e.off("initialized",o)},0),n()};e.on("initialized",r)}})}function bb(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=t.languages[0],o=t.options?t.options.fallbackLng:!1,i=t.languages[t.languages.length-1];if(r.toLowerCase()==="cimode")return!0;var a=function(l,u){var c=t.services.backendConnector.state["".concat(l,"|").concat(u)];return c===-1||c===2};return n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!a(t.isLanguageChangingTo,e)?!1:!!(t.hasResourceBundle(r,e)||!t.services.backendConnector.backend||t.options.resources&&!t.options.partialBundledLanguages||a(r,e)&&(!o||a(i,e)))}function Eb(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!t.languages||!t.languages.length)return dc("i18n.languages were undefined or empty",t.languages),!0;var r=t.options.ignoreJSONStructure!==void 0;return r?t.hasLoadedNamespace(e,{precheck:function(i,a){if(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&i.services.backendConnector.backend&&i.isLanguageChangingTo&&!a(i.isLanguageChangingTo,e))return!1}}):bb(e,t,n)}var Cb=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Rb={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Ob=function(t){return Rb[t]},xb=function(t){return t.replace(Cb,Ob)};function Ep(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Cp(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Ep(Object(n),!0).forEach(function(r){cn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ep(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var hc={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:xb};function kb(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};hc=Cp(Cp({},hc),e)}function Pb(){return hc}var Tg;function Tb(e){Tg=e}function Lb(){return Tg}var Nb={type:"3rdParty",init:function(t){kb(t.options.react),Tb(t)}},Ab=L.createContext(),Mb=function(){function e(){At(this,e),this.usedNamespaces={}}return Mt(e,[{key:"addUsedNamespaces",value:function(n){var r=this;n.forEach(function(o){r.usedNamespaces[o]||(r.usedNamespaces[o]=!0)})}},{key:"getUsedNamespaces",value:function(){return Object.keys(this.usedNamespaces)}}]),e}();function Ib(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r,o,i,a,s=[],l=!0,u=!1;try{if(i=(n=n.call(e)).next,t===0){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(c){u=!0,o=c}finally{try{if(!l&&n.return!=null&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}function Db(e,t){return gg(e)||Ib(e,t)||yg(e,t)||wg()}function Rp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function mu(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Rp(Object(n),!0).forEach(function(r){cn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Rp(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var $b=function(t,n){var r=L.useRef();return L.useEffect(function(){r.current=n?r.current:t},[t,n]),r.current};function No(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.i18n,r=L.useContext(Ab)||{},o=r.i18n,i=r.defaultNS,a=n||o||Lb();if(a&&!a.reportNamespaces&&(a.reportNamespaces=new Mb),!a){dc("You will need to pass in an i18next instance by using initReactI18next");var s=function(G){return Array.isArray(G)?G[G.length-1]:G},l=[s,{},!1];return l.t=s,l.i18n={},l.ready=!1,l}a.options.react&&a.options.react.wait!==void 0&&dc("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");var u=mu(mu(mu({},Pb()),a.options.react),t),c=u.useSuspense,f=u.keyPrefix,d=e||i||a.options&&a.options.defaultNS;d=typeof d=="string"?[d]:d||["translation"],a.reportNamespaces.addUsedNamespaces&&a.reportNamespaces.addUsedNamespaces(d);var p=(a.isInitialized||a.initializedStoreOnce)&&d.every(function(M){return Eb(M,a,u)});function v(){return a.getFixedT(null,u.nsMode==="fallback"?d:d[0],f)}var y=L.useState(v),_=Db(y,2),m=_[0],h=_[1],g=d.join(),S=$b(g),k=L.useRef(!0);L.useEffect(function(){var M=u.bindI18n,G=u.bindI18nStore;k.current=!0,!p&&!c&&bp(a,d,function(){k.current&&h(v)}),p&&S&&S!==g&&k.current&&h(v);function $(){k.current&&h(v)}return M&&a&&a.on(M,$),G&&a&&a.store.on(G,$),function(){k.current=!1,M&&a&&M.split(" ").forEach(function(X){return a.off(X,$)}),G&&a&&G.split(" ").forEach(function(X){return a.store.off(X,$)})}},[a,g]);var T=L.useRef(!0);L.useEffect(function(){k.current&&!T.current&&h(v),T.current=!1},[a,f]);var N=[m,a,p];if(N.t=m,N.i18n=a,N.ready=p,p||!p&&!c)return N;throw new Promise(function(M){bp(a,d,function(){M()})})}const Ko={zh_cn:Ot(()=>import("./zh-cn-d82b820c.js"),[],import.meta.url),zh_tw:Ot(()=>import("./zh-tw-9cdfa61b.js"),[],import.meta.url),en:Ot(()=>import("./en-f84bd3d3.js"),[],import.meta.url),vi:Ot(()=>import("./vi-75c7db25.js"),[],import.meta.url)};Ye.use(Pg).use(Nb).use(Cg).init({debug:!1,backend:{loadPath:"/__{{lng}}/{{ns}}.json",request:function(e,t,n,r){let o;switch(t){case"/__zh/translation.json":case"/__zh-CN/translation.json":o=Ko.zh_cn;break;case"/__zh-TW/translation.json":o=Ko.zh_tw;break;case"/__en/translation.json":o=Ko.en;break;case"/__vi/translation.json":o=Ko.vi;break;default:o=Ko.zh_cn;break}o&&o.then(i=>{r(null,{status:200,data:i.data})})}},supportedLngs:["zh-CN","zh-TW","en","vi"],load:"currentOnly",fallbackLng:"en",interpolation:{escapeValue:!1}});var mo={},Ub={get exports(){return mo},set exports(e){mo=e}},wt={},pc={},Fb={get exports(){return pc},set exports(e){pc=e}},Lg={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(O,A){var D=O.length;O.push(A);e:for(;0<D;){var z=D-1>>>1,b=O[z];if(0<o(b,A))O[z]=A,O[D]=b,D=z;else break e}}function n(O){return O.length===0?null:O[0]}function r(O){if(O.length===0)return null;var A=O[0],D=O.pop();if(D!==A){O[0]=D;e:for(var z=0,b=O.length,U=b>>>1;z<U;){var B=2*(z+1)-1,J=O[B],W=B+1,Z=O[W];if(0>o(J,D))W<b&&0>o(Z,J)?(O[z]=Z,O[W]=D,z=W):(O[z]=J,O[B]=D,z=B);else if(W<b&&0>o(Z,D))O[z]=Z,O[W]=D,z=W;else break e}}return A}function o(O,A){var D=O.sortIndex-A.sortIndex;return D!==0?D:O.id-A.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,s=a.now();e.unstable_now=function(){return a.now()-s}}var l=[],u=[],c=1,f=null,d=3,p=!1,v=!1,y=!1,_=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(O){for(var A=n(u);A!==null;){if(A.callback===null)r(u);else if(A.startTime<=O)r(u),A.sortIndex=A.expirationTime,t(l,A);else break;A=n(u)}}function S(O){if(y=!1,g(O),!v)if(n(l)!==null)v=!0,I(k);else{var A=n(u);A!==null&&C(S,A.startTime-O)}}function k(O,A){v=!1,y&&(y=!1,m(M),M=-1),p=!0;var D=d;try{for(g(A),f=n(l);f!==null&&(!(f.expirationTime>A)||O&&!X());){var z=f.callback;if(typeof z=="function"){f.callback=null,d=f.priorityLevel;var b=z(f.expirationTime<=A);A=e.unstable_now(),typeof b=="function"?f.callback=b:f===n(l)&&r(l),g(A)}else r(l);f=n(l)}if(f!==null)var U=!0;else{var B=n(u);B!==null&&C(S,B.startTime-A),U=!1}return U}finally{f=null,d=D,p=!1}}var T=!1,N=null,M=-1,G=5,$=-1;function X(){return!(e.unstable_now()-$<G)}function ce(){if(N!==null){var O=e.unstable_now();$=O;var A=!0;try{A=N(!0,O)}finally{A?re():(T=!1,N=null)}}else T=!1}var re;if(typeof h=="function")re=function(){h(ce)};else if(typeof MessageChannel<"u"){var w=new MessageChannel,P=w.port2;w.port1.onmessage=ce,re=function(){P.postMessage(null)}}else re=function(){_(ce,0)};function I(O){N=O,T||(T=!0,re())}function C(O,A){M=_(function(){O(e.unstable_now())},A)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(O){O.callback=null},e.unstable_continueExecution=function(){v||p||(v=!0,I(k))},e.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<O?Math.floor(1e3/O):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(O){switch(d){case 1:case 2:case 3:var A=3;break;default:A=d}var D=d;d=A;try{return O()}finally{d=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(O,A){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var D=d;d=O;try{return A()}finally{d=D}},e.unstable_scheduleCallback=function(O,A,D){var z=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?z+D:z):D=z,O){case 1:var b=-1;break;case 2:b=250;break;case 5:b=**********;break;case 4:b=1e4;break;default:b=5e3}return b=D+b,O={id:c++,callback:A,priorityLevel:O,startTime:D,expirationTime:b,sortIndex:-1},D>z?(O.sortIndex=D,t(u,O),n(l)===null&&O===n(u)&&(y?(m(M),M=-1):y=!0,C(S,D-z))):(O.sortIndex=b,t(l,O),v||p||(v=!0,I(k))),O},e.unstable_shouldYield=X,e.unstable_wrapCallback=function(O){var A=d;return function(){var D=d;d=A;try{return O.apply(this,arguments)}finally{d=D}}}})(Lg);(function(e){e.exports=Lg})(Fb);/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ng=L,mt=pc;function j(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ag=new Set,Ii={};function Ir(e,t){go(e,t),go(e+"Capture",t)}function go(e,t){for(Ii[e]=t,e=0;e<t.length;e++)Ag.add(t[e])}var _n=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vc=Object.prototype.hasOwnProperty,jb=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Op={},xp={};function Bb(e){return vc.call(xp,e)?!0:vc.call(Op,e)?!1:jb.test(e)?xp[e]=!0:(Op[e]=!0,!1)}function zb(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Vb(e,t,n,r){if(t===null||typeof t>"u"||zb(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function nt(e,t,n,r,o,i,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var We={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){We[e]=new nt(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];We[t]=new nt(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){We[e]=new nt(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){We[e]=new nt(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){We[e]=new nt(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){We[e]=new nt(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){We[e]=new nt(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){We[e]=new nt(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){We[e]=new nt(e,5,!1,e.toLowerCase(),null,!1,!1)});var Zf=/[\-:]([a-z])/g;function ed(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Zf,ed);We[t]=new nt(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Zf,ed);We[t]=new nt(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Zf,ed);We[t]=new nt(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){We[e]=new nt(e,1,!1,e.toLowerCase(),null,!1,!1)});We.xlinkHref=new nt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){We[e]=new nt(e,1,!1,e.toLowerCase(),null,!0,!0)});function td(e,t,n,r){var o=We.hasOwnProperty(t)?We[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Vb(t,n,o,r)&&(n=null),r||o===null?Bb(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Rn=Ng.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Na=Symbol.for("react.element"),Kr=Symbol.for("react.portal"),Qr=Symbol.for("react.fragment"),nd=Symbol.for("react.strict_mode"),mc=Symbol.for("react.profiler"),Mg=Symbol.for("react.provider"),Ig=Symbol.for("react.context"),rd=Symbol.for("react.forward_ref"),gc=Symbol.for("react.suspense"),yc=Symbol.for("react.suspense_list"),od=Symbol.for("react.memo"),Ln=Symbol.for("react.lazy"),Dg=Symbol.for("react.offscreen"),kp=Symbol.iterator;function Qo(e){return e===null||typeof e!="object"?null:(e=kp&&e[kp]||e["@@iterator"],typeof e=="function"?e:null)}var Te=Object.assign,gu;function si(e){if(gu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);gu=t&&t[1]||""}return`
`+gu+e}var yu=!1;function wu(e,t){if(!e||yu)return"";yu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),a=o.length-1,s=i.length-1;1<=a&&0<=s&&o[a]!==i[s];)s--;for(;1<=a&&0<=s;a--,s--)if(o[a]!==i[s]){if(a!==1||s!==1)do if(a--,s--,0>s||o[a]!==i[s]){var l=`
`+o[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=a&&0<=s);break}}}finally{yu=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?si(e):""}function Wb(e){switch(e.tag){case 5:return si(e.type);case 16:return si("Lazy");case 13:return si("Suspense");case 19:return si("SuspenseList");case 0:case 2:case 15:return e=wu(e.type,!1),e;case 11:return e=wu(e.type.render,!1),e;case 1:return e=wu(e.type,!0),e;default:return""}}function wc(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Qr:return"Fragment";case Kr:return"Portal";case mc:return"Profiler";case nd:return"StrictMode";case gc:return"Suspense";case yc:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ig:return(e.displayName||"Context")+".Consumer";case Mg:return(e._context.displayName||"Context")+".Provider";case rd:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case od:return t=e.displayName||null,t!==null?t:wc(e.type)||"Memo";case Ln:t=e._payload,e=e._init;try{return wc(e(t))}catch{}}return null}function Hb(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return wc(t);case 8:return t===nd?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function er(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function $g(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function qb(e){var t=$g(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Aa(e){e._valueTracker||(e._valueTracker=qb(e))}function Ug(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$g(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ns(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Sc(e,t){var n=t.checked;return Te({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Pp(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=er(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Fg(e,t){t=t.checked,t!=null&&td(e,"checked",t,!1)}function _c(e,t){Fg(e,t);var n=er(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?bc(e,t.type,n):t.hasOwnProperty("defaultValue")&&bc(e,t.type,er(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Tp(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function bc(e,t,n){(t!=="number"||Ns(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var li=Array.isArray;function lo(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+er(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ec(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(j(91));return Te({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Lp(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(j(92));if(li(n)){if(1<n.length)throw Error(j(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:er(n)}}function jg(e,t){var n=er(t.value),r=er(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Np(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Bg(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Cc(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Bg(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ma,zg=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ma=Ma||document.createElement("div"),Ma.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ma.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Di(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var pi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Kb=["Webkit","ms","Moz","O"];Object.keys(pi).forEach(function(e){Kb.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pi[t]=pi[e]})});function Vg(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||pi.hasOwnProperty(e)&&pi[e]?(""+t).trim():t+"px"}function Wg(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Vg(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Qb=Te({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Rc(e,t){if(t){if(Qb[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(j(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(j(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(j(61))}if(t.style!=null&&typeof t.style!="object")throw Error(j(62))}}function Oc(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xc=null;function id(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var kc=null,uo=null,co=null;function Ap(e){if(e=fa(e)){if(typeof kc!="function")throw Error(j(280));var t=e.stateNode;t&&(t=Tl(t),kc(e.stateNode,e.type,t))}}function Hg(e){uo?co?co.push(e):co=[e]:uo=e}function qg(){if(uo){var e=uo,t=co;if(co=uo=null,Ap(e),t)for(e=0;e<t.length;e++)Ap(t[e])}}function Kg(e,t){return e(t)}function Qg(){}var Su=!1;function Gg(e,t,n){if(Su)return e(t,n);Su=!0;try{return Kg(e,t,n)}finally{Su=!1,(uo!==null||co!==null)&&(Qg(),qg())}}function $i(e,t){var n=e.stateNode;if(n===null)return null;var r=Tl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(j(231,t,typeof n));return n}var Pc=!1;if(_n)try{var Go={};Object.defineProperty(Go,"passive",{get:function(){Pc=!0}}),window.addEventListener("test",Go,Go),window.removeEventListener("test",Go,Go)}catch{Pc=!1}function Gb(e,t,n,r,o,i,a,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var vi=!1,As=null,Ms=!1,Tc=null,Xb={onError:function(e){vi=!0,As=e}};function Yb(e,t,n,r,o,i,a,s,l){vi=!1,As=null,Gb.apply(Xb,arguments)}function Jb(e,t,n,r,o,i,a,s,l){if(Yb.apply(this,arguments),vi){if(vi){var u=As;vi=!1,As=null}else throw Error(j(198));Ms||(Ms=!0,Tc=u)}}function Dr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Xg(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Mp(e){if(Dr(e)!==e)throw Error(j(188))}function Zb(e){var t=e.alternate;if(!t){if(t=Dr(e),t===null)throw Error(j(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Mp(o),e;if(i===r)return Mp(o),t;i=i.sibling}throw Error(j(188))}if(n.return!==r.return)n=o,r=i;else{for(var a=!1,s=o.child;s;){if(s===n){a=!0,n=o,r=i;break}if(s===r){a=!0,r=o,n=i;break}s=s.sibling}if(!a){for(s=i.child;s;){if(s===n){a=!0,n=i,r=o;break}if(s===r){a=!0,r=i,n=o;break}s=s.sibling}if(!a)throw Error(j(189))}}if(n.alternate!==r)throw Error(j(190))}if(n.tag!==3)throw Error(j(188));return n.stateNode.current===n?e:t}function Yg(e){return e=Zb(e),e!==null?Jg(e):null}function Jg(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Jg(e);if(t!==null)return t;e=e.sibling}return null}var Zg=mt.unstable_scheduleCallback,Ip=mt.unstable_cancelCallback,eE=mt.unstable_shouldYield,tE=mt.unstable_requestPaint,Ae=mt.unstable_now,nE=mt.unstable_getCurrentPriorityLevel,ad=mt.unstable_ImmediatePriority,ey=mt.unstable_UserBlockingPriority,Is=mt.unstable_NormalPriority,rE=mt.unstable_LowPriority,ty=mt.unstable_IdlePriority,Ol=null,an=null;function oE(e){if(an&&typeof an.onCommitFiberRoot=="function")try{an.onCommitFiberRoot(Ol,e,void 0,(e.current.flags&128)===128)}catch{}}var qt=Math.clz32?Math.clz32:sE,iE=Math.log,aE=Math.LN2;function sE(e){return e>>>=0,e===0?32:31-(iE(e)/aE|0)|0}var Ia=64,Da=4194304;function ui(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ds(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,a=n&268435455;if(a!==0){var s=a&~o;s!==0?r=ui(s):(i&=a,i!==0&&(r=ui(i)))}else a=n&~o,a!==0?r=ui(a):i!==0&&(r=ui(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-qt(t),o=1<<n,r|=e[n],t&=~o;return r}function lE(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function uE(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-qt(i),s=1<<a,l=o[a];l===-1?(!(s&n)||s&r)&&(o[a]=lE(s,t)):l<=t&&(e.expiredLanes|=s),i&=~s}}function Lc(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ny(){var e=Ia;return Ia<<=1,!(Ia&4194240)&&(Ia=64),e}function _u(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ua(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-qt(t),e[t]=n}function cE(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-qt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function sd(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-qt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ve=0;function ry(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var oy,ld,iy,ay,sy,Nc=!1,$a=[],Vn=null,Wn=null,Hn=null,Ui=new Map,Fi=new Map,In=[],fE="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dp(e,t){switch(e){case"focusin":case"focusout":Vn=null;break;case"dragenter":case"dragleave":Wn=null;break;case"mouseover":case"mouseout":Hn=null;break;case"pointerover":case"pointerout":Ui.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Fi.delete(t.pointerId)}}function Xo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=fa(t),t!==null&&ld(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function dE(e,t,n,r,o){switch(t){case"focusin":return Vn=Xo(Vn,e,t,n,r,o),!0;case"dragenter":return Wn=Xo(Wn,e,t,n,r,o),!0;case"mouseover":return Hn=Xo(Hn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Ui.set(i,Xo(Ui.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Fi.set(i,Xo(Fi.get(i)||null,e,t,n,r,o)),!0}return!1}function ly(e){var t=mr(e.target);if(t!==null){var n=Dr(t);if(n!==null){if(t=n.tag,t===13){if(t=Xg(n),t!==null){e.blockedOn=t,sy(e.priority,function(){iy(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function fs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ac(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);xc=r,n.target.dispatchEvent(r),xc=null}else return t=fa(n),t!==null&&ld(t),e.blockedOn=n,!1;t.shift()}return!0}function $p(e,t,n){fs(e)&&n.delete(t)}function hE(){Nc=!1,Vn!==null&&fs(Vn)&&(Vn=null),Wn!==null&&fs(Wn)&&(Wn=null),Hn!==null&&fs(Hn)&&(Hn=null),Ui.forEach($p),Fi.forEach($p)}function Yo(e,t){e.blockedOn===t&&(e.blockedOn=null,Nc||(Nc=!0,mt.unstable_scheduleCallback(mt.unstable_NormalPriority,hE)))}function ji(e){function t(o){return Yo(o,e)}if(0<$a.length){Yo($a[0],e);for(var n=1;n<$a.length;n++){var r=$a[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Vn!==null&&Yo(Vn,e),Wn!==null&&Yo(Wn,e),Hn!==null&&Yo(Hn,e),Ui.forEach(t),Fi.forEach(t),n=0;n<In.length;n++)r=In[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<In.length&&(n=In[0],n.blockedOn===null);)ly(n),n.blockedOn===null&&In.shift()}var fo=Rn.ReactCurrentBatchConfig,$s=!0;function pE(e,t,n,r){var o=ve,i=fo.transition;fo.transition=null;try{ve=1,ud(e,t,n,r)}finally{ve=o,fo.transition=i}}function vE(e,t,n,r){var o=ve,i=fo.transition;fo.transition=null;try{ve=4,ud(e,t,n,r)}finally{ve=o,fo.transition=i}}function ud(e,t,n,r){if($s){var o=Ac(e,t,n,r);if(o===null)Lu(e,t,r,Us,n),Dp(e,r);else if(dE(o,e,t,n,r))r.stopPropagation();else if(Dp(e,r),t&4&&-1<fE.indexOf(e)){for(;o!==null;){var i=fa(o);if(i!==null&&oy(i),i=Ac(e,t,n,r),i===null&&Lu(e,t,r,Us,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Lu(e,t,r,null,n)}}var Us=null;function Ac(e,t,n,r){if(Us=null,e=id(r),e=mr(e),e!==null)if(t=Dr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Xg(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Us=e,null}function uy(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(nE()){case ad:return 1;case ey:return 4;case Is:case rE:return 16;case ty:return 536870912;default:return 16}default:return 16}}var $n=null,cd=null,ds=null;function cy(){if(ds)return ds;var e,t=cd,n=t.length,r,o="value"in $n?$n.value:$n.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===o[i-r];r++);return ds=o.slice(e,1<r?1-r:void 0)}function hs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ua(){return!0}function Up(){return!1}function St(e){function t(n,r,o,i,a){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ua:Up,this.isPropagationStopped=Up,this}return Te(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ua)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ua)},persist:function(){},isPersistent:Ua}),t}var Ao={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},fd=St(Ao),ca=Te({},Ao,{view:0,detail:0}),mE=St(ca),bu,Eu,Jo,xl=Te({},ca,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:dd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Jo&&(Jo&&e.type==="mousemove"?(bu=e.screenX-Jo.screenX,Eu=e.screenY-Jo.screenY):Eu=bu=0,Jo=e),bu)},movementY:function(e){return"movementY"in e?e.movementY:Eu}}),Fp=St(xl),gE=Te({},xl,{dataTransfer:0}),yE=St(gE),wE=Te({},ca,{relatedTarget:0}),Cu=St(wE),SE=Te({},Ao,{animationName:0,elapsedTime:0,pseudoElement:0}),_E=St(SE),bE=Te({},Ao,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),EE=St(bE),CE=Te({},Ao,{data:0}),jp=St(CE),RE={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},OE={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},xE={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function kE(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=xE[e])?!!t[e]:!1}function dd(){return kE}var PE=Te({},ca,{key:function(e){if(e.key){var t=RE[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=hs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?OE[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:dd,charCode:function(e){return e.type==="keypress"?hs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?hs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),TE=St(PE),LE=Te({},xl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Bp=St(LE),NE=Te({},ca,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:dd}),AE=St(NE),ME=Te({},Ao,{propertyName:0,elapsedTime:0,pseudoElement:0}),IE=St(ME),DE=Te({},xl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),$E=St(DE),UE=[9,13,27,32],hd=_n&&"CompositionEvent"in window,mi=null;_n&&"documentMode"in document&&(mi=document.documentMode);var FE=_n&&"TextEvent"in window&&!mi,fy=_n&&(!hd||mi&&8<mi&&11>=mi),zp=String.fromCharCode(32),Vp=!1;function dy(e,t){switch(e){case"keyup":return UE.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function hy(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Gr=!1;function jE(e,t){switch(e){case"compositionend":return hy(t);case"keypress":return t.which!==32?null:(Vp=!0,zp);case"textInput":return e=t.data,e===zp&&Vp?null:e;default:return null}}function BE(e,t){if(Gr)return e==="compositionend"||!hd&&dy(e,t)?(e=cy(),ds=cd=$n=null,Gr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return fy&&t.locale!=="ko"?null:t.data;default:return null}}var zE={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wp(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!zE[e.type]:t==="textarea"}function py(e,t,n,r){Hg(r),t=Fs(t,"onChange"),0<t.length&&(n=new fd("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var gi=null,Bi=null;function VE(e){Ry(e,0)}function kl(e){var t=Jr(e);if(Ug(t))return e}function WE(e,t){if(e==="change")return t}var vy=!1;if(_n){var Ru;if(_n){var Ou="oninput"in document;if(!Ou){var Hp=document.createElement("div");Hp.setAttribute("oninput","return;"),Ou=typeof Hp.oninput=="function"}Ru=Ou}else Ru=!1;vy=Ru&&(!document.documentMode||9<document.documentMode)}function qp(){gi&&(gi.detachEvent("onpropertychange",my),Bi=gi=null)}function my(e){if(e.propertyName==="value"&&kl(Bi)){var t=[];py(t,Bi,e,id(e)),Gg(VE,t)}}function HE(e,t,n){e==="focusin"?(qp(),gi=t,Bi=n,gi.attachEvent("onpropertychange",my)):e==="focusout"&&qp()}function qE(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return kl(Bi)}function KE(e,t){if(e==="click")return kl(t)}function QE(e,t){if(e==="input"||e==="change")return kl(t)}function GE(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Gt=typeof Object.is=="function"?Object.is:GE;function zi(e,t){if(Gt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!vc.call(t,o)||!Gt(e[o],t[o]))return!1}return!0}function Kp(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Qp(e,t){var n=Kp(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Kp(n)}}function gy(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?gy(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function yy(){for(var e=window,t=Ns();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ns(e.document)}return t}function pd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function XE(e){var t=yy(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&gy(n.ownerDocument.documentElement,n)){if(r!==null&&pd(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Qp(n,i);var a=Qp(n,r);o&&a&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var YE=_n&&"documentMode"in document&&11>=document.documentMode,Xr=null,Mc=null,yi=null,Ic=!1;function Gp(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ic||Xr==null||Xr!==Ns(r)||(r=Xr,"selectionStart"in r&&pd(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),yi&&zi(yi,r)||(yi=r,r=Fs(Mc,"onSelect"),0<r.length&&(t=new fd("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Xr)))}function Fa(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Yr={animationend:Fa("Animation","AnimationEnd"),animationiteration:Fa("Animation","AnimationIteration"),animationstart:Fa("Animation","AnimationStart"),transitionend:Fa("Transition","TransitionEnd")},xu={},wy={};_n&&(wy=document.createElement("div").style,"AnimationEvent"in window||(delete Yr.animationend.animation,delete Yr.animationiteration.animation,delete Yr.animationstart.animation),"TransitionEvent"in window||delete Yr.transitionend.transition);function Pl(e){if(xu[e])return xu[e];if(!Yr[e])return e;var t=Yr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in wy)return xu[e]=t[n];return e}var Sy=Pl("animationend"),_y=Pl("animationiteration"),by=Pl("animationstart"),Ey=Pl("transitionend"),Cy=new Map,Xp="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function nr(e,t){Cy.set(e,t),Ir(t,[e])}for(var ku=0;ku<Xp.length;ku++){var Pu=Xp[ku],JE=Pu.toLowerCase(),ZE=Pu[0].toUpperCase()+Pu.slice(1);nr(JE,"on"+ZE)}nr(Sy,"onAnimationEnd");nr(_y,"onAnimationIteration");nr(by,"onAnimationStart");nr("dblclick","onDoubleClick");nr("focusin","onFocus");nr("focusout","onBlur");nr(Ey,"onTransitionEnd");go("onMouseEnter",["mouseout","mouseover"]);go("onMouseLeave",["mouseout","mouseover"]);go("onPointerEnter",["pointerout","pointerover"]);go("onPointerLeave",["pointerout","pointerover"]);Ir("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ir("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ir("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ir("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ir("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ir("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ci="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),e2=new Set("cancel close invalid load scroll toggle".split(" ").concat(ci));function Yp(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Jb(r,t,void 0,e),e.currentTarget=null}function Ry(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var s=r[a],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==i&&o.isPropagationStopped())break e;Yp(o,s,u),i=l}else for(a=0;a<r.length;a++){if(s=r[a],l=s.instance,u=s.currentTarget,s=s.listener,l!==i&&o.isPropagationStopped())break e;Yp(o,s,u),i=l}}}if(Ms)throw e=Tc,Ms=!1,Tc=null,e}function _e(e,t){var n=t[jc];n===void 0&&(n=t[jc]=new Set);var r=e+"__bubble";n.has(r)||(Oy(t,e,2,!1),n.add(r))}function Tu(e,t,n){var r=0;t&&(r|=4),Oy(n,e,r,t)}var ja="_reactListening"+Math.random().toString(36).slice(2);function Vi(e){if(!e[ja]){e[ja]=!0,Ag.forEach(function(n){n!=="selectionchange"&&(e2.has(n)||Tu(n,!1,e),Tu(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ja]||(t[ja]=!0,Tu("selectionchange",!1,t))}}function Oy(e,t,n,r){switch(uy(t)){case 1:var o=pE;break;case 4:o=vE;break;default:o=ud}n=o.bind(null,t,n,e),o=void 0,!Pc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Lu(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(a===4)for(a=r.return;a!==null;){var l=a.tag;if((l===3||l===4)&&(l=a.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;a=a.return}for(;s!==null;){if(a=mr(s),a===null)return;if(l=a.tag,l===5||l===6){r=i=a;continue e}s=s.parentNode}}r=r.return}Gg(function(){var u=i,c=id(n),f=[];e:{var d=Cy.get(e);if(d!==void 0){var p=fd,v=e;switch(e){case"keypress":if(hs(n)===0)break e;case"keydown":case"keyup":p=TE;break;case"focusin":v="focus",p=Cu;break;case"focusout":v="blur",p=Cu;break;case"beforeblur":case"afterblur":p=Cu;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":p=Fp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":p=yE;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":p=AE;break;case Sy:case _y:case by:p=_E;break;case Ey:p=IE;break;case"scroll":p=mE;break;case"wheel":p=$E;break;case"copy":case"cut":case"paste":p=EE;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":p=Bp}var y=(t&4)!==0,_=!y&&e==="scroll",m=y?d!==null?d+"Capture":null:d;y=[];for(var h=u,g;h!==null;){g=h;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,m!==null&&(S=$i(h,m),S!=null&&y.push(Wi(h,S,g)))),_)break;h=h.return}0<y.length&&(d=new p(d,v,null,n,c),f.push({event:d,listeners:y}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",p=e==="mouseout"||e==="pointerout",d&&n!==xc&&(v=n.relatedTarget||n.fromElement)&&(mr(v)||v[bn]))break e;if((p||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,p?(v=n.relatedTarget||n.toElement,p=u,v=v?mr(v):null,v!==null&&(_=Dr(v),v!==_||v.tag!==5&&v.tag!==6)&&(v=null)):(p=null,v=u),p!==v)){if(y=Fp,S="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(y=Bp,S="onPointerLeave",m="onPointerEnter",h="pointer"),_=p==null?d:Jr(p),g=v==null?d:Jr(v),d=new y(S,h+"leave",p,n,c),d.target=_,d.relatedTarget=g,S=null,mr(c)===u&&(y=new y(m,h+"enter",v,n,c),y.target=g,y.relatedTarget=_,S=y),_=S,p&&v)t:{for(y=p,m=v,h=0,g=y;g;g=Hr(g))h++;for(g=0,S=m;S;S=Hr(S))g++;for(;0<h-g;)y=Hr(y),h--;for(;0<g-h;)m=Hr(m),g--;for(;h--;){if(y===m||m!==null&&y===m.alternate)break t;y=Hr(y),m=Hr(m)}y=null}else y=null;p!==null&&Jp(f,d,p,y,!1),v!==null&&_!==null&&Jp(f,_,v,y,!0)}}e:{if(d=u?Jr(u):window,p=d.nodeName&&d.nodeName.toLowerCase(),p==="select"||p==="input"&&d.type==="file")var k=WE;else if(Wp(d))if(vy)k=QE;else{k=qE;var T=HE}else(p=d.nodeName)&&p.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(k=KE);if(k&&(k=k(e,u))){py(f,k,n,c);break e}T&&T(e,d,u),e==="focusout"&&(T=d._wrapperState)&&T.controlled&&d.type==="number"&&bc(d,"number",d.value)}switch(T=u?Jr(u):window,e){case"focusin":(Wp(T)||T.contentEditable==="true")&&(Xr=T,Mc=u,yi=null);break;case"focusout":yi=Mc=Xr=null;break;case"mousedown":Ic=!0;break;case"contextmenu":case"mouseup":case"dragend":Ic=!1,Gp(f,n,c);break;case"selectionchange":if(YE)break;case"keydown":case"keyup":Gp(f,n,c)}var N;if(hd)e:{switch(e){case"compositionstart":var M="onCompositionStart";break e;case"compositionend":M="onCompositionEnd";break e;case"compositionupdate":M="onCompositionUpdate";break e}M=void 0}else Gr?dy(e,n)&&(M="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(M="onCompositionStart");M&&(fy&&n.locale!=="ko"&&(Gr||M!=="onCompositionStart"?M==="onCompositionEnd"&&Gr&&(N=cy()):($n=c,cd="value"in $n?$n.value:$n.textContent,Gr=!0)),T=Fs(u,M),0<T.length&&(M=new jp(M,e,null,n,c),f.push({event:M,listeners:T}),N?M.data=N:(N=hy(n),N!==null&&(M.data=N)))),(N=FE?jE(e,n):BE(e,n))&&(u=Fs(u,"onBeforeInput"),0<u.length&&(c=new jp("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=N))}Ry(f,t)})}function Wi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Fs(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=$i(e,n),i!=null&&r.unshift(Wi(e,i,o)),i=$i(e,t),i!=null&&r.push(Wi(e,i,o))),e=e.return}return r}function Hr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Jp(e,t,n,r,o){for(var i=t._reactName,a=[];n!==null&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(l!==null&&l===r)break;s.tag===5&&u!==null&&(s=u,o?(l=$i(n,i),l!=null&&a.unshift(Wi(n,l,s))):o||(l=$i(n,i),l!=null&&a.push(Wi(n,l,s)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var t2=/\r\n?/g,n2=/\u0000|\uFFFD/g;function Zp(e){return(typeof e=="string"?e:""+e).replace(t2,`
`).replace(n2,"")}function Ba(e,t,n){if(t=Zp(t),Zp(e)!==t&&n)throw Error(j(425))}function js(){}var Dc=null,$c=null;function Uc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Fc=typeof setTimeout=="function"?setTimeout:void 0,r2=typeof clearTimeout=="function"?clearTimeout:void 0,ev=typeof Promise=="function"?Promise:void 0,o2=typeof queueMicrotask=="function"?queueMicrotask:typeof ev<"u"?function(e){return ev.resolve(null).then(e).catch(i2)}:Fc;function i2(e){setTimeout(function(){throw e})}function Nu(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),ji(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);ji(t)}function qn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function tv(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Mo=Math.random().toString(36).slice(2),nn="__reactFiber$"+Mo,Hi="__reactProps$"+Mo,bn="__reactContainer$"+Mo,jc="__reactEvents$"+Mo,a2="__reactListeners$"+Mo,s2="__reactHandles$"+Mo;function mr(e){var t=e[nn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[bn]||n[nn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=tv(e);e!==null;){if(n=e[nn])return n;e=tv(e)}return t}e=n,n=e.parentNode}return null}function fa(e){return e=e[nn]||e[bn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Jr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(j(33))}function Tl(e){return e[Hi]||null}var Bc=[],Zr=-1;function rr(e){return{current:e}}function be(e){0>Zr||(e.current=Bc[Zr],Bc[Zr]=null,Zr--)}function Se(e,t){Zr++,Bc[Zr]=e.current,e.current=t}var tr={},Xe=rr(tr),st=rr(!1),Rr=tr;function yo(e,t){var n=e.type.contextTypes;if(!n)return tr;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function lt(e){return e=e.childContextTypes,e!=null}function Bs(){be(st),be(Xe)}function nv(e,t,n){if(Xe.current!==tr)throw Error(j(168));Se(Xe,t),Se(st,n)}function xy(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(j(108,Hb(e)||"Unknown",o));return Te({},n,r)}function zs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||tr,Rr=Xe.current,Se(Xe,e),Se(st,st.current),!0}function rv(e,t,n){var r=e.stateNode;if(!r)throw Error(j(169));n?(e=xy(e,t,Rr),r.__reactInternalMemoizedMergedChildContext=e,be(st),be(Xe),Se(Xe,e)):be(st),Se(st,n)}var pn=null,Ll=!1,Au=!1;function ky(e){pn===null?pn=[e]:pn.push(e)}function l2(e){Ll=!0,ky(e)}function or(){if(!Au&&pn!==null){Au=!0;var e=0,t=ve;try{var n=pn;for(ve=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}pn=null,Ll=!1}catch(o){throw pn!==null&&(pn=pn.slice(e+1)),Zg(ad,or),o}finally{ve=t,Au=!1}}return null}var eo=[],to=0,Vs=null,Ws=0,Ct=[],Rt=0,Or=null,mn=1,gn="";function fr(e,t){eo[to++]=Ws,eo[to++]=Vs,Vs=e,Ws=t}function Py(e,t,n){Ct[Rt++]=mn,Ct[Rt++]=gn,Ct[Rt++]=Or,Or=e;var r=mn;e=gn;var o=32-qt(r)-1;r&=~(1<<o),n+=1;var i=32-qt(t)+o;if(30<i){var a=o-o%5;i=(r&(1<<a)-1).toString(32),r>>=a,o-=a,mn=1<<32-qt(t)+o|n<<o|r,gn=i+e}else mn=1<<i|n<<o|r,gn=e}function vd(e){e.return!==null&&(fr(e,1),Py(e,1,0))}function md(e){for(;e===Vs;)Vs=eo[--to],eo[to]=null,Ws=eo[--to],eo[to]=null;for(;e===Or;)Or=Ct[--Rt],Ct[Rt]=null,gn=Ct[--Rt],Ct[Rt]=null,mn=Ct[--Rt],Ct[Rt]=null}var vt=null,pt=null,Ce=!1,Vt=null;function Ty(e,t){var n=xt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ov(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,vt=e,pt=qn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,vt=e,pt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Or!==null?{id:mn,overflow:gn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=xt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,vt=e,pt=null,!0):!1;default:return!1}}function zc(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Vc(e){if(Ce){var t=pt;if(t){var n=t;if(!ov(e,t)){if(zc(e))throw Error(j(418));t=qn(n.nextSibling);var r=vt;t&&ov(e,t)?Ty(r,n):(e.flags=e.flags&-4097|2,Ce=!1,vt=e)}}else{if(zc(e))throw Error(j(418));e.flags=e.flags&-4097|2,Ce=!1,vt=e}}}function iv(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;vt=e}function za(e){if(e!==vt)return!1;if(!Ce)return iv(e),Ce=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Uc(e.type,e.memoizedProps)),t&&(t=pt)){if(zc(e))throw Ly(),Error(j(418));for(;t;)Ty(e,t),t=qn(t.nextSibling)}if(iv(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(j(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){pt=qn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}pt=null}}else pt=vt?qn(e.stateNode.nextSibling):null;return!0}function Ly(){for(var e=pt;e;)e=qn(e.nextSibling)}function wo(){pt=vt=null,Ce=!1}function gd(e){Vt===null?Vt=[e]:Vt.push(e)}var u2=Rn.ReactCurrentBatchConfig;function Ft(e,t){if(e&&e.defaultProps){t=Te({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}var Hs=rr(null),qs=null,no=null,yd=null;function wd(){yd=no=qs=null}function Sd(e){var t=Hs.current;be(Hs),e._currentValue=t}function Wc(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ho(e,t){qs=e,yd=no=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(at=!0),e.firstContext=null)}function Lt(e){var t=e._currentValue;if(yd!==e)if(e={context:e,memoizedValue:t,next:null},no===null){if(qs===null)throw Error(j(308));no=e,qs.dependencies={lanes:0,firstContext:e}}else no=no.next=e;return t}var gr=null;function _d(e){gr===null?gr=[e]:gr.push(e)}function Ny(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,_d(t)):(n.next=o.next,o.next=n),t.interleaved=n,En(e,r)}function En(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Nn=!1;function bd(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ay(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Sn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Kn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,he&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,En(e,n)}return o=r.interleaved,o===null?(t.next=t,_d(r)):(t.next=o.next,o.next=t),r.interleaved=t,En(e,n)}function ps(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sd(e,n)}}function av(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ks(e,t,n,r){var o=e.updateQueue;Nn=!1;var i=o.firstBaseUpdate,a=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var l=s,u=l.next;l.next=null,a===null?i=u:a.next=u,a=l;var c=e.alternate;c!==null&&(c=c.updateQueue,s=c.lastBaseUpdate,s!==a&&(s===null?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(i!==null){var f=o.baseState;a=0,c=u=l=null,s=i;do{var d=s.lane,p=s.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:p,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var v=e,y=s;switch(d=t,p=n,y.tag){case 1:if(v=y.payload,typeof v=="function"){f=v.call(p,f,d);break e}f=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=y.payload,d=typeof v=="function"?v.call(p,f,d):v,d==null)break e;f=Te({},f,d);break e;case 2:Nn=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[s]:d.push(s))}else p={eventTime:p,lane:d,tag:s.tag,payload:s.payload,callback:s.callback,next:null},c===null?(u=c=p,l=f):c=c.next=p,a|=d;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;d=s,s=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(1);if(c===null&&(l=f),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do a|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);kr|=a,e.lanes=a,e.memoizedState=f}}function sv(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(j(191,o));o.call(r)}}}var My=new Ng.Component().refs;function Hc(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Te({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Nl={isMounted:function(e){return(e=e._reactInternals)?Dr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=et(),o=Gn(e),i=Sn(r,o);i.payload=t,n!=null&&(i.callback=n),t=Kn(e,i,o),t!==null&&(Kt(t,e,o,r),ps(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=et(),o=Gn(e),i=Sn(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Kn(e,i,o),t!==null&&(Kt(t,e,o,r),ps(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=et(),r=Gn(e),o=Sn(n,r);o.tag=2,t!=null&&(o.callback=t),t=Kn(e,o,r),t!==null&&(Kt(t,e,r,n),ps(t,e,r))}};function lv(e,t,n,r,o,i,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,a):t.prototype&&t.prototype.isPureReactComponent?!zi(n,r)||!zi(o,i):!0}function Iy(e,t,n){var r=!1,o=tr,i=t.contextType;return typeof i=="object"&&i!==null?i=Lt(i):(o=lt(t)?Rr:Xe.current,r=t.contextTypes,i=(r=r!=null)?yo(e,o):tr),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Nl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function uv(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Nl.enqueueReplaceState(t,t.state,null)}function qc(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=My,bd(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Lt(i):(i=lt(t)?Rr:Xe.current,o.context=yo(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Hc(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Nl.enqueueReplaceState(o,o.state,null),Ks(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Zo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(j(309));var r=n.stateNode}if(!r)throw Error(j(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(a){var s=o.refs;s===My&&(s=o.refs={}),a===null?delete s[i]:s[i]=a},t._stringRef=i,t)}if(typeof e!="string")throw Error(j(284));if(!n._owner)throw Error(j(290,e))}return e}function Va(e,t){throw e=Object.prototype.toString.call(t),Error(j(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function cv(e){var t=e._init;return t(e._payload)}function Dy(e){function t(m,h){if(e){var g=m.deletions;g===null?(m.deletions=[h],m.flags|=16):g.push(h)}}function n(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function r(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function o(m,h){return m=Xn(m,h),m.index=0,m.sibling=null,m}function i(m,h,g){return m.index=g,e?(g=m.alternate,g!==null?(g=g.index,g<h?(m.flags|=2,h):g):(m.flags|=2,h)):(m.flags|=1048576,h)}function a(m){return e&&m.alternate===null&&(m.flags|=2),m}function s(m,h,g,S){return h===null||h.tag!==6?(h=ju(g,m.mode,S),h.return=m,h):(h=o(h,g),h.return=m,h)}function l(m,h,g,S){var k=g.type;return k===Qr?c(m,h,g.props.children,S,g.key):h!==null&&(h.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Ln&&cv(k)===h.type)?(S=o(h,g.props),S.ref=Zo(m,h,g),S.return=m,S):(S=Ss(g.type,g.key,g.props,null,m.mode,S),S.ref=Zo(m,h,g),S.return=m,S)}function u(m,h,g,S){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=Bu(g,m.mode,S),h.return=m,h):(h=o(h,g.children||[]),h.return=m,h)}function c(m,h,g,S,k){return h===null||h.tag!==7?(h=_r(g,m.mode,S,k),h.return=m,h):(h=o(h,g),h.return=m,h)}function f(m,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ju(""+h,m.mode,g),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Na:return g=Ss(h.type,h.key,h.props,null,m.mode,g),g.ref=Zo(m,null,h),g.return=m,g;case Kr:return h=Bu(h,m.mode,g),h.return=m,h;case Ln:var S=h._init;return f(m,S(h._payload),g)}if(li(h)||Qo(h))return h=_r(h,m.mode,g,null),h.return=m,h;Va(m,h)}return null}function d(m,h,g,S){var k=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return k!==null?null:s(m,h,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Na:return g.key===k?l(m,h,g,S):null;case Kr:return g.key===k?u(m,h,g,S):null;case Ln:return k=g._init,d(m,h,k(g._payload),S)}if(li(g)||Qo(g))return k!==null?null:c(m,h,g,S,null);Va(m,g)}return null}function p(m,h,g,S,k){if(typeof S=="string"&&S!==""||typeof S=="number")return m=m.get(g)||null,s(h,m,""+S,k);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Na:return m=m.get(S.key===null?g:S.key)||null,l(h,m,S,k);case Kr:return m=m.get(S.key===null?g:S.key)||null,u(h,m,S,k);case Ln:var T=S._init;return p(m,h,g,T(S._payload),k)}if(li(S)||Qo(S))return m=m.get(g)||null,c(h,m,S,k,null);Va(h,S)}return null}function v(m,h,g,S){for(var k=null,T=null,N=h,M=h=0,G=null;N!==null&&M<g.length;M++){N.index>M?(G=N,N=null):G=N.sibling;var $=d(m,N,g[M],S);if($===null){N===null&&(N=G);break}e&&N&&$.alternate===null&&t(m,N),h=i($,h,M),T===null?k=$:T.sibling=$,T=$,N=G}if(M===g.length)return n(m,N),Ce&&fr(m,M),k;if(N===null){for(;M<g.length;M++)N=f(m,g[M],S),N!==null&&(h=i(N,h,M),T===null?k=N:T.sibling=N,T=N);return Ce&&fr(m,M),k}for(N=r(m,N);M<g.length;M++)G=p(N,m,M,g[M],S),G!==null&&(e&&G.alternate!==null&&N.delete(G.key===null?M:G.key),h=i(G,h,M),T===null?k=G:T.sibling=G,T=G);return e&&N.forEach(function(X){return t(m,X)}),Ce&&fr(m,M),k}function y(m,h,g,S){var k=Qo(g);if(typeof k!="function")throw Error(j(150));if(g=k.call(g),g==null)throw Error(j(151));for(var T=k=null,N=h,M=h=0,G=null,$=g.next();N!==null&&!$.done;M++,$=g.next()){N.index>M?(G=N,N=null):G=N.sibling;var X=d(m,N,$.value,S);if(X===null){N===null&&(N=G);break}e&&N&&X.alternate===null&&t(m,N),h=i(X,h,M),T===null?k=X:T.sibling=X,T=X,N=G}if($.done)return n(m,N),Ce&&fr(m,M),k;if(N===null){for(;!$.done;M++,$=g.next())$=f(m,$.value,S),$!==null&&(h=i($,h,M),T===null?k=$:T.sibling=$,T=$);return Ce&&fr(m,M),k}for(N=r(m,N);!$.done;M++,$=g.next())$=p(N,m,M,$.value,S),$!==null&&(e&&$.alternate!==null&&N.delete($.key===null?M:$.key),h=i($,h,M),T===null?k=$:T.sibling=$,T=$);return e&&N.forEach(function(ce){return t(m,ce)}),Ce&&fr(m,M),k}function _(m,h,g,S){if(typeof g=="object"&&g!==null&&g.type===Qr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Na:e:{for(var k=g.key,T=h;T!==null;){if(T.key===k){if(k=g.type,k===Qr){if(T.tag===7){n(m,T.sibling),h=o(T,g.props.children),h.return=m,m=h;break e}}else if(T.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Ln&&cv(k)===T.type){n(m,T.sibling),h=o(T,g.props),h.ref=Zo(m,T,g),h.return=m,m=h;break e}n(m,T);break}else t(m,T);T=T.sibling}g.type===Qr?(h=_r(g.props.children,m.mode,S,g.key),h.return=m,m=h):(S=Ss(g.type,g.key,g.props,null,m.mode,S),S.ref=Zo(m,h,g),S.return=m,m=S)}return a(m);case Kr:e:{for(T=g.key;h!==null;){if(h.key===T)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){n(m,h.sibling),h=o(h,g.children||[]),h.return=m,m=h;break e}else{n(m,h);break}else t(m,h);h=h.sibling}h=Bu(g,m.mode,S),h.return=m,m=h}return a(m);case Ln:return T=g._init,_(m,h,T(g._payload),S)}if(li(g))return v(m,h,g,S);if(Qo(g))return y(m,h,g,S);Va(m,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(n(m,h.sibling),h=o(h,g),h.return=m,m=h):(n(m,h),h=ju(g,m.mode,S),h.return=m,m=h),a(m)):n(m,h)}return _}var So=Dy(!0),$y=Dy(!1),da={},sn=rr(da),qi=rr(da),Ki=rr(da);function yr(e){if(e===da)throw Error(j(174));return e}function Ed(e,t){switch(Se(Ki,t),Se(qi,e),Se(sn,da),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Cc(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Cc(t,e)}be(sn),Se(sn,t)}function _o(){be(sn),be(qi),be(Ki)}function Uy(e){yr(Ki.current);var t=yr(sn.current),n=Cc(t,e.type);t!==n&&(Se(qi,e),Se(sn,n))}function Cd(e){qi.current===e&&(be(sn),be(qi))}var ke=rr(0);function Qs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Mu=[];function Rd(){for(var e=0;e<Mu.length;e++)Mu[e]._workInProgressVersionPrimary=null;Mu.length=0}var vs=Rn.ReactCurrentDispatcher,Iu=Rn.ReactCurrentBatchConfig,xr=0,Pe=null,De=null,je=null,Gs=!1,wi=!1,Qi=0,c2=0;function He(){throw Error(j(321))}function Od(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Gt(e[n],t[n]))return!1;return!0}function xd(e,t,n,r,o,i){if(xr=i,Pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,vs.current=e===null||e.memoizedState===null?p2:v2,e=n(r,o),wi){i=0;do{if(wi=!1,Qi=0,25<=i)throw Error(j(301));i+=1,je=De=null,t.updateQueue=null,vs.current=m2,e=n(r,o)}while(wi)}if(vs.current=Xs,t=De!==null&&De.next!==null,xr=0,je=De=Pe=null,Gs=!1,t)throw Error(j(300));return e}function kd(){var e=Qi!==0;return Qi=0,e}function tn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return je===null?Pe.memoizedState=je=e:je=je.next=e,je}function Nt(){if(De===null){var e=Pe.alternate;e=e!==null?e.memoizedState:null}else e=De.next;var t=je===null?Pe.memoizedState:je.next;if(t!==null)je=t,De=e;else{if(e===null)throw Error(j(310));De=e,e={memoizedState:De.memoizedState,baseState:De.baseState,baseQueue:De.baseQueue,queue:De.queue,next:null},je===null?Pe.memoizedState=je=e:je=je.next=e}return je}function Gi(e,t){return typeof t=="function"?t(e):t}function Du(e){var t=Nt(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=De,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var a=o.next;o.next=i.next,i.next=a}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=a=null,l=null,u=i;do{var c=u.lane;if((xr&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(s=l=f,a=r):l=l.next=f,Pe.lanes|=c,kr|=c}u=u.next}while(u!==null&&u!==i);l===null?a=r:l.next=s,Gt(r,t.memoizedState)||(at=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,Pe.lanes|=i,kr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function $u(e){var t=Nt(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var a=o=o.next;do i=e(i,a.action),a=a.next;while(a!==o);Gt(i,t.memoizedState)||(at=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Fy(){}function jy(e,t){var n=Pe,r=Nt(),o=t(),i=!Gt(r.memoizedState,o);if(i&&(r.memoizedState=o,at=!0),r=r.queue,Pd(Vy.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||je!==null&&je.memoizedState.tag&1){if(n.flags|=2048,Xi(9,zy.bind(null,n,r,o,t),void 0,null),Be===null)throw Error(j(349));xr&30||By(n,t,o)}return o}function By(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Pe.updateQueue,t===null?(t={lastEffect:null,stores:null},Pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function zy(e,t,n,r){t.value=n,t.getSnapshot=r,Wy(t)&&Hy(e)}function Vy(e,t,n){return n(function(){Wy(t)&&Hy(e)})}function Wy(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Gt(e,n)}catch{return!0}}function Hy(e){var t=En(e,1);t!==null&&Kt(t,e,1,-1)}function fv(e){var t=tn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Gi,lastRenderedState:e},t.queue=e,e=e.dispatch=h2.bind(null,Pe,e),[t.memoizedState,e]}function Xi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Pe.updateQueue,t===null?(t={lastEffect:null,stores:null},Pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function qy(){return Nt().memoizedState}function ms(e,t,n,r){var o=tn();Pe.flags|=e,o.memoizedState=Xi(1|t,n,void 0,r===void 0?null:r)}function Al(e,t,n,r){var o=Nt();r=r===void 0?null:r;var i=void 0;if(De!==null){var a=De.memoizedState;if(i=a.destroy,r!==null&&Od(r,a.deps)){o.memoizedState=Xi(t,n,i,r);return}}Pe.flags|=e,o.memoizedState=Xi(1|t,n,i,r)}function dv(e,t){return ms(8390656,8,e,t)}function Pd(e,t){return Al(2048,8,e,t)}function Ky(e,t){return Al(4,2,e,t)}function Qy(e,t){return Al(4,4,e,t)}function Gy(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Xy(e,t,n){return n=n!=null?n.concat([e]):null,Al(4,4,Gy.bind(null,t,e),n)}function Td(){}function Yy(e,t){var n=Nt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Od(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Jy(e,t){var n=Nt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Od(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Zy(e,t,n){return xr&21?(Gt(n,t)||(n=ny(),Pe.lanes|=n,kr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,at=!0),e.memoizedState=n)}function f2(e,t){var n=ve;ve=n!==0&&4>n?n:4,e(!0);var r=Iu.transition;Iu.transition={};try{e(!1),t()}finally{ve=n,Iu.transition=r}}function e0(){return Nt().memoizedState}function d2(e,t,n){var r=Gn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},t0(e))n0(t,n);else if(n=Ny(e,t,n,r),n!==null){var o=et();Kt(n,e,r,o),r0(n,t,r)}}function h2(e,t,n){var r=Gn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(t0(e))n0(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var a=t.lastRenderedState,s=i(a,n);if(o.hasEagerState=!0,o.eagerState=s,Gt(s,a)){var l=t.interleaved;l===null?(o.next=o,_d(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=Ny(e,t,o,r),n!==null&&(o=et(),Kt(n,e,r,o),r0(n,t,r))}}function t0(e){var t=e.alternate;return e===Pe||t!==null&&t===Pe}function n0(e,t){wi=Gs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function r0(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sd(e,n)}}var Xs={readContext:Lt,useCallback:He,useContext:He,useEffect:He,useImperativeHandle:He,useInsertionEffect:He,useLayoutEffect:He,useMemo:He,useReducer:He,useRef:He,useState:He,useDebugValue:He,useDeferredValue:He,useTransition:He,useMutableSource:He,useSyncExternalStore:He,useId:He,unstable_isNewReconciler:!1},p2={readContext:Lt,useCallback:function(e,t){return tn().memoizedState=[e,t===void 0?null:t],e},useContext:Lt,useEffect:dv,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ms(4194308,4,Gy.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ms(4194308,4,e,t)},useInsertionEffect:function(e,t){return ms(4,2,e,t)},useMemo:function(e,t){var n=tn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=d2.bind(null,Pe,e),[r.memoizedState,e]},useRef:function(e){var t=tn();return e={current:e},t.memoizedState=e},useState:fv,useDebugValue:Td,useDeferredValue:function(e){return tn().memoizedState=e},useTransition:function(){var e=fv(!1),t=e[0];return e=f2.bind(null,e[1]),tn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Pe,o=tn();if(Ce){if(n===void 0)throw Error(j(407));n=n()}else{if(n=t(),Be===null)throw Error(j(349));xr&30||By(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,dv(Vy.bind(null,r,i,e),[e]),r.flags|=2048,Xi(9,zy.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=tn(),t=Be.identifierPrefix;if(Ce){var n=gn,r=mn;n=(r&~(1<<32-qt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Qi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=c2++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},v2={readContext:Lt,useCallback:Yy,useContext:Lt,useEffect:Pd,useImperativeHandle:Xy,useInsertionEffect:Ky,useLayoutEffect:Qy,useMemo:Jy,useReducer:Du,useRef:qy,useState:function(){return Du(Gi)},useDebugValue:Td,useDeferredValue:function(e){var t=Nt();return Zy(t,De.memoizedState,e)},useTransition:function(){var e=Du(Gi)[0],t=Nt().memoizedState;return[e,t]},useMutableSource:Fy,useSyncExternalStore:jy,useId:e0,unstable_isNewReconciler:!1},m2={readContext:Lt,useCallback:Yy,useContext:Lt,useEffect:Pd,useImperativeHandle:Xy,useInsertionEffect:Ky,useLayoutEffect:Qy,useMemo:Jy,useReducer:$u,useRef:qy,useState:function(){return $u(Gi)},useDebugValue:Td,useDeferredValue:function(e){var t=Nt();return De===null?t.memoizedState=e:Zy(t,De.memoizedState,e)},useTransition:function(){var e=$u(Gi)[0],t=Nt().memoizedState;return[e,t]},useMutableSource:Fy,useSyncExternalStore:jy,useId:e0,unstable_isNewReconciler:!1};function bo(e,t){try{var n="",r=t;do n+=Wb(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Uu(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Kc(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var g2=typeof WeakMap=="function"?WeakMap:Map;function o0(e,t,n){n=Sn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Js||(Js=!0,rf=r),Kc(e,t)},n}function i0(e,t,n){n=Sn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Kc(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Kc(e,t),typeof r!="function"&&(Qn===null?Qn=new Set([this]):Qn.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function hv(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new g2;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=L2.bind(null,e,t,n),t.then(e,e))}function pv(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function vv(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Sn(-1,1),t.tag=2,Kn(n,t,1))),n.lanes|=1),e)}var y2=Rn.ReactCurrentOwner,at=!1;function Ze(e,t,n,r){t.child=e===null?$y(t,null,n,r):So(t,e.child,n,r)}function mv(e,t,n,r,o){n=n.render;var i=t.ref;return ho(t,o),r=xd(e,t,n,r,i,o),n=kd(),e!==null&&!at?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Cn(e,t,o)):(Ce&&n&&vd(t),t.flags|=1,Ze(e,t,r,o),t.child)}function gv(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Ud(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,a0(e,t,i,r,o)):(e=Ss(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:zi,n(a,r)&&e.ref===t.ref)return Cn(e,t,o)}return t.flags|=1,e=Xn(i,r),e.ref=t.ref,e.return=t,t.child=e}function a0(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(zi(i,r)&&e.ref===t.ref)if(at=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(at=!0);else return t.lanes=e.lanes,Cn(e,t,o)}return Qc(e,t,n,r,o)}function s0(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Se(oo,ht),ht|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Se(oo,ht),ht|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Se(oo,ht),ht|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Se(oo,ht),ht|=r;return Ze(e,t,o,n),t.child}function l0(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Qc(e,t,n,r,o){var i=lt(n)?Rr:Xe.current;return i=yo(t,i),ho(t,o),n=xd(e,t,n,r,i,o),r=kd(),e!==null&&!at?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Cn(e,t,o)):(Ce&&r&&vd(t),t.flags|=1,Ze(e,t,n,o),t.child)}function yv(e,t,n,r,o){if(lt(n)){var i=!0;zs(t)}else i=!1;if(ho(t,o),t.stateNode===null)gs(e,t),Iy(t,n,r),qc(t,n,r,o),r=!0;else if(e===null){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=Lt(u):(u=lt(n)?Rr:Xe.current,u=yo(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof a.getSnapshotBeforeUpdate=="function";f||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(s!==r||l!==u)&&uv(t,a,r,u),Nn=!1;var d=t.memoizedState;a.state=d,Ks(t,r,a,o),l=t.memoizedState,s!==r||d!==l||st.current||Nn?(typeof c=="function"&&(Hc(t,n,c,r),l=t.memoizedState),(s=Nn||lv(t,n,s,r,d,l,u))?(f||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=u,r=s):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Ay(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:Ft(t.type,s),a.props=u,f=t.pendingProps,d=a.context,l=n.contextType,typeof l=="object"&&l!==null?l=Lt(l):(l=lt(n)?Rr:Xe.current,l=yo(t,l));var p=n.getDerivedStateFromProps;(c=typeof p=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(s!==f||d!==l)&&uv(t,a,r,l),Nn=!1,d=t.memoizedState,a.state=d,Ks(t,r,a,o);var v=t.memoizedState;s!==f||d!==v||st.current||Nn?(typeof p=="function"&&(Hc(t,n,p,r),v=t.memoizedState),(u=Nn||lv(t,n,u,r,d,v,l)||!1)?(c||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,v,l),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,v,l)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),a.props=r,a.state=v,a.context=l,r=u):(typeof a.componentDidUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Gc(e,t,n,r,i,o)}function Gc(e,t,n,r,o,i){l0(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return o&&rv(t,n,!1),Cn(e,t,i);r=t.stateNode,y2.current=t;var s=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=So(t,e.child,null,i),t.child=So(t,null,s,i)):Ze(e,t,s,i),t.memoizedState=r.state,o&&rv(t,n,!0),t.child}function u0(e){var t=e.stateNode;t.pendingContext?nv(e,t.pendingContext,t.pendingContext!==t.context):t.context&&nv(e,t.context,!1),Ed(e,t.containerInfo)}function wv(e,t,n,r,o){return wo(),gd(o),t.flags|=256,Ze(e,t,n,r),t.child}var Xc={dehydrated:null,treeContext:null,retryLane:0};function Yc(e){return{baseLanes:e,cachePool:null,transitions:null}}function c0(e,t,n){var r=t.pendingProps,o=ke.current,i=!1,a=(t.flags&128)!==0,s;if((s=a)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Se(ke,o&1),e===null)return Vc(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,i?(r=t.mode,i=t.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=Dl(a,r,0,null),e=_r(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Yc(n),t.memoizedState=Xc,e):Ld(t,a));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return w2(e,t,a,r,s,o,n);if(i){i=r.fallback,a=t.mode,o=e.child,s=o.sibling;var l={mode:"hidden",children:r.children};return!(a&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Xn(o,l),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=Xn(s,i):(i=_r(i,a,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,a=e.child.memoizedState,a=a===null?Yc(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=e.childLanes&~n,t.memoizedState=Xc,r}return i=e.child,e=i.sibling,r=Xn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ld(e,t){return t=Dl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Wa(e,t,n,r){return r!==null&&gd(r),So(t,e.child,null,n),e=Ld(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function w2(e,t,n,r,o,i,a){if(n)return t.flags&256?(t.flags&=-257,r=Uu(Error(j(422))),Wa(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Dl({mode:"visible",children:r.children},o,0,null),i=_r(i,o,a,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&So(t,e.child,null,a),t.child.memoizedState=Yc(a),t.memoizedState=Xc,i);if(!(t.mode&1))return Wa(e,t,a,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(j(419)),r=Uu(i,r,void 0),Wa(e,t,a,r)}if(s=(a&e.childLanes)!==0,at||s){if(r=Be,r!==null){switch(a&-a){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|a)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,En(e,o),Kt(r,e,o,-1))}return $d(),r=Uu(Error(j(421))),Wa(e,t,a,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=N2.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,pt=qn(o.nextSibling),vt=t,Ce=!0,Vt=null,e!==null&&(Ct[Rt++]=mn,Ct[Rt++]=gn,Ct[Rt++]=Or,mn=e.id,gn=e.overflow,Or=t),t=Ld(t,r.children),t.flags|=4096,t)}function Sv(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Wc(e.return,t,n)}function Fu(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function f0(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Ze(e,t,r.children,n),r=ke.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Sv(e,n,t);else if(e.tag===19)Sv(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Se(ke,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Qs(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Fu(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Qs(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Fu(t,!0,n,null,i);break;case"together":Fu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function gs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Cn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),kr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(j(153));if(t.child!==null){for(e=t.child,n=Xn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Xn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function S2(e,t,n){switch(t.tag){case 3:u0(t),wo();break;case 5:Uy(t);break;case 1:lt(t.type)&&zs(t);break;case 4:Ed(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Se(Hs,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Se(ke,ke.current&1),t.flags|=128,null):n&t.child.childLanes?c0(e,t,n):(Se(ke,ke.current&1),e=Cn(e,t,n),e!==null?e.sibling:null);Se(ke,ke.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return f0(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Se(ke,ke.current),r)break;return null;case 22:case 23:return t.lanes=0,s0(e,t,n)}return Cn(e,t,n)}var d0,Jc,h0,p0;d0=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Jc=function(){};h0=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,yr(sn.current);var i=null;switch(n){case"input":o=Sc(e,o),r=Sc(e,r),i=[];break;case"select":o=Te({},o,{value:void 0}),r=Te({},r,{value:void 0}),i=[];break;case"textarea":o=Ec(e,o),r=Ec(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=js)}Rc(n,r);var a;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(a in s)s.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ii.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==s&&(l!=null||s!=null))if(u==="style")if(s){for(a in s)!s.hasOwnProperty(a)||l&&l.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in l)l.hasOwnProperty(a)&&s[a]!==l[a]&&(n||(n={}),n[a]=l[a])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,s=s?s.__html:void 0,l!=null&&s!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ii.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&_e("scroll",e),i||s===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};p0=function(e,t,n,r){n!==r&&(t.flags|=4)};function ei(e,t){if(!Ce)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function _2(e,t,n){var r=t.pendingProps;switch(md(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qe(t),null;case 1:return lt(t.type)&&Bs(),qe(t),null;case 3:return r=t.stateNode,_o(),be(st),be(Xe),Rd(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(za(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Vt!==null&&(sf(Vt),Vt=null))),Jc(e,t),qe(t),null;case 5:Cd(t);var o=yr(Ki.current);if(n=t.type,e!==null&&t.stateNode!=null)h0(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(j(166));return qe(t),null}if(e=yr(sn.current),za(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[nn]=t,r[Hi]=i,e=(t.mode&1)!==0,n){case"dialog":_e("cancel",r),_e("close",r);break;case"iframe":case"object":case"embed":_e("load",r);break;case"video":case"audio":for(o=0;o<ci.length;o++)_e(ci[o],r);break;case"source":_e("error",r);break;case"img":case"image":case"link":_e("error",r),_e("load",r);break;case"details":_e("toggle",r);break;case"input":Pp(r,i),_e("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},_e("invalid",r);break;case"textarea":Lp(r,i),_e("invalid",r)}Rc(n,i),o=null;for(var a in i)if(i.hasOwnProperty(a)){var s=i[a];a==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&Ba(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&Ba(r.textContent,s,e),o=["children",""+s]):Ii.hasOwnProperty(a)&&s!=null&&a==="onScroll"&&_e("scroll",r)}switch(n){case"input":Aa(r),Tp(r,i,!0);break;case"textarea":Aa(r),Np(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=js)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Bg(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[nn]=t,e[Hi]=r,d0(e,t,!1,!1),t.stateNode=e;e:{switch(a=Oc(n,r),n){case"dialog":_e("cancel",e),_e("close",e),o=r;break;case"iframe":case"object":case"embed":_e("load",e),o=r;break;case"video":case"audio":for(o=0;o<ci.length;o++)_e(ci[o],e);o=r;break;case"source":_e("error",e),o=r;break;case"img":case"image":case"link":_e("error",e),_e("load",e),o=r;break;case"details":_e("toggle",e),o=r;break;case"input":Pp(e,r),o=Sc(e,r),_e("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=Te({},r,{value:void 0}),_e("invalid",e);break;case"textarea":Lp(e,r),o=Ec(e,r),_e("invalid",e);break;default:o=r}Rc(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var l=s[i];i==="style"?Wg(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&zg(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Di(e,l):typeof l=="number"&&Di(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Ii.hasOwnProperty(i)?l!=null&&i==="onScroll"&&_e("scroll",e):l!=null&&td(e,i,l,a))}switch(n){case"input":Aa(e),Tp(e,r,!1);break;case"textarea":Aa(e),Np(e);break;case"option":r.value!=null&&e.setAttribute("value",""+er(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?lo(e,!!r.multiple,i,!1):r.defaultValue!=null&&lo(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=js)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return qe(t),null;case 6:if(e&&t.stateNode!=null)p0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(j(166));if(n=yr(Ki.current),yr(sn.current),za(t)){if(r=t.stateNode,n=t.memoizedProps,r[nn]=t,(i=r.nodeValue!==n)&&(e=vt,e!==null))switch(e.tag){case 3:Ba(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ba(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[nn]=t,t.stateNode=r}return qe(t),null;case 13:if(be(ke),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ce&&pt!==null&&t.mode&1&&!(t.flags&128))Ly(),wo(),t.flags|=98560,i=!1;else if(i=za(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(j(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(j(317));i[nn]=t}else wo(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;qe(t),i=!1}else Vt!==null&&(sf(Vt),Vt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ke.current&1?$e===0&&($e=3):$d())),t.updateQueue!==null&&(t.flags|=4),qe(t),null);case 4:return _o(),Jc(e,t),e===null&&Vi(t.stateNode.containerInfo),qe(t),null;case 10:return Sd(t.type._context),qe(t),null;case 17:return lt(t.type)&&Bs(),qe(t),null;case 19:if(be(ke),i=t.memoizedState,i===null)return qe(t),null;if(r=(t.flags&128)!==0,a=i.rendering,a===null)if(r)ei(i,!1);else{if($e!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Qs(e),a!==null){for(t.flags|=128,ei(i,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Se(ke,ke.current&1|2),t.child}e=e.sibling}i.tail!==null&&Ae()>Eo&&(t.flags|=128,r=!0,ei(i,!1),t.lanes=4194304)}else{if(!r)if(e=Qs(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ei(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!Ce)return qe(t),null}else 2*Ae()-i.renderingStartTime>Eo&&n!==1073741824&&(t.flags|=128,r=!0,ei(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(n=i.last,n!==null?n.sibling=a:t.child=a,i.last=a)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ae(),t.sibling=null,n=ke.current,Se(ke,r?n&1|2:n&1),t):(qe(t),null);case 22:case 23:return Dd(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ht&1073741824&&(qe(t),t.subtreeFlags&6&&(t.flags|=8192)):qe(t),null;case 24:return null;case 25:return null}throw Error(j(156,t.tag))}function b2(e,t){switch(md(t),t.tag){case 1:return lt(t.type)&&Bs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return _o(),be(st),be(Xe),Rd(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Cd(t),null;case 13:if(be(ke),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(j(340));wo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return be(ke),null;case 4:return _o(),null;case 10:return Sd(t.type._context),null;case 22:case 23:return Dd(),null;case 24:return null;default:return null}}var Ha=!1,Ge=!1,E2=typeof WeakSet=="function"?WeakSet:Set,K=null;function ro(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Le(e,t,r)}else n.current=null}function Zc(e,t,n){try{n()}catch(r){Le(e,t,r)}}var _v=!1;function C2(e,t){if(Dc=$s,e=yy(),pd(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,s=-1,l=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var p;f!==n||o!==0&&f.nodeType!==3||(s=a+o),f!==i||r!==0&&f.nodeType!==3||(l=a+r),f.nodeType===3&&(a+=f.nodeValue.length),(p=f.firstChild)!==null;)d=f,f=p;for(;;){if(f===e)break t;if(d===n&&++u===o&&(s=a),d===i&&++c===r&&(l=a),(p=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=p}n=s===-1||l===-1?null:{start:s,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for($c={focusedElem:e,selectionRange:n},$s=!1,K=t;K!==null;)if(t=K,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,K=e;else for(;K!==null;){t=K;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var y=v.memoizedProps,_=v.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?y:Ft(t.type,y),_);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(j(163))}}catch(S){Le(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,K=e;break}K=t.return}return v=_v,_v=!1,v}function Si(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Zc(t,n,i)}o=o.next}while(o!==r)}}function Ml(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ef(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function v0(e){var t=e.alternate;t!==null&&(e.alternate=null,v0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[nn],delete t[Hi],delete t[jc],delete t[a2],delete t[s2])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function m0(e){return e.tag===5||e.tag===3||e.tag===4}function bv(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||m0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function tf(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=js));else if(r!==4&&(e=e.child,e!==null))for(tf(e,t,n),e=e.sibling;e!==null;)tf(e,t,n),e=e.sibling}function nf(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(nf(e,t,n),e=e.sibling;e!==null;)nf(e,t,n),e=e.sibling}var ze=null,zt=!1;function kn(e,t,n){for(n=n.child;n!==null;)g0(e,t,n),n=n.sibling}function g0(e,t,n){if(an&&typeof an.onCommitFiberUnmount=="function")try{an.onCommitFiberUnmount(Ol,n)}catch{}switch(n.tag){case 5:Ge||ro(n,t);case 6:var r=ze,o=zt;ze=null,kn(e,t,n),ze=r,zt=o,ze!==null&&(zt?(e=ze,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ze.removeChild(n.stateNode));break;case 18:ze!==null&&(zt?(e=ze,n=n.stateNode,e.nodeType===8?Nu(e.parentNode,n):e.nodeType===1&&Nu(e,n),ji(e)):Nu(ze,n.stateNode));break;case 4:r=ze,o=zt,ze=n.stateNode.containerInfo,zt=!0,kn(e,t,n),ze=r,zt=o;break;case 0:case 11:case 14:case 15:if(!Ge&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&Zc(n,t,a),o=o.next}while(o!==r)}kn(e,t,n);break;case 1:if(!Ge&&(ro(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Le(n,t,s)}kn(e,t,n);break;case 21:kn(e,t,n);break;case 22:n.mode&1?(Ge=(r=Ge)||n.memoizedState!==null,kn(e,t,n),Ge=r):kn(e,t,n);break;default:kn(e,t,n)}}function Ev(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new E2),t.forEach(function(r){var o=A2.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function $t(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,a=t,s=a;e:for(;s!==null;){switch(s.tag){case 5:ze=s.stateNode,zt=!1;break e;case 3:ze=s.stateNode.containerInfo,zt=!0;break e;case 4:ze=s.stateNode.containerInfo,zt=!0;break e}s=s.return}if(ze===null)throw Error(j(160));g0(i,a,o),ze=null,zt=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){Le(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)y0(t,e),t=t.sibling}function y0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if($t(t,e),en(e),r&4){try{Si(3,e,e.return),Ml(3,e)}catch(y){Le(e,e.return,y)}try{Si(5,e,e.return)}catch(y){Le(e,e.return,y)}}break;case 1:$t(t,e),en(e),r&512&&n!==null&&ro(n,n.return);break;case 5:if($t(t,e),en(e),r&512&&n!==null&&ro(n,n.return),e.flags&32){var o=e.stateNode;try{Di(o,"")}catch(y){Le(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,a=n!==null?n.memoizedProps:i,s=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&Fg(o,i),Oc(s,a);var u=Oc(s,i);for(a=0;a<l.length;a+=2){var c=l[a],f=l[a+1];c==="style"?Wg(o,f):c==="dangerouslySetInnerHTML"?zg(o,f):c==="children"?Di(o,f):td(o,c,f,u)}switch(s){case"input":_c(o,i);break;case"textarea":jg(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var p=i.value;p!=null?lo(o,!!i.multiple,p,!1):d!==!!i.multiple&&(i.defaultValue!=null?lo(o,!!i.multiple,i.defaultValue,!0):lo(o,!!i.multiple,i.multiple?[]:"",!1))}o[Hi]=i}catch(y){Le(e,e.return,y)}}break;case 6:if($t(t,e),en(e),r&4){if(e.stateNode===null)throw Error(j(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){Le(e,e.return,y)}}break;case 3:if($t(t,e),en(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ji(t.containerInfo)}catch(y){Le(e,e.return,y)}break;case 4:$t(t,e),en(e);break;case 13:$t(t,e),en(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Md=Ae())),r&4&&Ev(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Ge=(u=Ge)||c,$t(t,e),Ge=u):$t(t,e),en(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(K=e,c=e.child;c!==null;){for(f=K=c;K!==null;){switch(d=K,p=d.child,d.tag){case 0:case 11:case 14:case 15:Si(4,d,d.return);break;case 1:ro(d,d.return);var v=d.stateNode;if(typeof v.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(y){Le(r,n,y)}}break;case 5:ro(d,d.return);break;case 22:if(d.memoizedState!==null){Rv(f);continue}}p!==null?(p.return=d,K=p):Rv(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=f.stateNode,l=f.memoizedProps.style,a=l!=null&&l.hasOwnProperty("display")?l.display:null,s.style.display=Vg("display",a))}catch(y){Le(e,e.return,y)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(y){Le(e,e.return,y)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:$t(t,e),en(e),r&4&&Ev(e);break;case 21:break;default:$t(t,e),en(e)}}function en(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(m0(n)){var r=n;break e}n=n.return}throw Error(j(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Di(o,""),r.flags&=-33);var i=bv(e);nf(e,i,o);break;case 3:case 4:var a=r.stateNode.containerInfo,s=bv(e);tf(e,s,a);break;default:throw Error(j(161))}}catch(l){Le(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function R2(e,t,n){K=e,w0(e)}function w0(e,t,n){for(var r=(e.mode&1)!==0;K!==null;){var o=K,i=o.child;if(o.tag===22&&r){var a=o.memoizedState!==null||Ha;if(!a){var s=o.alternate,l=s!==null&&s.memoizedState!==null||Ge;s=Ha;var u=Ge;if(Ha=a,(Ge=l)&&!u)for(K=o;K!==null;)a=K,l=a.child,a.tag===22&&a.memoizedState!==null?Ov(o):l!==null?(l.return=a,K=l):Ov(o);for(;i!==null;)K=i,w0(i),i=i.sibling;K=o,Ha=s,Ge=u}Cv(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,K=i):Cv(e)}}function Cv(e){for(;K!==null;){var t=K;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ge||Ml(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ge)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ft(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&sv(t,i,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}sv(t,a,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&ji(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(j(163))}Ge||t.flags&512&&ef(t)}catch(d){Le(t,t.return,d)}}if(t===e){K=null;break}if(n=t.sibling,n!==null){n.return=t.return,K=n;break}K=t.return}}function Rv(e){for(;K!==null;){var t=K;if(t===e){K=null;break}var n=t.sibling;if(n!==null){n.return=t.return,K=n;break}K=t.return}}function Ov(e){for(;K!==null;){var t=K;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ml(4,t)}catch(l){Le(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){Le(t,o,l)}}var i=t.return;try{ef(t)}catch(l){Le(t,i,l)}break;case 5:var a=t.return;try{ef(t)}catch(l){Le(t,a,l)}}}catch(l){Le(t,t.return,l)}if(t===e){K=null;break}var s=t.sibling;if(s!==null){s.return=t.return,K=s;break}K=t.return}}var O2=Math.ceil,Ys=Rn.ReactCurrentDispatcher,Nd=Rn.ReactCurrentOwner,Pt=Rn.ReactCurrentBatchConfig,he=0,Be=null,Me=null,Ve=0,ht=0,oo=rr(0),$e=0,Yi=null,kr=0,Il=0,Ad=0,_i=null,it=null,Md=0,Eo=1/0,hn=null,Js=!1,rf=null,Qn=null,qa=!1,Un=null,Zs=0,bi=0,of=null,ys=-1,ws=0;function et(){return he&6?Ae():ys!==-1?ys:ys=Ae()}function Gn(e){return e.mode&1?he&2&&Ve!==0?Ve&-Ve:u2.transition!==null?(ws===0&&(ws=ny()),ws):(e=ve,e!==0||(e=window.event,e=e===void 0?16:uy(e.type)),e):1}function Kt(e,t,n,r){if(50<bi)throw bi=0,of=null,Error(j(185));ua(e,n,r),(!(he&2)||e!==Be)&&(e===Be&&(!(he&2)&&(Il|=n),$e===4&&Dn(e,Ve)),ut(e,r),n===1&&he===0&&!(t.mode&1)&&(Eo=Ae()+500,Ll&&or()))}function ut(e,t){var n=e.callbackNode;uE(e,t);var r=Ds(e,e===Be?Ve:0);if(r===0)n!==null&&Ip(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ip(n),t===1)e.tag===0?l2(xv.bind(null,e)):ky(xv.bind(null,e)),o2(function(){!(he&6)&&or()}),n=null;else{switch(ry(r)){case 1:n=ad;break;case 4:n=ey;break;case 16:n=Is;break;case 536870912:n=ty;break;default:n=Is}n=x0(n,S0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function S0(e,t){if(ys=-1,ws=0,he&6)throw Error(j(327));var n=e.callbackNode;if(po()&&e.callbackNode!==n)return null;var r=Ds(e,e===Be?Ve:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=el(e,r);else{t=r;var o=he;he|=2;var i=b0();(Be!==e||Ve!==t)&&(hn=null,Eo=Ae()+500,Sr(e,t));do try{P2();break}catch(s){_0(e,s)}while(1);wd(),Ys.current=i,he=o,Me!==null?t=0:(Be=null,Ve=0,t=$e)}if(t!==0){if(t===2&&(o=Lc(e),o!==0&&(r=o,t=af(e,o))),t===1)throw n=Yi,Sr(e,0),Dn(e,r),ut(e,Ae()),n;if(t===6)Dn(e,r);else{if(o=e.current.alternate,!(r&30)&&!x2(o)&&(t=el(e,r),t===2&&(i=Lc(e),i!==0&&(r=i,t=af(e,i))),t===1))throw n=Yi,Sr(e,0),Dn(e,r),ut(e,Ae()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(j(345));case 2:dr(e,it,hn);break;case 3:if(Dn(e,r),(r&130023424)===r&&(t=Md+500-Ae(),10<t)){if(Ds(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){et(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Fc(dr.bind(null,e,it,hn),t);break}dr(e,it,hn);break;case 4:if(Dn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var a=31-qt(r);i=1<<a,a=t[a],a>o&&(o=a),r&=~i}if(r=o,r=Ae()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*O2(r/1960))-r,10<r){e.timeoutHandle=Fc(dr.bind(null,e,it,hn),r);break}dr(e,it,hn);break;case 5:dr(e,it,hn);break;default:throw Error(j(329))}}}return ut(e,Ae()),e.callbackNode===n?S0.bind(null,e):null}function af(e,t){var n=_i;return e.current.memoizedState.isDehydrated&&(Sr(e,t).flags|=256),e=el(e,t),e!==2&&(t=it,it=n,t!==null&&sf(t)),e}function sf(e){it===null?it=e:it.push.apply(it,e)}function x2(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Gt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Dn(e,t){for(t&=~Ad,t&=~Il,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-qt(t),r=1<<n;e[n]=-1,t&=~r}}function xv(e){if(he&6)throw Error(j(327));po();var t=Ds(e,0);if(!(t&1))return ut(e,Ae()),null;var n=el(e,t);if(e.tag!==0&&n===2){var r=Lc(e);r!==0&&(t=r,n=af(e,r))}if(n===1)throw n=Yi,Sr(e,0),Dn(e,t),ut(e,Ae()),n;if(n===6)throw Error(j(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,dr(e,it,hn),ut(e,Ae()),null}function Id(e,t){var n=he;he|=1;try{return e(t)}finally{he=n,he===0&&(Eo=Ae()+500,Ll&&or())}}function Pr(e){Un!==null&&Un.tag===0&&!(he&6)&&po();var t=he;he|=1;var n=Pt.transition,r=ve;try{if(Pt.transition=null,ve=1,e)return e()}finally{ve=r,Pt.transition=n,he=t,!(he&6)&&or()}}function Dd(){ht=oo.current,be(oo)}function Sr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,r2(n)),Me!==null)for(n=Me.return;n!==null;){var r=n;switch(md(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Bs();break;case 3:_o(),be(st),be(Xe),Rd();break;case 5:Cd(r);break;case 4:_o();break;case 13:be(ke);break;case 19:be(ke);break;case 10:Sd(r.type._context);break;case 22:case 23:Dd()}n=n.return}if(Be=e,Me=e=Xn(e.current,null),Ve=ht=t,$e=0,Yi=null,Ad=Il=kr=0,it=_i=null,gr!==null){for(t=0;t<gr.length;t++)if(n=gr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=o,r.next=a}n.pending=r}gr=null}return e}function _0(e,t){do{var n=Me;try{if(wd(),vs.current=Xs,Gs){for(var r=Pe.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Gs=!1}if(xr=0,je=De=Pe=null,wi=!1,Qi=0,Nd.current=null,n===null||n.return===null){$e=1,Yi=t,Me=null;break}e:{var i=e,a=n.return,s=n,l=t;if(t=Ve,s.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=s,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var p=pv(a);if(p!==null){p.flags&=-257,vv(p,a,s,i,t),p.mode&1&&hv(i,u,t),t=p,l=u;var v=t.updateQueue;if(v===null){var y=new Set;y.add(l),t.updateQueue=y}else v.add(l);break e}else{if(!(t&1)){hv(i,u,t),$d();break e}l=Error(j(426))}}else if(Ce&&s.mode&1){var _=pv(a);if(_!==null){!(_.flags&65536)&&(_.flags|=256),vv(_,a,s,i,t),gd(bo(l,s));break e}}i=l=bo(l,s),$e!==4&&($e=2),_i===null?_i=[i]:_i.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=o0(i,l,t);av(i,m);break e;case 1:s=l;var h=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Qn===null||!Qn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=i0(i,s,t);av(i,S);break e}}i=i.return}while(i!==null)}C0(n)}catch(k){t=k,Me===n&&n!==null&&(Me=n=n.return);continue}break}while(1)}function b0(){var e=Ys.current;return Ys.current=Xs,e===null?Xs:e}function $d(){($e===0||$e===3||$e===2)&&($e=4),Be===null||!(kr&268435455)&&!(Il&268435455)||Dn(Be,Ve)}function el(e,t){var n=he;he|=2;var r=b0();(Be!==e||Ve!==t)&&(hn=null,Sr(e,t));do try{k2();break}catch(o){_0(e,o)}while(1);if(wd(),he=n,Ys.current=r,Me!==null)throw Error(j(261));return Be=null,Ve=0,$e}function k2(){for(;Me!==null;)E0(Me)}function P2(){for(;Me!==null&&!eE();)E0(Me)}function E0(e){var t=O0(e.alternate,e,ht);e.memoizedProps=e.pendingProps,t===null?C0(e):Me=t,Nd.current=null}function C0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=b2(n,t),n!==null){n.flags&=32767,Me=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{$e=6,Me=null;return}}else if(n=_2(n,t,ht),n!==null){Me=n;return}if(t=t.sibling,t!==null){Me=t;return}Me=t=e}while(t!==null);$e===0&&($e=5)}function dr(e,t,n){var r=ve,o=Pt.transition;try{Pt.transition=null,ve=1,T2(e,t,n,r)}finally{Pt.transition=o,ve=r}return null}function T2(e,t,n,r){do po();while(Un!==null);if(he&6)throw Error(j(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(j(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(cE(e,i),e===Be&&(Me=Be=null,Ve=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||qa||(qa=!0,x0(Is,function(){return po(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Pt.transition,Pt.transition=null;var a=ve;ve=1;var s=he;he|=4,Nd.current=null,C2(e,n),y0(n,e),XE($c),$s=!!Dc,$c=Dc=null,e.current=n,R2(n),tE(),he=s,ve=a,Pt.transition=i}else e.current=n;if(qa&&(qa=!1,Un=e,Zs=o),i=e.pendingLanes,i===0&&(Qn=null),oE(n.stateNode),ut(e,Ae()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Js)throw Js=!1,e=rf,rf=null,e;return Zs&1&&e.tag!==0&&po(),i=e.pendingLanes,i&1?e===of?bi++:(bi=0,of=e):bi=0,or(),null}function po(){if(Un!==null){var e=ry(Zs),t=Pt.transition,n=ve;try{if(Pt.transition=null,ve=16>e?16:e,Un===null)var r=!1;else{if(e=Un,Un=null,Zs=0,he&6)throw Error(j(331));var o=he;for(he|=4,K=e.current;K!==null;){var i=K,a=i.child;if(K.flags&16){var s=i.deletions;if(s!==null){for(var l=0;l<s.length;l++){var u=s[l];for(K=u;K!==null;){var c=K;switch(c.tag){case 0:case 11:case 15:Si(8,c,i)}var f=c.child;if(f!==null)f.return=c,K=f;else for(;K!==null;){c=K;var d=c.sibling,p=c.return;if(v0(c),c===u){K=null;break}if(d!==null){d.return=p,K=d;break}K=p}}}var v=i.alternate;if(v!==null){var y=v.child;if(y!==null){v.child=null;do{var _=y.sibling;y.sibling=null,y=_}while(y!==null)}}K=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,K=a;else e:for(;K!==null;){if(i=K,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Si(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,K=m;break e}K=i.return}}var h=e.current;for(K=h;K!==null;){a=K;var g=a.child;if(a.subtreeFlags&2064&&g!==null)g.return=a,K=g;else e:for(a=h;K!==null;){if(s=K,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:Ml(9,s)}}catch(k){Le(s,s.return,k)}if(s===a){K=null;break e}var S=s.sibling;if(S!==null){S.return=s.return,K=S;break e}K=s.return}}if(he=o,or(),an&&typeof an.onPostCommitFiberRoot=="function")try{an.onPostCommitFiberRoot(Ol,e)}catch{}r=!0}return r}finally{ve=n,Pt.transition=t}}return!1}function kv(e,t,n){t=bo(n,t),t=o0(e,t,1),e=Kn(e,t,1),t=et(),e!==null&&(ua(e,1,t),ut(e,t))}function Le(e,t,n){if(e.tag===3)kv(e,e,n);else for(;t!==null;){if(t.tag===3){kv(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Qn===null||!Qn.has(r))){e=bo(n,e),e=i0(t,e,1),t=Kn(t,e,1),e=et(),t!==null&&(ua(t,1,e),ut(t,e));break}}t=t.return}}function L2(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=et(),e.pingedLanes|=e.suspendedLanes&n,Be===e&&(Ve&n)===n&&($e===4||$e===3&&(Ve&130023424)===Ve&&500>Ae()-Md?Sr(e,0):Ad|=n),ut(e,t)}function R0(e,t){t===0&&(e.mode&1?(t=Da,Da<<=1,!(Da&130023424)&&(Da=4194304)):t=1);var n=et();e=En(e,t),e!==null&&(ua(e,t,n),ut(e,n))}function N2(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),R0(e,n)}function A2(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(j(314))}r!==null&&r.delete(t),R0(e,n)}var O0;O0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||st.current)at=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return at=!1,S2(e,t,n);at=!!(e.flags&131072)}else at=!1,Ce&&t.flags&1048576&&Py(t,Ws,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;gs(e,t),e=t.pendingProps;var o=yo(t,Xe.current);ho(t,n),o=xd(null,t,r,e,o,n);var i=kd();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,lt(r)?(i=!0,zs(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,bd(t),o.updater=Nl,t.stateNode=o,o._reactInternals=t,qc(t,r,e,n),t=Gc(null,t,r,!0,i,n)):(t.tag=0,Ce&&i&&vd(t),Ze(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(gs(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=I2(r),e=Ft(r,e),o){case 0:t=Qc(null,t,r,e,n);break e;case 1:t=yv(null,t,r,e,n);break e;case 11:t=mv(null,t,r,e,n);break e;case 14:t=gv(null,t,r,Ft(r.type,e),n);break e}throw Error(j(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ft(r,o),Qc(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ft(r,o),yv(e,t,r,o,n);case 3:e:{if(u0(t),e===null)throw Error(j(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Ay(e,t),Ks(t,r,null,n);var a=t.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=bo(Error(j(423)),t),t=wv(e,t,r,n,o);break e}else if(r!==o){o=bo(Error(j(424)),t),t=wv(e,t,r,n,o);break e}else for(pt=qn(t.stateNode.containerInfo.firstChild),vt=t,Ce=!0,Vt=null,n=$y(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(wo(),r===o){t=Cn(e,t,n);break e}Ze(e,t,r,n)}t=t.child}return t;case 5:return Uy(t),e===null&&Vc(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,a=o.children,Uc(r,o)?a=null:i!==null&&Uc(r,i)&&(t.flags|=32),l0(e,t),Ze(e,t,a,n),t.child;case 6:return e===null&&Vc(t),null;case 13:return c0(e,t,n);case 4:return Ed(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=So(t,null,r,n):Ze(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ft(r,o),mv(e,t,r,o,n);case 7:return Ze(e,t,t.pendingProps,n),t.child;case 8:return Ze(e,t,t.pendingProps.children,n),t.child;case 12:return Ze(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,a=o.value,Se(Hs,r._currentValue),r._currentValue=a,i!==null)if(Gt(i.value,a)){if(i.children===o.children&&!st.current){t=Cn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){a=i.child;for(var l=s.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Sn(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),Wc(i.return,n,t),s.lanes|=n;break}l=l.next}}else if(i.tag===10)a=i.type===t.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error(j(341));a.lanes|=n,s=a.alternate,s!==null&&(s.lanes|=n),Wc(a,n,t),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}Ze(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,ho(t,n),o=Lt(o),r=r(o),t.flags|=1,Ze(e,t,r,n),t.child;case 14:return r=t.type,o=Ft(r,t.pendingProps),o=Ft(r.type,o),gv(e,t,r,o,n);case 15:return a0(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ft(r,o),gs(e,t),t.tag=1,lt(r)?(e=!0,zs(t)):e=!1,ho(t,n),Iy(t,r,o),qc(t,r,o,n),Gc(null,t,r,!0,e,n);case 19:return f0(e,t,n);case 22:return s0(e,t,n)}throw Error(j(156,t.tag))};function x0(e,t){return Zg(e,t)}function M2(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function xt(e,t,n,r){return new M2(e,t,n,r)}function Ud(e){return e=e.prototype,!(!e||!e.isReactComponent)}function I2(e){if(typeof e=="function")return Ud(e)?1:0;if(e!=null){if(e=e.$$typeof,e===rd)return 11;if(e===od)return 14}return 2}function Xn(e,t){var n=e.alternate;return n===null?(n=xt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ss(e,t,n,r,o,i){var a=2;if(r=e,typeof e=="function")Ud(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case Qr:return _r(n.children,o,i,t);case nd:a=8,o|=8;break;case mc:return e=xt(12,n,t,o|2),e.elementType=mc,e.lanes=i,e;case gc:return e=xt(13,n,t,o),e.elementType=gc,e.lanes=i,e;case yc:return e=xt(19,n,t,o),e.elementType=yc,e.lanes=i,e;case Dg:return Dl(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Mg:a=10;break e;case Ig:a=9;break e;case rd:a=11;break e;case od:a=14;break e;case Ln:a=16,r=null;break e}throw Error(j(130,e==null?e:typeof e,""))}return t=xt(a,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function _r(e,t,n,r){return e=xt(7,e,r,t),e.lanes=n,e}function Dl(e,t,n,r){return e=xt(22,e,r,t),e.elementType=Dg,e.lanes=n,e.stateNode={isHidden:!1},e}function ju(e,t,n){return e=xt(6,e,null,t),e.lanes=n,e}function Bu(e,t,n){return t=xt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function D2(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=_u(0),this.expirationTimes=_u(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=_u(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Fd(e,t,n,r,o,i,a,s,l){return e=new D2(e,t,n,s,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=xt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},bd(i),e}function $2(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Kr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function k0(e){if(!e)return tr;e=e._reactInternals;e:{if(Dr(e)!==e||e.tag!==1)throw Error(j(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(lt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(j(171))}if(e.tag===1){var n=e.type;if(lt(n))return xy(e,n,t)}return t}function P0(e,t,n,r,o,i,a,s,l){return e=Fd(n,r,!0,e,o,i,a,s,l),e.context=k0(null),n=e.current,r=et(),o=Gn(n),i=Sn(r,o),i.callback=t??null,Kn(n,i,o),e.current.lanes=o,ua(e,o,r),ut(e,r),e}function $l(e,t,n,r){var o=t.current,i=et(),a=Gn(o);return n=k0(n),t.context===null?t.context=n:t.pendingContext=n,t=Sn(i,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Kn(o,t,a),e!==null&&(Kt(e,o,a,i),ps(e,o,a)),a}function tl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Pv(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function jd(e,t){Pv(e,t),(e=e.alternate)&&Pv(e,t)}function U2(){return null}var T0=typeof reportError=="function"?reportError:function(e){console.error(e)};function Bd(e){this._internalRoot=e}Ul.prototype.render=Bd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(j(409));$l(e,t,null,null)};Ul.prototype.unmount=Bd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Pr(function(){$l(null,e,null,null)}),t[bn]=null}};function Ul(e){this._internalRoot=e}Ul.prototype.unstable_scheduleHydration=function(e){if(e){var t=ay();e={blockedOn:null,target:e,priority:t};for(var n=0;n<In.length&&t!==0&&t<In[n].priority;n++);In.splice(n,0,e),n===0&&ly(e)}};function zd(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Fl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Tv(){}function F2(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=tl(a);i.call(u)}}var a=P0(t,r,e,0,null,!1,!1,"",Tv);return e._reactRootContainer=a,e[bn]=a.current,Vi(e.nodeType===8?e.parentNode:e),Pr(),a}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=tl(l);s.call(u)}}var l=Fd(e,0,!1,null,null,!1,!1,"",Tv);return e._reactRootContainer=l,e[bn]=l.current,Vi(e.nodeType===8?e.parentNode:e),Pr(function(){$l(t,l,n,r)}),l}function jl(e,t,n,r,o){var i=n._reactRootContainer;if(i){var a=i;if(typeof o=="function"){var s=o;o=function(){var l=tl(a);s.call(l)}}$l(t,a,e,o)}else a=F2(n,t,e,o,r);return tl(a)}oy=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ui(t.pendingLanes);n!==0&&(sd(t,n|1),ut(t,Ae()),!(he&6)&&(Eo=Ae()+500,or()))}break;case 13:Pr(function(){var r=En(e,1);if(r!==null){var o=et();Kt(r,e,1,o)}}),jd(e,1)}};ld=function(e){if(e.tag===13){var t=En(e,134217728);if(t!==null){var n=et();Kt(t,e,134217728,n)}jd(e,134217728)}};iy=function(e){if(e.tag===13){var t=Gn(e),n=En(e,t);if(n!==null){var r=et();Kt(n,e,t,r)}jd(e,t)}};ay=function(){return ve};sy=function(e,t){var n=ve;try{return ve=e,t()}finally{ve=n}};kc=function(e,t,n){switch(t){case"input":if(_c(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Tl(r);if(!o)throw Error(j(90));Ug(r),_c(r,o)}}}break;case"textarea":jg(e,n);break;case"select":t=n.value,t!=null&&lo(e,!!n.multiple,t,!1)}};Kg=Id;Qg=Pr;var j2={usingClientEntryPoint:!1,Events:[fa,Jr,Tl,Hg,qg,Id]},ti={findFiberByHostInstance:mr,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},B2={bundleType:ti.bundleType,version:ti.version,rendererPackageName:ti.rendererPackageName,rendererConfig:ti.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Rn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Yg(e),e===null?null:e.stateNode},findFiberByHostInstance:ti.findFiberByHostInstance||U2,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ka=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ka.isDisabled&&Ka.supportsFiber)try{Ol=Ka.inject(B2),an=Ka}catch{}}wt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=j2;wt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!zd(t))throw Error(j(200));return $2(e,t,null,n)};wt.createRoot=function(e,t){if(!zd(e))throw Error(j(299));var n=!1,r="",o=T0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Fd(e,1,!1,null,null,n,!1,r,o),e[bn]=t.current,Vi(e.nodeType===8?e.parentNode:e),new Bd(t)};wt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(j(188)):(e=Object.keys(e).join(","),Error(j(268,e)));return e=Yg(t),e=e===null?null:e.stateNode,e};wt.flushSync=function(e){return Pr(e)};wt.hydrate=function(e,t,n){if(!Fl(t))throw Error(j(200));return jl(null,e,t,!0,n)};wt.hydrateRoot=function(e,t,n){if(!zd(e))throw Error(j(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",a=T0;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=P0(t,null,e,1,n??null,o,!1,i,a),e[bn]=t.current,Vi(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Ul(t)};wt.render=function(e,t,n){if(!Fl(t))throw Error(j(200));return jl(null,e,t,!1,n)};wt.unmountComponentAtNode=function(e){if(!Fl(e))throw Error(j(40));return e._reactRootContainer?(Pr(function(){jl(null,null,e,!1,function(){e._reactRootContainer=null,e[bn]=null})}),!0):!1};wt.unstable_batchedUpdates=Id;wt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Fl(n))throw Error(j(200));if(e==null||e._reactInternals===void 0)throw Error(j(38));return jl(e,t,n,!1,r)};wt.version="18.2.0-next-9e3b772b8-20220608";(function(e){function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(n){console.error(n)}}t(),e.exports=wt})(Ub);const L0=Kf(mo);var N0,Lv=mo;N0=Lv.createRoot,Lv.hydrateRoot;var nl={},z2={get exports(){return nl},set exports(e){nl=e}},Tr={},xe={},V2={get exports(){return xe},set exports(e){xe=e}},W2="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",H2=W2,q2=H2;function A0(){}function M0(){}M0.resetWarningCache=A0;var K2=function(){function e(r,o,i,a,s,l){if(l!==q2){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:M0,resetWarningCache:A0};return n.PropTypes=n,n};V2.exports=K2();var rl={},Q2={get exports(){return rl},set exports(e){rl=e}},Xt={},Ji={},G2={get exports(){return Ji},set exports(e){Ji=e}};(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=c;/*!
 * Adapted from jQuery UI core
 *
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/category/ui-core/
 */var n="none",r="contents",o=/input|select|textarea|button|object|iframe/;function i(f,d){return d.getPropertyValue("overflow")!=="visible"||f.scrollWidth<=0&&f.scrollHeight<=0}function a(f){var d=f.offsetWidth<=0&&f.offsetHeight<=0;if(d&&!f.innerHTML)return!0;try{var p=window.getComputedStyle(f),v=p.getPropertyValue("display");return d?v!==r&&i(f,p):v===n}catch{return console.warn("Failed to inspect element style"),!1}}function s(f){for(var d=f,p=f.getRootNode&&f.getRootNode();d&&d!==document.body;){if(p&&d===p&&(d=p.host.parentNode),a(d))return!1;d=d.parentNode}return!0}function l(f,d){var p=f.nodeName.toLowerCase(),v=o.test(p)&&!f.disabled||p==="a"&&f.href||d;return v&&s(f)}function u(f){var d=f.getAttribute("tabindex");d===null&&(d=void 0);var p=isNaN(d);return(p||d>=0)&&l(f,!p)}function c(f){var d=[].slice.call(f.querySelectorAll("*"),0).reduce(function(p,v){return p.concat(v.shadowRoot?c(v.shadowRoot):[v])},[]);return d.filter(u)}e.exports=t.default})(G2,Ji);Object.defineProperty(Xt,"__esModule",{value:!0});Xt.resetState=Z2;Xt.log=eC;Xt.handleBlur=Zi;Xt.handleFocus=ea;Xt.markForFocusLater=tC;Xt.returnFocus=nC;Xt.popWithoutFocus=rC;Xt.setupScopedFocus=oC;Xt.teardownScopedFocus=iC;var X2=Ji,Y2=J2(X2);function J2(e){return e&&e.__esModule?e:{default:e}}var Co=[],io=null,lf=!1;function Z2(){Co=[]}function eC(){}function Zi(){lf=!0}function ea(){if(lf){if(lf=!1,!io)return;setTimeout(function(){if(!io.contains(document.activeElement)){var e=(0,Y2.default)(io)[0]||io;e.focus()}},0)}}function tC(){Co.push(document.activeElement)}function nC(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,t=null;try{Co.length!==0&&(t=Co.pop(),t.focus({preventScroll:e}));return}catch{console.warn(["You tried to return focus to",t,"but it is not in the DOM anymore"].join(" "))}}function rC(){Co.length>0&&Co.pop()}function oC(e){io=e,window.addEventListener?(window.addEventListener("blur",Zi,!1),document.addEventListener("focus",ea,!0)):(window.attachEvent("onBlur",Zi),document.attachEvent("onFocus",ea))}function iC(){io=null,window.addEventListener?(window.removeEventListener("blur",Zi),document.removeEventListener("focus",ea)):(window.detachEvent("onBlur",Zi),document.detachEvent("onFocus",ea))}var ol={},aC={get exports(){return ol},set exports(e){ol=e}};(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=a;var n=Ji,r=o(n);function o(s){return s&&s.__esModule?s:{default:s}}function i(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:document;return s.activeElement.shadowRoot?i(s.activeElement.shadowRoot):s.activeElement}function a(s,l){var u=(0,r.default)(s);if(!u.length){l.preventDefault();return}var c=void 0,f=l.shiftKey,d=u[0],p=u[u.length-1],v=i();if(s===v){if(!f)return;c=p}if(p===v&&!f&&(c=d),d===v&&f&&(c=p),c){l.preventDefault(),c.focus();return}var y=/(\bChrome\b|\bSafari\b)\//.exec(navigator.userAgent),_=y!=null&&y[1]!="Chrome"&&/\biPod\b|\biPad\b/g.exec(navigator.userAgent)==null;if(_){var m=u.indexOf(v);if(m>-1&&(m+=f?-1:1),c=u[m],typeof c>"u"){l.preventDefault(),c=f?p:d,c.focus();return}l.preventDefault(),c.focus()}}e.exports=t.default})(aC,ol);var Yt={},sC=function(){},lC=sC,Qt={},uf={},uC={get exports(){return uf},set exports(e){uf=e}};/*!
  Copyright (c) 2015 Jed Watson.
  Based on code that is Copyright 2013-2015, Facebook, Inc.
  All rights reserved.
*/(function(e){(function(){var t=!!(typeof window<"u"&&window.document&&window.document.createElement),n={canUseDOM:t,canUseWorkers:typeof Worker<"u",canUseEventListeners:t&&!!(window.addEventListener||window.attachEvent),canUseViewport:t&&!!window.screen};e.exports?e.exports=n:window.ExecutionEnvironment=n})()})(uC);Object.defineProperty(Qt,"__esModule",{value:!0});Qt.canUseDOM=Qt.SafeNodeList=Qt.SafeHTMLCollection=void 0;var cC=uf,fC=dC(cC);function dC(e){return e&&e.__esModule?e:{default:e}}var Bl=fC.default,hC=Bl.canUseDOM?window.HTMLElement:{};Qt.SafeHTMLCollection=Bl.canUseDOM?window.HTMLCollection:{};Qt.SafeNodeList=Bl.canUseDOM?window.NodeList:{};Qt.canUseDOM=Bl.canUseDOM;Qt.default=hC;Object.defineProperty(Yt,"__esModule",{value:!0});Yt.resetState=yC;Yt.log=wC;Yt.assertNodeList=I0;Yt.setElement=SC;Yt.validateElement=Vd;Yt.hide=_C;Yt.show=bC;Yt.documentNotReadyOrSSRTesting=EC;var pC=lC,vC=gC(pC),mC=Qt;function gC(e){return e&&e.__esModule?e:{default:e}}var Et=null;function yC(){Et&&(Et.removeAttribute?Et.removeAttribute("aria-hidden"):Et.length!=null?Et.forEach(function(e){return e.removeAttribute("aria-hidden")}):document.querySelectorAll(Et).forEach(function(e){return e.removeAttribute("aria-hidden")})),Et=null}function wC(){}function I0(e,t){if(!e||!e.length)throw new Error("react-modal: No elements were found for selector "+t+".")}function SC(e){var t=e;if(typeof t=="string"&&mC.canUseDOM){var n=document.querySelectorAll(t);I0(n,t),t=n}return Et=t||Et,Et}function Vd(e){var t=e||Et;return t?Array.isArray(t)||t instanceof HTMLCollection||t instanceof NodeList?t:[t]:((0,vC.default)(!1,["react-modal: App element is not defined.","Please use `Modal.setAppElement(el)` or set `appElement={el}`.","This is needed so screen readers don't see main content","when modal is opened. It is not recommended, but you can opt-out","by setting `ariaHideApp={false}`."].join(" ")),[])}function _C(e){var t=!0,n=!1,r=void 0;try{for(var o=Vd(e)[Symbol.iterator](),i;!(t=(i=o.next()).done);t=!0){var a=i.value;a.setAttribute("aria-hidden","true")}}catch(s){n=!0,r=s}finally{try{!t&&o.return&&o.return()}finally{if(n)throw r}}}function bC(e){var t=!0,n=!1,r=void 0;try{for(var o=Vd(e)[Symbol.iterator](),i;!(t=(i=o.next()).done);t=!0){var a=i.value;a.removeAttribute("aria-hidden")}}catch(s){n=!0,r=s}finally{try{!t&&o.return&&o.return()}finally{if(n)throw r}}}function EC(){Et=null}var Io={};Object.defineProperty(Io,"__esModule",{value:!0});Io.resetState=CC;Io.log=RC;var Ei={},Ci={};function Nv(e,t){e.classList.remove(t)}function CC(){var e=document.getElementsByTagName("html")[0];for(var t in Ei)Nv(e,Ei[t]);var n=document.body;for(var r in Ci)Nv(n,Ci[r]);Ei={},Ci={}}function RC(){}var OC=function(t,n){return t[n]||(t[n]=0),t[n]+=1,n},xC=function(t,n){return t[n]&&(t[n]-=1),n},kC=function(t,n,r){r.forEach(function(o){OC(n,o),t.add(o)})},PC=function(t,n,r){r.forEach(function(o){xC(n,o),n[o]===0&&t.remove(o)})};Io.add=function(t,n){return kC(t.classList,t.nodeName.toLowerCase()=="html"?Ei:Ci,n.split(" "))};Io.remove=function(t,n){return PC(t.classList,t.nodeName.toLowerCase()=="html"?Ei:Ci,n.split(" "))};var Do={};Object.defineProperty(Do,"__esModule",{value:!0});Do.log=LC;Do.resetState=NC;function TC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var D0=function e(){var t=this;TC(this,e),this.register=function(n){t.openInstances.indexOf(n)===-1&&(t.openInstances.push(n),t.emit("register"))},this.deregister=function(n){var r=t.openInstances.indexOf(n);r!==-1&&(t.openInstances.splice(r,1),t.emit("deregister"))},this.subscribe=function(n){t.subscribers.push(n)},this.emit=function(n){t.subscribers.forEach(function(r){return r(n,t.openInstances.slice())})},this.openInstances=[],this.subscribers=[]},il=new D0;function LC(){console.log("portalOpenInstances ----------"),console.log(il.openInstances.length),il.openInstances.forEach(function(e){return console.log(e)}),console.log("end portalOpenInstances ----------")}function NC(){il=new D0}Do.default=il;var Wd={};Object.defineProperty(Wd,"__esModule",{value:!0});Wd.resetState=DC;Wd.log=$C;var AC=Do,MC=IC(AC);function IC(e){return e&&e.__esModule?e:{default:e}}var Ke=void 0,jt=void 0,br=[];function DC(){for(var e=[Ke,jt],t=0;t<e.length;t++){var n=e[t];n&&n.parentNode&&n.parentNode.removeChild(n)}Ke=jt=null,br=[]}function $C(){console.log("bodyTrap ----------"),console.log(br.length);for(var e=[Ke,jt],t=0;t<e.length;t++){var n=e[t],r=n||{};console.log(r.nodeName,r.className,r.id)}console.log("edn bodyTrap ----------")}function Av(){br.length!==0&&br[br.length-1].focusContent()}function UC(e,t){!Ke&&!jt&&(Ke=document.createElement("div"),Ke.setAttribute("data-react-modal-body-trap",""),Ke.style.position="absolute",Ke.style.opacity="0",Ke.setAttribute("tabindex","0"),Ke.addEventListener("focus",Av),jt=Ke.cloneNode(),jt.addEventListener("focus",Av)),br=t,br.length>0?(document.body.firstChild!==Ke&&document.body.insertBefore(Ke,document.body.firstChild),document.body.lastChild!==jt&&document.body.appendChild(jt)):(Ke.parentElement&&Ke.parentElement.removeChild(Ke),jt.parentElement&&jt.parentElement.removeChild(jt))}MC.default.subscribe(UC);(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=Object.assign||function(w){for(var P=1;P<arguments.length;P++){var I=arguments[P];for(var C in I)Object.prototype.hasOwnProperty.call(I,C)&&(w[C]=I[C])}return w},r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(w){return typeof w}:function(w){return w&&typeof Symbol=="function"&&w.constructor===Symbol&&w!==Symbol.prototype?"symbol":typeof w},o=function(){function w(P,I){for(var C=0;C<I.length;C++){var O=I[C];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(P,O.key,O)}}return function(P,I,C){return I&&w(P.prototype,I),C&&w(P,C),P}}(),i=L,a=xe,s=k(a),l=Xt,u=S(l),c=ol,f=k(c),d=Yt,p=S(d),v=Io,y=S(v),_=Qt,m=k(_),h=Do,g=k(h);function S(w){if(w&&w.__esModule)return w;var P={};if(w!=null)for(var I in w)Object.prototype.hasOwnProperty.call(w,I)&&(P[I]=w[I]);return P.default=w,P}function k(w){return w&&w.__esModule?w:{default:w}}function T(w,P){if(!(w instanceof P))throw new TypeError("Cannot call a class as a function")}function N(w,P){if(!w)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return P&&(typeof P=="object"||typeof P=="function")?P:w}function M(w,P){if(typeof P!="function"&&P!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof P);w.prototype=Object.create(P&&P.prototype,{constructor:{value:w,enumerable:!1,writable:!0,configurable:!0}}),P&&(Object.setPrototypeOf?Object.setPrototypeOf(w,P):w.__proto__=P)}var G={overlay:"ReactModal__Overlay",content:"ReactModal__Content"},$=function(P){return P.code==="Tab"||P.keyCode===9},X=function(P){return P.code==="Escape"||P.keyCode===27},ce=0,re=function(w){M(P,w);function P(I){T(this,P);var C=N(this,(P.__proto__||Object.getPrototypeOf(P)).call(this,I));return C.setOverlayRef=function(O){C.overlay=O,C.props.overlayRef&&C.props.overlayRef(O)},C.setContentRef=function(O){C.content=O,C.props.contentRef&&C.props.contentRef(O)},C.afterClose=function(){var O=C.props,A=O.appElement,D=O.ariaHideApp,z=O.htmlOpenClassName,b=O.bodyOpenClassName,U=O.parentSelector,B=U&&U().ownerDocument||document;b&&y.remove(B.body,b),z&&y.remove(B.getElementsByTagName("html")[0],z),D&&ce>0&&(ce-=1,ce===0&&p.show(A)),C.props.shouldFocusAfterRender&&(C.props.shouldReturnFocusAfterClose?(u.returnFocus(C.props.preventScroll),u.teardownScopedFocus()):u.popWithoutFocus()),C.props.onAfterClose&&C.props.onAfterClose(),g.default.deregister(C)},C.open=function(){C.beforeOpen(),C.state.afterOpen&&C.state.beforeClose?(clearTimeout(C.closeTimer),C.setState({beforeClose:!1})):(C.props.shouldFocusAfterRender&&(u.setupScopedFocus(C.node),u.markForFocusLater()),C.setState({isOpen:!0},function(){C.openAnimationFrame=requestAnimationFrame(function(){C.setState({afterOpen:!0}),C.props.isOpen&&C.props.onAfterOpen&&C.props.onAfterOpen({overlayEl:C.overlay,contentEl:C.content})})}))},C.close=function(){C.props.closeTimeoutMS>0?C.closeWithTimeout():C.closeWithoutTimeout()},C.focusContent=function(){return C.content&&!C.contentHasFocus()&&C.content.focus({preventScroll:!0})},C.closeWithTimeout=function(){var O=Date.now()+C.props.closeTimeoutMS;C.setState({beforeClose:!0,closesAt:O},function(){C.closeTimer=setTimeout(C.closeWithoutTimeout,C.state.closesAt-Date.now())})},C.closeWithoutTimeout=function(){C.setState({beforeClose:!1,isOpen:!1,afterOpen:!1,closesAt:null},C.afterClose)},C.handleKeyDown=function(O){$(O)&&(0,f.default)(C.content,O),C.props.shouldCloseOnEsc&&X(O)&&(O.stopPropagation(),C.requestClose(O))},C.handleOverlayOnClick=function(O){C.shouldClose===null&&(C.shouldClose=!0),C.shouldClose&&C.props.shouldCloseOnOverlayClick&&(C.ownerHandlesClose()?C.requestClose(O):C.focusContent()),C.shouldClose=null},C.handleContentOnMouseUp=function(){C.shouldClose=!1},C.handleOverlayOnMouseDown=function(O){!C.props.shouldCloseOnOverlayClick&&O.target==C.overlay&&O.preventDefault()},C.handleContentOnClick=function(){C.shouldClose=!1},C.handleContentOnMouseDown=function(){C.shouldClose=!1},C.requestClose=function(O){return C.ownerHandlesClose()&&C.props.onRequestClose(O)},C.ownerHandlesClose=function(){return C.props.onRequestClose},C.shouldBeClosed=function(){return!C.state.isOpen&&!C.state.beforeClose},C.contentHasFocus=function(){return document.activeElement===C.content||C.content.contains(document.activeElement)},C.buildClassName=function(O,A){var D=(typeof A>"u"?"undefined":r(A))==="object"?A:{base:G[O],afterOpen:G[O]+"--after-open",beforeClose:G[O]+"--before-close"},z=D.base;return C.state.afterOpen&&(z=z+" "+D.afterOpen),C.state.beforeClose&&(z=z+" "+D.beforeClose),typeof A=="string"&&A?z+" "+A:z},C.attributesFromObject=function(O,A){return Object.keys(A).reduce(function(D,z){return D[O+"-"+z]=A[z],D},{})},C.state={afterOpen:!1,beforeClose:!1},C.shouldClose=null,C.moveFromContentToOverlay=null,C}return o(P,[{key:"componentDidMount",value:function(){this.props.isOpen&&this.open()}},{key:"componentDidUpdate",value:function(C,O){this.props.isOpen&&!C.isOpen?this.open():!this.props.isOpen&&C.isOpen&&this.close(),this.props.shouldFocusAfterRender&&this.state.isOpen&&!O.isOpen&&this.focusContent()}},{key:"componentWillUnmount",value:function(){this.state.isOpen&&this.afterClose(),clearTimeout(this.closeTimer),cancelAnimationFrame(this.openAnimationFrame)}},{key:"beforeOpen",value:function(){var C=this.props,O=C.appElement,A=C.ariaHideApp,D=C.htmlOpenClassName,z=C.bodyOpenClassName,b=C.parentSelector,U=b&&b().ownerDocument||document;z&&y.add(U.body,z),D&&y.add(U.getElementsByTagName("html")[0],D),A&&(ce+=1,p.hide(O)),g.default.register(this)}},{key:"render",value:function(){var C=this.props,O=C.id,A=C.className,D=C.overlayClassName,z=C.defaultStyles,b=C.children,U=A?{}:z.content,B=D?{}:z.overlay;if(this.shouldBeClosed())return null;var J={ref:this.setOverlayRef,className:this.buildClassName("overlay",D),style:n({},B,this.props.style.overlay),onClick:this.handleOverlayOnClick,onMouseDown:this.handleOverlayOnMouseDown},W=n({id:O,ref:this.setContentRef,style:n({},U,this.props.style.content),className:this.buildClassName("content",A),tabIndex:"-1",onKeyDown:this.handleKeyDown,onMouseDown:this.handleContentOnMouseDown,onMouseUp:this.handleContentOnMouseUp,onClick:this.handleContentOnClick,role:this.props.role,"aria-label":this.props.contentLabel},this.attributesFromObject("aria",n({modal:!0},this.props.aria)),this.attributesFromObject("data",this.props.data||{}),{"data-testid":this.props.testId}),Z=this.props.contentElement(W,b);return this.props.overlayElement(J,Z)}}]),P}(i.Component);re.defaultProps={style:{overlay:{},content:{}},defaultStyles:{}},re.propTypes={isOpen:s.default.bool.isRequired,defaultStyles:s.default.shape({content:s.default.object,overlay:s.default.object}),style:s.default.shape({content:s.default.object,overlay:s.default.object}),className:s.default.oneOfType([s.default.string,s.default.object]),overlayClassName:s.default.oneOfType([s.default.string,s.default.object]),parentSelector:s.default.func,bodyOpenClassName:s.default.string,htmlOpenClassName:s.default.string,ariaHideApp:s.default.bool,appElement:s.default.oneOfType([s.default.instanceOf(m.default),s.default.instanceOf(_.SafeHTMLCollection),s.default.instanceOf(_.SafeNodeList),s.default.arrayOf(s.default.instanceOf(m.default))]),onAfterOpen:s.default.func,onAfterClose:s.default.func,onRequestClose:s.default.func,closeTimeoutMS:s.default.number,shouldFocusAfterRender:s.default.bool,shouldCloseOnOverlayClick:s.default.bool,shouldReturnFocusAfterClose:s.default.bool,preventScroll:s.default.bool,role:s.default.string,contentLabel:s.default.string,aria:s.default.object,data:s.default.object,children:s.default.node,shouldCloseOnEsc:s.default.bool,overlayRef:s.default.func,contentRef:s.default.func,id:s.default.string,overlayElement:s.default.func,contentElement:s.default.func,testId:s.default.string},t.default=re,e.exports=t.default})(Q2,rl);function $0(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);e!=null&&this.setState(e)}function U0(e){function t(n){var r=this.constructor.getDerivedStateFromProps(e,n);return r??null}this.setState(t.bind(this))}function F0(e,t){try{var n=this.props,r=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(n,r)}finally{this.props=n,this.state=r}}$0.__suppressDeprecationWarning=!0;U0.__suppressDeprecationWarning=!0;F0.__suppressDeprecationWarning=!0;function FC(e){var t=e.prototype;if(!t||!t.isReactComponent)throw new Error("Can only polyfill class components");if(typeof e.getDerivedStateFromProps!="function"&&typeof t.getSnapshotBeforeUpdate!="function")return e;var n=null,r=null,o=null;if(typeof t.componentWillMount=="function"?n="componentWillMount":typeof t.UNSAFE_componentWillMount=="function"&&(n="UNSAFE_componentWillMount"),typeof t.componentWillReceiveProps=="function"?r="componentWillReceiveProps":typeof t.UNSAFE_componentWillReceiveProps=="function"&&(r="UNSAFE_componentWillReceiveProps"),typeof t.componentWillUpdate=="function"?o="componentWillUpdate":typeof t.UNSAFE_componentWillUpdate=="function"&&(o="UNSAFE_componentWillUpdate"),n!==null||r!==null||o!==null){var i=e.displayName||e.name,a=typeof e.getDerivedStateFromProps=="function"?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error(`Unsafe legacy lifecycles will not be called for components using new component APIs.

`+i+" uses "+a+" but also contains the following legacy lifecycles:"+(n!==null?`
  `+n:"")+(r!==null?`
  `+r:"")+(o!==null?`
  `+o:"")+`

The above lifecycles should be removed. Learn more about this warning here:
https://fb.me/react-async-component-lifecycle-hooks`)}if(typeof e.getDerivedStateFromProps=="function"&&(t.componentWillMount=$0,t.componentWillReceiveProps=U0),typeof t.getSnapshotBeforeUpdate=="function"){if(typeof t.componentDidUpdate!="function")throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");t.componentWillUpdate=F0;var s=t.componentDidUpdate;t.componentDidUpdate=function(u,c,f){var d=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:f;s.call(this,u,c,d)}}return e}const jC=Object.freeze(Object.defineProperty({__proto__:null,polyfill:FC},Symbol.toStringTag,{value:"Module"})),BC=VS(jC);Object.defineProperty(Tr,"__esModule",{value:!0});Tr.bodyOpenClassName=Tr.portalClassName=void 0;var Mv=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},zC=function(){function e(t,n){for(var r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),j0=L,al=ha(j0),VC=mo,sl=ha(VC),WC=xe,oe=ha(WC),HC=rl,Iv=ha(HC),qC=Yt,KC=GC(qC),Fn=Qt,Dv=ha(Fn),QC=BC;function GC(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function ha(e){return e&&e.__esModule?e:{default:e}}function XC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $v(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function YC(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var JC=Tr.portalClassName="ReactModalPortal",ZC=Tr.bodyOpenClassName="ReactModal__Body--open",hr=Fn.canUseDOM&&sl.default.createPortal!==void 0,Uv=function(t){return document.createElement(t)},Fv=function(){return hr?sl.default.createPortal:sl.default.unstable_renderSubtreeIntoContainer};function Qa(e){return e()}var pa=function(e){YC(t,e);function t(){var n,r,o,i;XC(this,t);for(var a=arguments.length,s=Array(a),l=0;l<a;l++)s[l]=arguments[l];return i=(r=(o=$v(this,(n=t.__proto__||Object.getPrototypeOf(t)).call.apply(n,[this].concat(s))),o),o.removePortal=function(){!hr&&sl.default.unmountComponentAtNode(o.node);var u=Qa(o.props.parentSelector);u&&u.contains(o.node)?u.removeChild(o.node):console.warn('React-Modal: "parentSelector" prop did not returned any DOM element. Make sure that the parent element is unmounted to avoid any memory leaks.')},o.portalRef=function(u){o.portal=u},o.renderPortal=function(u){var c=Fv(),f=c(o,al.default.createElement(Iv.default,Mv({defaultStyles:t.defaultStyles},u)),o.node);o.portalRef(f)},r),$v(o,i)}return zC(t,[{key:"componentDidMount",value:function(){if(Fn.canUseDOM){hr||(this.node=Uv("div")),this.node.className=this.props.portalClassName;var r=Qa(this.props.parentSelector);r.appendChild(this.node),!hr&&this.renderPortal(this.props)}}},{key:"getSnapshotBeforeUpdate",value:function(r){var o=Qa(r.parentSelector),i=Qa(this.props.parentSelector);return{prevParent:o,nextParent:i}}},{key:"componentDidUpdate",value:function(r,o,i){if(Fn.canUseDOM){var a=this.props,s=a.isOpen,l=a.portalClassName;r.portalClassName!==l&&(this.node.className=l);var u=i.prevParent,c=i.nextParent;c!==u&&(u.removeChild(this.node),c.appendChild(this.node)),!(!r.isOpen&&!s)&&!hr&&this.renderPortal(this.props)}}},{key:"componentWillUnmount",value:function(){if(!(!Fn.canUseDOM||!this.node||!this.portal)){var r=this.portal.state,o=Date.now(),i=r.isOpen&&this.props.closeTimeoutMS&&(r.closesAt||o+this.props.closeTimeoutMS);i?(r.beforeClose||this.portal.closeWithTimeout(),setTimeout(this.removePortal,i-o)):this.removePortal()}}},{key:"render",value:function(){if(!Fn.canUseDOM||!hr)return null;!this.node&&hr&&(this.node=Uv("div"));var r=Fv();return r(al.default.createElement(Iv.default,Mv({ref:this.portalRef,defaultStyles:t.defaultStyles},this.props)),this.node)}}],[{key:"setAppElement",value:function(r){KC.setElement(r)}}]),t}(j0.Component);pa.propTypes={isOpen:oe.default.bool.isRequired,style:oe.default.shape({content:oe.default.object,overlay:oe.default.object}),portalClassName:oe.default.string,bodyOpenClassName:oe.default.string,htmlOpenClassName:oe.default.string,className:oe.default.oneOfType([oe.default.string,oe.default.shape({base:oe.default.string.isRequired,afterOpen:oe.default.string.isRequired,beforeClose:oe.default.string.isRequired})]),overlayClassName:oe.default.oneOfType([oe.default.string,oe.default.shape({base:oe.default.string.isRequired,afterOpen:oe.default.string.isRequired,beforeClose:oe.default.string.isRequired})]),appElement:oe.default.oneOfType([oe.default.instanceOf(Dv.default),oe.default.instanceOf(Fn.SafeHTMLCollection),oe.default.instanceOf(Fn.SafeNodeList),oe.default.arrayOf(oe.default.instanceOf(Dv.default))]),onAfterOpen:oe.default.func,onRequestClose:oe.default.func,closeTimeoutMS:oe.default.number,ariaHideApp:oe.default.bool,shouldFocusAfterRender:oe.default.bool,shouldCloseOnOverlayClick:oe.default.bool,shouldReturnFocusAfterClose:oe.default.bool,preventScroll:oe.default.bool,parentSelector:oe.default.func,aria:oe.default.object,data:oe.default.object,role:oe.default.string,contentLabel:oe.default.string,shouldCloseOnEsc:oe.default.bool,overlayRef:oe.default.func,contentRef:oe.default.func,id:oe.default.string,overlayElement:oe.default.func,contentElement:oe.default.func};pa.defaultProps={isOpen:!1,portalClassName:JC,bodyOpenClassName:ZC,role:"dialog",ariaHideApp:!0,closeTimeoutMS:0,shouldFocusAfterRender:!0,shouldCloseOnEsc:!0,shouldCloseOnOverlayClick:!0,shouldReturnFocusAfterClose:!0,preventScroll:!1,parentSelector:function(){return document.body},overlayElement:function(t,n){return al.default.createElement("div",t,n)},contentElement:function(t,n){return al.default.createElement("div",t,n)}};pa.defaultStyles={overlay:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(255, 255, 255, 0.75)"},content:{position:"absolute",top:"40px",left:"40px",right:"40px",bottom:"40px",border:"1px solid #ccc",background:"#fff",overflow:"auto",WebkitOverflowScrolling:"touch",borderRadius:"4px",outline:"none",padding:"20px"}};(0,QC.polyfill)(pa);Tr.default=pa;(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=Tr,r=o(n);function o(i){return i&&i.__esModule?i:{default:i}}t.default=r.default,e.exports=t.default})(z2,nl);const B0=Kf(nl);function va(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,xs(e,t)}var ma=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(r){var o=this,i=r||function(){};return this.listeners.push(i),this.onSubscribe(),function(){o.listeners=o.listeners.filter(function(a){return a!==i}),o.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}();function de(){return de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},de.apply(this,arguments)}var ll=typeof window>"u";function Qe(){}function eR(e,t){return typeof e=="function"?e(t):e}function cf(e){return typeof e=="number"&&e>=0&&e!==1/0}function ul(e){return Array.isArray(e)?e:[e]}function z0(e,t){return Math.max(e+(t||0)-Date.now(),0)}function _s(e,t,n){return ga(e)?typeof t=="function"?de({},n,{queryKey:e,queryFn:t}):de({},t,{queryKey:e}):e}function l$(e,t,n){return ga(e)?typeof t=="function"?de({},n,{mutationKey:e,mutationFn:t}):de({},t,{mutationKey:e}):typeof e=="function"?de({},t,{mutationFn:e}):de({},e)}function An(e,t,n){return ga(e)?[de({},t,{queryKey:e}),n]:[e||{},t]}function tR(e,t){if(e===!0&&t===!0||e==null&&t==null)return"all";if(e===!1&&t===!1)return"none";var n=e??!t;return n?"active":"inactive"}function jv(e,t){var n=e.active,r=e.exact,o=e.fetching,i=e.inactive,a=e.predicate,s=e.queryKey,l=e.stale;if(ga(s)){if(r){if(t.queryHash!==Hd(s,t.options))return!1}else if(!cl(t.queryKey,s))return!1}var u=tR(n,i);if(u==="none")return!1;if(u!=="all"){var c=t.isActive();if(u==="active"&&!c||u==="inactive"&&c)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||typeof o=="boolean"&&t.isFetching()!==o||a&&!a(t))}function Bv(e,t){var n=e.exact,r=e.fetching,o=e.predicate,i=e.mutationKey;if(ga(i)){if(!t.options.mutationKey)return!1;if(n){if(wr(t.options.mutationKey)!==wr(i))return!1}else if(!cl(t.options.mutationKey,i))return!1}return!(typeof r=="boolean"&&t.state.status==="loading"!==r||o&&!o(t))}function Hd(e,t){var n=(t==null?void 0:t.queryKeyHashFn)||wr;return n(e)}function wr(e){var t=ul(e);return nR(t)}function nR(e){return JSON.stringify(e,function(t,n){return ff(n)?Object.keys(n).sort().reduce(function(r,o){return r[o]=n[o],r},{}):n})}function cl(e,t){return V0(ul(e),ul(t))}function V0(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(function(n){return!V0(e[n],t[n])}):!1}function fl(e,t){if(e===t)return e;var n=Array.isArray(e)&&Array.isArray(t);if(n||ff(e)&&ff(t)){for(var r=n?e.length:Object.keys(e).length,o=n?t:Object.keys(t),i=o.length,a=n?[]:{},s=0,l=0;l<i;l++){var u=n?l:o[l];a[u]=fl(e[u],t[u]),a[u]===e[u]&&s++}return r===i&&s===r?e:a}return t}function rR(e,t){if(e&&!t||t&&!e)return!1;for(var n in e)if(e[n]!==t[n])return!1;return!0}function ff(e){if(!zv(e))return!1;var t=e.constructor;if(typeof t>"u")return!0;var n=t.prototype;return!(!zv(n)||!n.hasOwnProperty("isPrototypeOf"))}function zv(e){return Object.prototype.toString.call(e)==="[object Object]"}function ga(e){return typeof e=="string"||Array.isArray(e)}function oR(e){return new Promise(function(t){setTimeout(t,e)})}function Vv(e){Promise.resolve().then(e).catch(function(t){return setTimeout(function(){throw t})})}function W0(){if(typeof AbortController=="function")return new AbortController}var iR=function(e){va(t,e);function t(){var r;return r=e.call(this)||this,r.setup=function(o){var i;if(!ll&&((i=window)!=null&&i.addEventListener)){var a=function(){return o()};return window.addEventListener("visibilitychange",a,!1),window.addEventListener("focus",a,!1),function(){window.removeEventListener("visibilitychange",a),window.removeEventListener("focus",a)}}},r}var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){if(!this.hasListeners()){var o;(o=this.cleanup)==null||o.call(this),this.cleanup=void 0}},n.setEventListener=function(o){var i,a=this;this.setup=o,(i=this.cleanup)==null||i.call(this),this.cleanup=o(function(s){typeof s=="boolean"?a.setFocused(s):a.onFocus()})},n.setFocused=function(o){this.focused=o,o&&this.onFocus()},n.onFocus=function(){this.listeners.forEach(function(o){o()})},n.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},t}(ma),Ri=new iR,aR=function(e){va(t,e);function t(){var r;return r=e.call(this)||this,r.setup=function(o){var i;if(!ll&&((i=window)!=null&&i.addEventListener)){var a=function(){return o()};return window.addEventListener("online",a,!1),window.addEventListener("offline",a,!1),function(){window.removeEventListener("online",a),window.removeEventListener("offline",a)}}},r}var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){if(!this.hasListeners()){var o;(o=this.cleanup)==null||o.call(this),this.cleanup=void 0}},n.setEventListener=function(o){var i,a=this;this.setup=o,(i=this.cleanup)==null||i.call(this),this.cleanup=o(function(s){typeof s=="boolean"?a.setOnline(s):a.onOnline()})},n.setOnline=function(o){this.online=o,o&&this.onOnline()},n.onOnline=function(){this.listeners.forEach(function(o){o()})},n.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},t}(ma),bs=new aR;function sR(e){return Math.min(1e3*Math.pow(2,e),3e4)}function dl(e){return typeof(e==null?void 0:e.cancel)=="function"}var H0=function(t){this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent};function Es(e){return e instanceof H0}var q0=function(t){var n=this,r=!1,o,i,a,s;this.abort=t.abort,this.cancel=function(d){return o==null?void 0:o(d)},this.cancelRetry=function(){r=!0},this.continueRetry=function(){r=!1},this.continue=function(){return i==null?void 0:i()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(d,p){a=d,s=p});var l=function(p){n.isResolved||(n.isResolved=!0,t.onSuccess==null||t.onSuccess(p),i==null||i(),a(p))},u=function(p){n.isResolved||(n.isResolved=!0,t.onError==null||t.onError(p),i==null||i(),s(p))},c=function(){return new Promise(function(p){i=p,n.isPaused=!0,t.onPause==null||t.onPause()}).then(function(){i=void 0,n.isPaused=!1,t.onContinue==null||t.onContinue()})},f=function d(){if(!n.isResolved){var p;try{p=t.fn()}catch(v){p=Promise.reject(v)}o=function(y){if(!n.isResolved&&(u(new H0(y)),n.abort==null||n.abort(),dl(p)))try{p.cancel()}catch{}},n.isTransportCancelable=dl(p),Promise.resolve(p).then(l).catch(function(v){var y,_;if(!n.isResolved){var m=(y=t.retry)!=null?y:3,h=(_=t.retryDelay)!=null?_:sR,g=typeof h=="function"?h(n.failureCount,v):h,S=m===!0||typeof m=="number"&&n.failureCount<m||typeof m=="function"&&m(n.failureCount,v);if(r||!S){u(v);return}n.failureCount++,t.onFail==null||t.onFail(n.failureCount,v),oR(g).then(function(){if(!Ri.isFocused()||!bs.isOnline())return c()}).then(function(){r?u(v):d()})}})}};f()},lR=function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(n){n()},this.batchNotifyFn=function(n){n()}}var t=e.prototype;return t.batch=function(r){var o;this.transactions++;try{o=r()}finally{this.transactions--,this.transactions||this.flush()}return o},t.schedule=function(r){var o=this;this.transactions?this.queue.push(r):Vv(function(){o.notifyFn(r)})},t.batchCalls=function(r){var o=this;return function(){for(var i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];o.schedule(function(){r.apply(void 0,a)})}},t.flush=function(){var r=this,o=this.queue;this.queue=[],o.length&&Vv(function(){r.batchNotifyFn(function(){o.forEach(function(i){r.notifyFn(i)})})})},t.setNotifyFunction=function(r){this.notifyFn=r},t.setBatchNotifyFunction=function(r){this.batchNotifyFn=r},e}(),Ne=new lR,K0=console;function hl(){return K0}function uR(e){K0=e}var cR=function(){function e(n){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=n.defaultOptions,this.setOptions(n.options),this.observers=[],this.cache=n.cache,this.queryKey=n.queryKey,this.queryHash=n.queryHash,this.initialState=n.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=n.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(r){var o;this.options=de({},this.defaultOptions,r),this.meta=r==null?void 0:r.meta,this.cacheTime=Math.max(this.cacheTime||0,(o=this.options.cacheTime)!=null?o:5*60*1e3)},t.setDefaultOptions=function(r){this.defaultOptions=r},t.scheduleGc=function(){var r=this;this.clearGcTimeout(),cf(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){r.optionalRemove()},this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(r,o){var i,a,s=this.state.data,l=eR(r,s);return(i=(a=this.options).isDataEqual)!=null&&i.call(a,s,l)?l=s:this.options.structuralSharing!==!1&&(l=fl(s,l)),this.dispatch({data:l,type:"success",dataUpdatedAt:o==null?void 0:o.updatedAt}),l},t.setState=function(r,o){this.dispatch({type:"setState",state:r,setStateOptions:o})},t.cancel=function(r){var o,i=this.promise;return(o=this.retryer)==null||o.cancel(r),i?i.then(Qe).catch(Qe):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some(function(r){return r.options.enabled!==!1})},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(r){return r.getCurrentResult().isStale})},t.isStaleByTime=function(r){return r===void 0&&(r=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!z0(this.state.dataUpdatedAt,r)},t.onFocus=function(){var r,o=this.observers.find(function(i){return i.shouldFetchOnWindowFocus()});o&&o.refetch(),(r=this.retryer)==null||r.continue()},t.onOnline=function(){var r,o=this.observers.find(function(i){return i.shouldFetchOnReconnect()});o&&o.refetch(),(r=this.retryer)==null||r.continue()},t.addObserver=function(r){this.observers.indexOf(r)===-1&&(this.observers.push(r),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:r}))},t.removeObserver=function(r){this.observers.indexOf(r)!==-1&&(this.observers=this.observers.filter(function(o){return o!==r}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:r}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(r,o){var i=this,a,s,l;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(o!=null&&o.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var u;return(u=this.retryer)==null||u.continueRetry(),this.promise}}if(r&&this.setOptions(r),!this.options.queryFn){var c=this.observers.find(function(h){return h.options.queryFn});c&&this.setOptions(c.options)}var f=ul(this.queryKey),d=W0(),p={queryKey:f,pageParam:void 0,meta:this.meta};Object.defineProperty(p,"signal",{enumerable:!0,get:function(){if(d)return i.abortSignalConsumed=!0,d.signal}});var v=function(){return i.options.queryFn?(i.abortSignalConsumed=!1,i.options.queryFn(p)):Promise.reject("Missing queryFn")},y={fetchOptions:o,options:this.options,queryKey:f,state:this.state,fetchFn:v,meta:this.meta};if((a=this.options.behavior)!=null&&a.onFetch){var _;(_=this.options.behavior)==null||_.onFetch(y)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((s=y.fetchOptions)==null?void 0:s.meta)){var m;this.dispatch({type:"fetch",meta:(m=y.fetchOptions)==null?void 0:m.meta})}return this.retryer=new q0({fn:y.fetchFn,abort:d==null||(l=d.abort)==null?void 0:l.bind(d),onSuccess:function(g){i.setData(g),i.cache.config.onSuccess==null||i.cache.config.onSuccess(g,i),i.cacheTime===0&&i.optionalRemove()},onError:function(g){Es(g)&&g.silent||i.dispatch({type:"error",error:g}),Es(g)||(i.cache.config.onError==null||i.cache.config.onError(g,i),hl().error(g)),i.cacheTime===0&&i.optionalRemove()},onFail:function(){i.dispatch({type:"failed"})},onPause:function(){i.dispatch({type:"pause"})},onContinue:function(){i.dispatch({type:"continue"})},retry:y.options.retry,retryDelay:y.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(r){var o=this;this.state=this.reducer(this.state,r),Ne.batch(function(){o.observers.forEach(function(i){i.onQueryUpdate(r)}),o.cache.notify({query:o,type:"queryUpdated",action:r})})},t.getDefaultState=function(r){var o=typeof r.initialData=="function"?r.initialData():r.initialData,i=typeof r.initialData<"u",a=i?typeof r.initialDataUpdatedAt=="function"?r.initialDataUpdatedAt():r.initialDataUpdatedAt:0,s=typeof o<"u";return{data:o,dataUpdateCount:0,dataUpdatedAt:s?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:s?"success":"idle"}},t.reducer=function(r,o){var i,a;switch(o.type){case"failed":return de({},r,{fetchFailureCount:r.fetchFailureCount+1});case"pause":return de({},r,{isPaused:!0});case"continue":return de({},r,{isPaused:!1});case"fetch":return de({},r,{fetchFailureCount:0,fetchMeta:(i=o.meta)!=null?i:null,isFetching:!0,isPaused:!1},!r.dataUpdatedAt&&{error:null,status:"loading"});case"success":return de({},r,{data:o.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:(a=o.dataUpdatedAt)!=null?a:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var s=o.error;return Es(s)&&s.revert&&this.revertState?de({},this.revertState):de({},r,{error:s,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return de({},r,{isInvalidated:!0});case"setState":return de({},r,o.state);default:return r}},e}(),Q0=function(e){va(t,e);function t(r){var o;return o=e.call(this)||this,o.config=r||{},o.queries=[],o.queriesMap={},o}var n=t.prototype;return n.build=function(o,i,a){var s,l=i.queryKey,u=(s=i.queryHash)!=null?s:Hd(l,i),c=this.get(u);return c||(c=new cR({cache:this,queryKey:l,queryHash:u,options:o.defaultQueryOptions(i),state:a,defaultOptions:o.getQueryDefaults(l),meta:i.meta}),this.add(c)),c},n.add=function(o){this.queriesMap[o.queryHash]||(this.queriesMap[o.queryHash]=o,this.queries.push(o),this.notify({type:"queryAdded",query:o}))},n.remove=function(o){var i=this.queriesMap[o.queryHash];i&&(o.destroy(),this.queries=this.queries.filter(function(a){return a!==o}),i===o&&delete this.queriesMap[o.queryHash],this.notify({type:"queryRemoved",query:o}))},n.clear=function(){var o=this;Ne.batch(function(){o.queries.forEach(function(i){o.remove(i)})})},n.get=function(o){return this.queriesMap[o]},n.getAll=function(){return this.queries},n.find=function(o,i){var a=An(o,i),s=a[0];return typeof s.exact>"u"&&(s.exact=!0),this.queries.find(function(l){return jv(s,l)})},n.findAll=function(o,i){var a=An(o,i),s=a[0];return Object.keys(s).length>0?this.queries.filter(function(l){return jv(s,l)}):this.queries},n.notify=function(o){var i=this;Ne.batch(function(){i.listeners.forEach(function(a){a(o)})})},n.onFocus=function(){var o=this;Ne.batch(function(){o.queries.forEach(function(i){i.onFocus()})})},n.onOnline=function(){var o=this;Ne.batch(function(){o.queries.forEach(function(i){i.onOnline()})})},t}(ma),fR=function(){function e(n){this.options=de({},n.defaultOptions,n.options),this.mutationId=n.mutationId,this.mutationCache=n.mutationCache,this.observers=[],this.state=n.state||dR(),this.meta=n.meta}var t=e.prototype;return t.setState=function(r){this.dispatch({type:"setState",state:r})},t.addObserver=function(r){this.observers.indexOf(r)===-1&&this.observers.push(r)},t.removeObserver=function(r){this.observers=this.observers.filter(function(o){return o!==r})},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(Qe).catch(Qe)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var r=this,o,i=this.state.status==="loading",a=Promise.resolve();return i||(this.dispatch({type:"loading",variables:this.options.variables}),a=a.then(function(){r.mutationCache.config.onMutate==null||r.mutationCache.config.onMutate(r.state.variables,r)}).then(function(){return r.options.onMutate==null?void 0:r.options.onMutate(r.state.variables)}).then(function(s){s!==r.state.context&&r.dispatch({type:"loading",context:s,variables:r.state.variables})})),a.then(function(){return r.executeMutation()}).then(function(s){o=s,r.mutationCache.config.onSuccess==null||r.mutationCache.config.onSuccess(o,r.state.variables,r.state.context,r)}).then(function(){return r.options.onSuccess==null?void 0:r.options.onSuccess(o,r.state.variables,r.state.context)}).then(function(){return r.options.onSettled==null?void 0:r.options.onSettled(o,null,r.state.variables,r.state.context)}).then(function(){return r.dispatch({type:"success",data:o}),o}).catch(function(s){return r.mutationCache.config.onError==null||r.mutationCache.config.onError(s,r.state.variables,r.state.context,r),hl().error(s),Promise.resolve().then(function(){return r.options.onError==null?void 0:r.options.onError(s,r.state.variables,r.state.context)}).then(function(){return r.options.onSettled==null?void 0:r.options.onSettled(void 0,s,r.state.variables,r.state.context)}).then(function(){throw r.dispatch({type:"error",error:s}),s})})},t.executeMutation=function(){var r=this,o;return this.retryer=new q0({fn:function(){return r.options.mutationFn?r.options.mutationFn(r.state.variables):Promise.reject("No mutationFn found")},onFail:function(){r.dispatch({type:"failed"})},onPause:function(){r.dispatch({type:"pause"})},onContinue:function(){r.dispatch({type:"continue"})},retry:(o=this.options.retry)!=null?o:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(r){var o=this;this.state=hR(this.state,r),Ne.batch(function(){o.observers.forEach(function(i){i.onMutationUpdate(r)}),o.mutationCache.notify(o)})},e}();function dR(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function hR(e,t){switch(t.type){case"failed":return de({},e,{failureCount:e.failureCount+1});case"pause":return de({},e,{isPaused:!0});case"continue":return de({},e,{isPaused:!1});case"loading":return de({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return de({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return de({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return de({},e,t.state);default:return e}}var pR=function(e){va(t,e);function t(r){var o;return o=e.call(this)||this,o.config=r||{},o.mutations=[],o.mutationId=0,o}var n=t.prototype;return n.build=function(o,i,a){var s=new fR({mutationCache:this,mutationId:++this.mutationId,options:o.defaultMutationOptions(i),state:a,defaultOptions:i.mutationKey?o.getMutationDefaults(i.mutationKey):void 0,meta:i.meta});return this.add(s),s},n.add=function(o){this.mutations.push(o),this.notify(o)},n.remove=function(o){this.mutations=this.mutations.filter(function(i){return i!==o}),o.cancel(),this.notify(o)},n.clear=function(){var o=this;Ne.batch(function(){o.mutations.forEach(function(i){o.remove(i)})})},n.getAll=function(){return this.mutations},n.find=function(o){return typeof o.exact>"u"&&(o.exact=!0),this.mutations.find(function(i){return Bv(o,i)})},n.findAll=function(o){return this.mutations.filter(function(i){return Bv(o,i)})},n.notify=function(o){var i=this;Ne.batch(function(){i.listeners.forEach(function(a){a(o)})})},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var o=this.mutations.filter(function(i){return i.state.isPaused});return Ne.batch(function(){return o.reduce(function(i,a){return i.then(function(){return a.continue().catch(Qe)})},Promise.resolve())})},t}(ma);function vR(){return{onFetch:function(t){t.fetchFn=function(){var n,r,o,i,a,s,l=(n=t.fetchOptions)==null||(r=n.meta)==null?void 0:r.refetchPage,u=(o=t.fetchOptions)==null||(i=o.meta)==null?void 0:i.fetchMore,c=u==null?void 0:u.pageParam,f=(u==null?void 0:u.direction)==="forward",d=(u==null?void 0:u.direction)==="backward",p=((a=t.state.data)==null?void 0:a.pages)||[],v=((s=t.state.data)==null?void 0:s.pageParams)||[],y=W0(),_=y==null?void 0:y.signal,m=v,h=!1,g=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},S=function(w,P,I,C){return m=C?[P].concat(m):[].concat(m,[P]),C?[I].concat(w):[].concat(w,[I])},k=function(w,P,I,C){if(h)return Promise.reject("Cancelled");if(typeof I>"u"&&!P&&w.length)return Promise.resolve(w);var O={queryKey:t.queryKey,signal:_,pageParam:I,meta:t.meta},A=g(O),D=Promise.resolve(A).then(function(b){return S(w,I,b,C)});if(dl(A)){var z=D;z.cancel=A.cancel}return D},T;if(!p.length)T=k([]);else if(f){var N=typeof c<"u",M=N?c:Wv(t.options,p);T=k(p,N,M)}else if(d){var G=typeof c<"u",$=G?c:mR(t.options,p);T=k(p,G,$,!0)}else(function(){m=[];var re=typeof t.options.getNextPageParam>"u",w=l&&p[0]?l(p[0],0,p):!0;T=w?k([],re,v[0]):Promise.resolve(S([],v[0],p[0]));for(var P=function(O){T=T.then(function(A){var D=l&&p[O]?l(p[O],O,p):!0;if(D){var z=re?v[O]:Wv(t.options,A);return k(A,re,z)}return Promise.resolve(S(A,v[O],p[O]))})},I=1;I<p.length;I++)P(I)})();var X=T.then(function(re){return{pages:re,pageParams:m}}),ce=X;return ce.cancel=function(){h=!0,y==null||y.abort(),dl(T)&&T.cancel()},X}}}}function Wv(e,t){return e.getNextPageParam==null?void 0:e.getNextPageParam(t[t.length-1],t)}function mR(e,t){return e.getPreviousPageParam==null?void 0:e.getPreviousPageParam(t[0],t)}var gR=function(){function e(n){n===void 0&&(n={}),this.queryCache=n.queryCache||new Q0,this.mutationCache=n.mutationCache||new pR,this.defaultOptions=n.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var r=this;this.unsubscribeFocus=Ri.subscribe(function(){Ri.isFocused()&&bs.isOnline()&&(r.mutationCache.onFocus(),r.queryCache.onFocus())}),this.unsubscribeOnline=bs.subscribe(function(){Ri.isFocused()&&bs.isOnline()&&(r.mutationCache.onOnline(),r.queryCache.onOnline())})},t.unmount=function(){var r,o;(r=this.unsubscribeFocus)==null||r.call(this),(o=this.unsubscribeOnline)==null||o.call(this)},t.isFetching=function(r,o){var i=An(r,o),a=i[0];return a.fetching=!0,this.queryCache.findAll(a).length},t.isMutating=function(r){return this.mutationCache.findAll(de({},r,{fetching:!0})).length},t.getQueryData=function(r,o){var i;return(i=this.queryCache.find(r,o))==null?void 0:i.state.data},t.getQueriesData=function(r){return this.getQueryCache().findAll(r).map(function(o){var i=o.queryKey,a=o.state,s=a.data;return[i,s]})},t.setQueryData=function(r,o,i){var a=_s(r),s=this.defaultQueryOptions(a);return this.queryCache.build(this,s).setData(o,i)},t.setQueriesData=function(r,o,i){var a=this;return Ne.batch(function(){return a.getQueryCache().findAll(r).map(function(s){var l=s.queryKey;return[l,a.setQueryData(l,o,i)]})})},t.getQueryState=function(r,o){var i;return(i=this.queryCache.find(r,o))==null?void 0:i.state},t.removeQueries=function(r,o){var i=An(r,o),a=i[0],s=this.queryCache;Ne.batch(function(){s.findAll(a).forEach(function(l){s.remove(l)})})},t.resetQueries=function(r,o,i){var a=this,s=An(r,o,i),l=s[0],u=s[1],c=this.queryCache,f=de({},l,{active:!0});return Ne.batch(function(){return c.findAll(l).forEach(function(d){d.reset()}),a.refetchQueries(f,u)})},t.cancelQueries=function(r,o,i){var a=this,s=An(r,o,i),l=s[0],u=s[1],c=u===void 0?{}:u;typeof c.revert>"u"&&(c.revert=!0);var f=Ne.batch(function(){return a.queryCache.findAll(l).map(function(d){return d.cancel(c)})});return Promise.all(f).then(Qe).catch(Qe)},t.invalidateQueries=function(r,o,i){var a,s,l,u=this,c=An(r,o,i),f=c[0],d=c[1],p=de({},f,{active:(a=(s=f.refetchActive)!=null?s:f.active)!=null?a:!0,inactive:(l=f.refetchInactive)!=null?l:!1});return Ne.batch(function(){return u.queryCache.findAll(f).forEach(function(v){v.invalidate()}),u.refetchQueries(p,d)})},t.refetchQueries=function(r,o,i){var a=this,s=An(r,o,i),l=s[0],u=s[1],c=Ne.batch(function(){return a.queryCache.findAll(l).map(function(d){return d.fetch(void 0,de({},u,{meta:{refetchPage:l==null?void 0:l.refetchPage}}))})}),f=Promise.all(c).then(Qe);return u!=null&&u.throwOnError||(f=f.catch(Qe)),f},t.fetchQuery=function(r,o,i){var a=_s(r,o,i),s=this.defaultQueryOptions(a);typeof s.retry>"u"&&(s.retry=!1);var l=this.queryCache.build(this,s);return l.isStaleByTime(s.staleTime)?l.fetch(s):Promise.resolve(l.state.data)},t.prefetchQuery=function(r,o,i){return this.fetchQuery(r,o,i).then(Qe).catch(Qe)},t.fetchInfiniteQuery=function(r,o,i){var a=_s(r,o,i);return a.behavior=vR(),this.fetchQuery(a)},t.prefetchInfiniteQuery=function(r,o,i){return this.fetchInfiniteQuery(r,o,i).then(Qe).catch(Qe)},t.cancelMutations=function(){var r=this,o=Ne.batch(function(){return r.mutationCache.getAll().map(function(i){return i.cancel()})});return Promise.all(o).then(Qe).catch(Qe)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(r){return this.mutationCache.build(this,r).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(r){this.defaultOptions=r},t.setQueryDefaults=function(r,o){var i=this.queryDefaults.find(function(a){return wr(r)===wr(a.queryKey)});i?i.defaultOptions=o:this.queryDefaults.push({queryKey:r,defaultOptions:o})},t.getQueryDefaults=function(r){var o;return r?(o=this.queryDefaults.find(function(i){return cl(r,i.queryKey)}))==null?void 0:o.defaultOptions:void 0},t.setMutationDefaults=function(r,o){var i=this.mutationDefaults.find(function(a){return wr(r)===wr(a.mutationKey)});i?i.defaultOptions=o:this.mutationDefaults.push({mutationKey:r,defaultOptions:o})},t.getMutationDefaults=function(r){var o;return r?(o=this.mutationDefaults.find(function(i){return cl(r,i.mutationKey)}))==null?void 0:o.defaultOptions:void 0},t.defaultQueryOptions=function(r){if(r!=null&&r._defaulted)return r;var o=de({},this.defaultOptions.queries,this.getQueryDefaults(r==null?void 0:r.queryKey),r,{_defaulted:!0});return!o.queryHash&&o.queryKey&&(o.queryHash=Hd(o.queryKey,o)),o},t.defaultQueryObserverOptions=function(r){return this.defaultQueryOptions(r)},t.defaultMutationOptions=function(r){return r!=null&&r._defaulted?r:de({},this.defaultOptions.mutations,this.getMutationDefaults(r==null?void 0:r.mutationKey),r,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}(),yR=function(e){va(t,e);function t(r,o){var i;return i=e.call(this)||this,i.client=r,i.options=o,i.trackedProps=[],i.selectError=null,i.bindMethods(),i.setOptions(o),i}var n=t.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),Hv(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return df(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return df(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(o,i){var a=this.options,s=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(o),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=a.queryKey),this.updateQuery();var l=this.hasListeners();l&&qv(this.currentQuery,s,this.options,a)&&this.executeFetch(),this.updateResult(i),l&&(this.currentQuery!==s||this.options.enabled!==a.enabled||this.options.staleTime!==a.staleTime)&&this.updateStaleTimeout();var u=this.computeRefetchInterval();l&&(this.currentQuery!==s||this.options.enabled!==a.enabled||u!==this.currentRefetchInterval)&&this.updateRefetchInterval(u)},n.getOptimisticResult=function(o){var i=this.client.defaultQueryObserverOptions(o),a=this.client.getQueryCache().build(this.client,i);return this.createResult(a,i)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(o,i){var a=this,s={},l=function(c){a.trackedProps.includes(c)||a.trackedProps.push(c)};return Object.keys(o).forEach(function(u){Object.defineProperty(s,u,{configurable:!1,enumerable:!0,get:function(){return l(u),o[u]}})}),(i.useErrorBoundary||i.suspense)&&l("error"),s},n.getNextResult=function(o){var i=this;return new Promise(function(a,s){var l=i.subscribe(function(u){u.isFetching||(l(),u.isError&&(o!=null&&o.throwOnError)?s(u.error):a(u))})})},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(o){return this.fetch(de({},o,{meta:{refetchPage:o==null?void 0:o.refetchPage}}))},n.fetchOptimistic=function(o){var i=this,a=this.client.defaultQueryObserverOptions(o),s=this.client.getQueryCache().build(this.client,a);return s.fetch().then(function(){return i.createResult(s,a)})},n.fetch=function(o){var i=this;return this.executeFetch(o).then(function(){return i.updateResult(),i.currentResult})},n.executeFetch=function(o){this.updateQuery();var i=this.currentQuery.fetch(this.options,o);return o!=null&&o.throwOnError||(i=i.catch(Qe)),i},n.updateStaleTimeout=function(){var o=this;if(this.clearStaleTimeout(),!(ll||this.currentResult.isStale||!cf(this.options.staleTime))){var i=z0(this.currentResult.dataUpdatedAt,this.options.staleTime),a=i+1;this.staleTimeoutId=setTimeout(function(){o.currentResult.isStale||o.updateResult()},a)}},n.computeRefetchInterval=function(){var o;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(o=this.options.refetchInterval)!=null?o:!1},n.updateRefetchInterval=function(o){var i=this;this.clearRefetchInterval(),this.currentRefetchInterval=o,!(ll||this.options.enabled===!1||!cf(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(i.options.refetchIntervalInBackground||Ri.isFocused())&&i.executeFetch()},this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(o,i){var a=this.currentQuery,s=this.options,l=this.currentResult,u=this.currentResultState,c=this.currentResultOptions,f=o!==a,d=f?o.state:this.currentQueryInitialState,p=f?this.currentResult:this.previousQueryResult,v=o.state,y=v.dataUpdatedAt,_=v.error,m=v.errorUpdatedAt,h=v.isFetching,g=v.status,S=!1,k=!1,T;if(i.optimisticResults){var N=this.hasListeners(),M=!N&&Hv(o,i),G=N&&qv(o,a,i,s);(M||G)&&(h=!0,y||(g="loading"))}if(i.keepPreviousData&&!v.dataUpdateCount&&(p!=null&&p.isSuccess)&&g!=="error")T=p.data,y=p.dataUpdatedAt,g=p.status,S=!0;else if(i.select&&typeof v.data<"u")if(l&&v.data===(u==null?void 0:u.data)&&i.select===this.selectFn)T=this.selectResult;else try{this.selectFn=i.select,T=i.select(v.data),i.structuralSharing!==!1&&(T=fl(l==null?void 0:l.data,T)),this.selectResult=T,this.selectError=null}catch(ce){hl().error(ce),this.selectError=ce}else T=v.data;if(typeof i.placeholderData<"u"&&typeof T>"u"&&(g==="loading"||g==="idle")){var $;if(l!=null&&l.isPlaceholderData&&i.placeholderData===(c==null?void 0:c.placeholderData))$=l.data;else if($=typeof i.placeholderData=="function"?i.placeholderData():i.placeholderData,i.select&&typeof $<"u")try{$=i.select($),i.structuralSharing!==!1&&($=fl(l==null?void 0:l.data,$)),this.selectError=null}catch(ce){hl().error(ce),this.selectError=ce}typeof $<"u"&&(g="success",T=$,k=!0)}this.selectError&&(_=this.selectError,T=this.selectResult,m=Date.now(),g="error");var X={status:g,isLoading:g==="loading",isSuccess:g==="success",isError:g==="error",isIdle:g==="idle",data:T,dataUpdatedAt:y,error:_,errorUpdatedAt:m,failureCount:v.fetchFailureCount,errorUpdateCount:v.errorUpdateCount,isFetched:v.dataUpdateCount>0||v.errorUpdateCount>0,isFetchedAfterMount:v.dataUpdateCount>d.dataUpdateCount||v.errorUpdateCount>d.errorUpdateCount,isFetching:h,isRefetching:h&&g!=="loading",isLoadingError:g==="error"&&v.dataUpdatedAt===0,isPlaceholderData:k,isPreviousData:S,isRefetchError:g==="error"&&v.dataUpdatedAt!==0,isStale:qd(o,i),refetch:this.refetch,remove:this.remove};return X},n.shouldNotifyListeners=function(o,i){if(!i)return!0;var a=this.options,s=a.notifyOnChangeProps,l=a.notifyOnChangePropsExclusions;if(!s&&!l||s==="tracked"&&!this.trackedProps.length)return!0;var u=s==="tracked"?this.trackedProps:s;return Object.keys(o).some(function(c){var f=c,d=o[f]!==i[f],p=u==null?void 0:u.some(function(y){return y===c}),v=l==null?void 0:l.some(function(y){return y===c});return d&&!v&&(!u||p)})},n.updateResult=function(o){var i=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!rR(this.currentResult,i)){var a={cache:!0};(o==null?void 0:o.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,i)&&(a.listeners=!0),this.notify(de({},a,o))}},n.updateQuery=function(){var o=this.client.getQueryCache().build(this.client,this.options);if(o!==this.currentQuery){var i=this.currentQuery;this.currentQuery=o,this.currentQueryInitialState=o.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(i==null||i.removeObserver(this),o.addObserver(this))}},n.onQueryUpdate=function(o){var i={};o.type==="success"?i.onSuccess=!0:o.type==="error"&&!Es(o.error)&&(i.onError=!0),this.updateResult(i),this.hasListeners()&&this.updateTimers()},n.notify=function(o){var i=this;Ne.batch(function(){o.onSuccess?(i.options.onSuccess==null||i.options.onSuccess(i.currentResult.data),i.options.onSettled==null||i.options.onSettled(i.currentResult.data,null)):o.onError&&(i.options.onError==null||i.options.onError(i.currentResult.error),i.options.onSettled==null||i.options.onSettled(void 0,i.currentResult.error)),o.listeners&&i.listeners.forEach(function(a){a(i.currentResult)}),o.cache&&i.client.getQueryCache().notify({query:i.currentQuery,type:"observerResultsUpdated"})})},t}(ma);function wR(e,t){return t.enabled!==!1&&!e.state.dataUpdatedAt&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Hv(e,t){return wR(e,t)||e.state.dataUpdatedAt>0&&df(e,t,t.refetchOnMount)}function df(e,t,n){if(t.enabled!==!1){var r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&qd(e,t)}return!1}function qv(e,t,n,r){return n.enabled!==!1&&(e!==t||r.enabled===!1)&&(!n.suspense||e.state.status!=="error")&&qd(e,n)}function qd(e,t){return e.isStaleByTime(t.staleTime)}var SR=L0.unstable_batchedUpdates;Ne.setBatchNotifyFunction(SR);var _R=console;uR(_R);var Kv=V.createContext(void 0),G0=V.createContext(!1);function X0(e){return e&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Kv),window.ReactQueryClientContext):Kv}var bR=function(){var t=V.useContext(X0(V.useContext(G0)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},ER=function(t){var n=t.client,r=t.contextSharing,o=r===void 0?!1:r,i=t.children;V.useEffect(function(){return n.mount(),function(){n.unmount()}},[n]);var a=X0(o);return V.createElement(G0.Provider,{value:o},V.createElement(a.Provider,{value:n},i))};function CR(){var e=!1;return{clearReset:function(){e=!1},reset:function(){e=!0},isReset:function(){return e}}}var RR=V.createContext(CR()),OR=function(){return V.useContext(RR)};function xR(e,t,n){return typeof t=="function"?t.apply(void 0,n):typeof t=="boolean"?t:!!e}function kR(e,t){var n=V.useRef(!1),r=V.useState(0),o=r[1],i=bR(),a=OR(),s=i.defaultQueryObserverOptions(e);s.optimisticResults=!0,s.onError&&(s.onError=Ne.batchCalls(s.onError)),s.onSuccess&&(s.onSuccess=Ne.batchCalls(s.onSuccess)),s.onSettled&&(s.onSettled=Ne.batchCalls(s.onSettled)),s.suspense&&(typeof s.staleTime!="number"&&(s.staleTime=1e3),s.cacheTime===0&&(s.cacheTime=1)),(s.suspense||s.useErrorBoundary)&&(a.isReset()||(s.retryOnMount=!1));var l=V.useState(function(){return new t(i,s)}),u=l[0],c=u.getOptimisticResult(s);if(V.useEffect(function(){n.current=!0,a.clearReset();var f=u.subscribe(Ne.batchCalls(function(){n.current&&o(function(d){return d+1})}));return u.updateResult(),function(){n.current=!1,f()}},[a,u]),V.useEffect(function(){u.setOptions(s,{listeners:!1})},[s,u]),s.suspense&&c.isLoading)throw u.fetchOptimistic(s).then(function(f){var d=f.data;s.onSuccess==null||s.onSuccess(d),s.onSettled==null||s.onSettled(d,null)}).catch(function(f){a.clearReset(),s.onError==null||s.onError(f),s.onSettled==null||s.onSettled(void 0,f)});if(c.isError&&!a.isReset()&&!c.isFetching&&xR(s.suspense,s.useErrorBoundary,[c.error,u.getCurrentQuery()]))throw c.error;return s.notifyOnChangeProps==="tracked"&&(c=u.trackResult(c,s)),c}function Y0(e,t,n){var r=_s(e,t,n);return kR(r,yR)}/**
 * @remix-run/router v1.3.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ta(){return ta=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ta.apply(this,arguments)}var jn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(jn||(jn={}));const Qv="popstate";function PR(e){e===void 0&&(e={});function t(o,i){let{pathname:a="/",search:s="",hash:l=""}=$r(o.location.hash.substr(1));return hf("",{pathname:a,search:s,hash:l},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(o,i){let a=o.document.querySelector("base"),s="";if(a&&a.getAttribute("href")){let l=o.location.href,u=l.indexOf("#");s=u===-1?l:l.slice(0,u)}return s+"#"+(typeof i=="string"?i:na(i))}function r(o,i){TR(o.pathname.charAt(0)==="/","relative pathnames are not supported in hash history.push("+JSON.stringify(i)+")")}return NR(t,n,r,e)}function Ue(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function TR(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function LR(){return Math.random().toString(36).substr(2,8)}function Gv(e,t){return{usr:e.state,key:e.key,idx:t}}function hf(e,t,n,r){return n===void 0&&(n=null),ta({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?$r(t):t,{state:n,key:t&&t.key||r||LR()})}function na(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function $r(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function NR(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,a=o.history,s=jn.Pop,l=null,u=c();u==null&&(u=0,a.replaceState(ta({},a.state,{idx:u}),""));function c(){return(a.state||{idx:null}).idx}function f(){s=jn.Pop;let _=c(),m=_==null?null:_-u;u=_,l&&l({action:s,location:y.location,delta:m})}function d(_,m){s=jn.Push;let h=hf(y.location,_,m);n&&n(h,_),u=c()+1;let g=Gv(h,u),S=y.createHref(h);try{a.pushState(g,"",S)}catch{o.location.assign(S)}i&&l&&l({action:s,location:y.location,delta:1})}function p(_,m){s=jn.Replace;let h=hf(y.location,_,m);n&&n(h,_),u=c();let g=Gv(h,u),S=y.createHref(h);a.replaceState(g,"",S),i&&l&&l({action:s,location:y.location,delta:0})}function v(_){let m=o.location.origin!=="null"?o.location.origin:o.location.href,h=typeof _=="string"?_:na(_);return Ue(m,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,m)}let y={get action(){return s},get location(){return e(o,a)},listen(_){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(Qv,f),l=_,()=>{o.removeEventListener(Qv,f),l=null}},createHref(_){return t(o,_)},createURL:v,encodeLocation(_){let m=v(_);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:d,replace:p,go(_){return a.go(_)}};return y}var Xv;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Xv||(Xv={}));function AR(e,t,n){n===void 0&&(n="/");let r=typeof t=="string"?$r(t):t,o=e1(r.pathname||"/",n);if(o==null)return null;let i=J0(e);MR(i);let a=null;for(let s=0;a==null&&s<i.length;++s)a=VR(i[s],qR(o));return a}function J0(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,a,s)=>{let l={relativePath:s===void 0?i.path||"":s,caseSensitive:i.caseSensitive===!0,childrenIndex:a,route:i};l.relativePath.startsWith("/")&&(Ue(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=Yn([r,l.relativePath]),c=n.concat(l);i.children&&i.children.length>0&&(Ue(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),J0(i.children,t,c,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:BR(u,i.index),routesMeta:c})};return e.forEach((i,a)=>{var s;if(i.path===""||!((s=i.path)!=null&&s.includes("?")))o(i,a);else for(let l of Z0(i.path))o(i,a,l)}),t}function Z0(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let a=Z0(r.join("/")),s=[];return s.push(...a.map(l=>l===""?i:[i,l].join("/"))),o&&s.push(...a),s.map(l=>e.startsWith("/")&&l===""?"/":l)}function MR(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:zR(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const IR=/^:\w+$/,DR=3,$R=2,UR=1,FR=10,jR=-2,Yv=e=>e==="*";function BR(e,t){let n=e.split("/"),r=n.length;return n.some(Yv)&&(r+=jR),t&&(r+=$R),n.filter(o=>!Yv(o)).reduce((o,i)=>o+(IR.test(i)?DR:i===""?UR:FR),r)}function zR(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function VR(e,t){let{routesMeta:n}=e,r={},o="/",i=[];for(let a=0;a<n.length;++a){let s=n[a],l=a===n.length-1,u=o==="/"?t:t.slice(o.length)||"/",c=WR({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},u);if(!c)return null;Object.assign(r,c.params);let f=s.route;i.push({params:r,pathname:Yn([o,c.pathname]),pathnameBase:XR(Yn([o,c.pathnameBase])),route:f}),c.pathnameBase!=="/"&&(o=Yn([o,c.pathnameBase]))}return i}function WR(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=HR(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],a=i.replace(/(.)\/+$/,"$1"),s=o.slice(1);return{params:r.reduce((u,c,f)=>{if(c==="*"){let d=s[f]||"";a=i.slice(0,i.length-d.length).replace(/(.)\/+$/,"$1")}return u[c]=KR(s[f]||"",c),u},{}),pathname:i,pathnameBase:a,pattern:e}}function HR(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Kd(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^$?{}|()[\]]/g,"\\$&").replace(/\/:(\w+)/g,(a,s)=>(r.push(s),"/([^\\/]+)"));return e.endsWith("*")?(r.push("*"),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function qR(e){try{return decodeURI(e)}catch(t){return Kd(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function KR(e,t){try{return decodeURIComponent(e)}catch(n){return Kd(!1,'The value for the URL param "'+t+'" will not be decoded because'+(' the string "'+e+'" is a malformed URL segment. This is probably')+(" due to a bad percent encoding ("+n+").")),e}}function e1(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Kd(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function QR(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?$r(e):e;return{pathname:n?n.startsWith("/")?n:GR(n,t):t,search:YR(r),hash:JR(o)}}function GR(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function zu(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function t1(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function n1(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=$r(e):(o=ta({},e),Ue(!o.pathname||!o.pathname.includes("?"),zu("?","pathname","search",o)),Ue(!o.pathname||!o.pathname.includes("#"),zu("#","pathname","hash",o)),Ue(!o.search||!o.search.includes("#"),zu("#","search","hash",o)));let i=e===""||o.pathname==="",a=i?"/":o.pathname,s;if(r||a==null)s=n;else{let f=t.length-1;if(a.startsWith("..")){let d=a.split("/");for(;d[0]==="..";)d.shift(),f-=1;o.pathname=d.join("/")}s=f>=0?t[f]:"/"}let l=QR(o,s),u=a&&a!=="/"&&a.endsWith("/"),c=(i||a===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const Yn=e=>e.join("/").replace(/\/\/+/g,"/"),XR=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),YR=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,JR=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function ZR(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const r1=["post","put","patch","delete"];new Set(r1);const eO=["get",...r1];new Set(eO);/**
 * React Router v6.8.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pf(){return pf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},pf.apply(this,arguments)}function tO(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const nO=typeof Object.is=="function"?Object.is:tO,{useState:rO,useEffect:oO,useLayoutEffect:iO,useDebugValue:aO}=Tt;function sO(e,t,n){const r=t(),[{inst:o},i]=rO({inst:{value:r,getSnapshot:t}});return iO(()=>{o.value=r,o.getSnapshot=t,Vu(o)&&i({inst:o})},[e,r,t]),oO(()=>(Vu(o)&&i({inst:o}),e(()=>{Vu(o)&&i({inst:o})})),[e]),aO(r),r}function Vu(e){const t=e.getSnapshot,n=e.value;try{const r=t();return!nO(n,r)}catch{return!0}}function lO(e,t,n){return t()}const uO=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",cO=!uO,fO=cO?lO:sO;"useSyncExternalStore"in Tt&&(e=>e.useSyncExternalStore)(Tt);const o1=L.createContext(null),i1=L.createContext(null),zl=L.createContext(null),Vl=L.createContext(null),$o=L.createContext({outlet:null,matches:[]}),a1=L.createContext(null);function dO(e,t){let{relative:n}=t===void 0?{}:t;ya()||Ue(!1);let{basename:r,navigator:o}=L.useContext(zl),{hash:i,pathname:a,search:s}=s1(e,{relative:n}),l=a;return r!=="/"&&(l=a==="/"?r:Yn([r,a])),o.createHref({pathname:l,search:s,hash:i})}function ya(){return L.useContext(Vl)!=null}function wa(){return ya()||Ue(!1),L.useContext(Vl).location}function hO(){ya()||Ue(!1);let{basename:e,navigator:t}=L.useContext(zl),{matches:n}=L.useContext($o),{pathname:r}=wa(),o=JSON.stringify(t1(n).map(s=>s.pathnameBase)),i=L.useRef(!1);return L.useEffect(()=>{i.current=!0}),L.useCallback(function(s,l){if(l===void 0&&(l={}),!i.current)return;if(typeof s=="number"){t.go(s);return}let u=n1(s,JSON.parse(o),r,l.relative==="path");e!=="/"&&(u.pathname=u.pathname==="/"?e:Yn([e,u.pathname])),(l.replace?t.replace:t.push)(u,l.state,l)},[e,t,o,r])}function s1(e,t){let{relative:n}=t===void 0?{}:t,{matches:r}=L.useContext($o),{pathname:o}=wa(),i=JSON.stringify(t1(r).map(a=>a.pathnameBase));return L.useMemo(()=>n1(e,JSON.parse(i),o,n==="path"),[e,i,o,n])}function l1(e,t){ya()||Ue(!1);let{navigator:n}=L.useContext(zl),r=L.useContext(i1),{matches:o}=L.useContext($o),i=o[o.length-1],a=i?i.params:{};i&&i.pathname;let s=i?i.pathnameBase:"/";i&&i.route;let l=wa(),u;if(t){var c;let y=typeof t=="string"?$r(t):t;s==="/"||(c=y.pathname)!=null&&c.startsWith(s)||Ue(!1),u=y}else u=l;let f=u.pathname||"/",d=s==="/"?f:f.slice(s.length)||"/",p=AR(e,{pathname:d}),v=gO(p&&p.map(y=>Object.assign({},y,{params:Object.assign({},a,y.params),pathname:Yn([s,n.encodeLocation?n.encodeLocation(y.pathname).pathname:y.pathname]),pathnameBase:y.pathnameBase==="/"?s:Yn([s,n.encodeLocation?n.encodeLocation(y.pathnameBase).pathname:y.pathnameBase])})),o,r||void 0);return t&&v?L.createElement(Vl.Provider,{value:{location:pf({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:jn.Pop}},v):v}function pO(){let e=_O(),t=ZR(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return L.createElement(L.Fragment,null,L.createElement("h2",null,"Unexpected Application Error!"),L.createElement("h3",{style:{fontStyle:"italic"}},t),n?L.createElement("pre",{style:o},n):null,i)}class vO extends L.Component{constructor(t){super(t),this.state={location:t.location,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location?{error:t.error,location:t.location}:{error:t.error||n.error,location:n.location}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error?L.createElement($o.Provider,{value:this.props.routeContext},L.createElement(a1.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function mO(e){let{routeContext:t,match:n,children:r}=e,o=L.useContext(o1);return o&&o.static&&o.staticContext&&n.route.errorElement&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),L.createElement($o.Provider,{value:t},r)}function gO(e,t,n){if(t===void 0&&(t=[]),e==null)if(n!=null&&n.errors)e=n.matches;else return null;let r=e,o=n==null?void 0:n.errors;if(o!=null){let i=r.findIndex(a=>a.route.id&&(o==null?void 0:o[a.route.id]));i>=0||Ue(!1),r=r.slice(0,Math.min(r.length,i+1))}return r.reduceRight((i,a,s)=>{let l=a.route.id?o==null?void 0:o[a.route.id]:null,u=n?a.route.errorElement||L.createElement(pO,null):null,c=t.concat(r.slice(0,s+1)),f=()=>L.createElement(mO,{match:a,routeContext:{outlet:i,matches:c}},l?u:a.route.element!==void 0?a.route.element:i);return n&&(a.route.errorElement||s===0)?L.createElement(vO,{location:n.location,component:u,error:l,children:f(),routeContext:{outlet:null,matches:c}}):f()},null)}var Jv;(function(e){e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator"})(Jv||(Jv={}));var pl;(function(e){e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator"})(pl||(pl={}));function yO(e){let t=L.useContext(i1);return t||Ue(!1),t}function wO(e){let t=L.useContext($o);return t||Ue(!1),t}function SO(e){let t=wO(),n=t.matches[t.matches.length-1];return n.route.id||Ue(!1),n.route.id}function _O(){var e;let t=L.useContext(a1),n=yO(pl.UseRouteError),r=SO(pl.UseRouteError);return t||((e=n.errors)==null?void 0:e[r])}function vf(e){Ue(!1)}function bO(e){let{basename:t="/",children:n=null,location:r,navigationType:o=jn.Pop,navigator:i,static:a=!1}=e;ya()&&Ue(!1);let s=t.replace(/^\/*/,"/"),l=L.useMemo(()=>({basename:s,navigator:i,static:a}),[s,i,a]);typeof r=="string"&&(r=$r(r));let{pathname:u="/",search:c="",hash:f="",state:d=null,key:p="default"}=r,v=L.useMemo(()=>{let y=e1(u,s);return y==null?null:{pathname:y,search:c,hash:f,state:d,key:p}},[s,u,c,f,d,p]);return v==null?null:L.createElement(zl.Provider,{value:l},L.createElement(Vl.Provider,{children:n,value:{location:v,navigationType:o}}))}function EO(e){let{children:t,location:n}=e,r=L.useContext(o1),o=r&&!t?r.router.routes:mf(t);return l1(o,n)}var Zv;(function(e){e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error"})(Zv||(Zv={}));new Promise(()=>{});function mf(e,t){t===void 0&&(t=[]);let n=[];return L.Children.forEach(e,(r,o)=>{if(!L.isValidElement(r))return;if(r.type===L.Fragment){n.push.apply(n,mf(r.props.children,t));return}r.type!==vf&&Ue(!1),!r.props.index||!r.props.children||Ue(!1);let i=[...t,o],a={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,hasErrorBoundary:r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle};r.props.children&&(a.children=mf(r.props.children,i)),n.push(a)}),n}/**
 * React Router DOM v6.8.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function gf(){return gf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},gf.apply(this,arguments)}function CO(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function RO(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function OO(e,t){return e.button===0&&(!t||t==="_self")&&!RO(e)}const xO=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset"];function kO(e){let{basename:t,children:n,window:r}=e,o=L.useRef();o.current==null&&(o.current=PR({window:r,v5Compat:!0}));let i=o.current,[a,s]=L.useState({action:i.action,location:i.location});return L.useLayoutEffect(()=>i.listen(s),[i]),L.createElement(bO,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:i})}const PO=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",u1=L.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:i,replace:a,state:s,target:l,to:u,preventScrollReset:c}=t,f=CO(t,xO),d=typeof u=="string"?u:na(u),p=/^[a-z+]+:\/\//i.test(d)||d.startsWith("//"),v=d,y=!1;if(PO&&p){let g=new URL(window.location.href),S=d.startsWith("//")?new URL(g.protocol+d):new URL(d);S.origin===g.origin?v=S.pathname+S.search+S.hash:y=!0}let _=dO(v,{relative:o}),m=TO(v,{replace:a,state:s,target:l,preventScrollReset:c,relative:o});function h(g){r&&r(g),g.defaultPrevented||m(g)}return L.createElement("a",gf({},f,{href:p?d:_,onClick:y||i?r:h,ref:n,target:l}))});var em;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmitImpl="useSubmitImpl",e.UseFetcher="useFetcher"})(em||(em={}));var tm;(function(e){e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(tm||(tm={}));function TO(e,t){let{target:n,replace:r,state:o,preventScrollReset:i,relative:a}=t===void 0?{}:t,s=hO(),l=wa(),u=s1(e,{relative:a});return L.useCallback(c=>{if(OO(c,n)){c.preventDefault();let f=r!==void 0?r:na(l)===na(u);s(e,{replace:f,state:o,preventScrollReset:i,relative:a})}},[l,s,u,r,o,n,e,i,a])}function LO(e){const t=new Error(e);if(t.stack===void 0)try{throw t}catch{}return t}var NO=LO,se=NO;function AO(e){return!!e&&typeof e.then=="function"}var Re=AO;function MO(e,t){if(e!=null)return e;throw se(t??"Got unexpected null or undefined")}var Oe=MO;function ie(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Wl{getValue(){throw se("BaseLoadable")}toPromise(){throw se("BaseLoadable")}valueMaybe(){throw se("BaseLoadable")}valueOrThrow(){throw se(`Loadable expected value, but in "${this.state}" state`)}promiseMaybe(){throw se("BaseLoadable")}promiseOrThrow(){throw se(`Loadable expected promise, but in "${this.state}" state`)}errorMaybe(){throw se("BaseLoadable")}errorOrThrow(){throw se(`Loadable expected error, but in "${this.state}" state`)}is(t){return t.state===this.state&&t.contents===this.contents}map(t){throw se("BaseLoadable")}}class IO extends Wl{constructor(t){super(),ie(this,"state","hasValue"),ie(this,"contents",void 0),this.contents=t}getValue(){return this.contents}toPromise(){return Promise.resolve(this.contents)}valueMaybe(){return this.contents}valueOrThrow(){return this.contents}promiseMaybe(){}errorMaybe(){}map(t){try{const n=t(this.contents);return Re(n)?Lr(n):Ro(n)?n:Sa(n)}catch(n){return Re(n)?Lr(n.next(()=>this.map(t))):Hl(n)}}}class DO extends Wl{constructor(t){super(),ie(this,"state","hasError"),ie(this,"contents",void 0),this.contents=t}getValue(){throw this.contents}toPromise(){return Promise.reject(this.contents)}valueMaybe(){}promiseMaybe(){}errorMaybe(){return this.contents}errorOrThrow(){return this.contents}map(t){return this}}class c1 extends Wl{constructor(t){super(),ie(this,"state","loading"),ie(this,"contents",void 0),this.contents=t}getValue(){throw this.contents}toPromise(){return this.contents}valueMaybe(){}promiseMaybe(){return this.contents}promiseOrThrow(){return this.contents}errorMaybe(){}map(t){return Lr(this.contents.then(n=>{const r=t(n);if(Ro(r)){const o=r;switch(o.state){case"hasValue":return o.contents;case"hasError":throw o.contents;case"loading":return o.contents}}return r}).catch(n=>{if(Re(n))return n.then(()=>this.map(t).contents);throw n}))}}function Sa(e){return Object.freeze(new IO(e))}function Hl(e){return Object.freeze(new DO(e))}function Lr(e){return Object.freeze(new c1(e))}function f1(){return Object.freeze(new c1(new Promise(()=>{})))}function $O(e){return e.every(t=>t.state==="hasValue")?Sa(e.map(t=>t.contents)):e.some(t=>t.state==="hasError")?Hl(Oe(e.find(t=>t.state==="hasError"),"Invalid loadable passed to loadableAll").contents):Lr(Promise.all(e.map(t=>t.contents)))}function d1(e){const n=(Array.isArray(e)?e:Object.getOwnPropertyNames(e).map(o=>e[o])).map(o=>Ro(o)?o:Re(o)?Lr(o):Sa(o)),r=$O(n);return Array.isArray(e)?r:r.map(o=>Object.getOwnPropertyNames(e).reduce((i,a,s)=>({...i,[a]:o[s]}),{}))}function Ro(e){return e instanceof Wl}const UO={of:e=>Re(e)?Lr(e):Ro(e)?e:Sa(e),error:e=>Hl(e),loading:()=>f1(),all:d1,isLoadable:Ro};var Ur={loadableWithValue:Sa,loadableWithError:Hl,loadableWithPromise:Lr,loadableLoading:f1,loadableAll:d1,isLoadable:Ro,RecoilLoadable:UO},FO=Ur.loadableWithValue,jO=Ur.loadableWithError,BO=Ur.loadableWithPromise,zO=Ur.loadableLoading,VO=Ur.loadableAll,WO=Ur.isLoadable,HO=Ur.RecoilLoadable,_a=Object.freeze({__proto__:null,loadableWithValue:FO,loadableWithError:jO,loadableWithPromise:BO,loadableLoading:zO,loadableAll:VO,isLoadable:WO,RecoilLoadable:HO});const ql=new Map().set("recoil_hamt_2020",!0).set("recoil_sync_external_store",!0).set("recoil_suppress_rerender_in_callback",!0).set("recoil_memory_managament_2020",!0);function Kl(e){var t;return(t=ql.get(e))!==null&&t!==void 0?t:!1}Kl.setPass=e=>{ql.set(e,!0)};Kl.setFail=e=>{ql.set(e,!1)};Kl.clear=()=>{ql.clear()};var ge=Kl;function qO(e,t,{error:n}={}){return null}var KO=qO,Qd=KO,Wu,Hu,qu;const QO=(Wu=V.createMutableSource)!==null&&Wu!==void 0?Wu:V.unstable_createMutableSource,h1=(Hu=V.useMutableSource)!==null&&Hu!==void 0?Hu:V.unstable_useMutableSource,p1=(qu=V.useSyncExternalStore)!==null&&qu!==void 0?qu:V.unstable_useSyncExternalStore;function GO(){var e;const{ReactCurrentDispatcher:t,ReactCurrentOwner:n}=V.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;return((e=t==null?void 0:t.current)!==null&&e!==void 0?e:n.currentDispatcher).useSyncExternalStore!=null}function XO(){return ge("recoil_transition_support")?{mode:"TRANSITION_SUPPORT",early:!0,concurrent:!0}:ge("recoil_sync_external_store")&&p1!=null?{mode:"SYNC_EXTERNAL_STORE",early:!0,concurrent:!1}:ge("recoil_mutable_source")&&h1!=null&&typeof window<"u"&&!window.$disableRecoilValueMutableSource_TEMP_HACK_DO_NOT_USE?ge("recoil_suppress_rerender_in_callback")?{mode:"MUTABLE_SOURCE",early:!0,concurrent:!0}:{mode:"MUTABLE_SOURCE",early:!1,concurrent:!1}:ge("recoil_suppress_rerender_in_callback")?{mode:"LEGACY",early:!0,concurrent:!1}:{mode:"LEGACY",early:!1,concurrent:!1}}function YO(){return!1}var ba={createMutableSource:QO,useMutableSource:h1,useSyncExternalStore:p1,currentRendererSupportsUseSyncExternalStore:GO,reactMode:XO,isFastRefreshEnabled:YO};const v1={RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED:!0};function JO(){var e,t,n;if(typeof process>"u"||((e=process)===null||e===void 0?void 0:e.env)==null)return;const r=(t={}.RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED)===null||t===void 0||(n=t.toLowerCase())===null||n===void 0?void 0:n.trim();if(r==null||r==="")return;if(!["true","false"].includes(r))throw se(`({}).RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED value must be 'true', 'false', or empty: ${r}`);v1.RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED=r==="true"}JO();var m1=v1;class Gd{constructor(t){ie(this,"key",void 0),this.key=t}toJSON(){return{key:this.key}}}class g1 extends Gd{}class y1 extends Gd{}function ZO(e){return e instanceof g1||e instanceof y1}var Ql={AbstractRecoilValue:Gd,RecoilState:g1,RecoilValueReadOnly:y1,isRecoilValue:ZO},ex=Ql.AbstractRecoilValue,tx=Ql.RecoilState,nx=Ql.RecoilValueReadOnly,rx=Ql.isRecoilValue,Oo=Object.freeze({__proto__:null,AbstractRecoilValue:ex,RecoilState:tx,RecoilValueReadOnly:nx,isRecoilValue:rx});function ox(e,t){return function*(){let n=0;for(const r of e)yield t(r,n++)}()}var Gl=ox;class w1{}const ix=new w1,Nr=new Map,Xd=new Map;function ax(e){return Gl(e,t=>Oe(Xd.get(t)))}function sx(e){if(Nr.has(e)){const t=`Duplicate atom key "${e}". This is a FATAL ERROR in
      production. But it is safe to ignore this warning if it occurred because of
      hot module replacement.`;console.warn(t)}}function lx(e){m1.RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED&&sx(e.key),Nr.set(e.key,e);const t=e.set==null?new Oo.RecoilValueReadOnly(e.key):new Oo.RecoilState(e.key);return Xd.set(e.key,t),t}class S1 extends Error{}function ux(e){const t=Nr.get(e);if(t==null)throw new S1(`Missing definition for RecoilValue: "${e}""`);return t}function cx(e){return Nr.get(e)}const vl=new Map;function fx(e){var t;if(!ge("recoil_memory_managament_2020"))return;const n=Nr.get(e);if(n!=null&&(t=n.shouldDeleteConfigOnRelease)!==null&&t!==void 0&&t.call(n)){var r;Nr.delete(e),(r=_1(e))===null||r===void 0||r(),vl.delete(e)}}function dx(e,t){ge("recoil_memory_managament_2020")&&(t===void 0?vl.delete(e):vl.set(e,t))}function _1(e){return vl.get(e)}var ft={nodes:Nr,recoilValues:Xd,registerNode:lx,getNode:ux,getNodeMaybe:cx,deleteNodeConfigIfPossible:fx,setConfigDeletionHandler:dx,getConfigDeletionHandler:_1,recoilValuesForKeys:ax,NodeMissingError:S1,DefaultValue:w1,DEFAULT_VALUE:ix};function hx(e,t){t()}var px={enqueueExecution:hx};function vx(e,t){return t={exports:{}},e(t,t.exports),t.exports}var mx=vx(function(e){var t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(x){return typeof x}:function(x){return x&&typeof Symbol=="function"&&x.constructor===Symbol&&x!==Symbol.prototype?"symbol":typeof x},n={},r=5,o=Math.pow(2,r),i=o-1,a=o/2,s=o/4,l={},u=function(E){return function(){return E}},c=n.hash=function(x){var E=typeof x>"u"?"undefined":t(x);if(E==="number")return x;E!=="string"&&(x+="");for(var F=0,H=0,q=x.length;H<q;++H){var Q=x.charCodeAt(H);F=(F<<5)-F+Q|0}return F},f=function(E){return E-=E>>1&1431655765,E=(E&858993459)+(E>>2&858993459),E=E+(E>>4)&252645135,E+=E>>8,E+=E>>16,E&127},d=function(E,F){return F>>>E&i},p=function(E){return 1<<E},v=function(E,F){return f(E&F-1)},y=function(E,F,H,q){var Q=q;if(!E){var ne=q.length;Q=new Array(ne);for(var ee=0;ee<ne;++ee)Q[ee]=q[ee]}return Q[F]=H,Q},_=function(E,F,H){var q=H.length-1,Q=0,ne=0,ee=H;if(E)Q=ne=F;else for(ee=new Array(q);Q<F;)ee[ne++]=H[Q++];for(++Q;Q<=q;)ee[ne++]=H[Q++];return E&&(ee.length=q),ee},m=function(E,F,H,q){var Q=q.length;if(E){for(var ne=Q;ne>=F;)q[ne--]=q[ne];return q[F]=H,q}for(var ee=0,te=0,ue=new Array(Q+1);ee<F;)ue[te++]=q[ee++];for(ue[F]=H;ee<Q;)ue[++te]=q[ee++];return ue},h=1,g=2,S=3,k=4,T={__hamt_isEmpty:!0},N=function(E){return E===T||E&&E.__hamt_isEmpty},M=function(E,F,H,q){return{type:h,edit:E,hash:F,key:H,value:q,_modify:O}},G=function(E,F,H){return{type:g,edit:E,hash:F,children:H,_modify:A}},$=function(E,F,H){return{type:S,edit:E,mask:F,children:H,_modify:D}},X=function(E,F,H){return{type:k,edit:E,size:F,children:H,_modify:z}},ce=function(E){return E===T||E.type===h||E.type===g},re=function(E,F,H,q,Q){for(var ne=[],ee=q,te=0,ue=0;ee;++ue)ee&1&&(ne[ue]=Q[te++]),ee>>>=1;return ne[F]=H,X(E,te+1,ne)},w=function(E,F,H,q){for(var Q=new Array(F-1),ne=0,ee=0,te=0,ue=q.length;te<ue;++te)if(te!==H){var Ee=q[te];Ee&&!N(Ee)&&(Q[ne++]=Ee,ee|=1<<te)}return $(E,ee,Q)},P=function x(E,F,H,q,Q,ne){if(H===Q)return G(E,H,[ne,q]);var ee=d(F,H),te=d(F,Q);return $(E,p(ee)|p(te),ee===te?[x(E,F+r,H,q,Q,ne)]:ee<te?[q,ne]:[ne,q])},I=function(E,F,H,q,Q,ne,ee,te){for(var ue=Q.length,Ee=0;Ee<ue;++Ee){var ot=Q[Ee];if(H(ee,ot.key)){var Fe=ot.value,bt=ne(Fe);return bt===Fe?Q:bt===l?(--te.value,_(E,Ee,Q)):y(E,Ee,M(F,q,ee,bt),Q)}}var It=ne();return It===l?Q:(++te.value,y(E,ue,M(F,q,ee,It),Q))},C=function(E,F){return E===F.edit},O=function(E,F,H,q,Q,ne,ee){if(F(ne,this.key)){var te=q(this.value);return te===this.value?this:te===l?(--ee.value,T):C(E,this)?(this.value=te,this):M(E,Q,ne,te)}var ue=q();return ue===l?this:(++ee.value,P(E,H,this.hash,this,Q,M(E,Q,ne,ue)))},A=function(E,F,H,q,Q,ne,ee){if(Q===this.hash){var te=C(E,this),ue=I(te,E,F,this.hash,this.children,q,ne,ee);return ue===this.children?this:ue.length>1?G(E,this.hash,ue):ue[0]}var Ee=q();return Ee===l?this:(++ee.value,P(E,H,this.hash,this,Q,M(E,Q,ne,Ee)))},D=function(E,F,H,q,Q,ne,ee){var te=this.mask,ue=this.children,Ee=d(H,Q),ot=p(Ee),Fe=v(te,ot),bt=te&ot,It=bt?ue[Fe]:T,zr=It._modify(E,F,H+r,q,Q,ne,ee);if(It===zr)return this;var ka=C(E,this),zo=te,Vo=void 0;if(bt&&N(zr)){if(zo&=~ot,!zo)return T;if(ue.length<=2&&ce(ue[Fe^1]))return ue[Fe^1];Vo=_(ka,Fe,ue)}else if(!bt&&!N(zr)){if(ue.length>=a)return re(E,Ee,zr,te,ue);zo|=ot,Vo=m(ka,Fe,zr,ue)}else Vo=y(ka,Fe,zr,ue);return ka?(this.mask=zo,this.children=Vo,this):$(E,zo,Vo)},z=function(E,F,H,q,Q,ne,ee){var te=this.size,ue=this.children,Ee=d(H,Q),ot=ue[Ee],Fe=(ot||T)._modify(E,F,H+r,q,Q,ne,ee);if(ot===Fe)return this;var bt=C(E,this),It=void 0;if(N(ot)&&!N(Fe))++te,It=y(bt,Ee,Fe,ue);else if(!N(ot)&&N(Fe)){if(--te,te<=s)return w(E,te,Ee,ue);It=y(bt,Ee,T,ue)}else It=y(bt,Ee,Fe,ue);return bt?(this.size=te,this.children=It,this):X(E,te,It)};T._modify=function(x,E,F,H,q,Q,ne){var ee=H();return ee===l?T:(++ne.value,M(x,q,Q,ee))};function b(x,E,F,H,q){this._editable=x,this._edit=E,this._config=F,this._root=H,this._size=q}b.prototype.setTree=function(x,E){return this._editable?(this._root=x,this._size=E,this):x===this._root?this:new b(this._editable,this._edit,this._config,x,E)};var U=n.tryGetHash=function(x,E,F,H){for(var q=H._root,Q=0,ne=H._config.keyEq;;)switch(q.type){case h:return ne(F,q.key)?q.value:x;case g:{if(E===q.hash)for(var ee=q.children,te=0,ue=ee.length;te<ue;++te){var Ee=ee[te];if(ne(F,Ee.key))return Ee.value}return x}case S:{var ot=d(Q,E),Fe=p(ot);if(q.mask&Fe){q=q.children[v(q.mask,Fe)],Q+=r;break}return x}case k:{if(q=q.children[d(Q,E)],q){Q+=r;break}return x}default:return x}};b.prototype.tryGetHash=function(x,E,F){return U(x,E,F,this)};var B=n.tryGet=function(x,E,F){return U(x,F._config.hash(E),E,F)};b.prototype.tryGet=function(x,E){return B(x,E,this)};var J=n.getHash=function(x,E,F){return U(void 0,x,E,F)};b.prototype.getHash=function(x,E){return J(x,E,this)},n.get=function(x,E){return U(void 0,E._config.hash(x),x,E)},b.prototype.get=function(x,E){return B(E,x,this)};var W=n.has=function(x,E,F){return U(l,x,E,F)!==l};b.prototype.hasHash=function(x,E){return W(x,E,this)};var Z=n.has=function(x,E){return W(E._config.hash(x),x,E)};b.prototype.has=function(x){return Z(x,this)};var ae=function(E,F){return E===F};n.make=function(x){return new b(0,0,{keyEq:x&&x.keyEq||ae,hash:x&&x.hash||c},T,0)},n.empty=n.make();var Y=n.isEmpty=function(x){return x&&!!N(x._root)};b.prototype.isEmpty=function(){return Y(this)};var me=n.modifyHash=function(x,E,F,H){var q={value:H._size},Q=H._root._modify(H._editable?H._edit:NaN,H._config.keyEq,0,x,E,F,q);return H.setTree(Q,q.value)};b.prototype.modifyHash=function(x,E,F){return me(F,x,E,this)};var ye=n.modify=function(x,E,F){return me(x,F._config.hash(E),E,F)};b.prototype.modify=function(x,E){return ye(E,x,this)};var pe=n.setHash=function(x,E,F,H){return me(u(F),x,E,H)};b.prototype.setHash=function(x,E,F){return pe(x,E,F,this)};var we=n.set=function(x,E,F){return pe(F._config.hash(x),x,E,F)};b.prototype.set=function(x,E){return we(x,E,this)};var rt=u(l),lr=n.removeHash=function(x,E,F){return me(rt,x,E,F)};b.prototype.removeHash=b.prototype.deleteHash=function(x,E){return lr(x,E,this)};var _t=n.remove=function(x,E){return lr(E._config.hash(x),x,E)};b.prototype.remove=b.prototype.delete=function(x){return _t(x,this)};var dt=n.beginMutation=function(x){return new b(x._editable+1,x._edit+1,x._config,x._root,x._size)};b.prototype.beginMutation=function(){return dt(this)};var $h=n.endMutation=function(x){return x._editable=x._editable&&x._editable-1,x};b.prototype.endMutation=function(){return $h(this)};var LS=n.mutate=function(x,E){var F=dt(E);return x(F),$h(F)};b.prototype.mutate=function(x){return LS(x,this)};var fu=function(E){return E&&Uh(E[0],E[1],E[2],E[3],E[4])},Uh=function(E,F,H,q,Q){for(;H<E;){var ne=F[H++];if(ne&&!N(ne))return Fh(ne,q,[E,F,H,q,Q])}return fu(Q)},Fh=function(E,F,H){switch(E.type){case h:return{value:F(E),rest:H};case g:case k:case S:var q=E.children;return Uh(q.length,q,0,F,H);default:return fu(H)}},NS={done:!0};function du(x){this.v=x}du.prototype.next=function(){if(!this.v)return NS;var x=this.v;return this.v=fu(x.rest),x},du.prototype[Symbol.iterator]=function(){return this};var hu=function(E,F){return new du(Fh(E._root,F))},AS=function(E){return[E.key,E.value]},MS=n.entries=function(x){return hu(x,AS)};b.prototype.entries=b.prototype[Symbol.iterator]=function(){return MS(this)};var IS=function(E){return E.key},DS=n.keys=function(x){return hu(x,IS)};b.prototype.keys=function(){return DS(this)};var $S=function(E){return E.value},US=n.values=b.prototype.values=function(x){return hu(x,$S)};b.prototype.values=function(){return US(this)};var jh=n.fold=function(x,E,F){var H=F._root;if(H.type===h)return x(E,H.value,H.key);for(var q=[H.children],Q=void 0;Q=q.pop();)for(var ne=0,ee=Q.length;ne<ee;){var te=Q[ne++];te&&te.type&&(te.type===h?E=x(E,te.value,te.key):q.push(te.children))}return E};b.prototype.fold=function(x,E){return jh(x,E,this)};var FS=n.forEach=function(x,E){return jh(function(F,H,q){return x(H,q,E)},null,E)};b.prototype.forEach=function(x){return FS(x,this)};var jS=n.count=function(x){return x._size};b.prototype.count=function(){return jS(this)},Object.defineProperty(b.prototype,"size",{get:b.prototype.count}),e.exports?e.exports=n:(void 0).hamt=n});class gx{constructor(t){ie(this,"_map",void 0),this._map=new Map(t==null?void 0:t.entries())}keys(){return this._map.keys()}entries(){return this._map.entries()}get(t){return this._map.get(t)}has(t){return this._map.has(t)}set(t,n){return this._map.set(t,n),this}delete(t){return this._map.delete(t),this}clone(){return Jd(this)}toMap(){return new Map(this._map)}}class Yd{constructor(t){if(ie(this,"_hamt",mx.empty.beginMutation()),t instanceof Yd){const n=t._hamt.endMutation();t._hamt=n.beginMutation(),this._hamt=n.beginMutation()}else if(t)for(const[n,r]of t.entries())this._hamt.set(n,r)}keys(){return this._hamt.keys()}entries(){return this._hamt.entries()}get(t){return this._hamt.get(t)}has(t){return this._hamt.has(t)}set(t,n){return this._hamt.set(t,n),this}delete(t){return this._hamt.delete(t),this}clone(){return Jd(this)}toMap(){return new Map(this._hamt)}}function Jd(e){return ge("recoil_hamt_2020")?new Yd(e):new gx(e)}var yx={persistentMap:Jd},wx=yx.persistentMap,Sx=Object.freeze({__proto__:null,persistentMap:wx});function _x(e,...t){const n=new Set;e:for(const r of e){for(const o of t)if(o.has(r))continue e;n.add(r)}return n}var Oi=_x;function bx(e,t){const n=new Map;return e.forEach((r,o)=>{n.set(o,t(r,o))}),n}var ml=bx;function Ex(){return{nodeDeps:new Map,nodeToNodeSubscriptions:new Map}}function Cx(e){return{nodeDeps:ml(e.nodeDeps,t=>new Set(t)),nodeToNodeSubscriptions:ml(e.nodeToNodeSubscriptions,t=>new Set(t))}}function Ku(e,t,n,r){const{nodeDeps:o,nodeToNodeSubscriptions:i}=n,a=o.get(e);if(a&&r&&a!==r.nodeDeps.get(e))return;o.set(e,t);const s=a==null?t:Oi(t,a);for(const l of s)i.has(l)||i.set(l,new Set),Oe(i.get(l)).add(e);if(a){const l=Oi(a,t);for(const u of l){if(!i.has(u))return;const c=Oe(i.get(u));c.delete(e),c.size===0&&i.delete(u)}}}function Rx(e,t,n,r){var o,i,a,s;const l=n.getState();r===l.currentTree.version||r===((o=l.nextTree)===null||o===void 0?void 0:o.version)||((i=l.previousTree)===null||i===void 0||i.version);const u=n.getGraph(r);if(Ku(e,t,u),r===((a=l.previousTree)===null||a===void 0?void 0:a.version)){const f=n.getGraph(l.currentTree.version);Ku(e,t,f,u)}if(r===((s=l.previousTree)===null||s===void 0?void 0:s.version)||r===l.currentTree.version){var c;const f=(c=l.nextTree)===null||c===void 0?void 0:c.version;if(f!==void 0){const d=n.getGraph(f);Ku(e,t,d,u)}}}var Ea={cloneGraph:Cx,graph:Ex,saveDepsToStore:Rx};let Ox=0;const xx=()=>Ox++;let kx=0;const Px=()=>kx++;let Tx=0;const Lx=()=>Tx++;var Xl={getNextTreeStateVersion:xx,getNextStoreID:Px,getNextComponentID:Lx};const{persistentMap:nm}=Sx,{graph:Nx}=Ea,{getNextTreeStateVersion:b1}=Xl;function E1(){const e=b1();return{version:e,stateID:e,transactionMetadata:{},dirtyAtoms:new Set,atomValues:nm(),nonvalidatedAtoms:nm()}}function Ax(){const e=E1();return{currentTree:e,nextTree:null,previousTree:null,commitDepth:0,knownAtoms:new Set,knownSelectors:new Set,transactionSubscriptions:new Map,nodeTransactionSubscriptions:new Map,nodeToComponentSubscriptions:new Map,queuedComponentCallbacks_DEPRECATED:[],suspendedComponentResolvers:new Set,graphsByVersion:new Map().set(e.version,Nx()),retention:{referenceCounts:new Map,nodesRetainedByZone:new Map,retainablesToCheckForRelease:new Set},nodeCleanupFunctions:new Map}}var C1={makeEmptyTreeState:E1,makeEmptyStoreState:Ax,getNextTreeStateVersion:b1};class R1{}function Mx(){return new R1}var Yl={RetentionZone:R1,retentionZone:Mx};function Ix(e,t){const n=new Set(e);return n.add(t),n}function Dx(e,t){const n=new Set(e);return n.delete(t),n}function $x(e,t,n){const r=new Map(e);return r.set(t,n),r}function Ux(e,t,n){const r=new Map(e);return r.set(t,n(r.get(t))),r}function Fx(e,t){const n=new Map(e);return n.delete(t),n}function jx(e,t){const n=new Map(e);return t.forEach(r=>n.delete(r)),n}var O1={setByAddingToSet:Ix,setByDeletingFromSet:Dx,mapBySettingInMap:$x,mapByUpdatingInMap:Ux,mapByDeletingFromMap:Fx,mapByDeletingMultipleFromMap:jx};function*Bx(e,t){let n=0;for(const r of e)t(r,n++)&&(yield r)}var Zd=Bx;function zx(e,t){return new Proxy(e,{get:(r,o)=>(!(o in r)&&o in t&&(r[o]=t[o]()),r[o]),ownKeys:r=>Object.keys(r)})}var x1=zx;const{getNode:Ca,getNodeMaybe:Vx,recoilValuesForKeys:rm}=ft,{RetentionZone:om}=Yl,{setByAddingToSet:Wx}=O1,Hx=Object.freeze(new Set);class qx extends Error{}function Kx(e,t,n){if(!ge("recoil_memory_managament_2020"))return()=>{};const{nodesRetainedByZone:r}=e.getState().retention;function o(i){let a=r.get(i);a||r.set(i,a=new Set),a.add(t)}if(n instanceof om)o(n);else if(Array.isArray(n))for(const i of n)o(i);return()=>{if(!ge("recoil_memory_managament_2020"))return;const{retention:i}=e.getState();function a(s){const l=i.nodesRetainedByZone.get(s);l==null||l.delete(t),l&&l.size===0&&i.nodesRetainedByZone.delete(s)}if(n instanceof om)a(n);else if(Array.isArray(n))for(const s of n)a(s)}}function eh(e,t,n,r){const o=e.getState();if(o.nodeCleanupFunctions.has(n))return;const i=Ca(n),a=Kx(e,n,i.retainedBy),s=i.init(e,t,r);o.nodeCleanupFunctions.set(n,()=>{s(),a()})}function Qx(e,t,n){eh(e,e.getState().currentTree,t,n)}function Gx(e,t){var n;const r=e.getState();(n=r.nodeCleanupFunctions.get(t))===null||n===void 0||n(),r.nodeCleanupFunctions.delete(t)}function Xx(e,t,n){return eh(e,t,n,"get"),Ca(n).get(e,t)}function k1(e,t,n){return Ca(n).peek(e,t)}function Yx(e,t,n){var r;const o=Vx(t);return o==null||(r=o.invalidate)===null||r===void 0||r.call(o,e),{...e,atomValues:e.atomValues.clone().delete(t),nonvalidatedAtoms:e.nonvalidatedAtoms.clone().set(t,n),dirtyAtoms:Wx(e.dirtyAtoms,t)}}function Jx(e,t,n,r){const o=Ca(n);if(o.set==null)throw new qx(`Attempt to set read-only RecoilValue: ${n}`);const i=o.set;return eh(e,t,n,"set"),i(e,t,r)}function Zx(e,t,n){const r=e.getState(),o=e.getGraph(t.version),i=Ca(n).nodeType;return x1({type:i},{loadable:()=>k1(e,t,n),isActive:()=>r.knownAtoms.has(n)||r.knownSelectors.has(n),isSet:()=>i==="selector"?!1:t.atomValues.has(n),isModified:()=>t.dirtyAtoms.has(n),deps:()=>{var a;return rm((a=o.nodeDeps.get(n))!==null&&a!==void 0?a:[])},subscribers:()=>{var a,s;return{nodes:rm(Zd(P1(e,t,new Set([n])),l=>l!==n)),components:Gl((a=(s=r.nodeToComponentSubscriptions.get(n))===null||s===void 0?void 0:s.values())!==null&&a!==void 0?a:[],([l])=>({name:l}))}}})}function P1(e,t,n){const r=new Set,o=Array.from(n),i=e.getGraph(t.version);for(let s=o.pop();s;s=o.pop()){var a;r.add(s);const l=(a=i.nodeToNodeSubscriptions.get(s))!==null&&a!==void 0?a:Hx;for(const u of l)r.has(u)||o.push(u)}return r}var ir={getNodeLoadable:Xx,peekNodeLoadable:k1,setNodeValue:Jx,initializeNode:Qx,cleanUpNode:Gx,setUnvalidatedAtomValue_DEPRECATED:Yx,peekNodeInfo:Zx,getDownstreamNodes:P1};let T1=null;function ek(e){T1=e}function tk(){var e;(e=T1)===null||e===void 0||e()}var L1={setInvalidateMemoizedSnapshot:ek,invalidateMemoizedSnapshot:tk};const{getDownstreamNodes:nk,getNodeLoadable:N1,setNodeValue:rk}=ir,{getNextComponentID:ok}=Xl,{getNode:ik,getNodeMaybe:A1}=ft,{DefaultValue:th}=ft,{reactMode:ak}=ba,{AbstractRecoilValue:sk,RecoilState:lk,RecoilValueReadOnly:uk,isRecoilValue:ck}=Oo,{invalidateMemoizedSnapshot:fk}=L1;function dk(e,{key:t},n=e.getState().currentTree){var r,o;const i=e.getState();n.version===i.currentTree.version||n.version===((r=i.nextTree)===null||r===void 0?void 0:r.version)||(n.version,(o=i.previousTree)===null||o===void 0||o.version);const a=N1(e,n,t);return a.state==="loading"&&a.contents.catch(()=>{}),a}function hk(e,t){const n=e.clone();return t.forEach((r,o)=>{r.state==="hasValue"&&r.contents instanceof th?n.delete(o):n.set(o,r)}),n}function pk(e,t,{key:n},r){if(typeof r=="function"){const o=N1(e,t,n);if(o.state==="loading"){const i=`Tried to set atom or selector "${n}" using an updater function while the current state is pending, this is not currently supported.`;throw se(i)}else if(o.state==="hasError")throw o.contents;return r(o.contents)}else return r}function vk(e,t,n){if(n.type==="set"){const{recoilValue:o,valueOrUpdater:i}=n,a=pk(e,t,o,i),s=rk(e,t,o.key,a);for(const[l,u]of s.entries())yf(t,l,u)}else if(n.type==="setLoadable"){const{recoilValue:{key:o},loadable:i}=n;yf(t,o,i)}else if(n.type==="markModified"){const{recoilValue:{key:o}}=n;t.dirtyAtoms.add(o)}else if(n.type==="setUnvalidated"){var r;const{recoilValue:{key:o},unvalidatedValue:i}=n,a=A1(o);a==null||(r=a.invalidate)===null||r===void 0||r.call(a,t),t.atomValues.delete(o),t.nonvalidatedAtoms.set(o,i),t.dirtyAtoms.add(o)}else Qd(`Unknown action ${n.type}`)}function yf(e,t,n){n.state==="hasValue"&&n.contents instanceof th?e.atomValues.delete(t):e.atomValues.set(t,n),e.dirtyAtoms.add(t),e.nonvalidatedAtoms.delete(t)}function M1(e,t){e.replaceState(n=>{const r=I1(n);for(const o of t)vk(e,r,o);return D1(e,r),fk(),r})}function Jl(e,t){if(xi.length){const n=xi[xi.length-1];let r=n.get(e);r||n.set(e,r=[]),r.push(t)}else M1(e,[t])}const xi=[];function mk(){const e=new Map;return xi.push(e),()=>{for(const[t,n]of e)M1(t,n);xi.pop()}}function I1(e){return{...e,atomValues:e.atomValues.clone(),nonvalidatedAtoms:e.nonvalidatedAtoms.clone(),dirtyAtoms:new Set(e.dirtyAtoms)}}function D1(e,t){const n=nk(e,t,t.dirtyAtoms);for(const i of n){var r,o;(r=A1(i))===null||r===void 0||(o=r.invalidate)===null||o===void 0||o.call(r,t)}}function $1(e,t,n){Jl(e,{type:"set",recoilValue:t,valueOrUpdater:n})}function gk(e,t,n){if(n instanceof th)return $1(e,t,n);Jl(e,{type:"setLoadable",recoilValue:t,loadable:n})}function yk(e,t){Jl(e,{type:"markModified",recoilValue:t})}function wk(e,t,n){Jl(e,{type:"setUnvalidated",recoilValue:t,unvalidatedValue:n})}function Sk(e,{key:t},n,r=null){const o=ok(),i=e.getState();i.nodeToComponentSubscriptions.has(t)||i.nodeToComponentSubscriptions.set(t,new Map),Oe(i.nodeToComponentSubscriptions.get(t)).set(o,[r??"<not captured>",n]);const a=ak();if(a.early&&(a.mode==="LEGACY"||a.mode==="MUTABLE_SOURCE")){const s=e.getState().nextTree;s&&s.dirtyAtoms.has(t)&&n(s)}return{release:()=>{const s=e.getState(),l=s.nodeToComponentSubscriptions.get(t);l===void 0||!l.has(o)||(l.delete(o),l.size===0&&s.nodeToComponentSubscriptions.delete(t))}}}function _k(e,t){var n;const{currentTree:r}=e.getState(),o=ik(t.key);(n=o.clearCache)===null||n===void 0||n.call(o,e,r)}var fn={RecoilValueReadOnly:uk,AbstractRecoilValue:sk,RecoilState:lk,getRecoilValueAsLoadable:dk,setRecoilValue:$1,setRecoilValueLoadable:gk,markRecoilValueModified:yk,setUnvalidatedRecoilValue:wk,subscribeToRecoilValue:Sk,isRecoilValue:ck,applyAtomValueWrites:hk,batchStart:mk,writeLoadableToTreeState:yf,invalidateDownstreams:D1,copyTreeState:I1,refreshRecoilValue:_k};function bk(e,t,n){const r=e.entries();let o=r.next();for(;!o.done;){const i=o.value;if(t.call(n,i[1],i[0],e))return!0;o=r.next()}return!1}var Ek=bk;const{cleanUpNode:Ck}=ir,{deleteNodeConfigIfPossible:Rk,getNode:U1}=ft,{RetentionZone:F1}=Yl,Ok=12e4,j1=new Set;function B1(e,t){const n=e.getState(),r=n.currentTree;if(n.nextTree)return;const o=new Set;for(const a of t)if(a instanceof F1)for(const s of Tk(n,a))o.add(s);else o.add(a);const i=xk(e,o);for(const a of i)Pk(e,r,a)}function xk(e,t){const n=e.getState(),r=n.currentTree,o=e.getGraph(r.version),i=new Set,a=new Set;return s(t),i;function s(l){const u=new Set,c=kk(e,r,l,i,a);for(const v of c){var f;if(U1(v).retainedBy==="recoilRoot"){a.add(v);continue}if(((f=n.retention.referenceCounts.get(v))!==null&&f!==void 0?f:0)>0){a.add(v);continue}if(z1(v).some(_=>n.retention.referenceCounts.get(_))){a.add(v);continue}const y=o.nodeToNodeSubscriptions.get(v);if(y&&Ek(y,_=>a.has(_))){a.add(v);continue}i.add(v),u.add(v)}const d=new Set;for(const v of u)for(const y of(p=o.nodeDeps.get(v))!==null&&p!==void 0?p:j1){var p;i.has(y)||d.add(y)}d.size&&s(d)}}function kk(e,t,n,r,o){const i=e.getGraph(t.version),a=[],s=new Set;for(;n.size>0;)l(Oe(n.values().next().value));return a;function l(u){if(r.has(u)||o.has(u)){n.delete(u);return}if(s.has(u))return;const c=i.nodeToNodeSubscriptions.get(u);if(c)for(const f of c)l(f);s.add(u),n.delete(u),a.push(u)}}function Pk(e,t,n){if(!ge("recoil_memory_managament_2020"))return;Ck(e,n);const r=e.getState();r.knownAtoms.delete(n),r.knownSelectors.delete(n),r.nodeTransactionSubscriptions.delete(n),r.retention.referenceCounts.delete(n);const o=z1(n);for(const l of o){var i;(i=r.retention.nodesRetainedByZone.get(l))===null||i===void 0||i.delete(n)}t.atomValues.delete(n),t.dirtyAtoms.delete(n),t.nonvalidatedAtoms.delete(n);const a=r.graphsByVersion.get(t.version);if(a){const l=a.nodeDeps.get(n);if(l!==void 0){a.nodeDeps.delete(n);for(const u of l){var s;(s=a.nodeToNodeSubscriptions.get(u))===null||s===void 0||s.delete(n)}}a.nodeToNodeSubscriptions.delete(n)}Rk(n)}function Tk(e,t){var n;return(n=e.retention.nodesRetainedByZone.get(t))!==null&&n!==void 0?n:j1}function z1(e){const t=U1(e).retainedBy;return t===void 0||t==="components"||t==="recoilRoot"?[]:t instanceof F1?[t]:t}function Lk(e,t){const n=e.getState();n.nextTree?n.retention.retainablesToCheckForRelease.add(t):B1(e,new Set([t]))}function Nk(e,t,n){var r;if(!ge("recoil_memory_managament_2020"))return;const o=e.getState().retention.referenceCounts,i=((r=o.get(t))!==null&&r!==void 0?r:0)+n;i===0?V1(e,t):o.set(t,i)}function V1(e,t){if(!ge("recoil_memory_managament_2020"))return;e.getState().retention.referenceCounts.delete(t),Lk(e,t)}function Ak(e){if(!ge("recoil_memory_managament_2020"))return;const t=e.getState();B1(e,t.retention.retainablesToCheckForRelease),t.retention.retainablesToCheckForRelease.clear()}function Mk(e){return e===void 0?"recoilRoot":e}var Fr={SUSPENSE_TIMEOUT_MS:Ok,updateRetainCount:Nk,updateRetainCountToZero:V1,releaseScheduledRetainablesNow:Ak,retainedByOptionWithDefault:Mk};const{unstable_batchedUpdates:Ik}=L0;var Dk={unstable_batchedUpdates:Ik};const{unstable_batchedUpdates:$k}=Dk;var Uk={unstable_batchedUpdates:$k};const{batchStart:Fk}=fn,{unstable_batchedUpdates:jk}=Uk;let nh=jk;const Bk=e=>{nh=e},zk=()=>nh,Vk=e=>{nh(()=>{let t=()=>{};try{t=Fk(),e()}finally{t()}})};var Zl={getBatcher:zk,setBatcher:Bk,batchUpdates:Vk};function*Wk(e){for(const t of e)for(const n of t)yield n}var W1=Wk;const H1=typeof Window>"u"||typeof window>"u",Hk=e=>!H1&&(e===window||e instanceof Window),qk=typeof navigator<"u"&&navigator.product==="ReactNative";var rh={isSSR:H1,isReactNative:qk,isWindow:Hk};function Kk(e,t){let n;return(...o)=>{n||(n={});const i=t(...o);return Object.hasOwnProperty.call(n,i)||(n[i]=e(...o)),n[i]}}function Qk(e,t){let n,r;return(...i)=>{const a=t(...i);return n===a||(n=a,r=e(...i)),r}}function Gk(e,t){let n,r;return[(...a)=>{const s=t(...a);return n===s||(n=s,r=e(...a)),r},()=>{n=null}]}var Xk={memoizeWithArgsHash:Kk,memoizeOneWithArgsHash:Qk,memoizeOneWithArgsHashAndInvalidation:Gk};const{batchUpdates:wf}=Zl,{initializeNode:Yk,peekNodeInfo:Jk}=ir,{graph:Zk}=Ea,{getNextStoreID:eP}=Xl,{DEFAULT_VALUE:tP,recoilValues:im,recoilValuesForKeys:am}=ft,{AbstractRecoilValue:nP,getRecoilValueAsLoadable:rP,setRecoilValue:sm,setUnvalidatedRecoilValue:oP}=fn,{updateRetainCount:Cs}=Fr,{setInvalidateMemoizedSnapshot:iP}=L1,{getNextTreeStateVersion:aP,makeEmptyStoreState:sP}=C1,{isSSR:lP}=rh,{memoizeOneWithArgsHashAndInvalidation:uP}=Xk;class eu{constructor(t,n){ie(this,"_store",void 0),ie(this,"_refCount",1),ie(this,"getLoadable",r=>(this.checkRefCount_INTERNAL(),rP(this._store,r))),ie(this,"getPromise",r=>(this.checkRefCount_INTERNAL(),this.getLoadable(r).toPromise())),ie(this,"getNodes_UNSTABLE",r=>{if(this.checkRefCount_INTERNAL(),(r==null?void 0:r.isModified)===!0){if((r==null?void 0:r.isInitialized)===!1)return[];const a=this._store.getState().currentTree;return am(a.dirtyAtoms)}const o=this._store.getState().knownAtoms,i=this._store.getState().knownSelectors;return(r==null?void 0:r.isInitialized)==null?im.values():r.isInitialized===!0?am(W1([o,i])):Zd(im.values(),({key:a})=>!o.has(a)&&!i.has(a))}),ie(this,"getInfo_UNSTABLE",({key:r})=>(this.checkRefCount_INTERNAL(),Jk(this._store,this._store.getState().currentTree,r))),ie(this,"map",r=>{this.checkRefCount_INTERNAL();const o=new Sf(this,wf);return r(o),o}),ie(this,"asyncMap",async r=>{this.checkRefCount_INTERNAL();const o=new Sf(this,wf);return o.retain(),await r(o),o.autoRelease_INTERNAL(),o}),this._store={storeID:eP(),parentStoreID:n,getState:()=>t,replaceState:r=>{t.currentTree=r(t.currentTree)},getGraph:r=>{const o=t.graphsByVersion;if(o.has(r))return Oe(o.get(r));const i=Zk();return o.set(r,i),i},subscribeToTransactions:()=>({release:()=>{}}),addTransactionMetadata:()=>{throw se("Cannot subscribe to Snapshots")}};for(const r of this._store.getState().knownAtoms)Yk(this._store,r,"get"),Cs(this._store,r,1);this.autoRelease_INTERNAL()}retain(){this._refCount<=0,this._refCount++;let t=!1;return()=>{t||(t=!0,this._release())}}autoRelease_INTERNAL(){lP||window.setTimeout(()=>this._release(),10)}_release(){if(this._refCount--,this._refCount===0){if(this._store.getState().nodeCleanupFunctions.forEach(t=>t()),this._store.getState().nodeCleanupFunctions.clear(),!ge("recoil_memory_managament_2020"))return}else this._refCount<0}isRetained(){return this._refCount>0}checkRefCount_INTERNAL(){ge("recoil_memory_managament_2020")&&this._refCount<=0}getStore_INTERNAL(){return this.checkRefCount_INTERNAL(),this._store}getID(){return this.checkRefCount_INTERNAL(),this._store.getState().currentTree.stateID}getStoreID(){return this.checkRefCount_INTERNAL(),this._store.storeID}}function q1(e,t,n=!1){const r=e.getState(),o=n?aP():t.version;return{currentTree:{version:n?o:t.version,stateID:n?o:t.stateID,transactionMetadata:{...t.transactionMetadata},dirtyAtoms:new Set(t.dirtyAtoms),atomValues:t.atomValues.clone(),nonvalidatedAtoms:t.nonvalidatedAtoms.clone()},commitDepth:0,nextTree:null,previousTree:null,knownAtoms:new Set(r.knownAtoms),knownSelectors:new Set(r.knownSelectors),transactionSubscriptions:new Map,nodeTransactionSubscriptions:new Map,nodeToComponentSubscriptions:new Map,queuedComponentCallbacks_DEPRECATED:[],suspendedComponentResolvers:new Set,graphsByVersion:new Map().set(o,e.getGraph(t.version)),retention:{referenceCounts:new Map,nodesRetainedByZone:new Map,retainablesToCheckForRelease:new Set},nodeCleanupFunctions:new Map(Gl(r.nodeCleanupFunctions.entries(),([i])=>[i,()=>{}]))}}function cP(e){const t=new eu(sP());return e!=null?t.map(e):t}const[lm,K1]=uP((e,t)=>{var n;const r=e.getState(),o=t==="latest"?(n=r.nextTree)!==null&&n!==void 0?n:r.currentTree:Oe(r.previousTree);return new eu(q1(e,o),e.storeID)},(e,t)=>{var n,r;return String(t)+String(e.storeID)+String((n=e.getState().nextTree)===null||n===void 0?void 0:n.version)+String(e.getState().currentTree.version)+String((r=e.getState().previousTree)===null||r===void 0?void 0:r.version)});iP(K1);function fP(e,t="latest"){const n=lm(e,t);return n.isRetained()?n:(K1(),lm(e,t))}class Sf extends eu{constructor(t,n){super(q1(t.getStore_INTERNAL(),t.getStore_INTERNAL().getState().currentTree,!0),t.getStoreID()),ie(this,"_batch",void 0),ie(this,"set",(r,o)=>{this.checkRefCount_INTERNAL();const i=this.getStore_INTERNAL();this._batch(()=>{Cs(i,r.key,1),sm(this.getStore_INTERNAL(),r,o)})}),ie(this,"reset",r=>{this.checkRefCount_INTERNAL();const o=this.getStore_INTERNAL();this._batch(()=>{Cs(o,r.key,1),sm(this.getStore_INTERNAL(),r,tP)})}),ie(this,"setUnvalidatedAtomValues_DEPRECATED",r=>{this.checkRefCount_INTERNAL();const o=this.getStore_INTERNAL();wf(()=>{for(const[i,a]of r.entries())Cs(o,i,1),oP(o,new nP(i),a)})}),this._batch=n}}var tu={Snapshot:eu,MutableSnapshot:Sf,freshSnapshot:cP,cloneSnapshot:fP},dP=tu.Snapshot,hP=tu.MutableSnapshot,pP=tu.freshSnapshot,vP=tu.cloneSnapshot,nu=Object.freeze({__proto__:null,Snapshot:dP,MutableSnapshot:hP,freshSnapshot:pP,cloneSnapshot:vP});function mP(...e){const t=new Set;for(const n of e)for(const r of n)t.add(r);return t}var gP=mP;const{useRef:yP}=V;function wP(e){const t=yP(e);return t.current===e&&typeof e=="function"&&(t.current=e()),t}var um=wP;const{getNextTreeStateVersion:SP,makeEmptyStoreState:Q1}=C1,{cleanUpNode:_P,getDownstreamNodes:bP,initializeNode:EP,setNodeValue:CP,setUnvalidatedAtomValue_DEPRECATED:RP}=ir,{graph:OP}=Ea,{cloneGraph:xP}=Ea,{getNextStoreID:G1}=Xl,{createMutableSource:Qu,reactMode:X1}=ba,{applyAtomValueWrites:kP}=fn,{releaseScheduledRetainablesNow:Y1}=Fr,{freshSnapshot:PP}=nu,{useCallback:TP,useContext:J1,useEffect:_f,useMemo:LP,useRef:NP,useState:AP}=V;function ni(){throw se("This component must be used inside a <RecoilRoot> component.")}const Z1=Object.freeze({storeID:G1(),getState:ni,replaceState:ni,getGraph:ni,subscribeToTransactions:ni,addTransactionMetadata:ni});let bf=!1;function cm(e){if(bf)throw se("An atom update was triggered within the execution of a state updater function. State updater functions provided to Recoil must be pure functions.");const t=e.getState();if(t.nextTree===null){ge("recoil_memory_managament_2020")&&ge("recoil_release_on_cascading_update_killswitch_2021")&&t.commitDepth>0&&Y1(e);const n=t.currentTree.version,r=SP();t.nextTree={...t.currentTree,version:r,stateID:r,dirtyAtoms:new Set,transactionMetadata:{}},t.graphsByVersion.set(r,xP(Oe(t.graphsByVersion.get(n))))}}const ew=V.createContext({current:Z1}),ru=()=>J1(ew),tw=V.createContext(null);function MP(){return J1(tw)}function oh(e,t,n){const r=bP(e,n,n.dirtyAtoms);for(const o of r){const i=t.nodeToComponentSubscriptions.get(o);if(i)for(const[a,[s,l]]of i)l(n)}}function nw(e){const t=e.getState(),n=t.currentTree,r=n.dirtyAtoms;if(r.size){for(const[o,i]of t.nodeTransactionSubscriptions)if(r.has(o))for(const[a,s]of i)s(e);for(const[o,i]of t.transactionSubscriptions)i(e);(!X1().early||t.suspendedComponentResolvers.size>0)&&(oh(e,t,n),t.suspendedComponentResolvers.forEach(o=>o()),t.suspendedComponentResolvers.clear())}t.queuedComponentCallbacks_DEPRECATED.forEach(o=>o(n)),t.queuedComponentCallbacks_DEPRECATED.splice(0,t.queuedComponentCallbacks_DEPRECATED.length)}function IP(e){const t=e.getState();t.commitDepth++;try{const{nextTree:n}=t;if(n==null)return;t.previousTree=t.currentTree,t.currentTree=n,t.nextTree=null,nw(e),t.previousTree!=null?t.graphsByVersion.delete(t.previousTree.version):Qd("Ended batch with no previous state, which is unexpected","recoil"),t.previousTree=null,ge("recoil_memory_managament_2020")&&n==null&&Y1(e)}finally{t.commitDepth--}}function DP({setNotifyBatcherOfChange:e}){const t=ru(),[,n]=AP([]);return e(()=>n({})),_f(()=>(e(()=>n({})),()=>{e(()=>{})}),[e]),_f(()=>{px.enqueueExecution("Batcher",()=>{IP(t.current)})}),null}function $P(e,t){const n=Q1();return t({set:(r,o)=>{const i=n.currentTree,a=CP(e,i,r.key,o),s=new Set(a.keys()),l=i.nonvalidatedAtoms.clone();for(const u of s)l.delete(u);n.currentTree={...i,dirtyAtoms:gP(i.dirtyAtoms,s),atomValues:kP(i.atomValues,a),nonvalidatedAtoms:l}},setUnvalidatedAtomValues:r=>{r.forEach((o,i)=>{n.currentTree=RP(n.currentTree,i,o)})}}),n}function UP(e){const t=PP(e),n=t.getStore_INTERNAL().getState();return t.retain(),n.nodeCleanupFunctions.forEach(r=>r()),n.nodeCleanupFunctions.clear(),n}let fm=0;function FP({initializeState_DEPRECATED:e,initializeState:t,store_INTERNAL:n,children:r}){let o;const i=p=>{const v=o.current.graphsByVersion;if(v.has(p))return Oe(v.get(p));const y=OP();return v.set(p,y),y},a=(p,v)=>{if(v==null){const{transactionSubscriptions:y}=f.current.getState(),_=fm++;return y.set(_,p),{release:()=>{y.delete(_)}}}else{const{nodeTransactionSubscriptions:y}=f.current.getState();y.has(v)||y.set(v,new Map);const _=fm++;return Oe(y.get(v)).set(_,p),{release:()=>{const m=y.get(v);m&&(m.delete(_),m.size===0&&y.delete(v))}}}},s=p=>{cm(f.current);for(const v of Object.keys(p))Oe(f.current.getState().nextTree).transactionMetadata[v]=p[v]},l=p=>{cm(f.current);const v=Oe(o.current.nextTree);let y;try{bf=!0,y=p(v)}finally{bf=!1}y!==v&&(o.current.nextTree=y,X1().early&&oh(f.current,o.current,y),Oe(u.current)())},u=NP(null),c=TP(p=>{u.current=p},[u]),f=um(()=>n??{storeID:G1(),getState:()=>o.current,replaceState:l,getGraph:i,subscribeToTransactions:a,addTransactionMetadata:s});n!=null&&(f.current=n),o=um(()=>e!=null?$P(f.current,e):t!=null?UP(t):Q1());const d=LP(()=>Qu==null?void 0:Qu(o,()=>o.current.currentTree.version),[o]);return _f(()=>{const p=f.current;for(const v of new Set(p.getState().knownAtoms))EP(p,v,"get");return()=>{for(const v of p.getState().knownAtoms)_P(p,v)}},[f]),V.createElement(ew.Provider,{value:f},V.createElement(tw.Provider,{value:d},V.createElement(DP,{setNotifyBatcherOfChange:c}),r))}function jP(e){const{override:t,...n}=e,r=ru();return t===!1&&r.current!==Z1?e.children:V.createElement(FP,n)}function BP(){return ru().current.storeID}var On={RecoilRoot:jP,useStoreRef:ru,useRecoilMutableSource:MP,useRecoilStoreID:BP,notifyComponents_FOR_TESTING:oh,sendEndOfBatchNotifications_FOR_TESTING:nw};function zP(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(e[n]!==t[n])return!1;return!0}var VP=zP;const{useEffect:WP,useRef:HP}=V;function qP(e){const t=HP();return WP(()=>{t.current=e}),t.current}var rw=qP;const{useStoreRef:KP}=On,{SUSPENSE_TIMEOUT_MS:QP}=Fr,{updateRetainCount:ri}=Fr,{RetentionZone:GP}=Yl,{useEffect:XP,useRef:YP}=V,{isSSR:dm}=rh;function JP(e){if(ge("recoil_memory_managament_2020"))return ZP(e)}function ZP(e){const n=(Array.isArray(e)?e:[e]).map(a=>a instanceof GP?a:a.key),r=KP();XP(()=>{if(!ge("recoil_memory_managament_2020"))return;const a=r.current;if(o.current&&!dm)window.clearTimeout(o.current),o.current=null;else for(const s of n)ri(a,s,1);return()=>{for(const s of n)ri(a,s,-1)}},[r,...n]);const o=YP(),i=rw(n);if(!dm&&(i===void 0||!VP(i,n))){const a=r.current;for(const s of n)ri(a,s,1);if(i)for(const s of i)ri(a,s,-1);o.current&&window.clearTimeout(o.current),o.current=window.setTimeout(()=>{o.current=null;for(const s of n)ri(a,s,-1)},QP)}}var ih=JP;function eT(){return"<component name not available>"}var Ra=eT;const{batchUpdates:tT}=Zl,{DEFAULT_VALUE:ow}=ft,{currentRendererSupportsUseSyncExternalStore:nT,reactMode:Uo,useMutableSource:rT,useSyncExternalStore:oT}=ba,{useRecoilMutableSource:iT,useStoreRef:dn}=On,{AbstractRecoilValue:Ef,getRecoilValueAsLoadable:Oa,setRecoilValue:gl,setUnvalidatedRecoilValue:aT,subscribeToRecoilValue:xo}=fn,{useCallback:ct,useEffect:ko,useMemo:iw,useRef:ki,useState:ah}=V,{setByAddingToSet:sT}=O1;function sh(e,t,n){if(e.state==="hasValue")return e.contents;throw e.state==="loading"?new Promise(o=>{n.current.getState().suspendedComponentResolvers.add(o)}):e.state==="hasError"?e.contents:se(`Invalid value of loadable atom "${t.key}"`)}function lT(){const e=Ra(),t=dn(),[,n]=ah([]),r=ki(new Set);r.current=new Set;const o=ki(new Set),i=ki(new Map),a=ct(l=>{const u=i.current.get(l);u&&(u.release(),i.current.delete(l))},[i]),s=ct((l,u)=>{i.current.has(u)&&n([])},[]);return ko(()=>{const l=t.current;Oi(r.current,o.current).forEach(u=>{if(i.current.has(u))return;const c=xo(l,new Ef(u),d=>s(d,u),e);i.current.set(u,c),l.getState().nextTree?l.getState().queuedComponentCallbacks_DEPRECATED.push(()=>{s(l.getState(),u)}):s(l.getState(),u)}),Oi(o.current,r.current).forEach(u=>{a(u)}),o.current=r.current}),ko(()=>{const l=i.current;return Oi(r.current,new Set(l.keys())).forEach(u=>{const c=xo(t.current,new Ef(u),f=>s(f,u),e);l.set(u,c)}),()=>l.forEach((u,c)=>a(c))},[e,t,a,s]),iw(()=>{function l(v){return y=>{gl(t.current,v,y)}}function u(v){return()=>gl(t.current,v,ow)}function c(v){var y;r.current.has(v.key)||(r.current=sT(r.current,v.key));const _=t.current.getState();return Oa(t.current,v,Uo().early&&(y=_.nextTree)!==null&&y!==void 0?y:_.currentTree)}function f(v){const y=c(v);return sh(y,v,t)}function d(v){return[f(v),l(v)]}function p(v){return[c(v),l(v)]}return{getRecoilValue:f,getRecoilValueLoadable:c,getRecoilState:d,getRecoilStateLoadable:p,getSetRecoilState:l,getResetRecoilState:u}},[r,t])}const uT={current:0};function cT(e){const t=dn(),n=Ra(),r=ct(()=>{var s;const l=t.current,u=l.getState(),c=Uo().early&&(s=u.nextTree)!==null&&s!==void 0?s:u.currentTree;return{loadable:Oa(l,e,c),key:e.key}},[t,e]),o=ct(s=>{let l;return()=>{var u,c;const f=s();return(u=l)!==null&&u!==void 0&&u.loadable.is(f.loadable)&&((c=l)===null||c===void 0?void 0:c.key)===f.key?l:(l=f,f)}},[]),i=iw(()=>o(r),[r,o]),a=ct(s=>{const l=t.current;return xo(l,e,s,n).release},[t,e,n]);return oT(a,i,i).loadable}function fT(e){const t=dn(),n=ct(()=>{var u;const c=t.current,f=c.getState(),d=Uo().early&&(u=f.nextTree)!==null&&u!==void 0?u:f.currentTree;return Oa(c,e,d)},[t,e]),r=ct(()=>n(),[n]),o=Ra(),i=ct((u,c)=>{const f=t.current;return xo(f,e,()=>{if(!ge("recoil_suppress_rerender_in_callback"))return c();const p=n();l.current.is(p)||c(),l.current=p},o).release},[t,e,o,n]),a=iT();if(a==null)throw se("Recoil hooks must be used in components contained within a <RecoilRoot> component.");const s=rT(a,r,i),l=ki(s);return ko(()=>{l.current=s}),s}function Cf(e){const t=dn(),n=Ra(),r=ct(()=>{var l;const u=t.current,c=u.getState(),f=Uo().early&&(l=c.nextTree)!==null&&l!==void 0?l:c.currentTree;return Oa(u,e,f)},[t,e]),o=ct(()=>({loadable:r(),key:e.key}),[r,e.key]),i=ct(l=>{const u=o();return l.loadable.is(u.loadable)&&l.key===u.key?l:u},[o]);ko(()=>{const l=xo(t.current,e,u=>{s(i)},n);return s(i),l.release},[n,e,t,i]);const[a,s]=ah(o);return a.key!==e.key?o().loadable:a.loadable}function dT(e){const t=dn(),[,n]=ah([]),r=Ra(),o=ct(()=>{var s;const l=t.current,u=l.getState(),c=Uo().early&&(s=u.nextTree)!==null&&s!==void 0?s:u.currentTree;return Oa(l,e,c)},[t,e]),i=o(),a=ki(i);return ko(()=>{a.current=i}),ko(()=>{const s=t.current,l=s.getState(),u=xo(s,e,f=>{var d;if(!ge("recoil_suppress_rerender_in_callback"))return n([]);const p=o();(d=a.current)!==null&&d!==void 0&&d.is(p)||n(p),a.current=p},r);if(l.nextTree)s.getState().queuedComponentCallbacks_DEPRECATED.push(()=>{a.current=null,n([])});else{var c;if(!ge("recoil_suppress_rerender_in_callback"))return n([]);const f=o();(c=a.current)!==null&&c!==void 0&&c.is(f)||n(f),a.current=f}return u.release},[r,o,e,t]),i}function lh(e){return ge("recoil_memory_managament_2020")&&ih(e),{TRANSITION_SUPPORT:Cf,SYNC_EXTERNAL_STORE:nT()?cT:Cf,MUTABLE_SOURCE:fT,LEGACY:dT}[Uo().mode](e)}function aw(e){const t=dn(),n=lh(e);return sh(n,e,t)}function ou(e){const t=dn();return ct(n=>{gl(t.current,e,n)},[t,e])}function hT(e){const t=dn();return ct(()=>{gl(t.current,e,ow)},[t,e])}function pT(e){return[aw(e),ou(e)]}function vT(e){return[lh(e),ou(e)]}function mT(){const e=dn();return(t,n={})=>{tT(()=>{e.current.addTransactionMetadata(n),t.forEach((r,o)=>aT(e.current,new Ef(o),r))})}}function sw(e){return ge("recoil_memory_managament_2020")&&ih(e),Cf(e)}function lw(e){const t=dn(),n=sw(e);return sh(n,e,t)}function gT(e){return[lw(e),ou(e)]}var yT={recoilComponentGetRecoilValueCount_FOR_TESTING:uT,useRecoilInterface:lT,useRecoilState:pT,useRecoilStateLoadable:vT,useRecoilValue:aw,useRecoilValueLoadable:lh,useResetRecoilState:hT,useSetRecoilState:ou,useSetUnvalidatedAtomValues:mT,useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE:sw,useRecoilValue_TRANSITION_SUPPORT_UNSTABLE:lw,useRecoilState_TRANSITION_SUPPORT_UNSTABLE:gT};function wT(e,t){const n=new Map;for(const[r,o]of e)t(o,r)&&n.set(r,o);return n}var ST=wT;function _T(e,t){const n=new Set;for(const r of e)t(r)&&n.add(r);return n}var bT=_T;function ET(...e){const t=new Map;for(let n=0;n<e.length;n++){const r=e[n].keys();let o;for(;!(o=r.next()).done;)t.set(o.value,e[n].get(o.value))}return t}var CT=ET;const{batchUpdates:RT}=Zl,{DEFAULT_VALUE:OT,getNode:uw,nodes:xT}=ft,{useStoreRef:uh}=On,{AbstractRecoilValue:kT,setRecoilValueLoadable:PT}=fn,{SUSPENSE_TIMEOUT_MS:TT}=Fr,{cloneSnapshot:yl}=nu,{useCallback:iu,useEffect:cw,useRef:hm,useState:LT}=V,{isSSR:pm}=rh;function au(e){const t=uh();cw(()=>t.current.subscribeToTransactions(e).release,[e,t])}function vm(e){const t=e.atomValues.toMap(),n=ml(ST(t,(r,o)=>{const a=uw(o).persistence_UNSTABLE;return a!=null&&a.type!=="none"&&r.state==="hasValue"}),r=>r.contents);return CT(e.nonvalidatedAtoms.toMap(),n)}function NT(e){au(iu(t=>{let n=t.getState().previousTree;const r=t.getState().currentTree;n||(n=t.getState().currentTree);const o=vm(r),i=vm(n),a=ml(xT,l=>{var u,c,f,d;return{persistence_UNSTABLE:{type:(u=(c=l.persistence_UNSTABLE)===null||c===void 0?void 0:c.type)!==null&&u!==void 0?u:"none",backButton:(f=(d=l.persistence_UNSTABLE)===null||d===void 0?void 0:d.backButton)!==null&&f!==void 0?f:!1}}}),s=bT(r.dirtyAtoms,l=>o.has(l)||i.has(l));e({atomValues:o,previousAtomValues:i,atomInfo:a,modifiedAtoms:s,transactionMetadata:{...r.transactionMetadata}})},[e]))}function AT(e){au(iu(t=>{const n=yl(t,"latest"),r=yl(t,"previous");e({snapshot:n,previousSnapshot:r})},[e]))}function MT(){const e=uh(),[t,n]=LT(()=>yl(e.current)),r=rw(t),o=hm(),i=hm();if(au(iu(s=>n(yl(s)),[])),cw(()=>{const s=t.retain();if(o.current&&!pm){var l;window.clearTimeout(o.current),o.current=null,(l=i.current)===null||l===void 0||l.call(i),i.current=null}return()=>{window.setTimeout(s,10)}},[t]),r!==t&&!pm){if(o.current){var a;window.clearTimeout(o.current),o.current=null,(a=i.current)===null||a===void 0||a.call(i),i.current=null}i.current=t.retain(),o.current=window.setTimeout(()=>{var s;o.current=null,(s=i.current)===null||s===void 0||s.call(i),i.current=null},TT)}return t}function fw(e,t){var n;const r=e.getState(),o=(n=r.nextTree)!==null&&n!==void 0?n:r.currentTree,i=t.getStore_INTERNAL().getState().currentTree;RT(()=>{const a=new Set;for(const u of[o.atomValues.keys(),i.atomValues.keys()])for(const c of u){var s,l;((s=o.atomValues.get(c))===null||s===void 0?void 0:s.contents)!==((l=i.atomValues.get(c))===null||l===void 0?void 0:l.contents)&&uw(c).shouldRestoreFromSnapshots&&a.add(c)}a.forEach(u=>{PT(e,new kT(u),i.atomValues.has(u)?Oe(i.atomValues.get(u)):OT)}),e.replaceState(u=>({...u,stateID:t.getID()}))})}function IT(){const e=uh();return iu(t=>fw(e.current,t),[e])}var dw={useRecoilSnapshot:MT,gotoSnapshot:fw,useGotoRecoilSnapshot:IT,useRecoilTransactionObserver:AT,useTransactionObservation_DEPRECATED:NT,useTransactionSubscription_DEPRECATED:au};const{peekNodeInfo:DT}=ir,{useStoreRef:$T}=On;function UT(){const e=$T();return({key:t})=>DT(e.current,e.current.getState().currentTree,t)}var FT=UT;const{reactMode:jT}=ba,{RecoilRoot:BT,useStoreRef:zT}=On,{useMemo:VT}=V;function WT(){jT().mode==="MUTABLE_SOURCE"&&console.warn("Warning: There are known issues using useRecoilBridgeAcrossReactRoots() in recoil_mutable_source rendering mode.  Please consider upgrading to recoil_sync_external_store mode.");const e=zT().current;return VT(()=>{function t({children:n}){return V.createElement(BT,{store_INTERNAL:e},n)}return t},[e])}var HT=WT;const{loadableWithValue:qT}=_a,{initializeNode:KT}=ir,{DEFAULT_VALUE:QT,getNode:GT}=ft,{copyTreeState:XT,getRecoilValueAsLoadable:YT,invalidateDownstreams:JT,writeLoadableToTreeState:ZT}=fn;function mm(e){return GT(e.key).nodeType==="atom"}class eL{constructor(t,n){ie(this,"_store",void 0),ie(this,"_treeState",void 0),ie(this,"_changes",void 0),ie(this,"get",r=>{if(this._changes.has(r.key))return this._changes.get(r.key);if(!mm(r))throw se("Reading selectors within atomicUpdate is not supported");const o=YT(this._store,r,this._treeState);if(o.state==="hasValue")return o.contents;throw o.state==="hasError"?o.contents:se(`Expected Recoil atom ${r.key} to have a value, but it is in a loading state.`)}),ie(this,"set",(r,o)=>{if(!mm(r))throw se("Setting selectors within atomicUpdate is not supported");if(typeof o=="function"){const i=this.get(r);this._changes.set(r.key,o(i))}else KT(this._store,r.key,"set"),this._changes.set(r.key,o)}),ie(this,"reset",r=>{this.set(r,QT)}),this._store=t,this._treeState=n,this._changes=new Map}newTreeState_INTERNAL(){if(this._changes.size===0)return this._treeState;const t=XT(this._treeState);for(const[n,r]of this._changes)ZT(t,n,qT(r));return JT(this._store,t),t}}function tL(e){return t=>{e.replaceState(n=>{const r=new eL(e,n);return t(r),r.newTreeState_INTERNAL()})}}var nL={atomicUpdater:tL},rL=nL.atomicUpdater,hw=Object.freeze({__proto__:null,atomicUpdater:rL});function oL(e,t){if(!e)throw new Error(t)}var iL=oL,fi=iL;const{atomicUpdater:aL}=hw,{batchUpdates:sL}=Zl,{DEFAULT_VALUE:lL}=ft,{useStoreRef:uL}=On,{refreshRecoilValue:cL,setRecoilValue:gm}=fn,{cloneSnapshot:fL}=nu,{gotoSnapshot:dL}=dw,{useCallback:hL}=V;class pw{}const pL=new pw;function vw(e,t,n,r){let o=pL,i;if(sL(()=>{const s="useRecoilCallback() expects a function that returns a function: it accepts a function of the type (RecoilInterface) => (Args) => ReturnType and returns a callback function (Args) => ReturnType, where RecoilInterface is an object {snapshot, set, ...} and Args and ReturnType are the argument and return types of the callback you want to create.  Please see the docs at recoiljs.org for details.";if(typeof t!="function")throw se(s);const l=x1({...r??{},set:(c,f)=>gm(e,c,f),reset:c=>gm(e,c,lL),refresh:c=>cL(e,c),gotoSnapshot:c=>dL(e,c),transact_UNSTABLE:c=>aL(e)(c)},{snapshot:()=>{const c=fL(e);return i=c.retain(),c}}),u=t(l);if(typeof u!="function")throw se(s);o=u(...n)}),o instanceof pw&&fi(!1),Re(o))o.finally(()=>{var s;(s=i)===null||s===void 0||s()});else{var a;(a=i)===null||a===void 0||a()}return o}function vL(e,t){const n=uL();return hL((...r)=>vw(n.current,e,r),t!=null?[...t,n]:void 0)}var mw={recoilCallback:vw,useRecoilCallback:vL};const{useStoreRef:mL}=On,{refreshRecoilValue:gL}=fn,{useCallback:yL}=V;function wL(e){const t=mL();return yL(()=>{const n=t.current;gL(n,e)},[e,t])}var SL=wL;const{atomicUpdater:_L}=hw,{useStoreRef:bL}=On,{useMemo:EL}=V;function CL(e,t){const n=bL();return EL(()=>(...r)=>{_L(n.current)(i=>{e(i)(...r)})},t!=null?[...t,n]:void 0)}var RL=CL;class OL{constructor(t){ie(this,"value",void 0),this.value=t}}var xL={WrappedValue:OL},kL=xL.WrappedValue,gw=Object.freeze({__proto__:null,WrappedValue:kL});const{isFastRefreshEnabled:PL}=ba;class ym extends Error{}class TL{constructor(t){var n,r,o;ie(this,"_name",void 0),ie(this,"_numLeafs",void 0),ie(this,"_root",void 0),ie(this,"_onHit",void 0),ie(this,"_onSet",void 0),ie(this,"_mapNodeValue",void 0),this._name=t==null?void 0:t.name,this._numLeafs=0,this._root=null,this._onHit=(n=t==null?void 0:t.onHit)!==null&&n!==void 0?n:()=>{},this._onSet=(r=t==null?void 0:t.onSet)!==null&&r!==void 0?r:()=>{},this._mapNodeValue=(o=t==null?void 0:t.mapNodeValue)!==null&&o!==void 0?o:i=>i}size(){return this._numLeafs}root(){return this._root}get(t,n){var r;return(r=this.getLeafNode(t,n))===null||r===void 0?void 0:r.value}getLeafNode(t,n){if(this._root==null)return;let r=this._root;for(;r;){if(n==null||n.onNodeVisit(r),r.type==="leaf")return this._onHit(r),r;const o=this._mapNodeValue(t(r.nodeKey));r=r.branches.get(o)}}set(t,n,r){const o=()=>{var i,a,s,l;let u,c;for(const[_,m]of t){var f,d,p;const h=this._root;if((h==null?void 0:h.type)==="leaf")throw this.invalidCacheError();const g=u;if(u=g?g.branches.get(c):h,u=(f=u)!==null&&f!==void 0?f:{type:"branch",nodeKey:_,parent:g,branches:new Map,branchKey:c},u.type!=="branch"||u.nodeKey!==_)throw this.invalidCacheError();g==null||g.branches.set(c,u),r==null||(d=r.onNodeVisit)===null||d===void 0||d.call(r,u),c=this._mapNodeValue(m),this._root=(p=this._root)!==null&&p!==void 0?p:u}const v=u?(i=u)===null||i===void 0?void 0:i.branches.get(c):this._root;if(v!=null&&(v.type!=="leaf"||v.branchKey!==c))throw this.invalidCacheError();const y={type:"leaf",value:n,parent:u,branchKey:c};(a=u)===null||a===void 0||a.branches.set(c,y),this._root=(s=this._root)!==null&&s!==void 0?s:y,this._numLeafs++,this._onSet(y),r==null||(l=r.onNodeVisit)===null||l===void 0||l.call(r,y)};try{o()}catch(i){if(i instanceof ym)this.clear(),o();else throw i}}delete(t){const n=this.root();if(!n)return!1;if(t===n)return this._root=null,this._numLeafs=0,!0;let r=t.parent,o=t.branchKey;for(;r;){var i;if(r.branches.delete(o),r===n)return r.branches.size===0?(this._root=null,this._numLeafs=0):this._numLeafs--,!0;if(r.branches.size>0)break;o=(i=r)===null||i===void 0?void 0:i.branchKey,r=r.parent}for(;r!==n;r=r.parent)if(r==null)return!1;return this._numLeafs--,!0}clear(){this._numLeafs=0,this._root=null}invalidCacheError(){const t=PL()?"Possible Fast Refresh module reload detected.  This may also be caused by an selector returning inconsistent values. Resetting cache.":"Invalid cache values.  This happens when selectors do not return consistent values for the same input dependency values.  That may also be caused when using Fast Refresh to change a selector implementation.  Resetting cache.";throw Qd(t+(this._name!=null?` - ${this._name}`:"")),new ym}}var LL={TreeCache:TL},NL=LL.TreeCache,yw=Object.freeze({__proto__:null,TreeCache:NL});class AL{constructor(t){var n;ie(this,"_maxSize",void 0),ie(this,"_size",void 0),ie(this,"_head",void 0),ie(this,"_tail",void 0),ie(this,"_map",void 0),ie(this,"_keyMapper",void 0),this._maxSize=t.maxSize,this._size=0,this._head=null,this._tail=null,this._map=new Map,this._keyMapper=(n=t.mapKey)!==null&&n!==void 0?n:r=>r}head(){return this._head}tail(){return this._tail}size(){return this._size}maxSize(){return this._maxSize}has(t){return this._map.has(this._keyMapper(t))}get(t){const n=this._keyMapper(t),r=this._map.get(n);if(r)return this.set(t,r.value),r.value}set(t,n){const r=this._keyMapper(t);this._map.get(r)&&this.delete(t);const i=this.head(),a={key:t,right:i,left:null,value:n};i?i.left=a:this._tail=a,this._map.set(r,a),this._head=a,this._size++,this._maybeDeleteLRU()}_maybeDeleteLRU(){this.size()>this.maxSize()&&this.deleteLru()}deleteLru(){const t=this.tail();t&&this.delete(t.key)}delete(t){const n=this._keyMapper(t);if(!this._size||!this._map.has(n))return;const r=Oe(this._map.get(n)),o=r.right,i=r.left;o&&(o.left=r.left),i&&(i.right=r.right),r===this.head()&&(this._head=o),r===this.tail()&&(this._tail=i),this._map.delete(n),this._size--}clear(){this._size=0,this._head=null,this._tail=null,this._map=new Map}}var ML={LRUCache:AL},IL=ML.LRUCache,ww=Object.freeze({__proto__:null,LRUCache:IL});const{LRUCache:DL}=ww,{TreeCache:$L}=yw;function UL({name:e,maxSize:t,mapNodeValue:n=r=>r}){const r=new DL({maxSize:t}),o=new $L({name:e,mapNodeValue:n,onHit:i=>{r.set(i,!0)},onSet:i=>{const a=r.tail();r.set(i,!0),a&&o.size()>t&&o.delete(a.key)}});return o}var wm=UL;function Ut(e,t,n){if(typeof e=="string"&&!e.includes('"')&&!e.includes("\\"))return`"${e}"`;switch(typeof e){case"undefined":return"";case"boolean":return e?"true":"false";case"number":case"symbol":return String(e);case"string":return JSON.stringify(e);case"function":if((t==null?void 0:t.allowFunctions)!==!0)throw se("Attempt to serialize function in a Recoil cache key");return`__FUNCTION(${e.name})__`}if(e===null)return"null";if(typeof e!="object"){var r;return(r=JSON.stringify(e))!==null&&r!==void 0?r:""}if(Re(e))return"__PROMISE__";if(Array.isArray(e))return`[${e.map((o,i)=>Ut(o,t,i.toString()))}]`;if(typeof e.toJSON=="function")return Ut(e.toJSON(n),t,n);if(e instanceof Map){const o={};for(const[i,a]of e)o[typeof i=="string"?i:Ut(i,t)]=a;return Ut(o,t,n)}return e instanceof Set?Ut(Array.from(e).sort((o,i)=>Ut(o,t).localeCompare(Ut(i,t))),t,n):Symbol!==void 0&&e[Symbol.iterator]!=null&&typeof e[Symbol.iterator]=="function"?Ut(Array.from(e),t,n):`{${Object.keys(e).filter(o=>e[o]!==void 0).sort().map(o=>`${Ut(o,t)}:${Ut(e[o],t,o)}`).join(",")}}`}function FL(e,t={allowFunctions:!1}){return Ut(e,t)}var su=FL;const{TreeCache:jL}=yw,Ga={equality:"reference",eviction:"keep-all",maxSize:1/0};function BL({equality:e=Ga.equality,eviction:t=Ga.eviction,maxSize:n=Ga.maxSize}=Ga,r){const o=zL(e);return VL(t,n,o,r)}function zL(e){switch(e){case"reference":return t=>t;case"value":return t=>su(t)}throw se(`Unrecognized equality policy ${e}`)}function VL(e,t,n,r){switch(e){case"keep-all":return new jL({name:r,mapNodeValue:n});case"lru":return wm({name:r,maxSize:Oe(t),mapNodeValue:n});case"most-recent":return wm({name:r,maxSize:1,mapNodeValue:n})}throw se(`Unrecognized eviction policy ${e}`)}var WL=BL;function HL(e){return()=>null}var qL={startPerfBlock:HL};const{isLoadable:KL,loadableWithError:Xa,loadableWithPromise:QL,loadableWithValue:Gu}=_a,{WrappedValue:Sw}=gw,{getNodeLoadable:Ya,peekNodeLoadable:GL,setNodeValue:XL}=ir,{saveDepsToStore:YL}=Ea,{DEFAULT_VALUE:JL,getConfigDeletionHandler:ZL,getNode:eN,registerNode:Sm}=ft,{isRecoilValue:tN}=Oo,{markRecoilValueModified:_m}=fn,{retainedByOptionWithDefault:nN}=Fr,{recoilCallback:rN}=mw,{startPerfBlock:oN}=qL;class _w{}const oi=new _w,ii=[],Ja=new Map,iN=(()=>{let e=0;return()=>e++})();function bw(e){let t=null;const{key:n,get:r,cachePolicy_UNSTABLE:o}=e,i=e.set!=null?e.set:void 0,a=new Set,s=WL(o??{equality:"reference",eviction:"keep-all"},n),l=nN(e.retainedBy_UNSTABLE),u=new Map;let c=0;function f(){return!ge("recoil_memory_managament_2020")||c>0}function d(b){return b.getState().knownSelectors.add(n),c++,()=>{c--}}function p(){return ZL(n)!==void 0&&!f()}function v(b,U,B,J,W){I(U,J,W),y(b,B)}function y(b,U){w(b,U)&&re(b),m(U,!0)}function _(b,U){w(b,U)&&(Oe($(b)).stateVersions.clear(),m(U,!1))}function m(b,U){const B=Ja.get(b);if(B!=null){for(const J of B)_m(J,Oe(t));U&&Ja.delete(b)}}function h(b,U){let B=Ja.get(U);B==null&&Ja.set(U,B=new Set),B.add(b)}function g(b,U,B,J,W,Z){return U.then(ae=>{if(!f())throw re(b),oi;const Y=Gu(ae);return v(b,B,W,Y,J),ae}).catch(ae=>{if(!f())throw re(b),oi;if(Re(ae))return S(b,ae,B,J,W,Z);const Y=Xa(ae);throw v(b,B,W,Y,J),ae})}function S(b,U,B,J,W,Z){return U.then(ae=>{if(!f())throw re(b),oi;Z.loadingDepKey!=null&&Z.loadingDepPromise===U?B.atomValues.set(Z.loadingDepKey,Gu(ae)):b.getState().knownSelectors.forEach(pe=>{B.atomValues.delete(pe)});const Y=N(b,B);if(Y&&Y.state!=="loading"){if((w(b,W)||$(b)==null)&&y(b,W),Y.state==="hasValue")return Y.contents;throw Y.contents}if(!w(b,W)){const pe=G(b,B);if(pe!=null)return pe.loadingLoadable.contents}const[me,ye]=T(b,B,W);if(me.state!=="loading"&&v(b,B,W,me,ye),me.state==="hasError")throw me.contents;return me.contents}).catch(ae=>{if(ae instanceof _w)throw oi;if(!f())throw re(b),oi;const Y=Xa(ae);throw v(b,B,W,Y,J),ae})}function k(b,U,B,J){var W,Z,ae,Y;if(w(b,J)||U.version===((W=b.getState())===null||W===void 0||(Z=W.currentTree)===null||Z===void 0?void 0:Z.version)||U.version===((ae=b.getState())===null||ae===void 0||(Y=ae.nextTree)===null||Y===void 0?void 0:Y.version)){var me,ye,pe;YL(n,B,b,(me=(ye=b.getState())===null||ye===void 0||(pe=ye.nextTree)===null||pe===void 0?void 0:pe.version)!==null&&me!==void 0?me:b.getState().currentTree.version)}for(const we of B)a.add(we)}function T(b,U,B){const J=oN(n);let W=!0,Z=!0;const ae=()=>{J(),Z=!1};let Y,me=!1,ye;const pe={loadingDepKey:null,loadingDepPromise:null},we=new Map;function rt({key:_t}){const dt=Ya(b,U,_t);switch(we.set(_t,dt),W||(k(b,U,new Set(we.keys()),B),_(b,B)),dt.state){case"hasValue":return dt.contents;case"hasError":throw dt.contents;case"loading":throw pe.loadingDepKey=_t,pe.loadingDepPromise=dt.contents,dt.contents}throw se("Invalid Loadable state")}const lr=_t=>(...dt)=>{if(Z)throw se("Callbacks from getCallback() should only be called asynchronously after the selector is evalutated.  It can be used for selectors to return objects with callbacks that can work with Recoil state without a subscription.");return t==null&&fi(!1),rN(b,_t,dt,{node:t})};try{Y=r({get:rt,getCallback:lr}),Y=tN(Y)?rt(Y):Y,KL(Y)&&(Y.state==="hasError"&&(me=!0),Y=Y.contents),Re(Y)?Y=g(b,Y,U,we,B,pe).finally(ae):ae(),Y=Y instanceof Sw?Y.value:Y}catch(_t){Y=_t,Re(Y)?Y=S(b,Y,U,we,B,pe).finally(ae):(me=!0,ae())}return me?ye=Xa(Y):Re(Y)?ye=QL(Y):ye=Gu(Y),W=!1,ce(b,B,we),k(b,U,new Set(we.keys()),B),[ye,we]}function N(b,U){let B=U.atomValues.get(n);if(B!=null)return B;const J=new Set;try{B=s.get(Z=>(typeof Z!="string"&&fi(!1),Ya(b,U,Z).contents),{onNodeVisit:Z=>{Z.type==="branch"&&Z.nodeKey!==n&&J.add(Z.nodeKey)}})}catch(Z){throw se(`Problem with cache lookup for selector "${n}": ${Z.message}`)}if(B){var W;U.atomValues.set(n,B),k(b,U,J,(W=$(b))===null||W===void 0?void 0:W.executionID)}return B}function M(b,U){const B=N(b,U);if(B!=null)return re(b),B;const J=G(b,U);if(J!=null){var W;return((W=J.loadingLoadable)===null||W===void 0?void 0:W.state)==="loading"&&h(b,J.executionID),J.loadingLoadable}const Z=iN(),[ae,Y]=T(b,U,Z);return ae.state==="loading"?(X(b,Z,ae,Y,U),h(b,Z)):(re(b),I(U,ae,Y)),ae}function G(b,U){const B=W1([u.has(b)?[Oe(u.get(b))]:[],Gl(Zd(u,([W])=>W!==b),([,W])=>W)]);function J(W){for(const[Z,ae]of W)if(!Ya(b,U,Z).is(ae))return!0;return!1}for(const W of B){if(W.stateVersions.get(U.version)||!J(W.depValuesDiscoveredSoFarDuringAsyncWork))return W.stateVersions.set(U.version,!0),W;W.stateVersions.set(U.version,!1)}}function $(b){return u.get(b)}function X(b,U,B,J,W){u.set(b,{depValuesDiscoveredSoFarDuringAsyncWork:J,executionID:U,loadingLoadable:B,stateVersions:new Map([[W.version,!0]])})}function ce(b,U,B){if(w(b,U)){const J=$(b);J!=null&&(J.depValuesDiscoveredSoFarDuringAsyncWork=B)}}function re(b){u.delete(b)}function w(b,U){var B;return U===((B=$(b))===null||B===void 0?void 0:B.executionID)}function P(b){return Array.from(b.entries()).map(([U,B])=>[U,B.contents])}function I(b,U,B){b.atomValues.set(n,U);try{s.set(P(B),U)}catch(J){throw se(`Problem with setting cache for selector "${n}": ${J.message}`)}}function C(b){if(ii.includes(n)){const U=`Recoil selector has circular dependencies: ${ii.slice(ii.indexOf(n)).join(" → ")}`;return Xa(se(U))}ii.push(n);try{return b()}finally{ii.pop()}}function O(b,U){const B=U.atomValues.get(n);return B??s.get(J=>{var W;return typeof J!="string"&&fi(!1),(W=GL(b,U,J))===null||W===void 0?void 0:W.contents})}function A(b,U){return C(()=>M(b,U))}function D(b){b.atomValues.delete(n)}function z(b,U){t==null&&fi(!1);for(const J of a){var B;const W=eN(J);(B=W.clearCache)===null||B===void 0||B.call(W,b,U)}a.clear(),D(U),s.clear(),_m(b,t)}return i!=null?t=Sm({key:n,nodeType:"selector",peek:O,get:A,set:(U,B,J)=>{let W=!1;const Z=new Map;function ae({key:pe}){if(W)throw se("Recoil: Async selector sets are not currently supported.");const we=Ya(U,B,pe);if(we.state==="hasValue")return we.contents;if(we.state==="loading"){const rt=`Getting value of asynchronous atom or selector "${pe}" in a pending state while setting selector "${n}" is not yet supported.`;throw se(rt)}else throw we.contents}function Y(pe,we){if(W)throw se("Recoil: Async selector sets are not currently supported.");const rt=typeof we=="function"?we(ae(pe)):we;XL(U,B,pe.key,rt).forEach((_t,dt)=>Z.set(dt,_t))}function me(pe){Y(pe,JL)}const ye=i({set:Y,get:ae,reset:me},J);if(ye!==void 0)throw Re(ye)?se("Recoil: Async selector sets are not currently supported."):se("Recoil: selector set should be a void function.");return W=!0,Z},init:d,invalidate:D,clearCache:z,shouldDeleteConfigOnRelease:p,dangerouslyAllowMutability:e.dangerouslyAllowMutability,shouldRestoreFromSnapshots:!1,retainedBy:l}):t=Sm({key:n,nodeType:"selector",peek:O,get:A,init:d,invalidate:D,clearCache:z,shouldDeleteConfigOnRelease:p,dangerouslyAllowMutability:e.dangerouslyAllowMutability,shouldRestoreFromSnapshots:!1,retainedBy:l})}bw.value=e=>new Sw(e);var Po=bw;const{isLoadable:aN,loadableWithError:Xu,loadableWithPromise:Yu,loadableWithValue:qr}=_a,{WrappedValue:Ew}=gw,{peekNodeInfo:sN}=ir,{DEFAULT_VALUE:vr,DefaultValue:Mn,getConfigDeletionHandler:Cw,registerNode:lN,setConfigDeletionHandler:uN}=ft,{isRecoilValue:cN}=Oo,{getRecoilValueAsLoadable:fN,markRecoilValueModified:dN,setRecoilValue:bm,setRecoilValueLoadable:hN}=fn,{retainedByOptionWithDefault:pN}=Fr,ai=e=>e instanceof Ew?e.value:e;function vN(e){const{key:t,persistence_UNSTABLE:n}=e,r=pN(e.retainedBy_UNSTABLE);let o=0;function i(h){return Yu(h.then(g=>(a=qr(g),g)).catch(g=>{throw a=Xu(g),g}))}let a=Re(e.default)?i(e.default):aN(e.default)?e.default.state==="loading"?i(e.default.contents):e.default:qr(ai(e.default));a.contents;let s;const l=new Map;function u(h){return h}function c(h,g){const S=g.then(k=>{var T,N;return((N=((T=h.getState().nextTree)!==null&&T!==void 0?T:h.getState().currentTree).atomValues.get(t))===null||N===void 0?void 0:N.contents)===S&&bm(h,m,k),k}).catch(k=>{var T,N;throw((N=((T=h.getState().nextTree)!==null&&T!==void 0?T:h.getState().currentTree).atomValues.get(t))===null||N===void 0?void 0:N.contents)===S&&hN(h,m,Xu(k)),k});return S}function f(h,g,S){var k;o++;const T=()=>{var $;o--,($=l.get(h))===null||$===void 0||$.forEach(X=>X()),l.delete(h)};if(h.getState().knownAtoms.add(t),a.state==="loading"){const $=()=>{var X;((X=h.getState().nextTree)!==null&&X!==void 0?X:h.getState().currentTree).atomValues.has(t)||dN(h,m)};a.contents.finally($)}const N=(k=e.effects)!==null&&k!==void 0?k:e.effects_UNSTABLE;if(N!=null){let w=function(D){if(X&&D.key===t){const z=$;return z instanceof Mn?d(h,g):Re(z)?Yu(z.then(b=>b instanceof Mn?a.toPromise():b)):qr(z)}return fN(h,D)},P=function(D){return w(D).toPromise()},I=function(D){var z;const b=sN(h,(z=h.getState().nextTree)!==null&&z!==void 0?z:h.getState().currentTree,D.key);return X&&D.key===t&&!($ instanceof Mn)?{...b,isSet:!0,loadable:w(D)}:b},$=vr,X=!0,ce=!1,re=null;const C=D=>z=>{if(X){const b=w(m),U=b.state==="hasValue"?b.contents:vr;$=typeof z=="function"?z(U):z,Re($)&&($=$.then(B=>(re={effect:D,value:B},B)))}else{if(Re(z))throw se("Setting atoms to async values is not implemented.");typeof z!="function"&&(re={effect:D,value:ai(z)}),bm(h,m,typeof z=="function"?b=>{const U=ai(z(b));return re={effect:D,value:U},U}:ai(z))}},O=D=>()=>C(D)(vr),A=D=>z=>{var b;const{release:U}=h.subscribeToTransactions(B=>{var J;let{currentTree:W,previousTree:Z}=B.getState();Z||(Z=W);const ae=(J=W.atomValues.get(t))!==null&&J!==void 0?J:a;if(ae.state==="hasValue"){var Y,me,ye,pe;const we=ae.contents,rt=(Y=Z.atomValues.get(t))!==null&&Y!==void 0?Y:a,lr=rt.state==="hasValue"?rt.contents:vr;((me=re)===null||me===void 0?void 0:me.effect)!==D||((ye=re)===null||ye===void 0?void 0:ye.value)!==we?z(we,lr,!W.atomValues.has(t)):((pe=re)===null||pe===void 0?void 0:pe.effect)===D&&(re=null)}},t);l.set(h,[...(b=l.get(h))!==null&&b!==void 0?b:[],U])};for(const D of N)try{const z=D({node:m,storeID:h.storeID,parentStoreID_UNSTABLE:h.parentStoreID,trigger:S,setSelf:C(D),resetSelf:O(D),onSet:A(D),getPromise:P,getLoadable:w,getInfo_UNSTABLE:I});if(z!=null){var M;l.set(h,[...(M=l.get(h))!==null&&M!==void 0?M:[],z])}}catch(z){$=z,ce=!0}if(X=!1,!($ instanceof Mn)){var G;const D=ce?Xu($):Re($)?Yu(c(h,$)):qr(ai($));D.contents,g.atomValues.set(t,D),(G=h.getState().nextTree)===null||G===void 0||G.atomValues.set(t,D)}}return T}function d(h,g){var S,k;return(S=(k=g.atomValues.get(t))!==null&&k!==void 0?k:s)!==null&&S!==void 0?S:a}function p(h,g){if(g.atomValues.has(t))return Oe(g.atomValues.get(t));if(g.nonvalidatedAtoms.has(t)){if(s!=null)return s;if(n==null)return a;const S=g.nonvalidatedAtoms.get(t),k=n.validator(S,vr);return s=k instanceof Mn?a:qr(k),s}else return a}function v(){s=void 0}function y(h,g,S){if(g.atomValues.has(t)){const k=Oe(g.atomValues.get(t));if(k.state==="hasValue"&&S===k.contents)return new Map}else if(!g.nonvalidatedAtoms.has(t)&&S instanceof Mn)return new Map;return s=void 0,new Map().set(t,qr(S))}function _(){return Cw(t)!==void 0&&o<=0}const m=lN({key:t,nodeType:"atom",peek:d,get:p,set:y,init:f,invalidate:v,shouldDeleteConfigOnRelease:_,dangerouslyAllowMutability:e.dangerouslyAllowMutability,persistence_UNSTABLE:e.persistence_UNSTABLE?{type:e.persistence_UNSTABLE.type,backButton:e.persistence_UNSTABLE.backButton}:void 0,shouldRestoreFromSnapshots:!0,retainedBy:r});return m}function ch(e){const{...t}=e,n="default"in e?e.default:new Promise(()=>{});return cN(n)?mN({...t,default:n}):vN({...t,default:n})}function mN(e){const t=ch({...e,default:vr,persistence_UNSTABLE:e.persistence_UNSTABLE===void 0?void 0:{...e.persistence_UNSTABLE,validator:r=>r instanceof Mn?r:Oe(e.persistence_UNSTABLE).validator(r,vr)},effects:e.effects,effects_UNSTABLE:e.effects_UNSTABLE}),n=Po({key:`${e.key}__withFallback`,get:({get:r})=>{const o=r(t);return o instanceof Mn?e.default:o},set:({set:r},o)=>r(t,o),cachePolicy_UNSTABLE:{eviction:"most-recent"},dangerouslyAllowMutability:e.dangerouslyAllowMutability});return uN(n.key,Cw(e.key)),n}ch.value=e=>new Ew(e);var Rw=ch;class gN{constructor(t){var n;ie(this,"_map",void 0),ie(this,"_keyMapper",void 0),this._map=new Map,this._keyMapper=(n=t==null?void 0:t.mapKey)!==null&&n!==void 0?n:r=>r}size(){return this._map.size}has(t){return this._map.has(this._keyMapper(t))}get(t){return this._map.get(this._keyMapper(t))}set(t,n){this._map.set(this._keyMapper(t),n)}delete(t){this._map.delete(this._keyMapper(t))}clear(){this._map.clear()}}var yN={MapCache:gN},wN=yN.MapCache,SN=Object.freeze({__proto__:null,MapCache:wN});const{LRUCache:Em}=ww,{MapCache:_N}=SN,Za={equality:"reference",eviction:"none",maxSize:1/0};function bN({equality:e=Za.equality,eviction:t=Za.eviction,maxSize:n=Za.maxSize}=Za){const r=EN(e);return CN(t,n,r)}function EN(e){switch(e){case"reference":return t=>t;case"value":return t=>su(t)}throw se(`Unrecognized equality policy ${e}`)}function CN(e,t,n){switch(e){case"keep-all":return new _N({mapKey:n});case"lru":return new Em({mapKey:n,maxSize:Oe(t)});case"most-recent":return new Em({mapKey:n,maxSize:1})}throw se(`Unrecognized eviction policy ${e}`)}var Ow=bN;const{setConfigDeletionHandler:RN}=ft;function ON(e){var t,n;const r=Ow({equality:(t=(n=e.cachePolicyForParams_UNSTABLE)===null||n===void 0?void 0:n.equality)!==null&&t!==void 0?t:"value",eviction:"keep-all"});return o=>{var i,a;const s=r.get(o);if(s!=null)return s;const{cachePolicyForParams_UNSTABLE:l,...u}=e,c="default"in e?e.default:new Promise(()=>{}),f=Rw({...u,key:`${e.key}__${(i=su(o))!==null&&i!==void 0?i:"void"}`,default:typeof c=="function"?c(o):c,retainedBy_UNSTABLE:typeof e.retainedBy_UNSTABLE=="function"?e.retainedBy_UNSTABLE(o):e.retainedBy_UNSTABLE,effects:typeof e.effects=="function"?e.effects(o):typeof e.effects_UNSTABLE=="function"?e.effects_UNSTABLE(o):(a=e.effects)!==null&&a!==void 0?a:e.effects_UNSTABLE});return r.set(o,f),RN(f.key,()=>{r.delete(o)}),f}}var xN=ON;const{setConfigDeletionHandler:kN}=ft;let PN=0;function TN(e){var t,n;const r=Ow({equality:(t=(n=e.cachePolicyForParams_UNSTABLE)===null||n===void 0?void 0:n.equality)!==null&&t!==void 0?t:"value",eviction:"keep-all"});return o=>{var i;let a;try{a=r.get(o)}catch(d){throw se(`Problem with cache lookup for selector ${e.key}: ${d.message}`)}if(a!=null)return a;const s=`${e.key}__selectorFamily/${(i=su(o,{allowFunctions:!0}))!==null&&i!==void 0?i:"void"}/${PN++}`,l=d=>e.get(o)(d),u=e.cachePolicy_UNSTABLE,c=typeof e.retainedBy_UNSTABLE=="function"?e.retainedBy_UNSTABLE(o):e.retainedBy_UNSTABLE;let f;if(e.set!=null){const d=e.set;f=Po({key:s,get:l,set:(v,y)=>d(o)(v,y),cachePolicy_UNSTABLE:u,dangerouslyAllowMutability:e.dangerouslyAllowMutability,retainedBy_UNSTABLE:c})}else f=Po({key:s,get:l,cachePolicy_UNSTABLE:u,dangerouslyAllowMutability:e.dangerouslyAllowMutability,retainedBy_UNSTABLE:c});return r.set(o,f),kN(f.key,()=>{r.delete(o)}),f}}var ar=TN;const LN=ar({key:"__constant",get:e=>()=>e,cachePolicyForParams_UNSTABLE:{equality:"reference"}});function NN(e){return LN(e)}var AN=NN;const MN=ar({key:"__error",get:e=>()=>{throw se(e)},cachePolicyForParams_UNSTABLE:{equality:"reference"}});function IN(e){return MN(e)}var DN=IN;function $N(e){return e}var UN=$N;const{loadableWithError:xw,loadableWithPromise:kw,loadableWithValue:Pw}=_a;function lu(e,t){const n=Array(t.length).fill(void 0),r=Array(t.length).fill(void 0);for(const[o,i]of t.entries())try{n[o]=e(i)}catch(a){r[o]=a}return[n,r]}function FN(e){return e!=null&&!Re(e)}function uu(e){return Array.isArray(e)?e:Object.getOwnPropertyNames(e).map(t=>e[t])}function Rf(e,t){return Array.isArray(e)?t:Object.getOwnPropertyNames(e).reduce((n,r,o)=>({...n,[r]:t[o]}),{})}function vo(e,t,n){const r=n.map((o,i)=>o==null?Pw(t[i]):Re(o)?kw(o):xw(o));return Rf(e,r)}function jN(e,t){return t.map((n,r)=>n===void 0?e[r]:n)}const BN=ar({key:"__waitForNone",get:e=>({get:t})=>{const n=uu(e),[r,o]=lu(t,n);return vo(e,r,o)},dangerouslyAllowMutability:!0}),zN=ar({key:"__waitForAny",get:e=>({get:t})=>{const n=uu(e),[r,o]=lu(t,n);return o.some(i=>!Re(i))?vo(e,r,o):new Promise(i=>{for(const[a,s]of o.entries())Re(s)&&s.then(l=>{r[a]=l,o[a]=void 0,i(vo(e,r,o))}).catch(l=>{o[a]=l,i(vo(e,r,o))})})},dangerouslyAllowMutability:!0}),VN=ar({key:"__waitForAll",get:e=>({get:t})=>{const n=uu(e),[r,o]=lu(t,n);if(o.every(a=>a==null))return Rf(e,r);const i=o.find(FN);if(i!=null)throw i;return Promise.all(o).then(a=>Rf(e,jN(r,a)))},dangerouslyAllowMutability:!0}),WN=ar({key:"__waitForAllSettled",get:e=>({get:t})=>{const n=uu(e),[r,o]=lu(t,n);return o.every(i=>!Re(i))?vo(e,r,o):Promise.all(o.map((i,a)=>Re(i)?i.then(s=>{r[a]=s,o[a]=void 0}).catch(s=>{r[a]=void 0,o[a]=s}):null)).then(()=>vo(e,r,o))},dangerouslyAllowMutability:!0}),HN=ar({key:"__noWait",get:e=>({get:t})=>{try{return Po.value(Pw(t(e)))}catch(n){return Po.value(Re(n)?kw(n):xw(n))}},dangerouslyAllowMutability:!0});var qN={waitForNone:BN,waitForAny:zN,waitForAll:VN,waitForAllSettled:WN,noWait:HN};const{RecoilLoadable:KN}=_a,{DefaultValue:QN}=ft,{RecoilRoot:GN,useRecoilStoreID:XN}=On,{isRecoilValue:YN}=Oo,{retentionZone:JN}=Yl,{freshSnapshot:ZN}=nu,{useRecoilState:eA,useRecoilState_TRANSITION_SUPPORT_UNSTABLE:tA,useRecoilStateLoadable:nA,useRecoilValue:rA,useRecoilValue_TRANSITION_SUPPORT_UNSTABLE:oA,useRecoilValueLoadable:iA,useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE:aA,useResetRecoilState:sA,useSetRecoilState:lA}=yT,{useGotoRecoilSnapshot:uA,useRecoilSnapshot:cA,useRecoilTransactionObserver:fA}=dw,{useRecoilCallback:dA}=mw,{noWait:hA,waitForAll:pA,waitForAllSettled:vA,waitForAny:mA,waitForNone:gA}=qN;var fh={DefaultValue:QN,isRecoilValue:YN,RecoilLoadable:KN,RecoilEnv:m1,RecoilRoot:GN,useRecoilStoreID:XN,useRecoilBridgeAcrossReactRoots_UNSTABLE:HT,atom:Rw,selector:Po,atomFamily:xN,selectorFamily:ar,constSelector:AN,errorSelector:DN,readOnlySelector:UN,noWait:hA,waitForNone:gA,waitForAny:mA,waitForAll:pA,waitForAllSettled:vA,useRecoilValue:rA,useRecoilValueLoadable:iA,useRecoilState:eA,useRecoilStateLoadable:nA,useSetRecoilState:lA,useResetRecoilState:sA,useGetRecoilValueInfo_UNSTABLE:FT,useRecoilRefresher_UNSTABLE:SL,useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE:aA,useRecoilValue_TRANSITION_SUPPORT_UNSTABLE:oA,useRecoilState_TRANSITION_SUPPORT_UNSTABLE:tA,useRecoilCallback:dA,useRecoilTransaction_UNSTABLE:RL,useGotoRecoilSnapshot:uA,useRecoilSnapshot:cA,useRecoilTransactionObserver_UNSTABLE:fA,snapshot_UNSTABLE:ZN,useRetain:ih,retentionZone:JN},yA=fh.RecoilRoot,wA=fh.atom,u$=fh.useRecoilState;function SA(e,t){let n;return(...r)=>{n&&clearTimeout(n),n=setTimeout(()=>{e(...r)},t)}}function Tw(e){return e.replace(/\/$/,"")}function c$(e,t){let n=String(e);for(;n.length<t;)n="0"+n;return n}const _A={"Content-Type":"application/json"};function bA({secret:e}){const t={..._A};return e&&(t.Authorization=`Bearer ${e}`),t}function Lw(e,t,n){const r="?"+t.toString(),o=new URL(e);return o.protocol==="https:"?o.protocol="wss:":o.protocol="ws:",`${Tw(o.href)}${n}${r}`}function Ie({baseURL:e,secret:t}){const n=bA({secret:t});return{url:e,init:{headers:n}}}function dh(e,t){const{baseURL:n,secret:r}=e,o=new URLSearchParams({token:r});return Lw(n,o,t)}function f$(e,t){const{baseURL:n,secret:r,logLevel:o}=e,i=new URLSearchParams({token:r,level:o});return Lw(n,i,t)}const hh="/configs",EA="/configs/geo",CA="/cache/fakeip/flush",RA="/restart",OA="/upgrade";async function Nw(e){const{url:t,init:n}=Ie(e);return await fetch(t+hh,n)}function xA(e){return"socks-port"in e&&(e["socket-port"]=e["socks-port"]),e}async function kA(e,t){const{url:n,init:r}=Ie(e),o=JSON.stringify(xA(t));return await fetch(n+hh,{...r,body:o,method:"PATCH"})}async function PA(e){const{url:t,init:n}=Ie(e),r='{"path": "", "payload": ""}';return await fetch(t+hh+"?force=true",{...n,body:r,method:"PUT"})}async function TA(e){const{url:t,init:n}=Ie(e),r='{"path": "", "payload": ""}';return await fetch(t+EA,{...n,body:r,method:"POST"})}async function LA(e){const{url:t,init:n}=Ie(e),r='{"path": "", "payload": ""}';return await fetch(t+RA,{...n,body:r,method:"POST"})}async function NA(e){const{url:t,init:n}=Ie(e),r='{"path": "", "payload": ""}';return await fetch(t+OA,{...n,body:r,method:"POST"})}async function AA(e){const{url:t,init:n}=Ie(e);return await fetch(t+CA,{...n,method:"POST"})}function Aw(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Aw(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function Ar(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Aw(e))&&(r&&(r+=" "),r+=t);return r}function Of(){return Of=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Of.apply(this,arguments)}function MA(e,t){if(e==null)return{};var n=IA(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function IA(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}var ph=L.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=e.size,i=o===void 0?24:o,a=MA(e,["color","size"]);return V.createElement("svg",Of({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},a),V.createElement("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),V.createElement("line",{x1:"1",y1:"1",x2:"23",y2:"23"}))});ph.propTypes={color:xe.string,size:xe.oneOfType([xe.string,xe.number])};ph.displayName="EyeOff";const DA=ph;function xf(){return xf=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},xf.apply(this,arguments)}function $A(e,t){if(e==null)return{};var n=UA(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function UA(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}var vh=L.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=e.size,i=o===void 0?24:o,a=$A(e,["color","size"]);return V.createElement("svg",xf({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},a),V.createElement("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),V.createElement("circle",{cx:"12",cy:"12",r:"3"}))});vh.propTypes={color:xe.string,size:xe.oneOfType([xe.string,xe.number])};vh.displayName="Eye";const FA=vh;function kf(){return kf=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},kf.apply(this,arguments)}function jA(e,t){if(e==null)return{};var n=BA(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function BA(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}var mh=L.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=e.size,i=o===void 0?24:o,a=jA(e,["color","size"]);return V.createElement("svg",kf({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},a),V.createElement("path",{d:"M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"}))});mh.propTypes={color:xe.string,size:xe.oneOfType([xe.string,xe.number])};mh.displayName="GitHub";const zA=mh;function Pf(){return Pf=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pf.apply(this,arguments)}function VA(e,t){if(e==null)return{};var n=WA(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function WA(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}var gh=L.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=e.size,i=o===void 0?24:o,a=VA(e,["color","size"]);return V.createElement("svg",Pf({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},a),V.createElement("circle",{cx:"12",cy:"12",r:"10"}),V.createElement("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),V.createElement("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"}))});gh.propTypes={color:xe.string,size:xe.oneOfType([xe.string,xe.number])};gh.displayName="Info";const HA=gh;function Tf(){return Tf=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Tf.apply(this,arguments)}function qA(e,t){if(e==null)return{};var n=KA(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function KA(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}var yh=L.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=e.size,i=o===void 0?24:o,a=qA(e,["color","size"]);return V.createElement("svg",Tf({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},a),V.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),V.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))});yh.propTypes={color:xe.string,size:xe.oneOfType([xe.string,xe.number])};yh.displayName="X";const QA=yh,{useState:GA,useCallback:XA}=V;function YA(e=!1){const[t,n]=GA(e),r=XA(()=>n(o=>!o),[]);return[t,r]}const Mw="yacd.metacubex.one";function JA(){try{const e=localStorage.getItem(Mw);return e?JSON.parse(e):void 0}catch{return}}function jr(e){try{const t=JSON.stringify(e);localStorage.setItem(Mw,t)}catch{}}const Iw="/traffic",ZA=new TextDecoder("utf-8"),es=150,ra={labels:Array(es).fill(0),up:Array(es),down:Array(es),size:es,subscribers:[],appendData(e){this.up.shift(),this.down.shift(),this.labels.shift();const t=Date.now();this.up.push(e.up),this.down.push(e.down),this.labels.push(t),this.subscribers.forEach(n=>n(e))},subscribe(e){return this.subscribers.push(e),()=>{const t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}}};let ao=!1,ts="";function Lf(e){ra.appendData(JSON.parse(e))}function Dw(e){return e.read().then(({done:t,value:n})=>{const r=ZA.decode(n,{stream:!t});ts+=r;const o=ts.split(`
`),i=o[o.length-1];for(let a=0;a<o.length-1;a++)Lf(o[a]);if(t){Lf(i),ts="",console.log("GET /traffic streaming done"),ao=!1;return}else ts=i;return Dw(e)})}let ns;function wh(e){if(ao||ns===1)return ra;ns=1;const t=dh(e,Iw),n=new WebSocket(t);return n.addEventListener("error",function(r){ns=3}),n.addEventListener("close",function(r){ns=3,e3(e)}),n.addEventListener("message",function(r){Lf(r.data)}),ra}function e3(e){if(ao)return ra;ao=!0;const{url:t,init:n}=Ie(e);return fetch(t+Iw,n).then(r=>{if(r.ok){const o=r.body.getReader();Dw(o)}else ao=!1},r=>{console.log("fetch /traffic error",r),ao=!1}),ra}function Cm(e){return t=>{t(`openModal:${e}`,n=>{n.modals[e]=!0})}}function t3(e){return t=>{t(`closeModal:${e}`,n=>{n.modals[e]=!1})}}const n3={apiConfig:!1},d$=e=>e.configs.configs,r3=e=>e.configs.haveFetchedConfig,h$=e=>e.configs.configs["log-level"];function Br(e){return async(t,n)=>{let r;try{r=await Nw(e)}catch{t(Cm("apiConfig"));return}if(!r.ok){console.log("Error fetch configs",r.statusText),t(Cm("apiConfig"));return}const o=await r.json();t("store/configs#fetchConfigs",a=>{a.configs.configs=o}),r3(n())?wh(e):t(o3())}}function o3(){return e=>{e("store/configs#markHaveFetchedConfig",t=>{t.configs.haveFetchedConfig=!0})}}function p$(e,t){return async n=>{kA(e,t).then(r=>{r.ok===!1&&console.log("Error update configs",r.statusText)},r=>{throw console.log("Error update configs",r),r}).then(()=>{n(Br(e))}),n("storeConfigsOptimisticUpdateConfigs",r=>{r.configs.configs={...r.configs.configs,...t}})}}function v$(e){return async t=>{PA(e).then(n=>{n.ok===!1&&console.log("Error reload config file",n.statusText)},n=>{throw console.log("Error reload config file",n),n}).then(()=>{t(Br(e))})}}function m$(e){return async t=>{LA(e).then(n=>{n.ok===!1&&console.log("Error restart core",n.statusText)},n=>{throw console.log("Error restart core",n),n}).then(()=>{t(Br(e))})}}function g$(e){return async t=>{NA(e).then(n=>{n.ok===!1&&console.log("Error upgrade core",n.statusText)},n=>{throw console.log("Error upgrade core",n),n}).then(()=>{t(Br(e))})}}function y$(e){return async t=>{TA(e).then(n=>{n.ok===!1&&console.log("Error update geo databases file",n.statusText)},n=>{throw console.log("Error update geo databases file",n),n}).then(()=>{t(Br(e))})}}function w$(e){return async t=>{AA(e).then(n=>{n.ok===!1&&console.log("Error flush FakeIP pool",n.statusText)},n=>{throw console.log("Error flush FakeIP pool",n),n}).then(()=>{t(Br(e))})}}const i3={configs:{port:7890,"socks-port":7891,"mixed-port":0,"redir-port":0,"tproxy-port":0,"mitm-port":0,"allow-lan":!1,mode:"rule","log-level":"uninit",sniffing:!1,tun:{enable:!1,device:"",stack:"","dns-hijack":[],"auto-route":!1}},haveFetchedConfig:!1},sr=e=>{const t=e.app.selectedClashAPIConfigIndex;return e.app.clashAPIConfigs[t]},$w=e=>e.app.selectedClashAPIConfigIndex,Sh=e=>e.app.clashAPIConfigs,_h=e=>e.app.theme,Uw=e=>e.app.selectedChartStyleIndex,a3=e=>e.app.latencyTestUrl,S$=e=>e.app.collapsibleIsOpen,_$=e=>e.app.proxySortBy,b$=e=>e.app.hideUnavailableProxies,s3=e=>e.app.autoCloseOldConns,E$=e=>e.app.logStreamingPaused,l3=SA(jr,600);function bh(e,{baseURL:t,secret:n}){const r=Sh(e());for(let o=0;o<r.length;o++){const i=r[o];if(i.baseURL===t&&i.secret===n)return o}}function u3({baseURL:e,secret:t}){return async(n,r)=>{if(bh(r,{baseURL:e,secret:t}))return;const i={baseURL:e,secret:t,addedAt:Date.now()};n("addClashAPIConfig",a=>{a.app.clashAPIConfigs.push(i)}),jr(r().app)}}function c3({baseURL:e,secret:t}){return async(n,r)=>{const o=bh(r,{baseURL:e,secret:t});n("removeClashAPIConfig",i=>{i.app.clashAPIConfigs.splice(o,1)}),jr(r().app)}}function f3({baseURL:e,secret:t}){return async(n,r)=>{const o=bh(r,{baseURL:e,secret:t});$w(r())!==o&&n("selectClashAPIConfig",a=>{a.app.selectedClashAPIConfigIndex=o}),jr(r().app);try{window.location.reload()}catch{}}}const Ju=document.querySelector("html");function Fw(e="light"){e==="auto"?Ju.setAttribute("data-theme","auto"):e==="dark"?Ju.setAttribute("data-theme","dark"):Ju.setAttribute("data-theme","light")}function d3(e="auto"){return(t,n)=>{_h(n())!==e&&(Fw(e),t("storeSwitchTheme",o=>{o.app.theme=e}),jr(n().app))}}function h3(e){return(t,n)=>{t("appSelectChartStyleIndex",r=>{r.app.selectedChartStyleIndex=Number(e)}),jr(n().app)}}function Rm(e,t){return(n,r)=>{n("appUpdateAppConfig",o=>{o.app[e]=t}),jr(r().app)}}function p3(e,t,n){return(r,o)=>{r("updateCollapsibleIsOpen",i=>{i.app.collapsibleIsOpen[`${e}:${t}`]=n}),l3(o().app)}}var ag;const v3={baseURL:((ag=document.getElementById("app"))==null?void 0:ag.getAttribute("data-base-url"))??"http://127.0.0.1:9090",secret:"",addedAt:0},m3={selectedClashAPIConfigIndex:0,clashAPIConfigs:[v3],latencyTestUrl:"https://www.gstatic.com/generate_204",selectedChartStyleIndex:0,theme:"dark",collapsibleIsOpen:{},proxySortBy:"Natural",hideUnavailableProxies:!1,autoCloseOldConns:!1,logStreamingPaused:!1};function g3(){const{search:e}=window.location,t={};if(typeof e!="string"||e==="")return t;const n=e.replace(/^\?/,"").split("&");for(let r=0;r<n.length;r++){const[o,i]=n[r].split("=");t[o]=encodeURIComponent(i)}return t}function y3(){let e=JA();e={...m3,...e};const t=g3(),n=e.clashAPIConfigs[e.selectedClashAPIConfigIndex];if(n){const r=new URL(n.baseURL);t.hostname&&(t.hostname.indexOf("http")===0?r.href=decodeURIComponent(t.hostname):r.hostname=t.hostname),t.port&&(r.port=t.port),n.baseURL=Tw(r.href),t.secret&&(n.secret=t.secret)}return(t.theme==="dark"||t.theme==="light")&&(e.theme=t.theme),Fw(e.theme),e}const w3="_ul_1d6f2_1",S3="_li_1d6f2_10",_3="_close_1d6f2_28",b3="_eye_1d6f2_36",E3="_hasSecret_1d6f2_45",C3="_url_1d6f2_50",R3="_secret_1d6f2_54",O3="_btn_1d6f2_72",yn={ul:w3,li:S3,close:_3,eye:b3,hasSecret:E3,url:C3,secret:R3,btn:O3};function Wt(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map(function(o){return"'"+o+"'"}).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function To(e){return!!e&&!!e[gt]}function Mr(e){var t;return!!e&&(function(n){if(!n||typeof n!="object")return!1;var r=Object.getPrototypeOf(n);if(r===null)return!0;var o=Object.hasOwnProperty.call(r,"constructor")&&r.constructor;return o===Object||typeof o=="function"&&Function.toString.call(o)===M3}(e)||Array.isArray(e)||!!e[Am]||!!(!((t=e.constructor)===null||t===void 0)&&t[Am])||Eh(e)||Ch(e))}function oa(e,t,n){n===void 0&&(n=!1),Fo(e)===0?(n?Object.keys:Ph)(e).forEach(function(r){n&&typeof r=="symbol"||t(r,e[r],e)}):e.forEach(function(r,o){return t(o,r,e)})}function Fo(e){var t=e[gt];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:Eh(e)?2:Ch(e)?3:0}function Nf(e,t){return Fo(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function x3(e,t){return Fo(e)===2?e.get(t):e[t]}function jw(e,t,n){var r=Fo(e);r===2?e.set(t,n):r===3?e.add(n):e[t]=n}function k3(e,t){return e===t?e!==0||1/e==1/t:e!=e&&t!=t}function Eh(e){return N3&&e instanceof Map}function Ch(e){return A3&&e instanceof Set}function pr(e){return e.o||e.t}function Rh(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=I3(e);delete t[gt];for(var n=Ph(t),r=0;r<n.length;r++){var o=n[r],i=t[o];i.writable===!1&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(Object.getPrototypeOf(e),t)}function Oh(e,t){return t===void 0&&(t=!1),xh(e)||To(e)||!Mr(e)||(Fo(e)>1&&(e.set=e.add=e.clear=e.delete=P3),Object.freeze(e),t&&oa(e,function(n,r){return Oh(r,!0)},!0)),e}function P3(){Wt(2)}function xh(e){return e==null||typeof e!="object"||Object.isFrozen(e)}function ln(e){var t=D3[e];return t||Wt(18,e),t}function Om(){return ia}function Zu(e,t){t&&(ln("Patches"),e.u=[],e.s=[],e.v=t)}function wl(e){Af(e),e.p.forEach(T3),e.p=null}function Af(e){e===ia&&(ia=e.l)}function xm(e){return ia={p:[],l:ia,h:e,m:!0,_:0}}function T3(e){var t=e[gt];t.i===0||t.i===1?t.j():t.O=!0}function ec(e,t){t._=t.p.length;var n=t.p[0],r=e!==void 0&&e!==n;return t.h.g||ln("ES5").S(t,e,r),r?(n[gt].P&&(wl(t),Wt(4)),Mr(e)&&(e=Sl(t,e),t.l||_l(t,e)),t.u&&ln("Patches").M(n[gt].t,e,t.u,t.s)):e=Sl(t,n,[]),wl(t),t.u&&t.v(t.u,t.s),e!==Bw?e:void 0}function Sl(e,t,n){if(xh(t))return t;var r=t[gt];if(!r)return oa(t,function(s,l){return km(e,r,t,s,l,n)},!0),t;if(r.A!==e)return t;if(!r.P)return _l(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var o=r.i===4||r.i===5?r.o=Rh(r.k):r.o,i=o,a=!1;r.i===3&&(i=new Set(o),o.clear(),a=!0),oa(i,function(s,l){return km(e,r,o,s,l,n,a)}),_l(e,o,!1),n&&e.u&&ln("Patches").N(r,n,e.u,e.s)}return r.o}function km(e,t,n,r,o,i,a){if(To(o)){var s=Sl(e,o,i&&t&&t.i!==3&&!Nf(t.R,r)?i.concat(r):void 0);if(jw(n,r,s),!To(s))return;e.m=!1}else a&&n.add(o);if(Mr(o)&&!xh(o)){if(!e.h.D&&e._<1)return;Sl(e,o),t&&t.A.l||_l(e,o)}}function _l(e,t,n){n===void 0&&(n=!1),e.h.D&&e.m&&Oh(t,n)}function tc(e,t){var n=e[gt];return(n?pr(n):e)[t]}function Pm(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function Mf(e){e.P||(e.P=!0,e.l&&Mf(e.l))}function nc(e){e.o||(e.o=Rh(e.t))}function If(e,t,n){var r=Eh(t)?ln("MapSet").F(t,n):Ch(t)?ln("MapSet").T(t,n):e.g?function(o,i){var a=Array.isArray(o),s={i:a?1:0,A:i?i.A:Om(),P:!1,I:!1,R:{},l:i,t:o,k:null,o:null,j:null,C:!1},l=s,u=Df;a&&(l=[s],u=di);var c=Proxy.revocable(l,u),f=c.revoke,d=c.proxy;return s.k=d,s.j=f,d}(t,n):ln("ES5").J(t,n);return(n?n.A:Om()).p.push(r),r}function L3(e){return To(e)||Wt(22,e),function t(n){if(!Mr(n))return n;var r,o=n[gt],i=Fo(n);if(o){if(!o.P&&(o.i<4||!ln("ES5").K(o)))return o.t;o.I=!0,r=Tm(n,i),o.I=!1}else r=Tm(n,i);return oa(r,function(a,s){o&&x3(o.t,a)===s||jw(r,a,t(s))}),i===3?new Set(r):r}(e)}function Tm(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return Rh(e)}var Lm,ia,kh=typeof Symbol<"u"&&typeof Symbol("x")=="symbol",N3=typeof Map<"u",A3=typeof Set<"u",Nm=typeof Proxy<"u"&&Proxy.revocable!==void 0&&typeof Reflect<"u",Bw=kh?Symbol.for("immer-nothing"):((Lm={})["immer-nothing"]=!0,Lm),Am=kh?Symbol.for("immer-draftable"):"__$immer_draftable",gt=kh?Symbol.for("immer-state"):"__$immer_state",M3=""+Object.prototype.constructor,Ph=typeof Reflect<"u"&&Reflect.ownKeys?Reflect.ownKeys:Object.getOwnPropertySymbols!==void 0?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,I3=Object.getOwnPropertyDescriptors||function(e){var t={};return Ph(e).forEach(function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)}),t},D3={},Df={get:function(e,t){if(t===gt)return e;var n=pr(e);if(!Nf(n,t))return function(o,i,a){var s,l=Pm(i,a);return l?"value"in l?l.value:(s=l.get)===null||s===void 0?void 0:s.call(o.k):void 0}(e,n,t);var r=n[t];return e.I||!Mr(r)?r:r===tc(e.t,t)?(nc(e),e.o[t]=If(e.A.h,r,e)):r},has:function(e,t){return t in pr(e)},ownKeys:function(e){return Reflect.ownKeys(pr(e))},set:function(e,t,n){var r=Pm(pr(e),t);if(r!=null&&r.set)return r.set.call(e.k,n),!0;if(!e.P){var o=tc(pr(e),t),i=o==null?void 0:o[gt];if(i&&i.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(k3(n,o)&&(n!==void 0||Nf(e.t,t)))return!0;nc(e),Mf(e)}return e.o[t]===n&&(n!==void 0||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return tc(e.t,t)!==void 0||t in e.t?(e.R[t]=!1,nc(e),Mf(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=pr(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r&&{writable:!0,configurable:e.i!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty:function(){Wt(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){Wt(12)}},di={};oa(Df,function(e,t){di[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),di.deleteProperty=function(e,t){return di.set.call(this,e,t,void 0)},di.set=function(e,t,n){return Df.set.call(this,e[0],t,n,e[0])};var $3=function(){function e(n){var r=this;this.g=Nm,this.D=!0,this.produce=function(o,i,a){if(typeof o=="function"&&typeof i!="function"){var s=i;i=o;var l=r;return function(y){var _=this;y===void 0&&(y=s);for(var m=arguments.length,h=Array(m>1?m-1:0),g=1;g<m;g++)h[g-1]=arguments[g];return l.produce(y,function(S){var k;return(k=i).call.apply(k,[_,S].concat(h))})}}var u;if(typeof i!="function"&&Wt(6),a!==void 0&&typeof a!="function"&&Wt(7),Mr(o)){var c=xm(r),f=If(r,o,void 0),d=!0;try{u=i(f),d=!1}finally{d?wl(c):Af(c)}return typeof Promise<"u"&&u instanceof Promise?u.then(function(y){return Zu(c,a),ec(y,c)},function(y){throw wl(c),y}):(Zu(c,a),ec(u,c))}if(!o||typeof o!="object"){if((u=i(o))===void 0&&(u=o),u===Bw&&(u=void 0),r.D&&Oh(u,!0),a){var p=[],v=[];ln("Patches").M(o,u,p,v),a(p,v)}return u}Wt(21,o)},this.produceWithPatches=function(o,i){if(typeof o=="function")return function(u){for(var c=arguments.length,f=Array(c>1?c-1:0),d=1;d<c;d++)f[d-1]=arguments[d];return r.produceWithPatches(u,function(p){return o.apply(void 0,[p].concat(f))})};var a,s,l=r.produce(o,i,function(u,c){a=u,s=c});return typeof Promise<"u"&&l instanceof Promise?l.then(function(u){return[u,a,s]}):[l,a,s]},typeof(n==null?void 0:n.useProxies)=="boolean"&&this.setUseProxies(n.useProxies),typeof(n==null?void 0:n.autoFreeze)=="boolean"&&this.setAutoFreeze(n.autoFreeze)}var t=e.prototype;return t.createDraft=function(n){Mr(n)||Wt(8),To(n)&&(n=L3(n));var r=xm(this),o=If(this,n,void 0);return o[gt].C=!0,Af(r),o},t.finishDraft=function(n,r){var o=n&&n[gt],i=o.A;return Zu(i,r),ec(void 0,i)},t.setAutoFreeze=function(n){this.D=n},t.setUseProxies=function(n){n&&!Nm&&Wt(20),this.g=n},t.applyPatches=function(n,r){var o;for(o=r.length-1;o>=0;o--){var i=r[o];if(i.path.length===0&&i.op==="replace"){n=i.value;break}}o>-1&&(r=r.slice(o+1));var a=ln("Patches").$;return To(n)?a(n,r):this.produce(n,function(s){return a(s,r)})},e}(),yt=new $3,U3=yt.produce;yt.produceWithPatches.bind(yt);var F3=yt.setAutoFreeze.bind(yt);yt.setUseProxies.bind(yt);yt.applyPatches.bind(yt);yt.createDraft.bind(yt);yt.finishDraft.bind(yt);F3(!1);const{createContext:Th,memo:j3,useMemo:B3,useRef:z3,useEffect:V3,useCallback:Mm,useContext:$f,useState:W3}=V,zw=Th(null),Vw=Th(null),Ww=Th(null);function H3(){return $f(Ww)}function q3({initialState:e,actions:t={},children:n}){const r=z3(e),[o,i]=W3(e),a=Mm(()=>r.current,[]);V3(()=>{},[a]);const s=Mm((u,c)=>{if(typeof u=="function")return u(s,a);const f=U3(a(),c);f!==r.current&&(r.current=f,i(f))},[a]),l=B3(()=>Hw(t,s),[t,s]);return R(zw.Provider,{value:o,children:R(Vw.Provider,{value:s,children:R(Ww.Provider,{value:l,children:n})})})}function Jt(e){return t=>{const n=j3(t);function r(o){const i=$f(zw),a=$f(Vw),s=e(i,o),l={dispatch:a,...o,...s};return R(n,{...l})}return r}}function K3(e,t){return function(...n){return t(e.apply(this,n))}}function Hw(e,t){const n={};for(const r in e){const o=e[r];typeof o=="function"?n[r]=K3(o,t):typeof o=="object"&&(n[r]=Hw(o,t))}return n}const Q3=e=>({apiConfigs:Sh(e),selectedClashAPIConfigIndex:$w(e)}),G3=Jt(Q3)(X3);function X3({apiConfigs:e,selectedClashAPIConfigIndex:t}){const{app:{removeClashAPIConfig:n,selectClashAPIConfig:r}}=H3(),o=L.useCallback(a=>{n(a)},[n]),i=L.useCallback(a=>{r(a)},[r]);return R(Cr,{children:R("ul",{className:yn.ul,children:e.map((a,s)=>R("li",{className:Ar(yn.li,{[yn.hasSecret]:a.secret,[yn.isSelected]:s===t}),children:R(Y3,{disableRemove:s===t,baseURL:a.baseURL,secret:a.secret,onRemove:o,onSelect:i})},a.baseURL+a.secret))})})}function Y3({baseURL:e,secret:t,disableRemove:n,onRemove:r,onSelect:o}){const[i,a]=YA(),s=i?DA:FA,l=L.useCallback(u=>{u.stopPropagation()},[]);return le(Cr,{children:[R(Im,{disabled:n,onClick:()=>r({baseURL:e,secret:t}),className:yn.close,children:R(QA,{size:20})}),R("span",{className:yn.url,tabIndex:0,role:"button",onClick:()=>o({baseURL:e,secret:t}),onKeyUp:l,children:e}),R("span",{}),t?le(Cr,{children:[R("span",{className:yn.secret,children:i?t:"***"}),R(Im,{onClick:a,className:yn.eye,children:R(s,{size:20})})]}):null]})}function Im({children:e,onClick:t,className:n,disabled:r}){return R("button",{disabled:r,className:Ar(n,yn.btn),onClick:t,children:e})}const J3="_root_zwtea_1",Z3="_header_zwtea_5",e4="_icon_zwtea_10",t4="_body_zwtea_20",n4="_hostnamePort_zwtea_24",r4="_error_zwtea_36",o4="_footer_zwtea_42",ur={root:J3,header:Z3,icon:e4,body:t4,hostnamePort:n4,error:r4,footer:o4},i4="_btn_vsco8_4",a4="_minimal_vsco8_37",s4="_btnInternal_vsco8_54",l4="_btnStart_vsco8_61",u4="_loadingContainer_vsco8_67",Pi={btn:i4,minimal:a4,btnInternal:s4,btnStart:l4,loadingContainer:u4},c4="_sectionNameType_k6imc_4",f4="_loadingDot_k6imc_75",d4="_dot2_k6imc_1",h4="_dot1_k6imc_1",p4="_dot3_k6imc_1",qw={sectionNameType:c4,loadingDot:f4,dot2:d4,dot1:h4,dot3:p4};function C$({name:e,type:t}){return le("h2",{className:qw.sectionNameType,children:[R("span",{style:{marginRight:5},children:e}),R("span",{children:t})]})}function v4(){return R("span",{className:qw.loadingDot})}const{forwardRef:m4,useCallback:g4}=Tt;function y4(e,t){const{onClick:n,disabled:r=!1,isLoading:o,kind:i="primary",className:a,children:s,label:l,text:u,start:c,...f}=e,d={children:s,label:l,text:u,start:c},p=g4(y=>{o||n&&n(y)},[o,n]),v=Ar(Pi.btn,{[Pi.minimal]:i==="minimal"},a);return R("button",{className:v,ref:t,onClick:p,disabled:r,...f,children:o?le(Cr,{children:[R("span",{style:{display:"inline-flex",opacity:0},children:R(Dm,{...d})}),R("span",{className:Pi.loadingContainer,children:R(v4,{})})]}):R(Dm,{...d})})}function Dm({children:e,label:t,text:n,start:r}){return le("div",{className:Pi.btnInternal,children:[r&&R("span",{className:Pi.btnStart,children:typeof r=="function"?r():r}),e||t||n]})}const w4=m4(y4),S4="_root_1or8t_1",_4="_floatAbove_1or8t_32",$m={root:S4,floatAbove:_4},{useCallback:b4}=Tt;function Um({id:e,label:t,value:n,onChange:r,...o}){const i=b4(a=>r(a),[r]);return le("div",{className:$m.root,children:[R("input",{id:e,value:n,onChange:i,...o}),R("label",{htmlFor:e,className:$m.floatAbove,children:t})]})}const E4="_path_r8pm3_1",C4="_dash_r8pm3_1",R4={path:E4,dash:C4};function Lh({width:e=320,height:t=320,animate:n=!1,c0:r="#316eb5",c1:o="#f19500",line:i="#cccccc"}){const a=Ar({[R4.path]:n});return le("svg",{xmlns:"http://www.w3.org/2000/svg",version:"1.2",viewBox:"0 0 512 512",width:e,height:t,children:[R("path",{id:"Layer",className:a,fill:r,stroke:i,strokeLinecap:"round",strokeWidth:"4",d:"m280.8 182.4l119-108.3c1.9-1.7 4.3-2.7 6.8-2.4l39.5 4.1c2.1 0.3 3.9 2.2 3.9 4.4v251.1c0 2-1.5 3.9-3.5 4.4l-41.9 9c-0.5 0.3-1.2 0.3-1.9 0.3h-18.8c-2.4 0-4.4-2-4.4-4.4v-132.9c0-7.5-9-11.7-14.8-6.3l-59 53.4c-2.2 2.2-5.4 2.9-8.5 1.9-27.1-8-56.3-8-83.4 0-2.9 1-6.1 0.3-8.5-1.9l-59-53.4c-5.6-5.4-14.6-1.2-14.6 6.3v132.9c0 2.4-2.2 4.4-4.7 4.4h-18.7c-0.7 0-1.2 0-2-0.3l-41.6-9c-2-0.5-3.5-2.4-3.5-4.4v-251.1c0-2.2 1.8-4.1 3.9-4.4l39.5-4.1c2.5-0.3 4.9 0.7 6.9 2.4l115.7 105.3c2 1.7 4.6 2.5 7.1 2.2 15.3-2.2 31.4-1.9 46.5 0.8z"}),R("path",{id:"Layer",className:a,fill:r,stroke:i,strokeLinecap:"round",strokeWidth:"4",d:"m269.4 361.8l-7.1 13.4c-2.4 4.2-8.5 4.2-11 0l-7-13.4c-2.5-4.1 0.7-9.3 5.3-9h14.4c4.9 0 7.8 4.9 5.4 9z"}),R("path",{id:"Layer",className:a,fill:o,stroke:i,strokeLinecap:"round",strokeWidth:"4",d:"m160.7 362.5c3.6 0 6.8 3.2 6.8 6.9 0 3.6-3.2 6.5-6.8 6.5h-94.6c-3.6 0-6.8-2.9-6.8-6.5 0-3.7 3.2-6.9 6.8-6.9z"}),R("path",{id:"Layer",className:a,fill:o,stroke:i,strokeLinecap:"round",strokeWidth:"4",d:"m158.7 394.7c3.4-1 7.1 1 8.3 4.4 1 3.4-1 7.3-4.4 8.3l-92.8 31.7c-3.4 1.2-7.3-0.7-8.3-4.2-1.2-3.6 0.7-7.3 4.4-8.5z"}),R("path",{id:"Layer",className:a,fill:o,stroke:i,strokeLinecap:"round",strokeWidth:"4",d:"m446.1 426.4c3.4 1.2 5.3 4.9 4.3 8.5-1.2 3.5-4.8 5.4-8.2 4.2l-93.1-31.7c-3.5-1-5.4-4.9-4.2-8.3 1-3.4 4.9-5.4 8.3-4.4z"}),R("path",{id:"Layer",className:a,fill:o,stroke:i,strokeLinecap:"round",strokeWidth:"4",d:"m445.8 362.5c3.7 0 6.6 3.2 6.6 6.9 0 3.6-2.9 6.5-6.6 6.5h-94.8c-3.6 0-6.6-2.9-6.6-6.5 0-3.7 3-6.9 6.6-6.9z"})]})}const{useState:rc,useRef:Fm,useCallback:oc,useEffect:O4}=Tt,Kw=0,x4=e=>({apiConfig:sr(e)});function k4({dispatch:e}){const[t,n]=rc(""),[r,o]=rc(""),[i,a]=rc(""),s=Fm(!1),l=Fm(null),u=oc(p=>{s.current=!0,a("");const v=p.target,{name:y}=v,_=v.value;switch(y){case"baseURL":n(_);break;case"secret":o(_);break;default:throw new Error(`unknown input name ${y}`)}},[]),c=oc(()=>{let p=t;if(p){const v=t.substring(0,7);if(v.includes(":/")){if(v!=="http://"&&v!=="https:/")return[1,"Must starts with http:// or https://"]}else window.location.protocol&&(p=`${window.location.protocol}//${p}`)}P4({baseURL:p,secret:r}).then(v=>{v[0]!==Kw?a(v[1]):e(u3({baseURL:p,secret:r}))})},[t,r,e]),f=oc(p=>{p.target instanceof Element&&(!p.target.tagName||p.target.tagName.toUpperCase()!=="INPUT")||p.key==="Enter"&&c()},[c]),d=async()=>{(await fetch("/")).json().then(v=>{v.hello==="clash"&&n(window.location.origin)})};return O4(()=>{d()},[]),le("div",{className:ur.root,ref:l,onKeyDown:f,children:[R("div",{className:ur.header,children:R("div",{className:ur.icon,children:R(Lh,{width:160,height:160,stroke:"var(--stroke)"})})}),R("div",{className:ur.body,children:le("div",{className:ur.hostnamePort,children:[R(Um,{id:"baseURL",name:"baseURL",label:"API Base URL",type:"text",placeholder:"http://127.0.0.1:9090",value:t,onChange:u}),R(Um,{id:"secret",name:"secret",label:"Secret(optional)",value:r,type:"text",onChange:u})]})}),R("div",{className:ur.error,children:i||null}),R("div",{className:ur.footer,children:R(w4,{label:"Add",onClick:c})}),R("div",{style:{height:20}}),R(G3,{})]})}const Qw=Jt(x4)(k4);async function P4(e){try{new URL(e.baseURL)}catch{if(e.baseURL){const n=e.baseURL.substring(0,7);if(n!=="http://"&&n!=="https:/")return[1,"Must starts with http:// or https://"]}return[1,"Invalid URL"]}try{const t=await Nw(e);return t.status>399?[1,t.statusText]:[Kw]}catch{return[1,"Failed to connect"]}}async function Gw(e,t){let n={};try{const{url:r,init:o}=Ie(t),i=await fetch(r+e,o);i.ok&&(n=await i.json())}catch(r){console.log(`failed to fetch ${e}`,r)}return n}const T4="_root_ul0od_4",L4="_h1_ul0od_10",jm={root:T4,h1:L4};function N4({title:e}){return R("div",{className:jm.root,children:R("h1",{className:jm.h1,children:e})})}const Xw=V.memo(N4),A4="_root_10mcy_4",M4="_mono_10mcy_13",I4="_link_10mcy_17",ic={root:A4,mono:M4,link:I4};function Bm({name:e,link:t,version:n}){return le("div",{className:ic.root,children:[R("h2",{children:e}),le("p",{children:[R("span",{children:"Version "}),R("span",{className:ic.mono,children:n})]}),R("p",{children:le("a",{className:ic.link,href:t,target:"_blank",rel:"noopener noreferrer",children:[R(zA,{size:20}),R("span",{children:"Source"})]})})]})}function D4(e){const{data:t}=Y0(["/version",e.apiConfig],()=>Gw("/version",e.apiConfig));return le(Cr,{children:[R(Xw,{title:"About"}),t&&t.version?R(Bm,{name:t.meta&&t.premium?"sing-box":t.meta?"Clash.Meta":"Clash",version:t.version,link:t.meta&&t.premium?"https://github.com/SagerNet/sing-box":t.meta?"https://github.com/MetaCubeX/Clash.Meta":"https://github.com/Dreamacro/clash"}):null,R(Bm,{name:"Yacd",version:"0.3.7",link:"https://github.com/metacubex/yacd"})]})}const $4=e=>({apiConfig:sr(e)}),U4=Jt($4)(D4);/**
  * @reach/utils v0.18.0
  *
  * Copyright (c) 2018-2022, React Training LLC
  *
  * This source code is licensed under the MIT license found in the
  * LICENSE.md file in the root directory of this source tree.
  *
  * @license MIT
  */function Yw(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function Pn(e,t){return n=>{if(e&&e(n),!n.defaultPrevented)return t(n)}}function zm(e){return typeof e=="boolean"}function Uf(e){return!!(e&&{}.toString.call(e)=="[object Function]")}function F4(e,t){if(e!=null)if(Uf(e))e(t);else try{e.current=t}catch{throw new Error(`Cannot assign value "${t}" to ref "${e}"`)}}function Jw(...e){return L.useCallback(t=>{for(let n of e)F4(n,t)},e)}function Nh(e){return Yw()?e?e.ownerDocument:document:null}function j4(e){let t=Nh(e),n=t.defaultView||window;return t?{width:t.documentElement.clientWidth??n.innerWidth,height:t.documentElement.clientHeight??n.innerHeight}:{width:0,height:0}}function Zw(...e){return e.filter(t=>t!=null).join("--")}function B4(){let[,e]=L.useState(Object.create(null));return L.useCallback(()=>{e(Object.create(null))},[])}var Ti=Yw()?L.useLayoutEffect:L.useEffect,ac=!1,z4=0;function Vm(){return++z4}var Wm=Tt["useId".toString()];function V4(e){if(Wm!==void 0){let o=Wm();return e??o}let t=e??(ac?Vm():null),[n,r]=L.useState(t);return Ti(()=>{n===null&&r(Vm())},[]),L.useEffect(()=>{ac===!1&&(ac=!0)},[]),e??n??void 0}var W4=({children:e,type:t="reach-portal",containerRef:n})=>{let r=L.useRef(null),o=L.useRef(null),i=B4();return L.useEffect(()=>{n!=null&&(typeof n!="object"||!("current"in n)?console.warn("@reach/portal: Invalid value passed to the `containerRef` of a `Portal`. The portal will be appended to the document body, but if you want to attach it to another DOM node you must pass a valid React ref object to `containerRef`."):n.current==null&&console.warn("@reach/portal: A ref was passed to the `containerRef` prop of a `Portal`, but no DOM node was attached to it. Be sure to pass the ref to a DOM component.\n\nIf you are forwarding the ref from another component, be sure to use the React.forwardRef API. See https://reactjs.org/docs/forwarding-refs.html."))},[n]),Ti(()=>{if(!r.current)return;let a=r.current.ownerDocument,s=(n==null?void 0:n.current)||a.body;return o.current=a==null?void 0:a.createElement(t),s.appendChild(o.current),i(),()=>{o.current&&s&&s.removeChild(o.current)}},[t,i,n]),o.current?mo.createPortal(e,o.current):L.createElement("span",{ref:r})},eS=({unstable_skipInitialRender:e,...t})=>{let[n,r]=L.useState(!1);return L.useEffect(()=>{e&&r(!0)},[e]),e&&!n?null:L.createElement(W4,{...t})};eS.displayName="Portal";var tS=L.forwardRef(function({as:t="span",style:n={},...r},o){return L.createElement(t,{ref:o,style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal",...n},...r})});tS.displayName="VisuallyHidden";var H4=["bottom","height","left","right","top","width"],q4=function(t,n){return t===void 0&&(t={}),n===void 0&&(n={}),H4.some(function(r){return t[r]!==n[r]})},Tn=new Map,nS,K4=function e(){var t=[];Tn.forEach(function(n,r){var o=r.getBoundingClientRect();q4(o,n.rect)&&(n.rect=o,t.push(n))}),t.forEach(function(n){n.callbacks.forEach(function(r){return r(n.rect)})}),nS=window.requestAnimationFrame(e)};function Q4(e,t){return{observe:function(){var r=Tn.size===0;Tn.has(e)?Tn.get(e).callbacks.push(t):Tn.set(e,{rect:void 0,hasRectChanged:!1,callbacks:[t]}),r&&K4()},unobserve:function(){var r=Tn.get(e);if(r){var o=r.callbacks.indexOf(t);o>=0&&r.callbacks.splice(o,1),r.callbacks.length||Tn.delete(e),Tn.size||cancelAnimationFrame(nS)}}}}function rS(e,t,n){let r,o;zm(t)?r=t:(r=(t==null?void 0:t.observe)??!0,o=t==null?void 0:t.onChange),Uf(n)&&(o=n),L.useEffect(()=>{zm(t)&&console.warn("Passing `observe` as the second argument to `useRect` is deprecated and will be removed in a future version of Reach UI. Instead, you can pass an object of options with an `observe` property as the second argument (`useRect(ref, { observe })`).\nSee https://reach.tech/rect#userect-observe")},[t]),L.useEffect(()=>{Uf(n)&&console.warn("Passing `onChange` as the third argument to `useRect` is deprecated and will be removed in a future version of Reach UI. Instead, you can pass an object of options with an `onChange` property as the second argument (`useRect(ref, { onChange })`).\nSee https://reach.tech/rect#userect-onchange")},[n]);let[i,a]=L.useState(e.current),s=L.useRef(!1),l=L.useRef(!1),[u,c]=L.useState(null),f=L.useRef(o);return Ti(()=>{f.current=o,e.current!==i&&a(e.current)}),Ti(()=>{i&&!s.current&&(s.current=!0,c(i.getBoundingClientRect()))},[i]),Ti(()=>{if(!r)return;let d=i;if(l.current||(l.current=!0,d=e.current),!d){console.warn("You need to place the ref");return}let p=Q4(d,v=>{var y;(y=f.current)==null||y.call(f,v),c(v)});return p.observe(),()=>{p.unobserve()}},[r,i,e]),u}var G4=100,X4=500,Ff={initial:"IDLE",states:{IDLE:{enter:sc,on:{MOUSE_ENTER:"FOCUSED",FOCUS:"VISIBLE"}},FOCUSED:{enter:Z4,leave:eM,on:{MOUSE_MOVE:"FOCUSED",MOUSE_LEAVE:"IDLE",MOUSE_DOWN:"DISMISSED",BLUR:"IDLE",REST:"VISIBLE"}},VISIBLE:{on:{FOCUS:"FOCUSED",MOUSE_ENTER:"FOCUSED",MOUSE_LEAVE:"LEAVING_VISIBLE",BLUR:"LEAVING_VISIBLE",MOUSE_DOWN:"DISMISSED",SELECT_WITH_KEYBOARD:"DISMISSED",GLOBAL_MOUSE_MOVE:"LEAVING_VISIBLE"}},LEAVING_VISIBLE:{enter:tM,leave:()=>{nM(),sc()},on:{MOUSE_ENTER:"VISIBLE",FOCUS:"VISIBLE",TIME_COMPLETE:"IDLE"}},DISMISSED:{leave:()=>{sc()},on:{MOUSE_LEAVE:"IDLE",BLUR:"IDLE"}}}},kt={value:Ff.initial,context:{id:null}},Rs=[];function Y4(e){return Rs.push(e),()=>{Rs.splice(Rs.indexOf(e),1)}}function J4(){Rs.forEach(e=>e(kt))}var jf;function Z4(){window.clearTimeout(jf),jf=window.setTimeout(()=>{Bt({type:"REST"})},G4)}function eM(){window.clearTimeout(jf)}var Bf;function tM(){window.clearTimeout(Bf),Bf=window.setTimeout(()=>Bt({type:"TIME_COMPLETE"}),X4)}function nM(){window.clearTimeout(Bf)}function sc(){kt.context.id=null}function rM({id:e,onPointerEnter:t,onPointerMove:n,onPointerLeave:r,onPointerDown:o,onMouseEnter:i,onMouseMove:a,onMouseLeave:s,onMouseDown:l,onFocus:u,onBlur:c,onKeyDown:f,disabled:d,ref:p,DEBUG_STYLE:v}={}){let y=String(V4(e)),[_,m]=L.useState(v?!0:Hm(y,!0)),h=L.useRef(null),g=Jw(p,h),S=rS(h,{observe:_});L.useEffect(()=>Y4(()=>{m(Hm(y))}),[y]),L.useEffect(()=>{let I=Nh(h.current);function C(O){(O.key==="Escape"||O.key==="Esc")&&kt.value==="VISIBLE"&&Bt({type:"SELECT_WITH_KEYBOARD"})}return I.addEventListener("keydown",C),()=>I.removeEventListener("keydown",C)},[]),sM({disabled:d,isVisible:_,ref:h});function k(I,C){return typeof window<"u"&&"PointerEvent"in window?I:Pn(I,C)}function T(I){return function(O){O.pointerType==="mouse"&&I(O)}}function N(){Bt({type:"MOUSE_ENTER",id:y})}function M(){Bt({type:"MOUSE_MOVE",id:y})}function G(){Bt({type:"MOUSE_LEAVE"})}function $(){kt.context.id===y&&Bt({type:"MOUSE_DOWN"})}function X(){window.__REACH_DISABLE_TOOLTIPS||Bt({type:"FOCUS",id:y})}function ce(){kt.context.id===y&&Bt({type:"BLUR"})}function re(I){(I.key==="Enter"||I.key===" ")&&Bt({type:"SELECT_WITH_KEYBOARD"})}return[{"aria-describedby":_?Zw("tooltip",y):void 0,"data-state":_?"tooltip-visible":"tooltip-hidden","data-reach-tooltip-trigger":"",ref:g,onPointerEnter:Pn(t,T(N)),onPointerMove:Pn(n,T(M)),onPointerLeave:Pn(r,T(G)),onPointerDown:Pn(o,T($)),onMouseEnter:k(i,N),onMouseMove:k(a,M),onMouseLeave:k(s,G),onMouseDown:k(l,$),onFocus:Pn(u,X),onBlur:Pn(c,ce),onKeyDown:Pn(f,re)},{id:y,triggerRect:S,isVisible:_},_]}var Ah=L.forwardRef(function({children:e,label:t,ariaLabel:n,id:r,DEBUG_STYLE:o,...i},a){let s=L.Children.only(e);L.useEffect(()=>{n&&console.warn("The `ariaLabel prop is deprecated and will be removed from @reach/tooltip in a future version of Reach UI. Please use `aria-label` instead.")},[n]);let[l,u]=rM({id:r,onPointerEnter:s.props.onPointerEnter,onPointerMove:s.props.onPointerMove,onPointerLeave:s.props.onPointerLeave,onPointerDown:s.props.onPointerDown,onMouseEnter:s.props.onMouseEnter,onMouseMove:s.props.onMouseMove,onMouseLeave:s.props.onMouseLeave,onMouseDown:s.props.onMouseDown,onFocus:s.props.onFocus,onBlur:s.props.onBlur,onKeyDown:s.props.onKeyDown,disabled:s.props.disabled,ref:s.ref,DEBUG_STYLE:o});return L.createElement(L.Fragment,null,L.cloneElement(s,l),L.createElement(oS,{ref:a,label:t,"aria-label":n,...u,...i}))});Ah.displayName="Tooltip";var oS=L.forwardRef(function({label:t,ariaLabel:n,isVisible:r,id:o,...i},a){return r?L.createElement(eS,null,L.createElement(iS,{ref:a,label:t,"aria-label":n,isVisible:r,...i,id:Zw("tooltip",String(o))})):null});oS.displayName="TooltipPopup";var iS=L.forwardRef(function({ariaLabel:t,"aria-label":n,as:r="div",id:o,isVisible:i,label:a,position:s=aM,style:l,triggerRect:u,...c},f){let d=(n||t)!=null,p=L.useRef(null),v=Jw(f,p),y=rS(p,{observe:i});return L.createElement(L.Fragment,null,L.createElement(r,{role:d?void 0:"tooltip",...c,ref:v,"data-reach-tooltip":"",id:d?void 0:o,style:{...l,...oM(s,u,y)}},a),d&&L.createElement(tS,{role:"tooltip",id:o},n||t))});iS.displayName="TooltipContent";function oM(e,t,n){return n?e(t,n):{visibility:"hidden"}}var iM=8,aM=(e,t,n=iM)=>{let{width:r,height:o}=j4();if(!e||!t)return{};let i={top:e.top-t.height<0,right:r<e.left+t.width,bottom:o<e.bottom+t.height+n,left:e.left-t.width<0},a=i.right&&!i.left,s=i.bottom&&!i.top;return{left:a?`${e.right-t.width+window.pageXOffset}px`:`${e.left+window.pageXOffset}px`,top:s?`${e.top-n-t.height+window.pageYOffset}px`:`${e.top+n+e.height+window.pageYOffset}px`}};function sM({disabled:e,isVisible:t,ref:n}){L.useEffect(()=>{if(!(typeof window<"u"&&"PointerEvent"in window)||!e||!t)return;let r=Nh(n.current);function o(i){t&&(i.target instanceof Element&&i.target.closest("[data-reach-tooltip-trigger][data-state='tooltip-visible']")||Bt({type:"GLOBAL_MOUSE_MOVE"}))}return r.addEventListener("mousemove",o),()=>{r.removeEventListener("mousemove",o)}},[e,t,n])}function Bt(e){let{value:t,context:n,changed:r}=lM(kt,e);r&&(kt={value:t,context:n},J4())}function lM(e,t){let n=Ff.states[e.value],r=n&&n.on&&n.on[t.type];if(!r)return{...e,changed:!1};n&&n.leave&&n.leave(e.context,t);const{type:o,...i}=t;let a={...kt.context,...i},s=typeof r=="string"?r:r.target,l=Ff.states[s];return l&&l.enter&&l.enter(e.context,t),{value:s,context:a,changed:!0}}function Hm(e,t){return kt.context.id===e&&(t?kt.value==="VISIBLE":kt.value==="VISIBLE"||kt.value==="LEAVING_VISIBLE")}function uM(e){let t={};const n={},r={};function o(l="default"){return n[l]=e(l).then(u=>{delete n[l],t[l]=u}).catch(u=>{r[l]=u}),n[l]}function i(l="default"){t[l]!==void 0||n[l]||o(l)}function a(l="default"){if(t[l]!==void 0)return t[l];throw r[l]?r[l]:n[l]?n[l]:o(l)}function s(l){l?delete t[l]:t={}}return{preload:i,read:a,clear:s}}const Mh=uM(()=>Ot(()=>import("./index-6d3641b0.js"),[],import.meta.url)),cM="_iconWrapper_1rpjb_1",fM="_themeSwitchContainer_1rpjb_21",qm={iconWrapper:cM,themeSwitchContainer:fM};function dM({theme:e,dispatch:t}){const{t:n}=No(),r=L.useMemo(()=>{switch(e){case"dark":return R(Km,{});case"auto":return R(pM,{});case"light":return R(hM,{});default:return console.assert(!1,"Unknown theme"),R(Km,{})}},[e]),o=L.useCallback(i=>t(d3(i.target.value)),[t]);return R(Ah,{label:n("switch_theme"),"aria-label":"switch theme",children:le("div",{className:qm.themeSwitchContainer,children:[R("span",{className:qm.iconWrapper,children:r}),le("select",{onChange:o,children:[R("option",{value:"auto",children:"Auto"}),R("option",{value:"dark",children:"Dark"}),R("option",{value:"light",children:"Light"})]})]})})}function Km(){const t=Mh.read().motion;return R("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:R(t.path,{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z",initial:{rotate:-30},animate:{rotate:0},transition:{duration:.7}})})}function hM(){const t=Mh.read().motion;return le("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[R("circle",{cx:"12",cy:"12",r:"5"}),le(t.g,{initial:{scale:.7},animate:{scale:1},transition:{duration:.5},children:[R("line",{x1:"12",y1:"1",x2:"12",y2:"3"}),R("line",{x1:"12",y1:"21",x2:"12",y2:"23"}),R("line",{x1:"4.22",y1:"4.22",x2:"5.64",y2:"5.64"}),R("line",{x1:"18.36",y1:"18.36",x2:"19.78",y2:"19.78"}),R("line",{x1:"1",y1:"12",x2:"3",y2:"12"}),R("line",{x1:"21",y1:"12",x2:"23",y2:"12"}),R("line",{x1:"4.22",y1:"19.78",x2:"5.64",y2:"18.36"}),R("line",{x1:"18.36",y1:"5.64",x2:"19.78",y2:"4.22"})]})]})}function pM(){const t=Mh.read().motion;return le("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[R("circle",{cx:"12",cy:"12",r:"11"}),R("clipPath",{id:"cut-off-bottom",children:R(t.rect,{x:"12",y:"0",width:"12",height:"24",initial:{rotate:-30},animate:{rotate:0},transition:{duration:.7}})}),R("circle",{cx:"12",cy:"12",r:"6",clipPath:"url(#cut-off-bottom)",fill:"currentColor"})]})}const vM=e=>({theme:_h(e)}),aS=Jt(vM)(dM),zf=0,Vf={[zf]:{message:"Browser not supported!",detail:'This browser does not support "fetch", please choose another one.'},default:{message:`出错了!
 请尝试清理缓存和Cookie后重试`}};function mM(e){const{code:t}=e;return typeof t=="number"?Vf[t]:Vf.default}const gM="_content_b98hm_1",yM="_container_b98hm_16",wM="_overlay_b98hm_22",SM="_fixed_b98hm_26",rs={content:gM,container:yM,overlay:wM,fixed:SM},_M="_overlay_fy74n_1",bM="_content_fy74n_14",Qm={overlay:_M,content:bM};function EM({isOpen:e,onRequestClose:t,className:n,overlayClassName:r,children:o,...i}){const a=Ar(n,Qm.content),s=Ar(r,Qm.overlay);return R(B0,{isOpen:e,onRequestClose:t,className:a,overlayClassName:s,...i,children:o})}const CM=L.memo(EM),{useCallback:RM,useEffect:OM}=Tt;function xM({dispatch:e,apiConfig:t,modals:n}){if(!window.fetch){const{detail:o}=Vf[zf],i=new Error(o);throw i.code=zf,i}const r=RM(()=>{e(t3("apiConfig"))},[e]);return OM(()=>{e(Br(t))},[e,t]),le(CM,{isOpen:n.apiConfig,className:rs.content,overlayClassName:rs.overlay,shouldCloseOnOverlayClick:!1,shouldCloseOnEsc:!1,onRequestClose:r,children:[R("div",{className:rs.container,children:R(Qw,{})}),R("div",{className:rs.fixed,children:R(aS,{})})]})}const kM=e=>({modals:e.modals,apiConfig:sr(e)}),PM=Jt(kM)(xM),TM="_root_16avz_1",LM="_yacd_16avz_14",NM="_link_16avz_23",lc={root:TM,yacd:LM,link:NM};function AM({width:e=24,height:t=24}={}){return R("svg",{xmlns:"http://www.w3.org/2000/svg",width:e,height:t,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:R("path",{d:"M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"})})}const MM="https://github.com/metacubex/yacd";function IM({message:e,detail:t}){return le("div",{className:lc.root,children:[R("div",{className:lc.yacd,children:R(Lh,{width:150,height:150})}),e?R("h1",{children:e}):null,t?R("p",{children:t}):null,R("p",{children:le("a",{className:lc.link,href:MM,children:[R(AM,{width:16,height:16}),"metacubex/yacd"]})})]})}class DM extends L.Component{constructor(){super(...arguments);Bh(this,"state",{error:null})}static getDerivedStateFromError(n){return{error:n}}render(){if(this.state.error){const{message:n,detail:r}=mM(this.state.error);return R(IM,{message:n,detail:r})}else return this.props.children}}const $M="_root_1ddes_4",UM="_chart_1ddes_13",Gm={root:$M,chart:UM},FM="_loading_wpm96_1",jM="_spinner_wpm96_9",BM="_rotate_wpm96_1",Xm={loading:FM,spinner:jM,rotate:BM},sS=({height:e})=>{const t=e?{height:e}:{};return R("div",{className:Xm.loading,style:t,children:R("div",{className:Xm.spinner})})},lS="/memory",zM=new TextDecoder("utf-8"),os=150,aa={labels:Array(os).fill(0),inuse:Array(os),oslimit:Array(os),size:os,subscribers:[],appendData(e){this.inuse.shift(),this.oslimit.shift(),this.labels.shift();const t=Date.now();this.inuse.push(e.inuse),this.oslimit.push(e.oslimit),this.labels.push(t),this.subscribers.forEach(n=>n(e))},subscribe(e){return this.subscribers.push(e),()=>{const t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}}};let so=!1,is="";function Wf(e){aa.appendData(JSON.parse(e))}function uS(e){return e.read().then(({done:t,value:n})=>{const r=zM.decode(n,{stream:!t});is+=r;const o=is.split(`
`),i=o[o.length-1];for(let a=0;a<o.length-1;a++)Wf(o[a]);if(t){Wf(i),is="",console.log("GET /memory streaming done"),so=!1;return}else is=i;return uS(e)})}let as;function VM(e){if(so||as===1)return aa;as=1;const t=dh(e,lS),n=new WebSocket(t);return n.addEventListener("error",function(r){as=3}),n.addEventListener("close",function(r){as=3,WM(e)}),n.addEventListener("message",function(r){Wf(r.data)}),aa}function WM(e){if(so)return aa;so=!0;const{url:t,init:n}=Ie(e);return fetch(t+lS,n).then(r=>{if(r.ok){const o=r.body.getReader();uS(o)}else so=!1},r=>{console.log("fetch /memory error",r),so=!1}),aa}var Ih=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var r,o,i;if(Array.isArray(t)){if(r=t.length,r!=n.length)return!1;for(o=r;o--!==0;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(i=Object.keys(t),r=i.length,r!==Object.keys(n).length)return!1;for(o=r;o--!==0;)if(!Object.prototype.hasOwnProperty.call(n,i[o]))return!1;for(o=r;o--!==0;){var a=i[o];if(!e(t[a],n[a]))return!1}return!0}return t!==t&&n!==n};function Ym(e,t,n,r=0,o=!1){for(const a of t)if(Ih(n,a.args)){if(o)return;if(a.error)throw a.error;if(a.response)return a.response;throw a.promise}const i={args:n,promise:e(...n).then(a=>i.response=a??!0).catch(a=>i.error=a??"unknown error").then(()=>{r>0&&setTimeout(()=>{const a=t.indexOf(i);a!==-1&&t.splice(a,1)},r)})};if(t.push(i),!o)throw i.promise}function HM(e,...t){if(t===void 0||t.length===0)e.splice(0,e.length);else{const n=e.find(r=>Ih(t,r.args));if(n){const r=e.indexOf(n);r!==-1&&e.splice(r,1)}}}function cS(e,t=0){const n=[];return{read:(...r)=>Ym(e,n,r,t),preload:(...r)=>void Ym(e,n,r,t,!0),clear:(...r)=>HM(n,...r),peek:(...r)=>{var o;return(o=n.find(i=>Ih(r,i.args)))==null?void 0:o.response}}}const Jm=["B","KB","MB","GB","TB","PB","EB","ZB","YB"];function Er(e){if(e<1e3)return e+" B";const t=Math.min(Math.floor(Math.log10(e)/3),Jm.length-1);e=Number((e/Math.pow(1e3,t)).toPrecision(3));const n=Jm[t];return e+" "+n}const qM=cS(()=>Ot(()=>import("./chart-lib-9fcc76b8.js"),[],import.meta.url)),Zm={borderWidth:1,pointRadius:0,tension:.2,fill:!0},KM={responsive:!0,maintainAspectRatio:!0,plugins:{legend:{labels:{boxWidth:20}}},scales:{x:{display:!1,type:"category"},y:{type:"linear",display:!0,grid:{display:!0,color:"#555",drawTicks:!1},border:{dash:[3,6]},ticks:{maxTicksLimit:5,callback(e){return Er(e)+"/s "}}}}},eg=[{down:{backgroundColor:"rgba(81, 168, 221, 0.5)",borderColor:"rgb(81, 168, 221)"},up:{backgroundColor:"rgba(219, 77, 109, 0.5)",borderColor:"rgb(219, 77, 109)"}},{up:{backgroundColor:"rgba(245,78,162,0.6)",borderColor:"rgba(245,78,162,1)"},down:{backgroundColor:"rgba(123,59,140,0.6)",borderColor:"rgba(66,33,142,1)"}},{up:{backgroundColor:"rgba(94, 175, 223, 0.3)",borderColor:"rgb(94, 175, 223)"},down:{backgroundColor:"rgba(139, 227, 195, 0.3)",borderColor:"rgb(139, 227, 195)"}},{up:{backgroundColor:"rgba(242, 174, 62, 0.3)",borderColor:"rgb(242, 174, 62)"},down:{backgroundColor:"rgba(69, 154, 248, 0.3)",borderColor:"rgb(69, 154, 248)"}}],QM=cS(()=>Ot(()=>import("./chart-lib-9fcc76b8.js"),[],import.meta.url)),GM={borderWidth:1,pointRadius:0,tension:.2,fill:!0},fS={responsive:!0,maintainAspectRatio:!0,plugins:{legend:{labels:{boxWidth:20}}},scales:{x:{display:!1,type:"category"},y:{type:"linear",display:!0,grid:{display:!0,color:"#555",drawTicks:!1},border:{dash:[3,6]},ticks:{maxTicksLimit:3,callback(e){return Er(e)}}}}},XM=[{inuse:{backgroundColor:"rgba(81, 168, 221, 0.5)",borderColor:"rgb(81, 168, 221)"}},{inuse:{backgroundColor:"rgba(245,78,162,0.6)",borderColor:"rgba(245,78,162,1)"}},{inuse:{backgroundColor:"rgba(94, 175, 223, 0.3)",borderColor:"rgb(94, 175, 223)"}},{inuse:{backgroundColor:"rgba(242, 174, 62, 0.3)",borderColor:"rgb(242, 174, 62)"}}],{useEffect:dS}=V;function YM(e,t,n,r,o={}){dS(()=>{const i=document.getElementById(t).getContext("2d"),a={...KM,...o},s=new e(i,{type:"line",data:n,options:a}),l=r&&r.subscribe(()=>s.update());return()=>{l&&l(),s.destroy()}},[e,t,n,r,o])}function JM(e,t,n,r,o={}){dS(()=>{const i=document.getElementById(t).getContext("2d"),a={...fS,...o},s=new e(i,{type:"line",data:n,options:a}),l=r&&r.subscribe(()=>s.update());return()=>{l&&l(),s.destroy()}},[e,t,n,r,o])}const ZM="_TrafficChart_13afo_1",eI={TrafficChart:ZM},{useMemo:tI}=Tt,nI={justifySelf:"center",position:"relative",width:"100%",height:"100%"},rI={width:"100%",height:"100%",padding:"10px",borderRadius:"10px"},oI=e=>({apiConfig:sr(e),selectedChartStyleIndex:Uw(e)}),iI=Jt(oI)(aI);function aI({apiConfig:e,selectedChartStyleIndex:t}){const n=QM.read(),r=VM(e),{t:o}=No(),i=tI(()=>({labels:r.labels,datasets:[{...GM,...fS,...XM[t].inuse,label:o("Memory"),data:r.inuse}]}),[r,t,o]);return JM(n.Chart,"MemoryChart",i,r),R("div",{style:nI,children:R("canvas",{id:"MemoryChart",style:rI,className:eI.TrafficChart})})}const sI="_TrafficChart_13afo_1",lI={TrafficChart:sI},{useMemo:uI}=Tt,cI={justifySelf:"center",position:"relative",width:"100%",height:"100%"},fI={width:"100%",height:"100%",padding:"10px",borderRadius:"10px"},dI=e=>({apiConfig:sr(e),selectedChartStyleIndex:Uw(e)}),hI=Jt(dI)(pI);function pI({apiConfig:e,selectedChartStyleIndex:t}){const n=qM.read(),r=wh(e),{t:o}=No(),i=uI(()=>({labels:r.labels,datasets:[{...Zm,...eg[t].up,label:o("Up"),data:r.up},{...Zm,...eg[t].down,label:o("Down"),data:r.down}]}),[r,t,o]);return YM(n.Chart,"trafficChart",i,r),R("div",{style:cI,children:R("canvas",{id:"trafficChart",style:fI,className:lI.TrafficChart})})}const cu="/connections",Bn=[];function vI(e){let t;try{t=JSON.parse(e),t.connections.forEach(n=>{let r=n.metadata;r.process==null&&r.processPath!=null&&(r.process=r.processPath.replace(/^.*[/\\](.*)$/,"$1"))})}catch{console.log("JSON.parse error",JSON.parse(e))}Bn.forEach(n=>n.listner(t))}let ss;function mI(e,t,n){if(ss===1&&t)return tg({listner:t,onClose:n});ss=1;const r=dh(e,cu),o=new WebSocket(r);if(o.addEventListener("error",()=>{ss=3,Bn.forEach(i=>i.onClose()),Bn.length=0}),o.addEventListener("close",()=>{ss=3,Bn.forEach(i=>i.onClose()),Bn.length=0}),o.addEventListener("message",i=>vI(i.data)),t)return tg({listner:t,onClose:n})}function tg(e){return Bn.push(e),function(){const n=Bn.indexOf(e);Bn.splice(n,1)}}async function R$(e){const{url:t,init:n}=Ie(e);return await fetch(t+cu,{...n,method:"DELETE"})}async function gI(e){const{url:t,init:n}=Ie(e);return await fetch(t+cu,{...n})}async function yI(e,t){const{url:n,init:r}=Ie(e),o=`${n}${cu}/${t}`;return await fetch(o,{...r,method:"DELETE"})}const wI="_TrafficNow_w4nk9_2",SI="_sec_w4nk9_35",cr={TrafficNow:wI,sec:SI},{useState:hS,useEffect:pS,useCallback:_I}=Tt,bI=e=>({apiConfig:sr(e)}),EI=Jt(bI)(CI);function CI({apiConfig:e}){const{t}=No(),{upStr:n,downStr:r}=RI(e),{upTotal:o,dlTotal:i,connNumber:a,mUsage:s}=OI(e);return le("div",{className:cr.TrafficNow,children:[le("div",{className:cr.sec,children:[R("div",{children:t("Upload")}),R("div",{children:n})]}),le("div",{className:cr.sec,children:[R("div",{children:t("Download")}),R("div",{children:r})]}),le("div",{className:cr.sec,children:[R("div",{children:t("Upload Total")}),R("div",{children:o})]}),le("div",{className:cr.sec,children:[R("div",{children:t("Download Total")}),R("div",{children:i})]}),le("div",{className:cr.sec,children:[R("div",{children:t("Active Connections")}),R("div",{children:a})]}),le("div",{className:cr.sec,children:[R("div",{children:t("Memory Usage")}),R("div",{children:s})]})]})}function RI(e){const[t,n]=hS({upStr:"0 B/s",downStr:"0 B/s"});return pS(()=>wh(e).subscribe(r=>n({upStr:Er(r.up)+"/s",downStr:Er(r.down)+"/s"})),[e]),t}function OI(e){const[t,n]=hS({upTotal:"0 B",dlTotal:"0 B",connNumber:0,mUsage:"0 B"}),r=_I(({downloadTotal:o,uploadTotal:i,connections:a,memory:s})=>{n({upTotal:Er(i),dlTotal:Er(o),connNumber:a.length,mUsage:Er(s)})},[n]);return pS(()=>mI(e,r),[e,r]),t}function xI(){const{t:e}=No();return le("div",{children:[R(Xw,{title:e("Overview")}),le("div",{className:Gm.root,children:[R("div",{children:R(EI,{})}),R("div",{className:Gm.chart,children:le(L.Suspense,{fallback:R(sS,{height:"200px"}),children:[R(hI,{}),R(iI,{})]})})]})]})}const kI="_lo_pmly2_1",PI={lo:kI};function TI(){return R("div",{className:PI.lo,children:R(Lh,{width:280,height:280,animate:!0,c0:"transparent",c1:"#646464"})})}const LI=e=>({apiConfig:sr(e),apiConfigs:Sh(e)});function NI({apiConfig:e,apiConfigs:t}){return L.useEffect(()=>{let n="yacd";if(t.length>1)try{n=`${new URL(e.baseURL).host} - yacd`}catch{}document.title=n}),R(Cr,{})}const AI=Jt(LI)(NI);var vS={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},ng=V.createContext&&V.createContext(vS),Jn=globalThis&&globalThis.__assign||function(){return Jn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},Jn.apply(this,arguments)},MI=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function mS(e){return e&&e.map(function(t,n){return V.createElement(t.tag,Jn({key:n},t.attr),mS(t.child))})}function jo(e){return function(t){return V.createElement(II,Jn({attr:Jn({},e.attr)},t),mS(e.child))}}function II(e){var t=function(n){var r=e.attr,o=e.size,i=e.title,a=MI(e,["attr","size","title"]),s=o||n.size||"1em",l;return n.className&&(l=n.className),e.className&&(l=(l?l+" ":"")+e.className),V.createElement("svg",Jn({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,a,{className:l,style:Jn(Jn({color:e.color||n.color},n.style),e.style),height:s,width:s,xmlns:"http://www.w3.org/2000/svg"}),i&&V.createElement("title",null,i),e.children)};return ng!==void 0?V.createElement(ng.Consumer,null,function(n){return t(n)}):t(vS)}function DI(e){return jo({tag:"svg",attr:{version:"1",viewBox:"0 0 48 48",enableBackground:"new 0 0 48 48"},child:[{tag:"polygon",attr:{fill:"#3F51B5",points:"42,37 6,37 6,25 16,10 30,17 42,6"}},{tag:"polygon",attr:{fill:"#00BCD4",points:"42,42 6,42 6,32 16,24 30,26 42,17"}}]})(e)}function $I(e){return jo({tag:"svg",attr:{version:"1",viewBox:"0 0 48 48",enableBackground:"new 0 0 48 48"},child:[{tag:"polygon",attr:{fill:"#90CAF9",points:"40,45 8,45 8,3 30,3 40,13"}},{tag:"polygon",attr:{fill:"#E1F5FE",points:"38.5,14 29,14 29,4.5"}},{tag:"g",attr:{fill:"#1976D2"},child:[{tag:"rect",attr:{x:"16",y:"21",width:"17",height:"2"}},{tag:"rect",attr:{x:"16",y:"25",width:"13",height:"2"}},{tag:"rect",attr:{x:"16",y:"29",width:"17",height:"2"}},{tag:"rect",attr:{x:"16",y:"33",width:"13",height:"2"}}]}]})(e)}function UI(e){return jo({tag:"svg",attr:{version:"1",viewBox:"0 0 48 48",enableBackground:"new 0 0 48 48"},child:[{tag:"path",attr:{fill:"#7CB342",d:"M24,4C13,4,4,13,4,24s9,20,20,20s20-9,20-20S35,4,24,4z"}},{tag:"path",attr:{fill:"#0277BD",d:"M45,24c0,11.7-9.5,21-21,21S3,35.7,3,24S12.3,3,24,3S45,12.3,45,24z M23.8,33.7c0-0.4-0.2-0.6-0.6-0.8 c-1.3-0.4-2.5-0.4-3.6-1.5c-0.2-0.4-0.2-0.8-0.4-1.3c-0.4-0.4-1.5-0.6-2.1-0.8c-0.8,0-1.7,0-2.7,0c-0.4,0-1.1,0-1.5,0 c-0.6-0.2-1.1-1.1-1.5-1.7c0-0.2,0-0.6-0.4-0.6c-0.4-0.2-0.8,0.2-1.3,0c-0.2-0.2-0.2-0.4-0.2-0.6c0-0.6,0.4-1.3,0.8-1.7 c0.6-0.4,1.3,0.2,1.9,0.2c0.2,0,0.2,0,0.4,0.2c0.6,0.2,0.8,1,0.8,1.7c0,0.2,0,0.4,0,0.4c0,0.2,0.2,0.2,0.4,0.2 c0.2-1.1,0.2-2.1,0.4-3.2c0-1.3,1.3-2.5,2.3-2.9c0.4-0.2,0.6,0.2,1.1,0c1.3-0.4,4.4-1.7,3.8-3.4c-0.4-1.5-1.7-2.9-3.4-2.7 c-0.4,0.2-0.6,0.4-1,0.6c-0.6,0.4-1.9,1.7-2.5,1.7c-1.1-0.2-1.1-1.7-0.8-2.3c0.2-0.8,2.1-3.6,3.4-3.1c0.2,0.2,0.6,0.6,0.8,0.8 c0.4,0.2,1.1,0.2,1.7,0.2c0.2,0,0.4,0,0.6-0.2c0.2-0.2,0.2-0.2,0.2-0.4c0-0.6-0.6-1.3-1-1.7c-0.4-0.4-1.1-0.8-1.7-1.1 c-2.1-0.6-5.5,0.2-7.1,1.7s-2.9,4-3.8,6.1c-0.4,1.3-0.8,2.9-1,4.4c-0.2,1-0.4,1.9,0.2,2.9c0.6,1.3,1.9,2.5,3.2,3.4 c0.8,0.6,2.5,0.6,3.4,1.7c0.6,0.8,0.4,1.9,0.4,2.9c0,1.3,0.8,2.3,1.3,3.4c0.2,0.6,0.4,1.5,0.6,2.1c0,0.2,0.2,1.5,0.2,1.7 c1.3,0.6,2.3,1.3,3.8,1.7c0.2,0,1-1.3,1-1.5c0.6-0.6,1.1-1.5,1.7-1.9c0.4-0.2,0.8-0.4,1.3-0.8c0.4-0.4,0.6-1.3,0.8-1.9 C23.8,35.1,24,34.3,23.8,33.7z M24.2,14.3c0.2,0,0.4-0.2,0.8-0.4c0.6-0.4,1.3-1.1,1.9-1.5c0.6-0.4,1.3-1.1,1.7-1.5 c0.6-0.4,1.1-1.3,1.3-1.9c0.2-0.4,0.8-1.3,0.6-1.9c-0.2-0.4-1.3-0.6-1.7-0.8c-1.7-0.4-3.1-0.6-4.8-0.6c-0.6,0-1.5,0.2-1.7,0.8 c-0.2,1.1,0.6,0.8,1.5,1.1c0,0,0.2,1.7,0.2,1.9c0.2,1-0.4,1.7-0.4,2.7c0,0.6,0,1.7,0.4,2.1L24.2,14.3z M41.8,29 c0.2-0.4,0.2-1.1,0.4-1.5c0.2-1,0.2-2.1,0.2-3.1c0-2.1-0.2-4.2-0.8-6.1c-0.4-0.6-0.6-1.3-0.8-1.9c-0.4-1.1-1-2.1-1.9-2.9 c-0.8-1.1-1.9-4-3.8-3.1c-0.6,0.2-1,1-1.5,1.5c-0.4,0.6-0.8,1.3-1.3,1.9c-0.2,0.2-0.4,0.6-0.2,0.8c0,0.2,0.2,0.2,0.4,0.2 c0.4,0.2,0.6,0.2,1,0.4c0.2,0,0.4,0.2,0.2,0.4c0,0,0,0.2-0.2,0.2c-1,1.1-2.1,1.9-3.1,2.9c-0.2,0.2-0.4,0.6-0.4,0.8 c0,0.2,0.2,0.2,0.2,0.4c0,0.2-0.2,0.2-0.4,0.4c-0.4,0.2-0.8,0.4-1.1,0.6c-0.2,0.4,0,1.1-0.2,1.5c-0.2,1.1-0.8,1.9-1.3,2.9 c-0.4,0.6-0.6,1.3-1,1.9c0,0.8-0.2,1.5,0.2,2.1c1,1.5,2.9,0.6,4.4,1.3c0.4,0.2,0.8,0.2,1.1,0.6c0.6,0.6,0.6,1.7,0.8,2.3 c0.2,0.8,0.4,1.7,0.8,2.5c0.2,1,0.6,2.1,0.8,2.9c1.9-1.5,3.6-3.1,4.8-5.2C40.6,32.4,41.2,30.7,41.8,29z"}}]})(e)}function FI(e){return jo({tag:"svg",attr:{version:"1",viewBox:"0 0 48 48",enableBackground:"new 0 0 48 48"},child:[{tag:"g",attr:{fill:"#1976D2"},child:[{tag:"path",attr:{d:"M38,13h-3c-5.5,0-10,4.5-10,10s4.5,10,10,10h3c5.5,0,10-4.5,10-10S43.5,13,38,13z M38,29h-3 c-3.3,0-6-2.7-6-6s2.7-6,6-6h3c3.3,0,6,2.7,6,6S41.3,29,38,29z"}},{tag:"path",attr:{d:"M13,13h-3C4.5,13,0,17.5,0,23s4.5,10,10,10h3c5.5,0,10-4.5,10-10S18.5,13,13,13z M13,29h-3 c-3.3,0-6-2.7-6-6s2.7-6,6-6h3c3.3,0,6,2.7,6,6S16.3,29,13,29z"}}]},{tag:"path",attr:{fill:"#42A5F5",d:"M33,21H15c-1.1,0-2,0.9-2,2s0.9,2,2,2h18c1.1,0,2-0.9,2-2S34.1,21,33,21z"}}]})(e)}function jI(e){return jo({tag:"svg",attr:{version:"1",viewBox:"0 0 48 48",enableBackground:"new 0 0 48 48"},child:[{tag:"rect",attr:{x:"16.7",y:"1.6",transform:"matrix(-.707 -.707 .707 -.707 24 57.941)",fill:"#FFA000",width:"14.6",height:"44.8"}},{tag:"g",attr:{fill:"#9E6400"},child:[{tag:"rect",attr:{x:"17.9",y:"20.2",transform:"matrix(-.707 -.707 .707 -.707 21.177 51.125)",width:"6.6",height:"2"}},{tag:"rect",attr:{x:"22.3",y:"15.2",transform:"matrix(-.707 -.707 .707 -.707 29.833 44.71)",width:"3.7",height:"2"}},{tag:"rect",attr:{x:"25.9",y:"12.2",transform:"matrix(-.707 -.707 .707 -.707 40.49 43.125)",width:"6.6",height:"2"}},{tag:"rect",attr:{x:"31.2",y:"6.3",transform:"matrix(.707 -.707 .707 .707 3.643 25.147)",width:"2",height:"3.7"}},{tag:"rect",attr:{x:"6.3",y:"31.2",transform:"matrix(-.707 -.707 .707 -.707 -8.794 60.71)",width:"3.7",height:"2"}},{tag:"rect",attr:{x:"9.9",y:"28.2",transform:"matrix(-.707 -.707 .707 -.707 1.863 59.125)",width:"6.6",height:"2"}},{tag:"rect",attr:{x:"14.3",y:"23.2",transform:"matrix(-.707 -.707 .707 -.707 10.52 52.71)",width:"3.7",height:"2"}}]}]})(e)}function BI(e){return jo({tag:"svg",attr:{version:"1",viewBox:"0 0 48 48",enableBackground:"new 0 0 48 48"},child:[{tag:"path",attr:{fill:"#607D8B",d:"M39.6,27.2c0.1-0.7,0.2-1.4,0.2-2.2s-0.1-1.5-0.2-2.2l4.5-3.2c0.4-0.3,0.6-0.9,0.3-1.4L40,10.8 c-0.3-0.5-0.8-0.7-1.3-0.4l-5,2.3c-1.2-0.9-2.4-1.6-3.8-2.2l-0.5-5.5c-0.1-0.5-0.5-0.9-1-0.9h-8.6c-0.5,0-1,0.4-1,0.9l-0.5,5.5 c-1.4,0.6-2.7,1.3-3.8,2.2l-5-2.3c-0.5-0.2-1.1,0-1.3,0.4l-4.3,7.4c-0.3,0.5-0.1,1.1,0.3,1.4l4.5,3.2c-0.1,0.7-0.2,1.4-0.2,2.2 s0.1,1.5,0.2,2.2L4,30.4c-0.4,0.3-0.6,0.9-0.3,1.4L8,39.2c0.3,0.5,0.8,0.7,1.3,0.4l5-2.3c1.2,0.9,2.4,1.6,3.8,2.2l0.5,5.5 c0.1,0.5,0.5,0.9,1,0.9h8.6c0.5,0,1-0.4,1-0.9l0.5-5.5c1.4-0.6,2.7-1.3,3.8-2.2l5,2.3c0.5,0.2,1.1,0,1.3-0.4l4.3-7.4 c0.3-0.5,0.1-1.1-0.3-1.4L39.6,27.2z M24,35c-5.5,0-10-4.5-10-10c0-5.5,4.5-10,10-10c5.5,0,10,4.5,10,10C34,30.5,29.5,35,24,35z"}},{tag:"path",attr:{fill:"#455A64",d:"M24,13c-6.6,0-12,5.4-12,12c0,6.6,5.4,12,12,12s12-5.4,12-12C36,18.4,30.6,13,24,13z M24,30 c-2.8,0-5-2.2-5-5c0-2.8,2.2-5,5-5s5,2.2,5,5C29,27.8,26.8,30,24,30z"}}]})(e)}const zI="_root_hmmtd_4",VI="_logo_meta_hmmtd_10",WI="_logo_singbox_hmmtd_24",HI="_rows_hmmtd_39",qI="_row_hmmtd_39",KI="_rowActive_hmmtd_76",QI="_label_hmmtd_87",GI="_footer_hmmtd_103",XI="_iconWrapper_hmmtd_115",vn={root:zI,logo_meta:VI,logo_singbox:WI,rows:HI,row:qI,rowActive:KI,label:QI,footer:GI,iconWrapper:XI},YI={activity:DI,globe:UI,command:jI,file:$I,settings:BI,link:FI},JI=L.memo(function({isActive:t,to:n,iconId:r,labelText:o}){const i=YI[r],a=Ar(vn.row,t?vn.rowActive:null);return le(u1,{to:n,className:a,children:[R(i,{}),R("div",{className:vn.label,children:o})]})}),ZI=[{to:"/",iconId:"activity",labelText:"Overview"},{to:"/proxies",iconId:"globe",labelText:"Proxies"},{to:"/rules",iconId:"command",labelText:"Rules"},{to:"/connections",iconId:"link",labelText:"Conns"},{to:"/configs",iconId:"settings",labelText:"Config"},{to:"/logs",iconId:"file",labelText:"Logs"}],eD=e=>({apiConfig:sr(e)}),tD=Jt(eD)(nD);function nD(e){const{t}=No(),n=wa(),{data:r}=Y0(["/version",e.apiConfig],()=>Gw("/version",e.apiConfig));return le("div",{className:vn.root,children:[R("div",{className:r.meta&&r.premium?vn.logo_singbox:vn.logo_meta}),R("div",{className:vn.rows,children:ZI.map(({to:o,iconId:i,labelText:a})=>R(JI,{to:o,isActive:n.pathname===o,iconId:i,labelText:t(a)},o))}),le("div",{className:vn.footer,children:[R(aS,{}),R(Ah,{label:t("about"),children:R(u1,{to:"/about",className:vn.iconWrapper,children:R(HA,{size:20})})})]})]})}const rD="_input_12jxq_1",O$={input:rD};function Hf(){return Hf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Hf.apply(this,arguments)}var oD=V.createElement("svg",{viewBox:"-2 -5 14 20",height:"100%",width:"100%",style:{position:"absolute",top:0}},V.createElement("path",{d:"M9.9 2.12L7.78 0 4.95 2.828 2.12 0 0 2.12l2.83 2.83L0 7.776 2.123 9.9 4.95 7.07 7.78 9.9 9.9 7.776 7.072 4.95 9.9 2.12",fill:"#fff",fillRule:"evenodd"})),iD=V.createElement("svg",{height:"100%",width:"100%",viewBox:"-2 -5 17 21",style:{position:"absolute",top:0}},V.createElement("path",{d:"M11.264 0L5.26 6.004 2.103 2.847 0 4.95l5.26 5.26 8.108-8.107L11.264 0",fill:"#fff",fillRule:"evenodd"}));function rg(e){if(e.length===7)return e;for(var t="#",n=1;n<4;n+=1)t+=e[n]+e[n];return t}function og(e,t,n,r,o){return function(i,a,s,l,u){var c=(i-s)/(a-s);if(c===0)return l;if(c===1)return u;for(var f="#",d=1;d<6;d+=2){var p=parseInt(l.substr(d,2),16),v=parseInt(u.substr(d,2),16),y=Math.round((1-c)*p+c*v).toString(16);y.length===1&&(y="0"+y),f+=y}return f}(e,t,n,rg(r),rg(o))}var Os=function(e){function t(n){e.call(this,n);var r=n.height,o=n.width,i=n.checked;this.t=n.handleDiameter||r-2,this.i=Math.max(o-r,o-(r+this.t)/2),this.o=Math.max(0,(r-this.t)/2),this.state={h:i?this.i:this.o},this.l=0,this.u=0,this.p=this.p.bind(this),this.v=this.v.bind(this),this.g=this.g.bind(this),this.k=this.k.bind(this),this.m=this.m.bind(this),this.M=this.M.bind(this),this.T=this.T.bind(this),this.$=this.$.bind(this),this.C=this.C.bind(this),this.D=this.D.bind(this),this.O=this.O.bind(this),this.S=this.S.bind(this)}return e&&(t.__proto__=e),(t.prototype=Object.create(e&&e.prototype)).constructor=t,t.prototype.componentDidMount=function(){this.W=!0},t.prototype.componentDidUpdate=function(n){n.checked!==this.props.checked&&this.setState({h:this.props.checked?this.i:this.o})},t.prototype.componentWillUnmount=function(){this.W=!1},t.prototype.I=function(n){this.H.focus(),this.setState({R:n,j:!0,B:Date.now()})},t.prototype.L=function(n){var r=this.state,o=r.R,i=r.h,a=(this.props.checked?this.i:this.o)+n-o;r.N||n===o||this.setState({N:!0});var s=Math.min(this.i,Math.max(this.o,a));s!==i&&this.setState({h:s})},t.prototype.U=function(n){var r=this.state,o=r.h,i=r.N,a=r.B,s=this.props.checked,l=(this.i+this.o)/2;this.setState({h:this.props.checked?this.i:this.o});var u=Date.now()-a;(!i||u<250||s&&o<=l||!s&&o>=l)&&this.A(n),this.W&&this.setState({N:!1,j:!1}),this.l=Date.now()},t.prototype.p=function(n){n.preventDefault(),typeof n.button=="number"&&n.button!==0||(this.I(n.clientX),window.addEventListener("mousemove",this.v),window.addEventListener("mouseup",this.g))},t.prototype.v=function(n){n.preventDefault(),this.L(n.clientX)},t.prototype.g=function(n){this.U(n),window.removeEventListener("mousemove",this.v),window.removeEventListener("mouseup",this.g)},t.prototype.k=function(n){this.X=null,this.I(n.touches[0].clientX)},t.prototype.m=function(n){this.L(n.touches[0].clientX)},t.prototype.M=function(n){n.preventDefault(),this.U(n)},t.prototype.$=function(n){Date.now()-this.l>50&&(this.A(n),Date.now()-this.u>50&&this.W&&this.setState({j:!1}))},t.prototype.C=function(){this.u=Date.now()},t.prototype.D=function(){this.setState({j:!0})},t.prototype.O=function(){this.setState({j:!1})},t.prototype.S=function(n){this.H=n},t.prototype.T=function(n){n.preventDefault(),this.H.focus(),this.A(n),this.W&&this.setState({j:!1})},t.prototype.A=function(n){var r=this.props;(0,r.onChange)(!r.checked,n,r.id)},t.prototype.render=function(){var n=this.props,r=n.checked,o=n.disabled,i=n.className,a=n.offColor,s=n.onColor,l=n.offHandleColor,u=n.onHandleColor,c=n.checkedIcon,f=n.uncheckedIcon,d=n.checkedHandleIcon,p=n.uncheckedHandleIcon,v=n.boxShadow,y=n.activeBoxShadow,_=n.height,m=n.width,h=n.borderRadius,g=function(P,I){var C={};for(var O in P)Object.prototype.hasOwnProperty.call(P,O)&&I.indexOf(O)===-1&&(C[O]=P[O]);return C}(n,["checked","disabled","className","offColor","onColor","offHandleColor","onHandleColor","checkedIcon","uncheckedIcon","checkedHandleIcon","uncheckedHandleIcon","boxShadow","activeBoxShadow","height","width","borderRadius","handleDiameter"]),S=this.state,k=S.h,T=S.N,N=S.j,M={position:"relative",display:"inline-block",textAlign:"left",opacity:o?.5:1,direction:"ltr",borderRadius:_/2,WebkitTransition:"opacity 0.25s",MozTransition:"opacity 0.25s",transition:"opacity 0.25s",touchAction:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",userSelect:"none"},G={height:_,width:m,margin:Math.max(0,(this.t-_)/2),position:"relative",background:og(k,this.i,this.o,a,s),borderRadius:typeof h=="number"?h:_/2,cursor:o?"default":"pointer",WebkitTransition:T?null:"background 0.25s",MozTransition:T?null:"background 0.25s",transition:T?null:"background 0.25s"},$={height:_,width:Math.min(1.5*_,m-(this.t+_)/2+1),position:"relative",opacity:(k-this.o)/(this.i-this.o),pointerEvents:"none",WebkitTransition:T?null:"opacity 0.25s",MozTransition:T?null:"opacity 0.25s",transition:T?null:"opacity 0.25s"},X={height:_,width:Math.min(1.5*_,m-(this.t+_)/2+1),position:"absolute",opacity:1-(k-this.o)/(this.i-this.o),right:0,top:0,pointerEvents:"none",WebkitTransition:T?null:"opacity 0.25s",MozTransition:T?null:"opacity 0.25s",transition:T?null:"opacity 0.25s"},ce={height:this.t,width:this.t,background:og(k,this.i,this.o,l,u),display:"inline-block",cursor:o?"default":"pointer",borderRadius:typeof h=="number"?h-1:"50%",position:"absolute",transform:"translateX("+k+"px)",top:Math.max(0,(_-this.t)/2),outline:0,boxShadow:N?y:v,border:0,WebkitTransition:T?null:"background-color 0.25s, transform 0.25s, box-shadow 0.15s",MozTransition:T?null:"background-color 0.25s, transform 0.25s, box-shadow 0.15s",transition:T?null:"background-color 0.25s, transform 0.25s, box-shadow 0.15s"},re={height:this.t,width:this.t,opacity:Math.max(2*(1-(k-this.o)/(this.i-this.o)-.5),0),position:"absolute",left:0,top:0,pointerEvents:"none",WebkitTransition:T?null:"opacity 0.25s",MozTransition:T?null:"opacity 0.25s",transition:T?null:"opacity 0.25s"},w={height:this.t,width:this.t,opacity:Math.max(2*((k-this.o)/(this.i-this.o)-.5),0),position:"absolute",left:0,top:0,pointerEvents:"none",WebkitTransition:T?null:"opacity 0.25s",MozTransition:T?null:"opacity 0.25s",transition:T?null:"opacity 0.25s"};return V.createElement("div",{className:i,style:M},V.createElement("div",{className:"react-switch-bg",style:G,onClick:o?null:this.T,onMouseDown:function(P){return P.preventDefault()}},c&&V.createElement("div",{style:$},c),f&&V.createElement("div",{style:X},f)),V.createElement("div",{className:"react-switch-handle",style:ce,onClick:function(P){return P.preventDefault()},onMouseDown:o?null:this.p,onTouchStart:o?null:this.k,onTouchMove:o?null:this.m,onTouchEnd:o?null:this.M,onTouchCancel:o?null:this.O},p&&V.createElement("div",{style:re},p),d&&V.createElement("div",{style:w},d)),V.createElement("input",Hf({},{type:"checkbox",role:"switch","aria-checked":r,checked:r,disabled:o,style:{border:0,clip:"rect(0 0 0 0)",height:1,margin:-1,overflow:"hidden",padding:0,position:"absolute",width:1}},g,{ref:this.S,onFocus:this.D,onBlur:this.O,onKeyUp:this.C,onChange:this.$})))},t}(L.Component);Os.defaultProps={disabled:!1,offColor:"#888",onColor:"#080",offHandleColor:"#fff",onHandleColor:"#fff",uncheckedIcon:oD,checkedIcon:iD,boxShadow:null,activeBoxShadow:"0 0 2px 3px #3bf",height:28,width:56};const aD=Os.default?Os.default:Os;function sD({checked:e=!1,onChange:t,theme:n,name:r}){return R(aD,{onChange:t,checked:e,uncheckedIcon:!1,checkedIcon:!1,offColor:n==="dark"?"#393939":"#e9e9e9",onColor:n==="dark"?"#306081":"#005caf",offHandleColor:"#fff",onHandleColor:"#fff",handleDiameter:24,height:28,width:44,className:"rs",name:r})}const x$=Jt(e=>({theme:_h(e)}))(sD),lD="_ToggleSwitch_10mtp_1",uD="_slider_10mtp_28",ig={ToggleSwitch:lD,slider:uD};function cD({options:e,value:t,name:n,onChange:r}){const o=L.useMemo(()=>e.map(s=>s.value).indexOf(t),[e,t]),i=L.useCallback(s=>{const l=Math.floor(100/e.length);if(s===e.length-1)return 100-e.length*l+l;if(s>-1)return l},[e]),a=L.useMemo(()=>({width:i(o)+"%",left:o*i(0)+"%"}),[o,i]);return le("div",{className:ig.ToggleSwitch,children:[R("div",{className:ig.slider,style:a}),e.map((s,l)=>{const u=`${n}-${s.label}`;return le("label",{htmlFor:u,className:l===0?"":"border-left",style:{width:i(l)+"%"},children:[R("input",{id:u,name:n,type:"radio",value:s.value,checked:t===s.value,onChange:r}),R("div",{children:s.label})]},u)})]})}V.memo(cD);const fD=new Q0,dD=new gR({queryCache:fD,defaultOptions:{queries:{suspense:!0}}});var bl="NOT_FOUND";function hD(e){var t;return{get:function(r){return t&&e(t.key,r)?t.value:bl},put:function(r,o){t={key:r,value:o}},getEntries:function(){return t?[t]:[]},clear:function(){t=void 0}}}function pD(e,t){var n=[];function r(s){var l=n.findIndex(function(c){return t(s,c.key)});if(l>-1){var u=n[l];return l>0&&(n.splice(l,1),n.unshift(u)),u.value}return bl}function o(s,l){r(s)===bl&&(n.unshift({key:s,value:l}),n.length>e&&n.pop())}function i(){return n}function a(){n=[]}return{get:r,put:o,getEntries:i,clear:a}}var vD=function(t,n){return t===n};function mD(e){return function(n,r){if(n===null||r===null||n.length!==r.length)return!1;for(var o=n.length,i=0;i<o;i++)if(!e(n[i],r[i]))return!1;return!0}}function gD(e,t){var n=typeof t=="object"?t:{equalityCheck:t},r=n.equalityCheck,o=r===void 0?vD:r,i=n.maxSize,a=i===void 0?1:i,s=n.resultEqualityCheck,l=mD(o),u=a===1?hD(l):pD(a,l);function c(){var f=u.get(arguments);if(f===bl){if(f=e.apply(null,arguments),s){var d=u.getEntries(),p=d.find(function(v){return s(v.value,f)});p&&(f=p.value)}u.put(arguments,f)}return f}return c.clearCache=function(){return u.clear()},c}function yD(e){var t=Array.isArray(e[0])?e[0]:e;if(!t.every(function(r){return typeof r=="function"})){var n=t.map(function(r){return typeof r=="function"?"function "+(r.name||"unnamed")+"()":typeof r}).join(", ");throw new Error("createSelector expects all input-selectors to be functions, but received the following types: ["+n+"]")}return t}function wD(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=function(){for(var a=arguments.length,s=new Array(a),l=0;l<a;l++)s[l]=arguments[l];var u=0,c,f={memoizeOptions:void 0},d=s.pop();if(typeof d=="object"&&(f=d,d=s.pop()),typeof d!="function")throw new Error("createSelector expects an output function after the inputs, but received: ["+typeof d+"]");var p=f,v=p.memoizeOptions,y=v===void 0?n:v,_=Array.isArray(y)?y:[y],m=yD(s),h=e.apply(void 0,[function(){return u++,d.apply(null,arguments)}].concat(_)),g=e(function(){for(var k=[],T=m.length,N=0;N<T;N++)k.push(m[N].apply(null,arguments));return c=h.apply(null,k),c});return Object.assign(g,{resultFunc:d,memoizedResultFunc:h,dependencies:m,lastResult:function(){return c},recomputations:function(){return u},resetRecomputations:function(){return u=0}}),g};return o}var SD=wD(gD);const qf=300,gS=e=>e.logs.logs,yS=e=>e.logs.tail,_D=e=>e.logs.searchText,k$=SD(gS,yS,_D,(e,t,n)=>{const r=[];for(let o=t;o>=0;o--)r.push(e[o]);if(e.length===qf)for(let o=qf-1;o>t;o--)r.push(e[o]);return n===""?r:r.filter(o=>o.payload.toLowerCase().indexOf(n)>=0)});function P$(e){return t=>{t("logsUpdateSearchText",n=>{n.logs.searchText=e.toLowerCase()})}}function T$(e){return(t,n)=>{const r=n(),o=gS(r),i=yS(r),a=i>=qf-1?0:i+1;o[a]=e,t("logsAppendLog",s=>{s.logs.tail=a})}}const bD={searchText:"",logs:[],tail:-1},Dh="/proxies";async function ED(e){const{url:t,init:n}=Ie(e);return await(await fetch(t+Dh,n)).json()}async function CD(e,t,n){const r={name:n},{url:o,init:i}=Ie(e),a=`${o}${Dh}/${t}`;return await fetch(a,{...i,method:"PUT",body:JSON.stringify(r)})}async function RD(e,t,n="https://www.gstatic.com/generate_204"){const{url:r,init:o}=Ie(e),i=`timeout=5000&url=${encodeURIComponent(n)}`,a=`${r}${Dh}/${encodeURIComponent(t)}/delay?${i}`;return await fetch(a,o)}async function L$(e,t,n="http://www.gstatic.com/generate_202"){const{url:r,init:o}=Ie(e),i=`url=${encodeURIComponent(n)}&timeout=2000`,a=`${r}/group/${encodeURIComponent(t)}/delay?${i}`;return await fetch(a,o)}async function OD(e){const{url:t,init:n}=Ie(e),r=await fetch(t+"/providers/proxies",n);return r.status===404?{providers:{}}:await r.json()}async function wS(e,t){const{url:n,init:r}=Ie(e),o={...r,method:"PUT"};return await fetch(n+"/providers/proxies/"+encodeURIComponent(t),o)}async function xD(e,t){const{url:n,init:r}=Ie(e),o={...r,method:"GET"};return await fetch(n+"/providers/proxies/"+encodeURIComponent(t)+"/healthcheck",o)}const kD={proxies:{},delay:{},groupNames:[],showModalClosePrevConns:!1},SS=()=>null,PD=["Direct","Fallback","Reject","Pass","Selector","URLTest","LoadBalance","Unknown"],TD=e=>e.proxies.proxies,_S=e=>e.proxies.delay,N$=e=>e.proxies.groupNames,LD=e=>e.proxies.proxyProviders||[],bS=e=>e.proxies.dangleProxyNames,A$=e=>e.proxies.showModalClosePrevConns;function Bo(e){return async(t,n)=>{const[r,o]=await Promise.all([ED(e),OD(e)]),{providers:i,proxies:a}=FD(o.providers),s={...a,...r.proxies},[l,u]=UD(s),f={..._S(n())};for(let p=0;p<u.length;p++){const v=u[p],{history:y}=s[v]||{history:[]},_=y[y.length-1];_&&typeof _.delay=="number"&&(f[v]={number:_.delay})}const d=[];for(const p of u)a[p]||d.push(p);t("store/proxies#fetchProxies",p=>{p.proxies.proxies=s,p.proxies.groupNames=l,p.proxies.delay=f,p.proxies.proxyProviders=i,p.proxies.dangleProxyNames=d})}}function M$(e,t){return async n=>{try{await wS(e,t)}catch{}n(Bo(e))}}function I$(e,t){return async n=>{for(let r=0;r<t.length;r++)try{await wS(e,t[r])}catch{}n(Bo(e))}}async function ES(e,t){try{await xD(e,t)}catch{}}function D$(e,t){return async n=>{await ES(e,t),await n(Bo(e))}}async function ND(e,t,n){const r=await gI(e);r.ok||console.log("unable to fetch all connections",r.statusText);const i=(await r.json()).connections,a=[];for(const s of i)s.chains.indexOf(t)>-1&&s.chains.indexOf(n)<0&&a.push(s.id);await Promise.all(a.map(s=>yI(e,s).catch(SS)))}function AD(e,t,n){const r=[n,t];let o,i=n;for(;(o=e[i])&&o.now;)r.unshift(o.now),i=o.now;return r}async function MD(e,t,n,r,o){try{if((await CD(n,r,o)).ok===!1)throw new Error("failed to switch proxy: res.statusText")}catch(a){throw console.log(a,"failed to swith proxy"),a}if(e(Bo(n)),s3(t())){const a=TD(t());RS(n,a,{groupName:r,itemName:o})}}function CS(){return e=>{e("closeModalClosePrevConns",t=>{t.proxies.showModalClosePrevConns=!1})}}function RS(e,t,n){const r=AD(t,n.groupName,n.itemName);ND(e,n.groupName,r[0])}function ID(e){return async(t,n)=>{var a;const r=n(),o=(a=r.proxies.switchProxyCtx)==null?void 0:a.to;if(!o){t(CS());return}const i=r.proxies.proxies;RS(e,i,o),t("closePrevConnsAndTheModal",s=>{s.proxies.showModalClosePrevConns=!1,s.proxies.switchProxyCtx=void 0})}}function $$(e,t,n){return async(r,o)=>{MD(r,o,e,t,n).catch(SS),r("store/proxies#switchProxy",i=>{const a=i.proxies.proxies;a[t]&&a[t].now&&(a[t].now=n)})}}function DD(e,t){return async(n,r)=>{const o=a3(r()),i=await RD(e,t,o);let a="";i.ok===!1&&(a=i.statusText);const{delay:s}=await i.json(),u={..._S(r()),[t]:{error:a,number:s}};n("requestDelayForProxyOnce",c=>{c.proxies.delay=u})}}function OS(e,t){return async n=>{await n(DD(e,t))}}function $D(e,t){return async(n,r)=>{const o=bS(r()),i=t.filter(a=>o.indexOf(a)>-1).map(a=>n(OS(e,a)));await Promise.all(i),await n(Bo(e))}}function U$(e){return async(t,n)=>{const r=bS(n());await Promise.all(r.map(i=>t(OS(e,i))));const o=LD(n());for(const i of o)await ES(e,i.name);await t(Bo(e))}}function UD(e){let t=[],n;const r=[];for(const o in e){const i=e[o];i.all&&Array.isArray(i.all)?(t.push(o),o==="GLOBAL"&&(n=Array.from(i.all))):PD.indexOf(i.type)<0&&r.push(o)}return n&&(n.push("GLOBAL"),t=t.map(o=>[n.indexOf(o),o]).sort((o,i)=>o[0]-i[0]).map(o=>o[1])),[t,r]}function FD(e){const t=Object.keys(e),n=[],r={};for(let o=0;o<t.length;o++){const i=e[t[o]];if(i.name==="default"||i.vehicleType==="Compatible")continue;const a=i.proxies,s=[];for(let l=0;l<a.length;l++){const u=a[l];r[u.name]=u,s.push(u.name)}i.proxies=s,n.push(i)}return{providers:n,proxies:r}}const jD={requestDelayForProxies:$D,closeModalClosePrevConns:CS,closePrevConnsAndTheModal:ID},F$=wA({key:"proxyFilterText",default:""}),BD={app:y3(),modals:n3,configs:i3,proxies:kD,logs:bD},zD={selectChartStyleIndex:h3,updateAppConfig:Rm,app:{updateCollapsibleIsOpen:p3,updateAppConfig:Rm,removeClashAPIConfig:c3,selectClashAPIConfig:f3},proxies:jD},VD="_app_tjhhp_1",WD="_content_tjhhp_17",xS={app:VD,content:WD},{lazy:xa,Suspense:kS}=Tt,HD=xa(()=>Ot(()=>import("./Connections-c9877694.js"),["./Connections-c9877694.js","./Select-04258549.js","./Select-20369999.css","./useRemainingViewPortHeight-dbe2192e.js","./BaseModal-f42f892a.js","./BaseModal-e9f180d4.css","./index-84fa0cb3.js","./Input-e46653b4.js","./memoize-one.esm-efa1e849.js","./Fab-47e19297.js","./Fab-0b65516b.css","./play-cb571d64.js","./Connections-796224c1.css"],import.meta.url)),qD=xa(()=>Ot(()=>import("./Config-0abd223e.js"),["./Config-0abd223e.js","./logs-ca50193b.js","./Select-04258549.js","./Select-20369999.css","./Input-e46653b4.js","./rotate-cw-e799f805.js","./Config-7eb3f1bb.css"],import.meta.url)),KD=xa(()=>Ot(()=>import("./Logs-7103293e.js"),["./Logs-7103293e.js","./logs-ca50193b.js","./debounce-c1ba2006.js","./useRemainingViewPortHeight-dbe2192e.js","./Fab-47e19297.js","./Fab-0b65516b.css","./play-cb571d64.js","./Logs-beb8fc98.css"],import.meta.url)),QD=xa(()=>Ot(()=>import("./Proxies-70a463d2.js"),["./Proxies-70a463d2.js","./BaseModal-f42f892a.js","./BaseModal-e9f180d4.css","./Fab-47e19297.js","./Fab-0b65516b.css","./TextFitler-3b17a569.js","./rotate-cw-e799f805.js","./debounce-c1ba2006.js","./TextFitler-cbc3b0fe.css","./index-84fa0cb3.js","./Select-04258549.js","./Select-20369999.css","./Proxies-dfd53d55.css"],import.meta.url)),GD=xa(()=>Ot(()=>import("./Rules-a8c74e6e.js"),["./Rules-a8c74e6e.js","./memoize-one.esm-efa1e849.js","./TextFitler-3b17a569.js","./rotate-cw-e799f805.js","./debounce-c1ba2006.js","./TextFitler-cbc3b0fe.css","./index-84fa0cb3.js","./Fab-47e19297.js","./Fab-0b65516b.css","./useRemainingViewPortHeight-dbe2192e.js","./Rules-162ef666.css"],import.meta.url)),XD=[{path:"/",element:R(xI,{})},{path:"/connections",element:R(HD,{})},{path:"/configs",element:R(qD,{})},{path:"/logs",element:R(KD,{})},{path:"/proxies",element:R(QD,{})},{path:"/rules",element:R(GD,{})},{path:"/about",element:R(U4,{})},!1].filter(Boolean);function YD(){return le(Cr,{children:[R(PM,{}),R(tD,{}),R("div",{className:xS.content,children:R(kS,{fallback:R(TI,{}),children:l1(XD)})})]})}const JD=()=>R(DM,{children:R(yA,{children:R(q3,{initialState:BD,actions:zD,children:R(ER,{client:dD,children:le("div",{className:xS.app,children:[R(AI,{}),R(kS,{fallback:R(sS,{}),children:R(kO,{children:le(EO,{children:[R(vf,{path:"/backend",element:R(Qw,{})}),R(vf,{path:"*",element:R(YD,{})})]})})})]})})})})}),ZD=Boolean(window.location.hostname==="localhost"||window.location.hostname==="[::1]"||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function e$(e){if("serviceWorker"in navigator){if(new URL("./",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",()=>{const n=".//sw.js";ZD?(t$(n,e),navigator.serviceWorker.ready.then(()=>{console.log("This web app is being served cache-first by a service worker")})):PS(n,e)})}}function PS(e,t){navigator.serviceWorker.register(e).then(n=>{n.onupdatefound=()=>{const r=n.installing;r!=null&&(r.onstatechange=()=>{r.state==="installed"&&(navigator.serviceWorker.controller?(console.log("New content is available and will be used when all tabs for this page are closed. See https://cra.link/PWA."),t&&t.onUpdate&&t.onUpdate(n)):(console.log("Content is cached for offline use."),t&&t.onSuccess&&t.onSuccess(n)))})}}).catch(n=>{console.error("Error during service worker registration:",n)})}function t$(e,t){fetch(e,{headers:{"Service-Worker":"script"}}).then(n=>{const r=n.headers.get("content-type");n.status===404||r!=null&&r.indexOf("javascript")===-1?navigator.serviceWorker.ready.then(o=>{o.unregister().then(()=>{window.location.reload()})}):PS(e,t)}).catch(()=>{console.log("No internet connection found. App is running in offline mode.")})}const TS=document.getElementById("app"),n$=N0(TS);B0.setAppElement(TS);n$.render(R(JD,{}));e$();console.log("Checkout the repo: https://github.com/MetaCubeX/yacd");console.log("Version:","0.3.7");window.onload=function(){const t=document.getElementById("app");t.addEventListener("touchstart",r$,{passive:!0}),t.addEventListener("touchmove",o$,!1),t.addEventListener("touchend",i$,!1)};const on={touching:!1,trace:[]};function r$(e){if(e.touches.length!==1){on.touching=!1,on.trace=[];return}on.touching=!0,on.trace=[{x:e.touches[0].screenX,y:e.touches[0].screenY}]}function o$(e){on.touching&&on.trace.push({x:e.touches[0].screenX,y:e.touches[0].screenY})}function i$(){if(!on.touching)return;const e=on.trace;on.touching=!1,on.trace=[],a$(e)}function a$(e){const t=["/","/proxies","/rules","/connections","/configs","/logs"],n=e[0],r=e[e.length-1],o=window.location.hash.slice(1),i=t.indexOf(o);console.log(i,o,t.length),i!==3&&(r.x-n.x>200&&i>0?window.location.hash=t[i-1]:r.x-n.x<-200&&i<t.length-1&&(window.location.hash=t[i+1],i===-1&&(window.location.hash=t[i+2])))}export{f$ as $,Cm as A,w4 as B,Xw as C,p$ as D,H3 as E,v$ as F,m$ as G,g$ as H,QA as I,y$ as J,w$ as K,Y0 as L,B0 as M,Cr as N,Gw as O,Uw as P,a3 as Q,V as R,x$ as S,O$ as T,_D as U,P$ as V,T$ as W,k$ as X,h$ as Y,E$ as Z,cn as _,ls as a,Ie as a0,c$ as a1,C$ as a2,u$ as a3,F$ as a4,PD as a5,rM as a6,oS as a7,TD as a8,_S as a9,$$ as aa,L$ as ab,Bo as ac,S$ as ad,_$ as ae,b$ as af,M$ as ag,I$ as ah,U$ as ai,Mh as aj,D$ as ak,s3 as al,Ah as am,N$ as an,LD as ao,A$ as ap,dR as aq,Ne as ar,ma as as,l$ as at,bR as au,Qe as av,xR as aw,zn as ax,wA as ay,R as b,Ar as c,Jt as d,yI as e,Er as f,sr as g,de as h,mo as i,le as j,va as k,L0 as l,Qm as m,R$ as n,mI as o,xe as p,Lh as q,L as r,qM as s,Zm as t,No as u,eg as v,YM as w,Tt as x,d$ as y,Br as z};
