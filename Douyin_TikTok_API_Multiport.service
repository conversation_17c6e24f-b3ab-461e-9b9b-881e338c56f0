[Unit]
Description=Douyin TikTok Download API Multi-Port Service
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/API/Douyin_TikTok_Download_API
Environment="PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
Environment="PYTHONPATH=/home/<USER>/API/Douyin_TikTok_Download_API"
ExecStart=/usr/bin/python3 /home/<USER>/API/Douyin_TikTok_Download_API/start_multiport.py
Restart=always
RestartSec=10

# 进程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 日志
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target