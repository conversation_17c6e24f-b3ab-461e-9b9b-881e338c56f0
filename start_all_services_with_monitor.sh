#!/bin/bash

echo "======================================"
echo "启动4个API服务实例和集成监控服务"
echo "======================================"

# 检查是否以root权限运行（需要systemctl权限）
if [ "$EUID" -ne 0 ] && [ -z "$SUDO_USER" ]; then 
    echo "请使用 sudo 运行此脚本"
    exit 1
fi

# 启动所有API服务
echo ""
echo "1. 启动API服务实例..."
echo "------------------------"

# 启动原始服务（端口8080）
echo "启动 API实例0 (端口 8080)..."
sudo systemctl start Douyin_TikTok_Download_API_New.service
sleep 2

# 复制新的服务文件
sudo cp /home/<USER>/API/Douyin_TikTok_API_Instance*.service /etc/systemd/system/
sudo systemctl daemon-reload

# 启动新的3个实例
for i in 1 2 3; do
    port=$((8080 + i))
    echo "启动 API实例$i (端口 $port)..."
    sudo systemctl enable Douyin_TikTok_API_Instance$i.service
    sudo systemctl start Douyin_TikTok_API_Instance$i.service
    sleep 2
done

# 检查所有API服务状态
echo ""
echo "2. 检查API服务状态..."
echo "------------------------"
sudo systemctl status Douyin_TikTok_Download_API_New.service --no-pager | grep -E "(Active:|Main PID)"
for i in 1 2 3; do
    sudo systemctl status Douyin_TikTok_API_Instance$i.service --no-pager | grep -E "(Active:|Main PID)"
done

# 启动集成监控服务 (JS版本)
echo ""
echo "3. 启动集成监控服务 (JS版本)..."
echo "------------------------"
sudo systemctl enable integrated-monitor-multi-js.service
sudo systemctl start integrated-monitor-multi-js.service
sleep 3

# 检查监控服务状态
echo ""
echo "4. 检查监控服务状态..."
echo "------------------------"
sudo systemctl status integrated-monitor-multi-js.service --no-pager | grep -E "(Active:|Main PID)"

# 显示访问信息
echo ""
echo "======================================"
echo "所有服务已启动！"
echo "======================================"
echo ""
echo "API服务访问地址："
echo "  实例0: http://localhost:8080"
echo "  实例1: http://localhost:8081"
echo "  实例2: http://localhost:8082"
echo "  实例3: http://localhost:8083"
echo ""
echo "查看服务日志："
echo "  sudo journalctl -u Douyin_TikTok_Download_API_New -f"
echo "  sudo journalctl -u Douyin_TikTok_API_Instance1 -f"
echo "  sudo journalctl -u Douyin_TikTok_API_Instance2 -f"
echo "  sudo journalctl -u Douyin_TikTok_API_Instance3 -f"
echo "  sudo journalctl -u integrated-monitor-multi-js -f"
echo ""
echo "停止所有服务："
echo "  sudo /home/<USER>/API/stop_all_services_with_monitor.sh"